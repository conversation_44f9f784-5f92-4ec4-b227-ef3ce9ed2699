<script setup lang="ts">
import { onLaunch, onShow, onHide } from "@dcloudio/uni-app";
import { reportError } from "./utils/error-handler";
import { StorageEnum } from "./utils/enum";
import { setToken } from "./utils/auth";
import { getDictionary } from "./services/dictionary";
import { initDictionary } from "./services/dictionary";
import { preloadCurrentOrgan } from "./services/organService";

interface ErrorWithMessage {
  message?: string;
  stack?: string;
}

// 初始化错误处理
const initErrorHandling = () => {
  // 设置全局的未处理Promise错误监听
  uni.onUnhandledRejection((res) => {
    let errorMessage = "未处理的Promise错误";
    let errorStack = "";

    // 安全地获取错误信息
    if (res.reason && typeof res.reason === "object") {
      const errorObj = res.reason as ErrorWithMessage;
      errorMessage = errorObj.message || errorMessage;
      errorStack = errorObj.stack || "";
    } else if (typeof res.reason === "string") {
      errorMessage = res.reason;
    } else {
      errorMessage = String(res.reason);
    }

    const error = {
      message: errorMessage,
      stack: errorStack,
      timestamp: Date.now(),
      platform: uni.getSystemInfoSync().platform,
      type: "unhandledRejection",
    };
    reportError(error);
  });

  // 监听小程序脚本错误
  uni.onError((err) => {
    const error = {
      message: err,
      timestamp: Date.now(),
      platform: uni.getSystemInfoSync().platform,
      type: "appError",
    };
    reportError(error);
  });
};

onLaunch(() => {
  // 获取当前环境
  console.log("当前环境:", import.meta.env.VITE_NODE_ENV);
  // 获取API地址
  console.log("API地址:", import.meta.env.VITE_API_URL);
  console.log("HYT API BASE:", import.meta.env.VITE_HYT_API_BASE);
  console.log("App Launch");

  // 初始化错误处理
  initErrorHandling();

  // 检查本地存储的错误日志，如果有则尝试上报
  const localLogs = uni.getStorageSync("error_logs");
  if (localLogs && Array.isArray(localLogs) && localLogs.length > 0) {
    console.log("发现本地错误日志，尝试上报...");
    // TODO: 实现错误日志上报逻辑
    // 上报成功后清除本地日志
    // uni.removeStorageSync('error_logs');
  }
  // const token = `eyJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OY9FM3C-jBnY2ARHcwxANxJ2YL0cEoXfl-WE9YvTfqo***HXGYAPP`;
  const token =
    "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiIzNzQ1ODM2NDMzNDQ5OTQzMDQ5YTE2MWM0Yzk1ZGM1ZDZmNDAxYjNhOTdhOTU5N2I2OCIsImlhdCI6MTc1MzI1NDc2Nywic3ViIjoie1widXNlcklkXCI6XCIzNzQ1ODM2NDMzNDQ5OTQzMDRcIixcImFjY291bnRJZFwiOlwiMzc0NTgzNjQzMzcwMTYwMTI4XCIsXCJ1c2VyVHlwZVwiOjAsXCJhcHBDb2RlXCI6XCJIWEdZQVBQXCIsXCJjaGFubmVsQ29kZVwiOlwiUEFUSUVOVF9XRUNIQVRcIixcImRldmljZW51bWJlclwiOlwiOWExNjFjNGM5NWRjNWQ2ZjQwMWIzYTk3YTk1OTdiNjhcIixcImRldmljZVR5cGVcIjpcIldYX0g1XCIsXCJhY2NvdW50Tm9cIjpcIjE4MzY1NDAzODg0XCIsXCJuYW1lXCI6XCLmtYvor5Xogpbkuq4xXCIsXCJkb2N0b3JJZFwiOm51bGwsXCJvcmdhbkNvZGVcIjpcIkhJRDAxMDFcIn0iLCJleHAiOjE3NTU4NDY3Njd9.0gANfobnGJYps0KqokmROmSVudpZCTE2NwzjG88YRnY***HXGYAPP";
  // // setToken(
  //   "eyJhbGciOiJIUzI1NiJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.OY9FM3C-jBnY2ARHcwxANxJ2YL0cEoXfl-WE9YvTfqo***HXGYAPP"
  // );
  setToken(token);
});

onShow(() => {
  console.log("App Show");
});

onHide(() => {
  console.log("App Hide");
});
</script>
<style lang="scss">
/* 引入颜色工具类，全局可用 */
@use "./styles/color-utils.scss";
view,
text {
  box-sizing: border-box;
}
</style>
