# HytEmpty 空状态组件

> 用于展示空状态的通用组件，支持自定义图片、文案和样式

## 📸 预览

空状态组件会自动垂直和水平居中，包含：
- 可选的虚线边框容器
- 自定义空状态图片
- 主要文案
- 描述文案
- 自定义插槽内容

## 🚀 基本用法

```vue
<template>
  <!-- 基础用法 -->
  <hyt-empty />
  
  <!-- 自定义文案 -->
  <hyt-empty text="暂无导诊单" description="当前就诊人暂无导诊单记录" />
  
  <!-- 自定义图片 -->
  <hyt-empty 
    image="/static/images/no-data.png"
    text="暂无数据"
    :image-width="160"
    :image-height="160"
  />
  
  <!-- 不显示边框 -->
  <hyt-empty 
    text="暂无内容"
    :show-border="false"
  />
  
  <!-- 带自定义操作按钮 -->
  <hyt-empty text="暂无就诊人">
    <view class="empty-action" @click="addPatient">
      添加就诊人
    </view>
  </hyt-empty>
</template>

<script setup>
import { HytEmpty } from "@/components/hyt-empty";

const addPatient = () => {
  // 添加就诊人逻辑
};
</script>
```

## 📋 Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| image | string | `/static/images/empty-box.png` | 空状态图片路径 |
| text | string | `暂无内容` | 主要文案 |
| description | string | `""` | 描述文案 |
| imageWidth | number | `200` | 图片宽度，单位rpx |
| imageHeight | number | `200` | 图片高度，单位rpx |
| showBorder | boolean | `true` | 是否显示虚线边框 |
| customClass | string | `""` | 自定义样式类名 |

## 🎨 插槽

| 插槽名 | 说明 |
|--------|------|
| default | 自定义内容，通常用于放置操作按钮 |

## 💡 使用场景

### 1. 列表为空
```vue
<hyt-empty 
  v-if="list.length === 0"
  text="暂无数据"
  description="暂时没有相关数据"
/>
```

### 2. 搜索无结果
```vue
<hyt-empty 
  v-if="searchResult.length === 0"
  image="/static/images/no-search.png"
  text="暂无搜索结果"
  description="请尝试其他关键词"
/>
```

### 3. 网络错误
```vue
<hyt-empty 
  image="/static/images/network-error.png"
  text="网络连接失败"
  description="请检查网络连接后重试"
  :show-border="false"
>
  <view class="retry-btn" @click="retry">
    重新加载
  </view>
</hyt-empty>
```

### 4. 权限不足
```vue
<hyt-empty 
  image="/static/images/no-permission.png"
  text="暂无访问权限"
  description="请联系管理员开通权限"
/>
```

## 🎯 设计规范

### 图片规范
- 推荐尺寸：200x200rpx
- 支持格式：PNG、JPG、SVG
- 建议使用浅色调、简洁的插画风格

### 文案规范
- 主文案：简洁明了，不超过8个字
- 描述文案：提供更多上下文，不超过20个字
- 语气友好，避免负面表达

### 布局规范
- 组件自动垂直水平居中
- 图片与文案间距：32rpx
- 主文案与描述文案间距：16rpx
- 最小高度：400rpx

## 🔧 自定义样式

```vue
<template>
  <hyt-empty 
    custom-class="my-empty"
    text="自定义样式"
  />
</template>

<style>
.my-empty {
  background-color: #f8f9fa;
  border-radius: 16rpx;
}

.my-empty .empty-text {
  color: #ff6b6b;
  font-weight: 600;
}
</style>
```

## 📱 响应式支持

组件支持响应式设计，在小屏设备上会自动调整：
- 减小内边距
- 调整字体大小
- 优化布局间距

## 🎨 主题定制

组件使用设计令牌系统，支持主题定制：
- 文字颜色：`colors.$text-primary`、`colors.$text-secondary`
- 边框颜色：`#d0d0d0`
- 背景颜色：`rgba(240, 240, 240, 0.3)`
