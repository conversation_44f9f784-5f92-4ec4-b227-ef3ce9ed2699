<template>
  <view class="hyt-empty" :class="customClass">
    <view class="empty-container">
      <!-- 空状态图片 -->
      <view
        class="empty-image-wrapper"
        :class="{ 'with-border': showBorder }"
        :style="imageWrapperStyle"
      >
        <image
          :src="imageUrl"
          class="empty-image"
          :style="imageStyle"
          mode="aspectFit"
        />
      </view>

      <!-- 空状态文案 -->
      <view class="empty-text" v-if="text"> {{ text }}</view>

      <!-- 描述文案 -->
      <view class="empty-description" v-if="description">
        {{ description }}
      </view>

      <!-- 自定义插槽 -->
      <slot></slot>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { EmptyProps } from "./types";
import emptyPng from "./assets/empty.png";

// Props
const props = withDefaults(defineProps<EmptyProps>(), {
  image: emptyPng,
  text: "暂无内容",
  description: "",
  imageWidth: 200,
  imageHeight: 200,
  showBorder: true,
  customClass: "",
});

// 计算属性
const imageUrl = computed(() => {
  return props.image;
});

const imageStyle = computed(() => {
  return {
    width: `${props.imageWidth}rpx`,
    height: `${props.imageHeight}rpx`,
  };
});

const imageWrapperStyle = computed(() => {
  const padding = 40; // 内边距
  return {
    width: `${props.imageWidth + padding * 2}rpx`,
    height: `${props.imageHeight + padding * 2}rpx`,
  };
});
</script>

<style lang="scss" scoped>
@use "@/styles/tokens/colors.scss" as colors;

.hyt-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  width: 100%;
  padding: 60rpx 40rpx;
  box-sizing: border-box;

  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;

    .empty-image-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 32rpx;
      position: relative;
      animation: fadeInUp 0.6s ease-out;

      &.with-border {
        border: 2rpx dashed #d0d0d0;
        border-radius: 16rpx;
        background-color: rgba(240, 240, 240, 0.3);
        animation: borderPulse 2s ease-in-out infinite;
      }

      .empty-image {
        display: block;
        animation: float 3s ease-in-out infinite;
      }
    }

    .empty-text {
      font-size: 32rpx;
      color: colors.$text-primary;
      font-weight: 500;
      margin-bottom: 16rpx;
      line-height: 1.4;
    }

    .empty-description {
      font-size: 26rpx;
      color: colors.$text-secondary;
      line-height: 1.5;
      margin-bottom: 32rpx;
      max-width: 500rpx;
    }
  }
}

// 动画定义
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes borderPulse {
  0%,
  100% {
    border-color: #d0d0d0;
    background-color: rgba(240, 240, 240, 0.3);
  }
  50% {
    border-color: #c0c0c0;
    background-color: rgba(240, 240, 240, 0.5);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

// 响应式适配
@media screen and (max-width: 750rpx) {
  .hyt-empty {
    min-height: 300rpx;
    padding: 40rpx 30rpx;

    .empty-container {
      .empty-text {
        font-size: 30rpx;
      }

      .empty-description {
        font-size: 24rpx;
      }
    }
  }
}
</style>
