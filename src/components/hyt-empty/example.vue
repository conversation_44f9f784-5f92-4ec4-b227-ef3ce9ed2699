<template>
  <view class="example-container">
    <view class="example-title">HytEmpty 组件使用示例</view>
    
    <!-- 基础用法 -->
    <view class="example-section">
      <view class="section-title">基础用法</view>
      <view class="example-box">
        <hyt-empty />
      </view>
    </view>
    
    <!-- 自定义文案 -->
    <view class="example-section">
      <view class="section-title">自定义文案</view>
      <view class="example-box">
        <hyt-empty 
          text="暂无导诊单" 
          description="当前就诊人暂无导诊单记录" 
        />
      </view>
    </view>
    
    <!-- 不显示边框 -->
    <view class="example-section">
      <view class="section-title">不显示边框</view>
      <view class="example-box">
        <hyt-empty 
          text="暂无数据"
          description="请稍后再试"
          :show-border="false"
        />
      </view>
    </view>
    
    <!-- 自定义图片尺寸 -->
    <view class="example-section">
      <view class="section-title">自定义图片尺寸</view>
      <view class="example-box">
        <hyt-empty 
          text="小尺寸图片"
          :image-width="120"
          :image-height="120"
        />
      </view>
    </view>
    
    <!-- 带操作按钮 -->
    <view class="example-section">
      <view class="section-title">带操作按钮</view>
      <view class="example-box">
        <hyt-empty 
          text="请先选择就诊人"
          description="选择就诊人后可查看相关信息"
          :show-border="false"
        >
          <view class="action-btn" @click="handleAction">
            选择就诊人
          </view>
        </hyt-empty>
      </view>
    </view>
    
    <!-- 网络错误状态 -->
    <view class="example-section">
      <view class="section-title">网络错误状态</view>
      <view class="example-box">
        <hyt-empty 
          image="/static/images/network-error.png"
          text="网络连接失败"
          description="请检查网络连接后重试"
          :show-border="false"
        >
          <view class="retry-btn" @click="handleRetry">
            重新加载
          </view>
        </hyt-empty>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
const handleAction = () => {
  uni.showToast({
    title: "选择就诊人",
    icon: "none",
  });
};

const handleRetry = () => {
  uni.showToast({
    title: "重新加载",
    icon: "none",
  });
};
</script>

<style lang="scss" scoped>
@use "@/styles/tokens/colors.scss" as colors;

.example-container {
  padding: 30rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.example-title {
  font-size: 36rpx;
  font-weight: 600;
  color: colors.$text-primary;
  text-align: center;
  margin-bottom: 40rpx;
}

.example-section {
  margin-bottom: 40rpx;

  .section-title {
    font-size: 28rpx;
    font-weight: 500;
    color: colors.$text-primary;
    margin-bottom: 20rpx;
    padding-left: 10rpx;
  }

  .example-box {
    background-color: white;
    border-radius: 16rpx;
    min-height: 400rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  }
}

.action-btn,
.retry-btn {
  padding: 20rpx 40rpx;
  background-color: colors.$color-primary;
  color: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin-top: 24rpx;

  &:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
}

.retry-btn {
  background-color: #ff6b6b;
}
</style>
