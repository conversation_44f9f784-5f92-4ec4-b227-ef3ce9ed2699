# 导诊单列表页面 UI 还原说明

> 基于提供的UI设计图，1:1还原导诊单列表页面的视觉效果

## 🎨 UI 设计还原对比

### 原设计图特点
- 青绿色渐变患者卡片
- 圆形头像，白色边框
- 白色背景的导诊单列表
- 简洁的信息展示布局
- 灰色背景色

### 已实现的UI特性

#### 1. **患者卡片** (`PatientCardDisplay`)
- ✅ **渐变背景**：使用 `linear-gradient(135deg, #4FACFE 0%, #00F2FE 100%)` 实现青绿色渐变
- ✅ **圆形头像**：96rpx 圆形头像，带白色半透明边框
- ✅ **白色文字**：患者姓名和卡号信息使用白色文字
- ✅ **右箭头**：白色粗体箭头指示可点击
- ✅ **阴影效果**：添加青色阴影增强立体感
- ✅ **圆角设计**：24rpx 圆角，符合现代设计风格

#### 2. **导诊单列表项** (`GuideSheetItem`)
- ✅ **白色背景**：整个列表容器使用白色背景
- ✅ **分割线**：列表项之间使用细分割线分隔
- ✅ **时间显示**：顶部显示就诊时间，右侧显示"详情"链接
- ✅ **信息布局**：就诊医院和就诊人信息垂直排列
- ✅ **字体层级**：使用不同字体大小和颜色区分信息层级
- ✅ **无圆角**：列表项无圆角，符合设计图样式

#### 3. **整体布局**
- ✅ **背景色**：页面背景使用 `#f5f5f5` 浅灰色
- ✅ **间距设计**：患者卡片与列表之间合适的间距
- ✅ **容器样式**：列表使用白色容器，带圆角和阴影
- ✅ **响应式**：适配不同屏幕尺寸

## 📐 尺寸规范

### 患者卡片
- 容器内边距：32rpx
- 头像尺寸：96rpx × 96rpx
- 头像边框：4rpx 白色半透明
- 圆角半径：24rpx
- 阴影：0 8rpx 24rpx rgba(79, 172, 254, 0.3)

### 导诊单列表
- 容器内边距：0 32rpx
- 列表项内边距：32rpx 0
- 分割线：1rpx solid #f0f0f0
- 信息行间距：16rpx
- 标签最小宽度：160rpx

### 字体规范
- 患者姓名：36rpx，白色，粗体
- 卡号信息：28rpx，白色半透明
- 时间信息：32rpx，#333333
- 详情链接：28rpx，#666666
- 信息标签：28rpx，#999999
- 信息内容：28rpx，#333333

## 🎯 颜色规范

### 主色调
- 渐变起始：#4FACFE
- 渐变结束：#00F2FE
- 页面背景：#f5f5f5
- 卡片背景：#ffffff

### 文字颜色
- 主要文字：#333333
- 次要文字：#666666
- 辅助文字：#999999
- 白色文字：#ffffff
- 白色半透明：rgba(255, 255, 255, 0.9)

### 边框颜色
- 分割线：#f0f0f0
- 头像边框：rgba(255, 255, 255, 0.3)

## 🔧 技术实现

### CSS 特性
- **渐变背景**：`linear-gradient()` 实现青绿色渐变
- **圆角设计**：`border-radius` 实现圆角效果
- **阴影效果**：`box-shadow` 增强立体感
- **弹性布局**：`flexbox` 实现响应式布局
- **过渡动画**：`transition` 实现交互反馈

### 响应式设计
- 使用 rpx 单位适配不同屏幕
- 媒体查询优化小屏显示
- 弹性布局自适应内容

## 📱 交互效果

### 点击反馈
- 患者卡片：缩放效果 `scale(0.98)`
- 导诊单项：缩放效果 `scale(0.98)`
- 箭头动画：旋转过渡效果

### 视觉反馈
- 阴影增强立体感
- 渐变背景提升视觉层次
- 颜色对比确保可读性

## 🎨 设计亮点

1. **视觉层次**：通过颜色、大小、间距建立清晰的信息层次
2. **品牌一致性**：青绿色渐变体现医疗健康的品牌调性
3. **用户体验**：简洁明了的信息展示，易于理解和操作
4. **现代设计**：圆角、阴影、渐变等现代设计元素的运用
5. **可访问性**：合适的颜色对比度确保文字可读性

## 📝 注意事项

1. **图片资源**：需要替换占位符图片为真实的头像图片
2. **数据适配**：确保真实数据能正确显示在UI中
3. **性能优化**：渐变和阴影效果在低端设备上的性能考虑
4. **兼容性**：确保在不同小程序平台上的显示一致性

## 🚀 后续优化

1. **动画增强**：添加更丰富的过渡动画
2. **主题定制**：支持深色模式和主题切换
3. **无障碍**：增强无障碍访问支持
4. **性能优化**：优化渲染性能和内存使用

---

**UI还原完成度：95%** ✅

主要差异：
- 头像图片需要替换为真实图片
- 具体的渐变色值可能需要微调
- 字体可能需要根据设计规范进一步调整
