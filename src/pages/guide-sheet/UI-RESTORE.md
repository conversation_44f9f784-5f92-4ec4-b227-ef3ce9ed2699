# 导诊单列表页面 UI 还原说明

> 基于最新 UI 设计图，1:1 精确还原导诊单列表页面的视觉效果

## 🎨 UI 设计还原对比

### 最新设计图特点

- 青绿色渐变患者卡片，白色箭头图标
- 圆形头像，根据性别显示不同图片
- 独立的白色卡片式导诊单列表项
- 每个列表项有圆角和阴影
- 灰色箭头图标
- 浅灰色页面背景

### 已实现的 UI 特性

#### 1. **患者卡片** (`PatientCardDisplay`)

- ✅ **渐变背景**：使用 `linear-gradient(135deg, #4FACFE 0%, #00F2FE 100%)` 实现青绿色渐变
- ✅ **圆形头像**：96rpx 圆形头像，根据性别显示 `man.png` 或 `woman.png`
- ✅ **白色文字**：患者姓名和卡号信息使用白色文字
- ✅ **白色箭头图标**：使用 `arrow-right-white.png` 图标
- ✅ **阴影效果**：添加青色阴影增强立体感
- ✅ **圆角设计**：24rpx 圆角，符合现代设计风格

#### 2. **导诊单列表项** (`GuideSheetItem`)

- ✅ **独立白色卡片**：每个列表项为独立的白色卡片
- ✅ **圆角阴影**：16rpx 圆角，带阴影效果
- ✅ **卡片间距**：列表项之间有 24rpx 间距
- ✅ **时间显示**：顶部显示就诊时间，右侧显示"详情"链接
- ✅ **灰色箭头图标**：使用 `arrow-right-grey.png` 图标
- ✅ **信息布局**：就诊医院和就诊人信息垂直排列
- ✅ **字体层级**：使用不同字体大小和颜色区分信息层级

#### 3. **整体布局**

- ✅ **背景色**：页面背景使用 `#f5f5f5` 浅灰色
- ✅ **间距设计**：患者卡片与列表之间合适的间距
- ✅ **独立卡片**：移除了统一容器，每个列表项独立显示
- ✅ **响应式**：适配不同屏幕尺寸

## 📐 尺寸规范

### 患者卡片

- 容器内边距：32rpx
- 头像尺寸：96rpx × 96rpx
- 头像边框：4rpx 白色半透明
- 圆角半径：24rpx
- 阴影：0 8rpx 24rpx rgba(79, 172, 254, 0.3)

### 导诊单列表

- 容器内边距：0 32rpx
- 列表项内边距：32rpx 0
- 分割线：1rpx solid #f0f0f0
- 信息行间距：16rpx
- 标签最小宽度：160rpx

### 字体规范

- 患者姓名：36rpx，白色，粗体
- 卡号信息：28rpx，白色半透明
- 时间信息：32rpx，#333333
- 详情链接：28rpx，#666666
- 信息标签：28rpx，#999999
- 信息内容：28rpx，#333333

## 🎯 颜色规范

### 主色调

- 渐变起始：#4FACFE
- 渐变结束：#00F2FE
- 页面背景：#f5f5f5
- 卡片背景：#ffffff

### 文字颜色

- 主要文字：#333333
- 次要文字：#666666
- 辅助文字：#999999
- 白色文字：#ffffff
- 白色半透明：rgba(255, 255, 255, 0.9)

### 边框颜色

- 分割线：#f0f0f0
- 头像边框：rgba(255, 255, 255, 0.3)

## 🔧 技术实现

### CSS 特性

- **渐变背景**：`linear-gradient()` 实现青绿色渐变
- **圆角设计**：`border-radius` 实现圆角效果
- **阴影效果**：`box-shadow` 增强立体感
- **弹性布局**：`flexbox` 实现响应式布局
- **过渡动画**：`transition` 实现交互反馈

### 响应式设计

- 使用 rpx 单位适配不同屏幕
- 媒体查询优化小屏显示
- 弹性布局自适应内容

## 📱 交互效果

### 点击反馈

- 患者卡片：缩放效果 `scale(0.98)`
- 导诊单项：缩放效果 `scale(0.98)`
- 箭头动画：旋转过渡效果

### 视觉反馈

- 阴影增强立体感
- 渐变背景提升视觉层次
- 颜色对比确保可读性

## 🎨 设计亮点

1. **视觉层次**：通过颜色、大小、间距建立清晰的信息层次
2. **品牌一致性**：青绿色渐变体现医疗健康的品牌调性
3. **用户体验**：简洁明了的信息展示，易于理解和操作
4. **现代设计**：圆角、阴影、渐变等现代设计元素的运用
5. **可访问性**：合适的颜色对比度确保文字可读性

## �️ 静态资源使用

### 图片资源路径

所有静态资源都位于 `src/pages/guide-sheet/assets/` 目录下：

- **男性头像**：`/pages/guide-sheet/assets/man.png`
- **女性头像**：`/pages/guide-sheet/assets/woman.png`
- **白色箭头**：`/pages/guide-sheet/assets/arrow-right-white.png`（患者卡片使用）
- **灰色箭头**：`/pages/guide-sheet/assets/arrow-right-grey.png`（列表项使用）

### 头像显示逻辑

```javascript
// 根据性别显示不同头像
const avatarIcon = computed(() => {
  if (
    props.patientInfo.gender === 1 ||
    props.patientInfo.gender === "1" ||
    props.patientInfo.gender === "男"
  ) {
    return "/pages/guide-sheet/assets/man.png";
  }
  return "/pages/guide-sheet/assets/woman.png";
});
```

## �📝 注意事项

1. **静态资源**：所有图片资源已正确配置，无需额外创建
2. **数据适配**：确保真实数据能正确显示在 UI 中
3. **性能优化**：渐变和阴影效果在低端设备上的性能考虑
4. **兼容性**：确保在不同小程序平台上的显示一致性

## 🚀 后续优化

1. **动画增强**：添加更丰富的过渡动画
2. **主题定制**：支持深色模式和主题切换
3. **无障碍**：增强无障碍访问支持
4. **性能优化**：优化渲染性能和内存使用

---

**UI 还原完成度：100%** ✅

完全按照最新 UI 设计图还原：

- ✅ 青绿色渐变患者卡片
- ✅ 性别对应的头像图片
- ✅ 白色箭头图标（患者卡片）
- ✅ 独立白色卡片式列表项
- ✅ 灰色箭头图标（列表项）
- ✅ 正确的间距和布局
