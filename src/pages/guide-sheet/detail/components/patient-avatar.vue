<template>
  <view class="patient-avatar">
    <view class="avatar-bg" :class="{ 'female': gender === '女' }">
      <svg width="48" height="48" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
        <!-- 头部 -->
        <circle cx="24" cy="18" r="8" :fill="avatarColor" />
        <!-- 身体 -->
        <path d="M12 40c0-8 5.5-12 12-12s12 4 12 12" :fill="avatarColor" />
      </svg>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  gender?: string;
}

const props = withDefaults(defineProps<Props>(), {
  gender: '男'
});

const avatarColor = computed(() => {
  return props.gender === '女' ? '#FF9999' : '#66B3FF';
});
</script>

<style lang="scss" scoped>
.patient-avatar {
  width: 96rpx;
  height: 96rpx;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  .avatar-bg {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #66B3FF 0%, #4A90E2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;

    &.female {
      background: linear-gradient(135deg, #FF9999 0%, #FF6B6B 100%);
    }

    svg {
      width: 48rpx;
      height: 48rpx;
    }
  }
}
</style>
