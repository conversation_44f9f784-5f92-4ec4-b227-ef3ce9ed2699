<template>
  <canvas 
    class="qrcode-canvas"
    :canvas-id="canvasId"
    :style="{ width: size + 'rpx', height: size + 'rpx' }"
  ></canvas>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";

// Props
interface Props {
  value: string;
  size: number;
}

const props = defineProps<Props>();

// 生成唯一的canvas ID
const canvasId = ref(`qrcode-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);

onMounted(() => {
  drawQRCode();
});

const drawQRCode = () => {
  const ctx = uni.createCanvasContext(canvasId.value);
  if (!ctx) return;

  const canvasSize = uni.upx2px(props.size);
  
  // 清空画布
  ctx.clearRect(0, 0, canvasSize, canvasSize);
  
  // 设置背景色
  ctx.setFillStyle('#ffffff');
  ctx.fillRect(0, 0, canvasSize, canvasSize);

  if (!props.value) {
    ctx.draw();
    return;
  }

  try {
    // 简化的二维码生成（实际项目中应该使用专业的二维码库）
    // 这里只是绘制一个模拟的二维码图案
    generateSimpleQRCode(ctx, canvasSize);
    ctx.draw();
  } catch (error) {
    console.error('绘制二维码失败:', error);
    
    // 绘制错误提示
    ctx.setFillStyle('#ff0000');
    ctx.setFontSize(12);
    ctx.setTextAlign('center');
    ctx.fillText('二维码生成失败', canvasSize / 2, canvasSize / 2);
    ctx.draw();
  }
};

const generateSimpleQRCode = (ctx: any, size: number) => {
  // 简化的二维码模式生成
  const moduleCount = 25; // 二维码模块数量
  const moduleSize = Math.floor(size / moduleCount);
  const offset = (size - (moduleCount * moduleSize)) / 2;

  // 生成伪随机模式（基于输入值）
  const pattern = generatePattern(props.value, moduleCount);

  ctx.setFillStyle('#000000');

  // 绘制定位标记（左上角）
  drawFinderPattern(ctx, offset, offset, moduleSize);
  // 绘制定位标记（右上角）
  drawFinderPattern(ctx, offset + (moduleCount - 7) * moduleSize, offset, moduleSize);
  // 绘制定位标记（左下角）
  drawFinderPattern(ctx, offset, offset + (moduleCount - 7) * moduleSize, moduleSize);

  // 绘制数据模块
  for (let row = 0; row < moduleCount; row++) {
    for (let col = 0; col < moduleCount; col++) {
      // 跳过定位标记区域
      if (isFinderPatternArea(row, col, moduleCount)) {
        continue;
      }

      if (pattern[row] && pattern[row][col]) {
        const x = offset + col * moduleSize;
        const y = offset + row * moduleSize;
        ctx.fillRect(x, y, moduleSize, moduleSize);
      }
    }
  }
};

const generatePattern = (value: string, size: number): boolean[][] => {
  const pattern: boolean[][] = [];
  
  // 基于输入值生成伪随机种子
  let seed = 0;
  for (let i = 0; i < value.length; i++) {
    seed += value.charCodeAt(i);
  }

  // 生成模式
  for (let row = 0; row < size; row++) {
    pattern[row] = [];
    for (let col = 0; col < size; col++) {
      // 使用简单的伪随机算法
      seed = (seed * 9301 + 49297) % 233280;
      pattern[row][col] = (seed / 233280) > 0.5;
    }
  }

  return pattern;
};

const drawFinderPattern = (ctx: any, x: number, y: number, moduleSize: number) => {
  // 绘制7x7的定位标记
  const pattern = [
    [1,1,1,1,1,1,1],
    [1,0,0,0,0,0,1],
    [1,0,1,1,1,0,1],
    [1,0,1,1,1,0,1],
    [1,0,1,1,1,0,1],
    [1,0,0,0,0,0,1],
    [1,1,1,1,1,1,1]
  ];

  for (let row = 0; row < 7; row++) {
    for (let col = 0; col < 7; col++) {
      if (pattern[row][col]) {
        ctx.fillRect(
          x + col * moduleSize,
          y + row * moduleSize,
          moduleSize,
          moduleSize
        );
      }
    }
  }
};

const isFinderPatternArea = (row: number, col: number, size: number): boolean => {
  // 检查是否在定位标记区域内
  // 左上角
  if (row < 9 && col < 9) return true;
  // 右上角
  if (row < 9 && col >= size - 8) return true;
  // 左下角
  if (row >= size - 8 && col < 9) return true;
  
  return false;
};
</script>

<style lang="scss" scoped>
.qrcode-canvas {
  border: 1rpx solid #e9ecef;
  background-color: white;
  border-radius: 4rpx;
}
</style>
