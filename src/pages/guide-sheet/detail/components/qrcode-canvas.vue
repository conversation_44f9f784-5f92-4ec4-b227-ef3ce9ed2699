<template>
  <canvas 
    :canvas-id="canvasId" 
    class="qrcode-canvas"
    :style="{ width: size + 'px', height: size + 'px' }"
  ></canvas>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from "vue";

interface Props {
  value: string;
  size?: number;
}

const props = withDefaults(defineProps<Props>(), {
  size: 200,
});

const canvasId = ref(`qrcode-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);

/**
 * 绘制二维码
 */
const drawQrcode = () => {
  if (!props.value) return;

  const ctx = uni.createCanvasContext(canvasId.value);
  
  // 清空画布
  ctx.clearRect(0, 0, props.size, props.size);
  
  // 设置背景色
  ctx.setFillStyle('#ffffff');
  ctx.fillRect(0, 0, props.size, props.size);
  
  // 简单的二维码绘制逻辑（模拟）
  // 实际项目中应该使用专业的二维码生成库
  const moduleSize = Math.floor(props.size / 25); // 25x25的模块
  const offset = (props.size - moduleSize * 25) / 2;
  
  ctx.setFillStyle('#000000');
  
  // 生成简单的二维码模式
  for (let row = 0; row < 25; row++) {
    for (let col = 0; col < 25; col++) {
      // 简单的模式生成逻辑
      const shouldFill = (row + col + props.value.length) % 3 === 0 ||
                        (row === 0 || row === 24 || col === 0 || col === 24) ||
                        (row < 7 && col < 7) ||
                        (row < 7 && col > 17) ||
                        (row > 17 && col < 7);
      
      if (shouldFill) {
        ctx.fillRect(
          offset + col * moduleSize,
          offset + row * moduleSize,
          moduleSize,
          moduleSize
        );
      }
    }
  }
  
  ctx.draw();
};

// 监听值变化
watch(() => props.value, () => {
  drawQrcode();
});

// 组件挂载后绘制
onMounted(() => {
  // 延迟绘制，确保canvas已经渲染
  setTimeout(() => {
    drawQrcode();
  }, 100);
});
</script>

<style lang="scss" scoped>
.qrcode-canvas {
  border: 1rpx solid #e9ecef;
  background-color: white;
  border-radius: 4rpx;
}
</style>
