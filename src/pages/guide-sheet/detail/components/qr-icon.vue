<template>
  <view class="qr-icon" @click="$emit('click')">
    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="2" y="2" width="8" height="8" rx="1" stroke="#666" stroke-width="1.5" fill="none"/>
      <rect x="14" y="2" width="8" height="8" rx="1" stroke="#666" stroke-width="1.5" fill="none"/>
      <rect x="2" y="14" width="8" height="8" rx="1" stroke="#666" stroke-width="1.5" fill="none"/>
      <rect x="4" y="4" width="4" height="4" rx="0.5" fill="#666"/>
      <rect x="16" y="4" width="4" height="4" rx="0.5" fill="#666"/>
      <rect x="4" y="16" width="4" height="4" rx="0.5" fill="#666"/>
      <rect x="14" y="14" width="2" height="2" fill="#666"/>
      <rect x="18" y="14" width="2" height="2" fill="#666"/>
      <rect x="14" y="18" width="2" height="2" fill="#666"/>
      <rect x="18" y="18" width="2" height="2" fill="#666"/>
      <rect x="16" y="16" width="2" height="2" fill="#666"/>
    </svg>
  </view>
</template>

<script setup lang="ts">
defineEmits<{
  click: [];
}>();
</script>

<style lang="scss" scoped>
.qr-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  cursor: pointer;
  
  &:active {
    opacity: 0.7;
  }
}
</style>
