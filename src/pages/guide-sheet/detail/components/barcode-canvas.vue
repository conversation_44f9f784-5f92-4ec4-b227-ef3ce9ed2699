<template>
  <canvas 
    class="barcode-canvas"
    :canvas-id="canvasId"
    :style="{ width: width + 'rpx', height: height + 'rpx' }"
  ></canvas>
</template>

<script setup lang="ts">
import { onMounted, ref } from "vue";

// Props
interface Props {
  value: string;
  width: number;
  height: number;
}

const props = defineProps<Props>();

// 生成唯一的canvas ID
const canvasId = ref(`barcode-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`);

// 条形码字符映射 (CODE128)
const CODE128_PATTERNS = {
  '0': '11011001100',
  '1': '11001101100',
  '2': '11001100110',
  '3': '10010011000',
  '4': '10010001100',
  '5': '10001001100',
  '6': '10011001000',
  '7': '10011000100',
  '8': '10001100100',
  '9': '11001001000',
  'A': '11001000100',
  'B': '11000100100',
  'C': '10110011100',
  'D': '10011011100',
  'E': '10011001110',
  'F': '10111001000',
  'G': '10011101000',
  'H': '10011100100',
  'I': '11001110010',
  'J': '11001011100',
  'K': '11001001110',
  'L': '11011100100',
  'M': '11001110100',
  'N': '11101101110',
  'O': '11101001100',
  'P': '11100101100',
  'Q': '11100100110',
  'R': '11101100100',
  'S': '11100110100',
  'T': '11100110010',
  'U': '11011011000',
  'V': '11011000110',
  'W': '11000110110',
  'X': '10100011000',
  'Y': '10001011000',
  'Z': '10001000110',
  ' ': '10110001000',
  '!': '10001101000',
  '"': '10001100010',
  '#': '11010001000',
  '$': '11000101000',
  '%': '11000100010',
  '&': '10110111000',
  "'": '10110001110',
  '(': '10001101110',
  ')': '10111011000',
  '*': '10111000110',
  '+': '10001110110',
  ',': '11101110110',
  '-': '11010001110',
  '.': '11000101110',
  '/': '11011101000',
  ':': '11011100010',
  ';': '11011101110',
  '<': '11101011000',
  '=': '11101000110',
  '>': '11100010110',
  '?': '11101101000',
  '@': '11101100010',
  '[': '11100011010',
  '\\': '11101111010',
  ']': '11001000010',
  '^': '11110001010',
  '_': '10100110000',
  '`': '10100001100',
  '{': '10010110000',
  '|': '10010000110',
  '}': '10000101100',
  '~': '10000100110',
};

// 起始和结束模式
const START_PATTERN = '11010000100';
const STOP_PATTERN = '1100011101011';

onMounted(() => {
  drawBarcode();
});

const drawBarcode = () => {
  const ctx = uni.createCanvasContext(canvasId.value);
  if (!ctx) return;

  const canvasWidth = uni.upx2px(props.width);
  const canvasHeight = uni.upx2px(props.height);
  
  // 清空画布
  ctx.clearRect(0, 0, canvasWidth, canvasHeight);
  
  // 设置背景色
  ctx.setFillStyle('#ffffff');
  ctx.fillRect(0, 0, canvasWidth, canvasHeight);

  if (!props.value) {
    ctx.draw();
    return;
  }

  try {
    // 生成条形码模式
    let pattern = START_PATTERN;
    
    // 添加数据模式
    for (let i = 0; i < props.value.length; i++) {
      const char = props.value[i];
      const charPattern = CODE128_PATTERNS[char as keyof typeof CODE128_PATTERNS];
      if (charPattern) {
        pattern += charPattern;
      }
    }
    
    // 添加校验位（简化处理）
    pattern += CODE128_PATTERNS['0']; // 简化的校验位
    
    // 添加结束模式
    pattern += STOP_PATTERN;

    // 计算条形码宽度
    const barWidth = Math.floor(canvasWidth / pattern.length);
    const startX = (canvasWidth - (pattern.length * barWidth)) / 2;

    // 绘制条形码
    ctx.setFillStyle('#000000');
    
    for (let i = 0; i < pattern.length; i++) {
      if (pattern[i] === '1') {
        const x = startX + (i * barWidth);
        ctx.fillRect(x, 0, barWidth, canvasHeight);
      }
    }

    ctx.draw();
  } catch (error) {
    console.error('绘制条形码失败:', error);
    
    // 绘制错误提示
    ctx.setFillStyle('#ff0000');
    ctx.setFontSize(12);
    ctx.setTextAlign('center');
    ctx.fillText('条形码生成失败', canvasWidth / 2, canvasHeight / 2);
    ctx.draw();
  }
};
</script>

<style lang="scss" scoped>
.barcode-canvas {
  border: 1rpx solid #e9ecef;
  background-color: white;
  border-radius: 4rpx;
}
</style>
