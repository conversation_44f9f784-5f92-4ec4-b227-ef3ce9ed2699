<template>
  <hyt-page navbarTitle="导诊单详情" :loading="loading">
    <view class="guide-detail-container">
      <!-- 患者信息卡片 -->
      <view class="patient-card" v-if="guideDetail">
        <view class="card-header">
          <view class="patient-info">
            <text class="patient-name">{{
              guideDetail.patientName || ""
            }}</text>
            <text class="patient-gender">{{ guideDetail.gender || "" }}</text>
            <text class="register-no">{{ guideDetail.registerNo || "" }}</text>
          </view>
          <view class="card-badge">
            <text>门诊</text>
          </view>
        </view>

        <!-- 条形码/二维码区域 -->
        <view class="code-section" v-if="shouldShowCode">
          <!-- 条形码 -->
          <view class="barcode-section" v-if="shouldShowBarcode">
            <view class="code-label"
              >登记号条形码：{{ guideDetail.registerNo }}</view
            >
            <view class="barcode-container">
              <BarcodeCanvas
                :value="guideDetail.registerNo || ''"
                :width="barcodeWidth"
                :height="barcodeHeight"
              />
            </view>
          </view>

          <!-- 二维码 -->
          <view class="qrcode-section" v-if="shouldShowQrcode">
            <view class="code-label">登记号二维码</view>
            <view class="qrcode-container">
              <QrcodeCanvas
                :value="guideDetail.qrCode || guideDetail.registerNo || ''"
                :size="qrcodeSize"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 导诊信息 -->
      <view class="guide-content" v-if="guideDetail?.guideData">
        <view class="content-title">导诊信息</view>
        <view class="content-text" v-html="formattedGuideData"></view>
      </view>

      <!-- 预约按钮 -->
      <view class="appointment-section" v-if="guideDetail?.showAppointmentBtn">
        <view class="appointment-btn" @click="handleAppointment">
          预约挂号
        </view>
      </view>
    </view>
  </hyt-page>
</template>

<script setup lang="ts">
import { useGuideSheetDetail } from "./hooks";
import BarcodeCanvas from "./components/barcode-canvas.vue";
import QrcodeCanvas from "./components/qrcode-canvas.vue";

// 使用导诊单详情 Hook
const {
  // 响应式数据
  guideDetail,
  loading,
  shouldShowCode,
  shouldShowBarcode,
  shouldShowQrcode,
  formattedGuideData,
  barcodeWidth,
  barcodeHeight,
  qrcodeSize,

  // 方法
  handleAppointment,
} = useGuideSheetDetail();
</script>

<style lang="scss" scoped>
@use "@/styles/tokens/colors.scss" as colors;

.guide-detail-container {
  min-height: 100vh;
  background-color: colors.$color-background;
  padding: 21rpx 30rpx;
}

.patient-card {
  background-color: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .patient-info {
      display: flex;
      gap: 16rpx;
      align-items: center;

      .patient-name {
        font-size: 32rpx;
        font-weight: 600;
        color: colors.$text-primary;
      }

      .patient-gender,
      .register-no {
        font-size: 28rpx;
        color: colors.$text-secondary;
      }
    }

    .card-badge {
      background-color: colors.$color-primary;
      color: white;
      padding: 8rpx 16rpx;
      border-radius: 8rpx;
      font-size: 24rpx;
    }
  }

  .code-section {
    .barcode-section,
    .qrcode-section {
      margin-bottom: 24rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .code-label {
        font-size: 26rpx;
        color: colors.$text-secondary;
        margin-bottom: 16rpx;
      }

      .barcode-container,
      .qrcode-container {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 16rpx;
        background-color: #f8f9fa;
        border-radius: 8rpx;
      }
    }
  }
}

.guide-content {
  background-color: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .content-title {
    font-size: 32rpx;
    font-weight: 600;
    color: colors.$text-primary;
    margin-bottom: 16rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #e5e5e5;
  }

  .content-text {
    font-size: 28rpx;
    line-height: 1.6;
    color: colors.$text-primary;
    white-space: pre-wrap;
  }
}

.appointment-section {
  padding: 24rpx 0;

  .appointment-btn {
    background-color: colors.$color-primary;
    color: white;
    text-align: center;
    padding: 24rpx;
    border-radius: 12rpx;
    font-size: 32rpx;
    font-weight: 600;
    box-shadow: 0 4rpx 12rpx rgba(79, 172, 254, 0.3);

    &:active {
      transform: scale(0.98);
      transition: transform 0.1s ease;
    }
  }
}
</style>
