<template>
  <hyt-page navbarTitle="导诊单详情" :loading="loading">
    <view class="guide-detail-container">
      <!-- 患者信息头部 -->
      <view class="patient-header" v-if="guideDetail">
        <view class="patient-info">
          <PatientAvatar :gender="guideDetail.gender" />
          <view class="info-content">
            <view class="name-section">
              <text class="patient-name">{{
                guideDetail.patientName || ""
              }}</text>
              <view class="patient-badge">
                <text>门诊</text>
              </view>
            </view>
            <view class="detail-info">
              <text class="gender">{{ guideDetail.gender || "" }}</text>
              <text class="age">{{ patientAge }}</text>
              <text class="id-card">{{ maskedIdCard }}</text>
            </view>
          </view>
        </view>
        <QrIcon @click="showQrCodeModal" />
      </view>

      <!-- 条形码区域 -->
      <view class="barcode-section" v-if="guideDetail?.registerNo">
        <view class="barcode-container">
          <tki-barcode
            :val="guideDetail.registerNo"
            :show="true"
            :cid="`barcode-${guideDetail.registerNo}`"
            format="CODE128"
            :opations="barcodeOptions"
            unit="px"
          />
        </view>
        <view class="barcode-number">{{ guideDetail.registerNo }}</view>
      </view>

      <!-- 导诊信息 -->
      <view class="guide-content" v-if="guideDetail?.guideData">
        <view class="content-title">导诊信息</view>
        <view class="content-text" v-html="formattedGuideData"></view>
      </view>

      <!-- 费用信息 -->
      <view class="fee-info" v-if="guideDetail">
        <view class="fee-item">
          <text class="fee-label">工作Mini POS支付：</text>
          <text class="fee-value"
            >{{ guideDetail.miniPosAmount || "106.00" }}元</text
          >
        </view>
        <view class="fee-item">
          <text class="fee-label">医保基金支付：</text>
          <text class="fee-value"
            >{{ guideDetail.insuranceAmount || "144.00" }}元（主卡支付：{{
              guideDetail.mainCardAmount || "0.00"
            }}共济支付：{{ guideDetail.mutualAmount || "0.00" }}）</text
          >
        </view>
        <view class="fee-total">
          <text class="fee-label">合计：</text>
          <text class="fee-value total"
            >{{ guideDetail.totalAmount || "250.00" }}元</text
          >
        </view>
        <view class="fee-time">
          <text class="time-label">收费时间：</text>
          <text class="time-value">{{
            guideDetail.chargeTime || "2023-06-12 13:17:27"
          }}</text>
        </view>
        <view class="fee-time">
          <text class="time-label">打印时间：</text>
          <text class="time-value">{{
            guideDetail.printTime || "2023-06-12 13:18"
          }}</text>
        </view>
      </view>

      <!-- 温馨提示 -->
      <view class="notice-section" v-if="guideDetail">
        <text class="notice-text"
          >以上费用包含医保基金支付的，如需退费请在次日前到收费处办理退费手续，逾期将无法退费。</text
        >
      </view>

      <!-- 底部按钮 -->
      <view class="bottom-buttons" v-if="guideDetail">
        <view class="btn-container">
          <view class="btn btn-secondary" @click="handleGuideConsult">
            导诊指引
          </view>
          <view class="btn btn-primary" @click="handlePreCheck">
            预约检查
          </view>
        </view>
      </view>
    </view>

    <!-- 二维码弹窗 -->
    <view class="qr-modal" v-if="showQrModal" @click="hideQrCodeModal">
      <view class="qr-modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">{{ guideDetail?.patientName || "" }}</text>
          <view class="close-btn" @click="hideQrCodeModal">
            <text>×</text>
          </view>
        </view>
        <view class="qr-code-container">
          <uqrcode
            ref="qrcode"
            :value="guideDetail?.qrCode || guideDetail?.registerNo || ''"
            :size="qrcodeModalSize"
            :margin="0"
            :backgroundColor="'#ffffff'"
            :foregroundColor="'#000000'"
            :fileType="'png'"
            :errorCorrectLevel="'M'"
            :typeNumber="-1"
          />
        </view>
        <view class="register-number">
          <text>登记号：{{ guideDetail?.registerNo || "" }}</text>
        </view>
      </view>
    </view>
  </hyt-page>
</template>

<script setup lang="ts">
import { useGuideSheetDetail } from "./hooks";
import QrIcon from "./components/qr-icon.vue";
import PatientAvatar from "./components/patient-avatar.vue";
import tkiBarcode from "@/uni_modules/tki-barcode/tki-barcode.vue";
import uqrcode from "@/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue";

// 使用导诊单详情 Hook
const {
  // 响应式数据
  guideDetail,
  loading,
  formattedGuideData,
  barcodeOptions,
  qrcodeModalSize,
  avatarUrl,
  patientAge,
  maskedIdCard,
  showQrModal,

  // 方法
  showQrCodeModal,
  hideQrCodeModal,
  handleGuideConsult,
  handlePreCheck,
} = useGuideSheetDetail();
</script>

<style lang="scss" scoped>
@use "@/styles/tokens/colors.scss" as colors;

.guide-detail-container {
  min-height: 100vh;
  background-color: #f5f6f7;
  padding: 0;
  padding-bottom: 200rpx; // 为底部按钮留出空间
}

// 患者信息头部
.patient-header {
  background-color: white;
  padding: 32rpx 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #ebedf5;

  .patient-info {
    display: flex;
    align-items: center;
    flex: 1;
    gap: 24rpx;

    .info-content {
      flex: 1;

      .name-section {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        .patient-name {
          font-size: 36rpx;
          font-weight: 600;
          color: #333;
          margin-right: 16rpx;
        }

        .patient-badge {
          background-color: #3ad3c1;
          color: white;
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
          font-size: 20rpx;
        }
      }

      .detail-info {
        display: flex;
        align-items: center;
        gap: 16rpx;

        .gender,
        .age {
          font-size: 28rpx;
          color: #666;
        }

        .id-card {
          font-size: 28rpx;
          color: #666;
        }
      }
    }
  }
}

// 条形码区域
.barcode-section {
  background-color: white;
  padding: 40rpx;
  text-align: center;

  .barcode-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 16rpx;

    // 条形码组件样式
    :deep(.tki-barcode) {
      display: flex;
      flex-direction: column;
      align-items: center;

      .tki-barcode-canvas {
        margin-bottom: 8rpx;
      }
    }
  }

  .barcode-number {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
  }
}

// 导诊信息
.guide-content {
  background-color: white;
  margin-top: 24rpx;
  padding: 40rpx;

  .content-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }

  .content-text {
    font-size: 28rpx;
    line-height: 1.6;
    color: #333;
    white-space: pre-wrap;
  }
}

// 费用信息
.fee-info {
  background-color: white;
  margin-top: 24rpx;
  padding: 40rpx;

  .fee-item,
  .fee-total,
  .fee-time {
    display: flex;
    margin-bottom: 16rpx;
    font-size: 28rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .fee-label,
    .time-label {
      color: #666;
      flex-shrink: 0;
    }

    .fee-value,
    .time-value {
      color: #333;
      flex: 1;

      &.total {
        font-weight: 600;
        color: #333;
      }
    }
  }
}

// 温馨提示
.notice-section {
  background-color: white;
  margin-top: 24rpx;
  padding: 40rpx;

  .notice-text {
    font-size: 28rpx;
    line-height: 1.6;
    color: #666;
  }
}

// 底部按钮
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 24rpx 40rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);

  .btn-container {
    display: flex;
    gap: 24rpx;

    .btn {
      flex: 1;
      height: 88rpx;
      border-radius: 44rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      font-weight: 500;

      &.btn-secondary {
        background-color: #f5f6f7;
        color: #666;
        border: 1rpx solid #e0e0e0;
      }

      &.btn-primary {
        background-color: #3ad3c1;
        color: white;
      }

      &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
      }
    }
  }
}

// 二维码弹窗
.qr-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .qr-modal-content {
    background-color: white;
    border-radius: 24rpx;
    padding: 48rpx;
    margin: 40rpx;
    max-width: 600rpx;
    width: 100%;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40rpx;

      .modal-title {
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 48rpx;
        color: #999;
        line-height: 1;
      }
    }

    .qr-code-container {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 32rpx;

      // 二维码组件样式
      :deep(.uqrcode) {
        display: flex;
        justify-content: center;
        align-items: center;

        .uqrcode-canvas {
          border-radius: 8rpx;
        }
      }
    }

    .register-number {
      text-align: center;
      font-size: 28rpx;
      color: #666;
    }
  }
}
</style>
