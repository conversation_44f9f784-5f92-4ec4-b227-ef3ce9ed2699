<template>
  <hyt-page navbarTitle="导诊单详情" :loading="loading">
    <view class="guide-detail-container">
      <!-- 患者信息头部 -->
      <view class="patient-header" v-if="guideDetail">
        <view class="patient-info">
          <view class="avatar-section">
            <PatientAvatar :gender="guideDetail.gender" />
          </view>
          <view class="info-content">
            <view class="name-row">
              <text class="patient-name">{{
                guideDetail.patientName || "薛丽丽"
              }}</text>
              <view class="patient-badge">
                <text>本人</text>
              </view>
            </view>
            <view class="detail-row">
              <text class="gender">{{ guideDetail.gender || "女" }}</text>
              <text class="age">{{ patientAge || "32岁" }}</text>
              <text class="id-card">{{
                maskedIdCard || "510***********443"
              }}</text>
            </view>
          </view>
        </view>
        <view class="qr-icon-section" @click="showQrCodeModal">
          <image :src="QrIcon" mode="widthFix" style="width: 48rpx; height: 48rpx;" />
          <text class="arrow">></text>
        </view>
      </view>

      <!-- 条形码区域 -->
      <view class="barcode-section" v-if="guideDetail?.registerNo">
        <view class="barcode-wrapper">
          <tki-barcode
            :val="guideDetail.registerNo || '**********'"
            :show="true"
            :cid="`barcode-${guideDetail.registerNo || 'default'}`"
            format="CODE128"
            :opations="barcodeOptions"
            unit="px"
          />
        </view>
        <view class="barcode-number">{{
          guideDetail.registerNo || "**********"
        }}</view>
      </view>

      <!-- 导诊信息 -->
      <view class="guide-info-section">
        <view class="section-title">导诊信息</view>
        <view class="guide-content">
          <text class="guide-text">{{ formattedGuideText }}</text>
        </view>
      </view>

      <!-- 费用信息 -->
      <view class="fee-section">
        <view class="fee-item">
          <text class="fee-text">工行Mini POS支付：106.00元</text>
        </view>
        <view class="fee-item">
          <text class="fee-text"
            >医保基金支付：144.00元（主卡支付：0.00共济支付：0.00）</text
          >
        </view>
        <view class="fee-total">
          <text class="total-text">合计：250.00元</text>
        </view>
        <view class="time-info">
          <text class="time-text">收费时间：2023-06-12 13:17:27</text>
        </view>
        <view class="time-info">
          <text class="time-text">打印时间：2023-06-12 13:18</text>
        </view>
      </view>

      <!-- 温馨提示 -->
      <view class="notice-section">
        <text class="notice-text"
          >以上费用包含医保基金支付的，如需退费请在次日前三个工作日前到收费窗口办理，逾期将无法退费该单为病人医疗服务指引单，请妥善保管，便于科室检查！不做报销凭证使用完成缴费项目如有超声及内镜检查</text
        >
      </view>

      <!-- 底部按钮 -->
      <view class="bottom-buttons">
        <view class="btn-container">
          <view class="btn btn-guide" @click="handleGuideConsult">
            导诊指引
          </view>
          <view class="btn btn-appointment" @click="handlePreCheck">
            预约检查
          </view>
        </view>
      </view>
    </view>

    <!-- 二维码弹窗 -->
    <view class="qr-modal" v-if="showQrModal" @click="hideQrCodeModal">
      <view class="qr-modal-content" @click.stop>
        <view class="modal-header">
          <text class="modal-title">{{
            guideDetail?.patientName || "薛丽丽"
          }}</text>
          <view class="close-btn" @click="hideQrCodeModal">
            <text>×</text>
          </view>
        </view>
        <view class="qr-code-container">
          <uqrcode
            ref="qrcode"
            :value="
              guideDetail?.qrCode || guideDetail?.registerNo || '**********'
            "
            :size="qrcodeModalSize"
            :margin="0"
            :backgroundColor="'#ffffff'"
            :foregroundColor="'#000000'"
            :fileType="'png'"
            :errorCorrectLevel="'M'"
            :typeNumber="-1"
          />
        </view>
        <view class="register-number">
          <text>登记号：{{ guideDetail?.registerNo || "**********" }}</text>
        </view>
      </view>
    </view>
  </hyt-page>
</template>

<script setup lang="ts">
import { useGuideSheetDetail } from "./hooks";
import PatientAvatar from "./components/patient-avatar.vue";
import tkiBarcode from "@/uni_modules/tki-barcode/tki-barcode.vue";
import uqrcode from "@/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue";
import QrIconPng from '../assets/qrcode.png';

// 使用导诊单详情 Hook  
const {
  // 响应式数据
  guideDetail,
  loading,
  formattedGuideData,
  formattedGuideText,
  barcodeOptions,
  qrcodeModalSize,
  avatarUrl,
  patientAge,
  maskedIdCard,
  showQrModal,

  // 方法
  showQrCodeModal,
  hideQrCodeModal,
  handleGuideConsult,
  handlePreCheck,
} = useGuideSheetDetail();
</script>

<style lang="scss" scoped>
.guide-detail-container {
  min-height: 100vh;
  background-color: #f5f6f7;
  padding: 0;
  padding-bottom: 200rpx;
}

// 患者信息头部
.patient-header {
  background-color: white;
  padding: 32rpx 40rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;

  .patient-info {
    display: flex;
    align-items: center;
    flex: 1;

    .avatar-section {
      margin-right: 24rpx;
    }

    .info-content {
      flex: 1;

      .name-row {
        display: flex;
        align-items: center;
        margin-bottom: 12rpx;

        .patient-name {
          font-size: 36rpx;
          font-weight: 600;
          color: #333;
          margin-right: 16rpx;
        }

        .patient-badge {
          background-color: #3ad3c1;
          color: white;
          padding: 6rpx 16rpx;
          border-radius: 12rpx;
          font-size: 24rpx;
        }
      }

      .detail-row {
        display: flex;
        align-items: center;
        gap: 24rpx;

        .gender,
        .age,
        .id-card {
          font-size: 28rpx;
          color: #666;
        }
      }
    }
  }

  .qr-icon-section {
    display: flex;
    align-items: center;
    gap: 8rpx;

    .arrow {
      font-size: 32rpx;
      color: #ccc;
      font-weight: 300;
    }
  }
}

// 条形码区域
.barcode-section {
  background-color: white;
  padding: 60rpx 40rpx 40rpx;
  text-align: center;
  margin-top: 24rpx;

  .barcode-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 24rpx;

    // 条形码组件样式
    :deep(.tki-barcode) {
      display: flex;
      flex-direction: column;
      align-items: center;

      .tki-barcode-canvas {
        margin-bottom: 0;
      }

      image {
        width: 100% !important;
        max-width: 600rpx;
        height: auto;
      }
    }
  }

  .barcode-number {
    font-size: 32rpx;
    color: #333;
    font-weight: 600;
    letter-spacing: 2rpx;
  }
}

// 导诊信息
.guide-info-section {
  background-color: white;
  margin-top: 24rpx;
  padding: 40rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }

  .guide-content {
    .guide-text {
      font-size: 28rpx;
      line-height: 1.8;
      color: #333;
      text-align: justify;
    }
  }
}

// 费用信息
.fee-section {
  background-color: white;
  margin-top: 24rpx;
  padding: 40rpx;

  .fee-item {
    margin-bottom: 16rpx;

    .fee-text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
    }
  }

  .fee-total {
    margin: 24rpx 0 16rpx;

    .total-text {
      font-size: 28rpx;
      color: #333;
      font-weight: 600;
    }
  }

  .time-info {
    margin-bottom: 12rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .time-text {
      font-size: 28rpx;
      color: #333;
      line-height: 1.6;
    }
  }
}

// 温馨提示
.notice-section {
  background-color: white;
  margin-top: 24rpx;
  padding: 40rpx;

  .notice-text {
    font-size: 28rpx;
    line-height: 1.8;
    color: #333;
    text-align: justify;
  }
}

// 底部按钮
.bottom-buttons {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 32rpx 40rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);

  .btn-container {
    display: flex;
    gap: 32rpx;

    .btn {
      flex: 1;
      height: 96rpx;
      border-radius: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      font-weight: 500;

      &.btn-guide {
        background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
        color: white;
        box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.3);
      }

      &.btn-appointment {
        background: linear-gradient(135deg, #3ad3c1 0%, #2bb8a6 100%);
        color: white;
        box-shadow: 0 8rpx 24rpx rgba(58, 211, 193, 0.3);
      }

      &:active {
        transform: scale(0.98);
        transition: transform 0.1s ease;
      }
    }
  }
}

// 二维码弹窗
.qr-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;

  .qr-modal-content {
    background-color: white;
    border-radius: 24rpx;
    padding: 48rpx;
    margin: 40rpx;
    max-width: 600rpx;
    width: 100%;

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 40rpx;

      .modal-title {
        font-size: 36rpx;
        font-weight: 600;
        color: #333;
      }

      .close-btn {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 48rpx;
        color: #999;
        line-height: 1;
      }
    }

    .qr-code-container {
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 32rpx;

      // 二维码组件样式
      :deep(.uqrcode) {
        display: flex;
        justify-content: center;
        align-items: center;

        .uqrcode-canvas {
          border-radius: 8rpx;
        }
      }
    }

    .register-number {
      text-align: center;
      font-size: 28rpx;
      color: #666;
    }
  }
}
</style>
</style>
