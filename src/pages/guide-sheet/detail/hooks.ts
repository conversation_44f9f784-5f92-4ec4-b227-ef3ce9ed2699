/**
 * 导诊单详情页面 Hooks
 */
import { ref, computed, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getGuideSheetDetail } from "../shared/services";
import type { GuideSheetContent, GuideSheetDetailParams } from "../shared/types";

/**
 * 导诊单详情管理 Hook
 */
export const useGuideSheetDetail = () => {
  // 响应式数据
  const guideDetail = ref<GuideSheetContent>({});
  const loading = ref(false);

  // 页面参数
  const pageParams = ref<{
    cardId?: string;
    guideInfoId?: string;
    pmiNo?: string;
    purePlayKey?: string;
    purePlayParam?: string;
  }>({});

  /**
   * 页面加载时处理参数
   */
  onLoad((options: any) => {
    pageParams.value = {
      cardId: options.cardId || "",
      guideInfoId: options.guideInfoId || "",
      pmiNo: options.pmiNo || "",
      purePlayKey: options.purePlayKey || "",
      purePlayParam: options.purePlayParam || "",
    };
  });

  /**
   * 组件挂载时初始化
   */
  onMounted(async () => {
    await fetchGuideDetail();
  });

  /**
   * 获取导诊单详情
   */
  const fetchGuideDetail = async () => {
    const { cardId, guideInfoId, purePlayKey, purePlayParam } = pageParams.value;

    // 处理纯播放参数
    if (purePlayKey && purePlayParam) {
      try {
        const parsedParam = JSON.parse(purePlayParam);
        const { cardId: pureCardId, guideInfoId: pureGuideInfoId } = parsedParam;
        
        if (pureCardId && pureGuideInfoId) {
          await getDetailData(pureCardId, pureGuideInfoId);
        }
      } catch (error) {
        console.error("解析纯播放参数失败:", error);
        uni.showToast({
          title: "参数解析失败",
          icon: "none",
        });
      }
      return;
    }

    // 正常参数处理
    if (cardId && guideInfoId) {
      await getDetailData(cardId, guideInfoId);
    } else {
      uni.showToast({
        title: "缺少必要参数",
        icon: "none",
      });
    }
  };

  /**
   * 获取详情数据
   */
  const getDetailData = async (cardId: string, guideInfoId: string) => {
    try {
      loading.value = true;

      const params: GuideSheetDetailParams = {
        cardId,
        guideInfoId,
      };

      const response = await getGuideSheetDetail(params);
      guideDetail.value = response;
    } catch (error) {
      console.error("获取导诊单详情失败:", error);
      uni.showToast({
        title: "获取导诊单详情失败",
        icon: "none",
      });
    } finally {
      loading.value = false;
    }
  };

  // 二维码弹窗状态
  const showQrModal = ref(false);

  // 计算属性
  const formattedGuideData = computed(() => {
    const { guideData } = guideDetail.value;
    if (!guideData) return "";

    // 简单的HTML格式化处理
    return guideData
      .replace(/\n/g, "<br/>")
      .replace(/\t/g, "&nbsp;&nbsp;&nbsp;&nbsp;");
  });

  // 导诊信息文本（按UI图显示的内容）
  const formattedGuideText = computed(() => {
    const { guideData } = guideDetail.value;
    if (guideData) return guideData;

    // 默认显示UI图中的内容
    return "1、门诊楼1楼A1区患者服务中心5号、6号窗口预约CT胸部普通扫描(开医嘱时注意：如头部1部、胸部和肌骨关节怀疑是肿瘤性病变、感染性病变或血管性病变者，建议做增强扫描。)";
  });

  // 条形码配置选项
  const barcodeOptions = computed(() => ({
    width: 2,              // 条形码线条宽度
    height: 80,            // 条形码高度
    displayValue: false,   // 不显示条形码下方的文字（我们自己显示）
    textAlign: "center",
    textPosition: "bottom",
    textMargin: 0,
    fontSize: 0,
    fontColor: "#000000",
    lineColor: "#000000",
    background: "#FFFFFF",
    margin: 0,
    marginTop: 0,
    marginBottom: 0,
    marginLeft: 0,
    marginRight: 0,
  }));

  // 二维码尺寸
  const qrcodeModalSize = computed(() => 240);

  // 患者头像URL
  const avatarUrl = computed(() => {
    const { gender } = guideDetail.value;
    // 根据性别返回不同的默认头像
    if (gender === '女') {
      return '/static/images/avatar-female.png';
    }
    return '/static/images/avatar-male.png';
  });

  // 患者年龄
  const patientAge = computed(() => {
    // 这里可以根据实际数据计算年龄，暂时返回固定值
    return '32岁';
  });

  // 脱敏身份证号
  const maskedIdCard = computed(() => {
    // 这里可以根据实际数据脱敏，暂时返回固定值
    return '510***********443';
  });

  /**
   * 显示二维码弹窗
   */
  const showQrCodeModal = () => {
    showQrModal.value = true;
  };

  /**
   * 隐藏二维码弹窗
   */
  const hideQrCodeModal = () => {
    showQrModal.value = false;
  };

  /**
   * 处理导诊指引
   */
  const handleGuideConsult = () => {
    uni.showToast({
      title: "导诊指引功能暂未开放",
      icon: "none",
    });
  };

  /**
   * 处理预约检查
   */
  const handlePreCheck = () => {
    const { pmiNo } = pageParams.value;
    if (pmiNo) {
      // 这里可以实现跳转到预约检查页面的逻辑
      // 参考old-project中的逻辑
      uni.showToast({
        title: "跳转预约检查",
        icon: "none",
      });
    } else {
      uni.showToast({
        title: "预约检查功能暂未开放",
        icon: "none",
      });
    }
  };

  return {
    // 响应式数据
    guideDetail,
    loading,
    formattedGuideData,
    formattedGuideText,
    barcodeOptions,
    qrcodeModalSize,
    avatarUrl,
    patientAge,
    maskedIdCard,
    showQrModal,

    // 方法
    showQrCodeModal,
    hideQrCodeModal,
    handleGuideConsult,
    handlePreCheck,
  };
};
