/**
 * 导诊单详情页面 Hooks
 */
import { ref, computed, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getGuideSheetDetail } from "../shared/services";
import type { GuideSheetContent, GuideSheetDetailParams } from "../shared/types";

/**
 * 导诊单详情管理 Hook
 */
export const useGuideSheetDetail = () => {
  // 响应式数据
  const guideDetail = ref<GuideSheetContent>({});
  const loading = ref(false);

  // 页面参数
  const pageParams = ref<{
    cardId?: string;
    guideInfoId?: string;
    pmiNo?: string;
    purePlayKey?: string;
    purePlayParam?: string;
  }>({});

  /**
   * 页面加载时处理参数
   */
  onLoad((options: any) => {
    console.log("导诊单详情页面参数:", options);
    pageParams.value = {
      cardId: options.cardId || "",
      guideInfoId: options.guideInfoId || "",
      pmiNo: options.pmiNo || "",
      purePlayKey: options.purePlayKey || "",
      purePlayParam: options.purePlayParam || "",
    };
  });

  /**
   * 组件挂载时初始化
   */
  onMounted(async () => {
    await fetchGuideDetail();
  });

  /**
   * 获取导诊单详情
   */
  const fetchGuideDetail = async () => {
    const { cardId, guideInfoId, purePlayKey, purePlayParam } = pageParams.value;

    // 处理纯播放参数
    if (purePlayKey && purePlayParam) {
      try {
        const parsedParam = JSON.parse(purePlayParam);
        const { cardId: pureCardId, guideInfoId: pureGuideInfoId } = parsedParam;
        
        if (pureCardId && pureGuideInfoId) {
          await getDetailData(pureCardId, pureGuideInfoId);
        }
      } catch (error) {
        console.error("解析纯播放参数失败:", error);
        uni.showToast({
          title: "参数解析失败",
          icon: "none",
        });
      }
      return;
    }

    // 正常参数处理
    if (cardId && guideInfoId) {
      await getDetailData(cardId, guideInfoId);
    } else {
      uni.showToast({
        title: "缺少必要参数",
        icon: "none",
      });
    }
  };

  /**
   * 获取详情数据
   */
  const getDetailData = async (cardId: string, guideInfoId: string) => {
    try {
      loading.value = true;

      const params: GuideSheetDetailParams = {
        cardId,
        guideInfoId,
      };

      const response = await getGuideSheetDetail(params);
      guideDetail.value = response;
    } catch (error) {
      console.error("获取导诊单详情失败:", error);
      uni.showToast({
        title: "获取导诊单详情失败",
        icon: "none",
      });
    } finally {
      loading.value = false;
    }
  };

  // 计算属性
  const shouldShowCode = computed(() => {
    return !!guideDetail.value.registerNo;
  });

  const shouldShowBarcode = computed(() => {
    const { displayQrCode } = guideDetail.value;
    return shouldShowCode.value && displayQrCode !== 1 && displayQrCode !== 2;
  });

  const shouldShowQrcode = computed(() => {
    const { displayQrCode } = guideDetail.value;
    return shouldShowCode.value && (displayQrCode === 1 || displayQrCode === 2);
  });

  const formattedGuideData = computed(() => {
    const { guideData } = guideDetail.value;
    if (!guideData) return "";
    
    // 简单的HTML格式化处理
    return guideData
      .replace(/\n/g, "<br/>")
      .replace(/\t/g, "&nbsp;&nbsp;&nbsp;&nbsp;");
  });

  // 条形码和二维码尺寸
  const barcodeWidth = computed(() => 340);
  const barcodeHeight = computed(() => 50);
  const qrcodeSize = computed(() => 200);

  /**
   * 处理预约按钮点击
   */
  const handleAppointment = () => {
    uni.showToast({
      title: "预约功能暂未开放",
      icon: "none",
    });
  };

  return {
    // 响应式数据
    guideDetail,
    loading,
    shouldShowCode,
    shouldShowBarcode,
    shouldShowQrcode,
    formattedGuideData,
    barcodeWidth,
    barcodeHeight,
    qrcodeSize,

    // 方法
    handleAppointment,
  };
};
