/**
 * 导诊单详情页面 Hooks
 */
import { ref, computed, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getGuideSheetDetail } from "../shared/services/api";
import type { GuideSheetContent, GuideSheetDetailParams } from "../shared/types";

/**
 * 导诊单详情管理 Hook
 */
export const useGuideSheetDetail = () => {
  // 响应式数据
  const guideDetail = ref<GuideSheetContent>({});
  const loading = ref(false);
  
  // 页面参数
  const pageParams = ref<{
    cardId?: string;
    guideInfoId?: string;
    pmiNo?: string;
  }>({});

  // 条形码/二维码配置
  const barcodeWidth = ref(300);
  const barcodeHeight = ref(80);
  const qrcodeSize = ref(200);

  /**
   * 页面加载时处理参数
   */
  onLoad((options: any) => {
    console.log("导诊单详情页面参数:", options);
    
    if (options.cardId && options.guideInfoId) {
      pageParams.value = {
        cardId: options.cardId,
        guideInfoId: options.guideInfoId,
        pmiNo: options.pmiNo,
      };
      
      // 获取导诊单详情
      fetchGuideDetail();
    } else {
      uni.showToast({
        title: "参数错误",
        icon: "none",
      });
    }
  });

  /**
   * 获取导诊单详情
   */
  const fetchGuideDetail = async () => {
    if (!pageParams.value.cardId || !pageParams.value.guideInfoId) {
      return;
    }

    try {
      loading.value = true;
      
      const params: GuideSheetDetailParams = {
        cardId: pageParams.value.cardId,
        guideInfoId: pageParams.value.guideInfoId,
      };
      
      const response = await getGuideSheetDetail(params);
      guideDetail.value = response;
      
      console.log("导诊单详情:", response);
    } catch (error) {
      console.error("获取导诊单详情失败:", error);
      uni.showToast({
        title: "获取导诊单详情失败",
        icon: "none",
      });
    } finally {
      loading.value = false;
    }
  };

  /**
   * 是否显示条形码/二维码
   */
  const shouldShowCode = computed(() => {
    return !!guideDetail.value.registerNo;
  });

  /**
   * 是否显示条形码
   */
  const shouldShowBarcode = computed(() => {
    const { displayQrCode } = guideDetail.value;
    // 当displayQrCode不为1或2时显示条形码
    return displayQrCode !== 1 && displayQrCode !== 2 && !!guideDetail.value.registerNo;
  });

  /**
   * 是否显示二维码
   */
  const shouldShowQrcode = computed(() => {
    const { displayQrCode, qrCode } = guideDetail.value;
    // 当displayQrCode为1或2，且qrCode有值时显示二维码
    return (displayQrCode === 1 || displayQrCode === 2) && (!!qrCode || !!guideDetail.value.registerNo);
  });

  /**
   * 格式化导诊信息
   */
  const formattedGuideData = computed(() => {
    if (!guideDetail.value.guideData) {
      return "";
    }
    
    // 将换行符替换为<br/>
    return guideDetail.value.guideData.replace(/(\n)/g, "<br/>");
  });

  /**
   * 处理预约挂号
   */
  const handleAppointment = () => {
    // 跳转到预约挂号页面
    uni.showToast({
      title: "预约功能暂未开放",
      icon: "none",
    });
  };

  return {
    // 响应式数据
    guideDetail,
    loading,
    shouldShowCode,
    shouldShowBarcode,
    shouldShowQrcode,
    formattedGuideData,
    barcodeWidth,
    barcodeHeight,
    qrcodeSize,

    // 方法
    handleAppointment,
  };
};
