# 导诊单模块

> 基于老项目 GuideSheet 功能 1:1 迁移的导诊单模块

## 📁 目录结构

```
src/pages/guide-sheet/
├── list/                           # 导诊单列表页面
│   ├── index.vue                   # 列表页面主组件
│   ├── hooks.ts                    # 列表页面业务逻辑
│   └── components/                 # 列表页面组件
│       ├── patient-card-display.vue   # 患者卡显示组件
│       └── guide-sheet-item.vue        # 导诊单列表项组件
├── detail/                         # 导诊单详情页面
│   ├── index.vue                   # 详情页面主组件
│   ├── hooks.ts                    # 详情页面业务逻辑
│   └── components/                 # 详情页面组件
│       ├── barcode-canvas.vue          # 条形码组件
│       └── qrcode-canvas.vue           # 二维码组件
├── shared/                         # 共享资源
│   ├── services/                   # API服务
│   │   ├── api.ts                  # 导诊单相关API
│   │   └── index.ts                # 服务入口
│   └── types/                      # 类型定义
│       └── index.ts                # 导诊单相关类型
└── README.md                       # 说明文档
```

## 🚀 页面路由

### 导诊单列表页面

- 路径: `/pages/guide-sheet/list/index`
- 参数:
  - `data` (可选): JSON字符串，包含 cardId、pmi、papmi 等信息
  - `bizDealSeq` (可选): 支付回调序列号

### 导诊单详情页面

- 路径: `/pages/guide-sheet/detail/index`
- 参数:
  - `cardId` (必需): 患者卡 ID
  - `guideInfoId` (必需): 导诊单系统 ID
  - `pmiNo` (可选): 患者登记号
  - `purePlayKey` (可选): 纯播放密钥
  - `purePlayParam` (可选): 纯播放参数

## 📦 子包配置

在 `pages.json` 中的配置：

```json
{
  "subPackages": [
    {
      "root": "pages/guide-sheet",
      "name": "guide-sheet",
      "pages": [
        {
          "path": "list/index",
          "style": {
            "navigationBarTitleText": "导诊单",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        },
        {
          "path": "detail/index",
          "style": {
            "navigationBarTitleText": "导诊单详情",
            "enablePullDownRefresh": false,
            "navigationStyle": "custom"
          }
        }
      ]
    }
  ]
}
```

## 🔌 API 接口

### 获取导诊单列表

```typescript
getGuideSheetList(params: GuideSheetListParams): Promise<GuideSheetListResponse>
```

### 获取导诊单详情

```typescript
getGuideSheetDetail(params: GuideSheetDetailParams): Promise<GuideSheetDetailResponse>
```

### 获取就诊卡列表

```typescript
getCardList(params: Partial<CardListParams>): Promise<{ code: string; data: CardListData }>
```

## 🎯 使用方式

### 1. 从主包跳转

```typescript
// 跳转到导诊单列表
uni.navigateTo({
  url: "/pages/guide-sheet/list/index",
});

// 跳转到导诊单详情
uni.navigateTo({
  url: "/pages/guide-sheet/detail/index?guideInfoId=xxx&cardId=xxx",
});
```

### 2. 首页菜单集成

首页菜单中的"导诊单"项会自动跳转到导诊单列表页面，路径已在 `PageInteractionHandler` 中配置：
- `/guidesheet/index`
- `/guidesheet/home`

## ✨ 功能特性

### 导诊单列表

- ✅ 支持患者切换
- ✅ 支持分页加载
- ✅ 支持下拉刷新
- ✅ 空状态处理
- ✅ 响应式设计
- ✅ 支持传入参数初始化

### 导诊单详情

- ✅ 患者信息展示
- ✅ 导诊信息展示
- ✅ 条形码/二维码显示
- ✅ 预约按钮（可配置）
- ✅ 支持纯播放模式

### 条形码/二维码显示规则

- 当 `displayQrCode` 不为 1 或 2 时，显示条形码
- 当 `displayQrCode` 为 1 或 2 时，显示二维码
- 优先显示 `qrCode` 字段的值，如果为空则使用 `registerNo`

## 🏗️ 技术实现

### MVVM 架构

- **View**: Vue 组件 (index.vue)
- **ViewModel**: Hooks (hooks.ts)
- **Model**: API 服务 (shared/services)

### 组件化设计

- 患者卡显示组件：支持切换就诊人
- 导诊单列表项：展示导诊单基本信息
- 条形码/二维码组件：支持动态生成

### 类型安全

- 完整的 TypeScript 类型定义
- 严格的接口约束
- 类型安全的 API 调用

## 🔄 迁移对比

### 老项目 vs 新项目

| 功能 | 老项目 (React) | 新项目 (Vue3) |
|------|----------------|---------------|
| 状态管理 | Redux + DVA | Composition API |
| 路由跳转 | history.push | uni.navigateTo |
| 列表渲染 | HxListView | scroll-view + v-for |
| 患者卡组件 | PatientCard | PatientCardDisplay |
| 条形码 | HxBarcode | BarcodeCanvas |
| 二维码 | QRCode.react | QrcodeCanvas |

### 保持一致的功能

- ✅ 页面参数处理逻辑
- ✅ API 接口调用
- ✅ 业务流程
- ✅ UI 样式设计
- ✅ 交互体验

## 📝 注意事项

1. **分包独立性**: 本分包不依赖其他分包，遵循分包规则
2. **API 兼容性**: 保持与老项目相同的 API 接口
3. **样式一致性**: 基于 750px 设计稿 1:1 迁移
4. **类型安全**: 严格的 TypeScript 类型检查
5. **错误处理**: 完善的错误提示和异常处理
