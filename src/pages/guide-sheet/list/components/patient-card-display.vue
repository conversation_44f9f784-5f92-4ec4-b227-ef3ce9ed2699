<template>
  <view class="patient-card-display" @click="showCardSelector">
    <view class="card-content">
      <view class="patient-avatar">
        <image :src="avatarIcon" class="avatar-icon" mode="aspectFit" />
      </view>
      <view class="patient-info">
        <view class="patient-name">{{ patientInfo.patientName }}</view>
        <view class="patient-card-info"> 就诊卡号：{{ maskedCardNo }} </view>
      </view>
      <view class="arrow-icon">
        <image :src="arrowRightWhitePng" class="arrow-image" mode="aspectFit" />
      </view>
    </view>
  </view>

  <!-- 患者卡选择弹窗 -->
  <view
    class="card-selector-mask"
    v-if="showSelector"
    @click="hideCardSelector"
  >
    <view class="card-selector" @click.stop>
      <view class="selector-header">
        <text class="selector-title">选择就诊人</text>
        <text class="close-btn" @click="hideCardSelector">×</text>
      </view>
      <view class="card-list">
        <view
          v-for="card in cardList"
          :key="card.cardId"
          class="card-item"
          :class="{ selected: card.cardId === patientInfo.cardId }"
          @click="selectCard(card)"
        >
          <view class="card-avatar">
            <image
              :src="getGenderIcon(card.gender)"
              class="avatar-icon"
              mode="aspectFit"
            />
          </view>
          <view class="card-info">
            <view class="card-name">{{ card.patientName }}</view>
            <view class="card-details">
              <text class="card-gender">{{ getGenderText(card.gender) }}</text>
              <text class="card-no">{{ getMaskedCardNo(card.cardNo) }}</text>
            </view>
          </view>
          <view class="card-tag">
            {{ card.isSelf === 0 ? "本人" : "其他" }}
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from "vue";
import { getCardList } from "../../shared/services";
import type { PatientCard } from "../../shared/types";

import manPng from "../../assets/man.png";
import womanPng from "../../assets/woman.png";
import arrowRightWhitePng from "../../assets/arrow-right-white.png";

// Props
interface Props {
  patientInfo: PatientCard;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  change: [card: PatientCard];
}>();

// 响应式数据
const showSelector = ref(false);
const cardList = ref<PatientCard[]>([]);

// 计算属性
const genderText = computed(() => {
  return getGenderText(props.patientInfo.gender);
});

const avatarIcon = computed(() => {
  // 根据性别返回不同的头像
  if (
    props.patientInfo.gender === 1 ||
    props.patientInfo.gender === "1" ||
    props.patientInfo.gender === "男"
  ) {
    return manPng;
  }
  return womanPng;
});

const maskedCardNo = computed(() => {
  return getMaskedCardNo(props.patientInfo.cardNo);
});

// 方法
const getGenderText = (gender?: string | number): string => {
  if (gender === 1 || gender === "1" || gender === "男") return "男";
  if (gender === 2 || gender === "2" || gender === "女") return "女";
  return "未知";
};

const getGenderIcon = (gender?: string | number): string => {
  if (gender === 1 || gender === "1" || gender === "男") {
    return manPng;
  }
  return womanPng;
};

const getMaskedCardNo = (cardNo?: string): string => {
  if (!cardNo || cardNo.length < 8) return cardNo || "";
  return `${cardNo.slice(0, 4)}****${cardNo.slice(-4)}`;
};

const showCardSelector = async () => {
  try {
    const response = await getCardList({});
    if (response.code === "1" && response.data.userCardList?.length > 0) {
      cardList.value = response.data.userCardList;
      showSelector.value = true;
    } else {
      uni.showToast({
        title: "暂无就诊卡",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("获取就诊卡列表失败:", error);
    uni.showToast({
      title: "获取就诊卡列表失败",
      icon: "none",
    });
  }
};

const hideCardSelector = () => {
  showSelector.value = false;
};

const selectCard = (card: PatientCard) => {
  emit("change", card);
  hideCardSelector();
};
</script>

<style lang="scss" scoped>
@use "@/styles/tokens/colors.scss" as colors;

.patient-card-display {
  background: colors.$color-primary;
  border-radius: 24rpx;
  padding: 24rpx;

  .card-content {
    display: flex;
    align-items: center;
    gap: 24rpx;

    .patient-avatar {
      .avatar-icon {
        width: 96rpx;
        height: 96rpx;
        border-radius: 50%;
        border: 4rpx solid rgba(255, 255, 255, 0.3);
      }
    }

    .patient-info {
      flex: 1;

      .patient-name {
        font-size: 36rpx;
        font-weight: 600;
        color: white;
        margin-bottom: 12rpx;
      }

      .patient-card-info {
        font-size: 28rpx;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.4;
      }
    }

    .arrow-icon {
      .arrow-image {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }

  // 点击效果
  &:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}

.card-selector-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;

  .card-selector {
    background-color: white;
    border-radius: 24rpx 24rpx 0 0;
    width: 100%;
    max-height: 80vh;
    overflow: hidden;

    .selector-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 32rpx 24rpx;
      border-bottom: 1rpx solid #f0f0f0;

      .selector-title {
        font-size: 32rpx;
        font-weight: 600;
        color: colors.$text-primary;
      }

      .close-btn {
        font-size: 48rpx;
        color: colors.$text-secondary;
        line-height: 1;
      }
    }

    .card-list {
      max-height: 60vh;
      overflow-y: auto;

      .card-item {
        display: flex;
        align-items: center;
        gap: 24rpx;
        padding: 24rpx 32rpx;
        border-bottom: 1rpx solid #f8f8f8;

        &.selected {
          background-color: #f0f8ff;
        }

        .card-avatar {
          .avatar-icon {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
          }
        }

        .card-info {
          flex: 1;

          .card-name {
            font-size: 30rpx;
            font-weight: 500;
            color: colors.$text-primary;
            margin-bottom: 8rpx;
          }

          .card-details {
            display: flex;
            gap: 16rpx;
            font-size: 24rpx;
            color: colors.$text-secondary;
          }
        }

        .card-tag {
          background-color: colors.$color-primary;
          color: white;
          padding: 6rpx 12rpx;
          border-radius: 6rpx;
          font-size: 22rpx;
        }
      }
    }
  }
}
</style>
