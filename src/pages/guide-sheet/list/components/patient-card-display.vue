<template>
  <view class="patient-card-display" @click="handleCardClick">
    <view class="card-content">
      <view class="patient-info">
        <view class="patient-name">{{ patientInfo.patientName }}</view>
        <view class="patient-details">
          <text>{{ patientInfo.gender }}</text>
          <text v-if="patientInfo.age">{{ patientInfo.age }}岁</text>
          <text v-if="patientInfo.cardNo">{{ patientInfo.cardNo }}</text>
        </view>
      </view>
      <view class="card-action">
        <text class="action-text">切换</text>
        <text class="action-icon">></text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import type { PatientInfo } from "../../shared/types";

interface Props {
  patientInfo: PatientInfo;
}

interface Emits {
  (e: "change"): void;
}

defineProps<Props>();
const emit = defineEmits<Emits>();

/**
 * 处理卡片点击
 */
const handleCardClick = () => {
  // 跳转到患者卡选择页面
  uni.navigateTo({
    url: "/pages/patient-card/list/index",
  });
};
</script>

<style lang="scss" scoped>
@use "@/styles/tokens/colors.scss" as colors;

.patient-card-display {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 12rpx rgba(79, 172, 254, 0.3);

  .card-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .patient-info {
    flex: 1;

    .patient-name {
      font-size: 32rpx;
      font-weight: 600;
      color: white;
      margin-bottom: 8rpx;
    }

    .patient-details {
      display: flex;
      gap: 16rpx;
      font-size: 24rpx;
      color: rgba(255, 255, 255, 0.9);

      text {
        position: relative;

        &:not(:last-child)::after {
          content: "·";
          position: absolute;
          right: -10rpx;
          color: rgba(255, 255, 255, 0.7);
        }
      }
    }
  }

  .card-action {
    display: flex;
    align-items: center;
    gap: 8rpx;
    color: rgba(255, 255, 255, 0.9);
    font-size: 24rpx;

    .action-icon {
      font-size: 20rpx;
      transform: rotate(0deg);
      transition: transform 0.2s ease;
    }
  }

  &:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;

    .card-action .action-icon {
      transform: rotate(90deg);
    }
  }
}
</style>
