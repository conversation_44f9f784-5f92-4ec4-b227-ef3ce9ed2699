<template>
  <view class="guide-sheet-item" @click="handleClick">
    <view class="item-header">
      <text class="item-time">{{ item.admTime || '' }}</text>
      <view class="item-action">
        <text class="action-text">详情</text>
        <text class="arrow-icon">></text>
      </view>
    </view>
    
    <view class="item-content">
      <view 
        v-for="info in infoList" 
        :key="info.title"
        class="info-row"
      >
        <view class="info-title">
          <view class="title-chars">
            <text 
              v-for="char in info.title.split('')" 
              :key="char"
              class="title-char"
            >
              {{ char }}
            </text>
          </view>
          <text class="colon">：</text>
        </view>
        <text class="info-value">{{ info.value }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { GuideSheetContent } from "../../shared/types";

// Props
interface Props {
  item: GuideSheetContent;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  click: [item: GuideSheetContent];
}>();

// 计算属性
const infoList = computed(() => {
  return [
    {
      title: '就诊医院',
      value: props.item.hospitalName || '',
    },
    {
      title: '患者',
      value: props.item.patientName || '',
    },
  ];
});

// 方法
const handleClick = () => {
  emit('click', props.item);
};
</script>

<style lang="scss" scoped>
@use "@/styles/tokens/colors.scss" as colors;

.guide-sheet-item {
  background-color: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    padding-bottom: 16rpx;
    border-bottom: 1rpx solid #f0f0f0;

    .item-time {
      font-size: 28rpx;
      color: colors.$text-primary;
      font-weight: 500;
    }

    .item-action {
      display: flex;
      align-items: center;
      gap: 8rpx;

      .action-text {
        font-size: 26rpx;
        color: colors.$color-primary;
      }

      .arrow-icon {
        font-size: 24rpx;
        color: colors.$color-primary;
        transform: rotate(0deg);
        transition: transform 0.2s ease;
      }
    }
  }

  .item-content {
    .info-row {
      display: flex;
      align-items: flex-start;
      margin-bottom: 12rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .info-title {
        display: flex;
        align-items: center;
        min-width: 140rpx;

        .title-chars {
          display: flex;
          flex-direction: column;
          align-items: center;

          .title-char {
            font-size: 26rpx;
            color: colors.$text-secondary;
            line-height: 1.2;
            margin-bottom: 2rpx;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        .colon {
          font-size: 26rpx;
          color: colors.$text-secondary;
          margin-left: 4rpx;
        }
      }

      .info-value {
        flex: 1;
        font-size: 26rpx;
        color: colors.$text-primary;
        line-height: 1.4;
        margin-left: 16rpx;
      }
    }
  }

  // 点击效果
  &:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}
</style>
