<template>
  <view class="guide-sheet-item" @click="handleClick">
    <view class="item-header">
      <text class="item-time">{{ item.admTime || "" }}</text>
      <view class="item-action">
        <text class="action-text">详情</text>
        <image :src="arrowRightGreyPng" class="arrow-icon" mode="aspectFit" />
      </view>
    </view>

    <view class="item-content">
      <view class="info-row">
        <text class="info-label">就诊医院：</text>
        <text class="info-value">{{ item.hospitalName || "" }}</text>
      </view>
      <view class="info-row">
        <text class="info-label">就诊人：</text>
        <text class="info-value">{{ item.patientName || "" }}</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import type { GuideSheetContent } from "../../shared/types";
import arrowRightGreyPng from "../../assets/arrow-right-grey.png";

// Props
interface Props {
  item: GuideSheetContent;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  click: [item: GuideSheetContent];
}>();

// 方法
const handleClick = () => {
  emit("click", props.item);
};
</script>

<style lang="scss" scoped>
@use "@/styles/tokens/colors.scss" as colors;

.guide-sheet-item {
  background-color: white;
  border-radius: 16rpx;
  padding: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;

    .item-time {
      font-size: 32rpx;
      color: #333333;
      font-weight: 500;
    }

    .item-action {
      display: flex;
      align-items: center;
      gap: 8rpx;

      .action-text {
        font-size: 28rpx;
        color: #666666;
      }

      .arrow-icon {
        width: 24rpx;
        height: 24rpx;
      }
    }
  }

  .item-content {
    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .info-label {
        font-size: 28rpx;
        color: #999999;
        min-width: 160rpx;
      }

      .info-value {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
        line-height: 1.4;
      }
    }
  }

  // 点击效果
  &:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }
}
</style>
