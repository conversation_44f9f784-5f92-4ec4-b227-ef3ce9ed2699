<template>
  <hyt-page navbarTitle="导诊单" :loading="loading" class="guide-sheet-page">
    <view class="guide-sheet-container">
      <!-- 患者卡片区域 - 固定在顶部 -->
      <view class="header" v-if="patientInfo">
        <PatientCardDisplay
          :patientInfo="patientInfo"
          @change="handlePatientChange"
        />
      </view>

      <!-- 内容区域 -->
      <view class="content-area">
        <!-- 导诊单列表 -->
        <scroll-view
          class="guide-sheet-list"
          scroll-y
          enable-back-to-top
          refresher-enabled
          :refresher-triggered="refreshing"
          @refresherrefresh="handleRefresh"
          @scrolltolower="loadMore"
          v-if="!loading && guideSheetList.length > 0"
        >
          <GuideSheetItem
            v-for="item in guideSheetList"
            :key="item.guideInfoId"
            :item="item"
            @click="handleItemClick"
          />

          <!-- 加载更多提示 -->
          <view class="load-more" v-if="isLoading">
            <text class="load-text">加载中...</text>
          </view>
          <view
            class="load-more"
            v-else-if="!hasMore && guideSheetList.length > 0"
          >
            <text class="load-text">没有更多了</text>
          </view>
        </scroll-view>

        <!-- 空状态 -->
        <view
          class="empty-container"
          v-if="!loading && guideSheetList.length === 0 && patientInfo"
        >
          <hyt-empty
            text="暂无导诊单"
            description="当前就诊人暂无导诊单记录"
            :show-border="false"
          />
        </view>

        <!-- 无患者卡状态 -->
        <view class="empty-container" v-if="!loading && !patientInfo">
          <hyt-empty
            text="请先选择就诊人"
            description="选择就诊人后可查看导诊单"
            :show-border="false"
          >
            <view class="empty-action" @click="selectPatient">选择就诊人</view>
          </hyt-empty>
        </view>
      </view>
    </view>
  </hyt-page>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useGuideSheetList } from "./hooks";
import PatientCardDisplay from "./components/patient-card-display.vue";
import GuideSheetItem from "./components/guide-sheet-item.vue";
import { HytEmpty } from "@/components/hyt-empty";

// 刷新状态
const refreshing = ref(false);

// 使用导诊单列表 Hook
const {
  // 响应式数据
  guideSheetList,
  patientInfo,
  loading,
  isLoading,
  hasMore,

  // 方法
  handleItemClick,
  handlePatientChange,
  selectPatient,
  loadMore,
  onRefresh,
} = useGuideSheetList();

// 下拉刷新处理
const handleRefresh = async () => {
  refreshing.value = true;
  try {
    await onRefresh();
  } finally {
    refreshing.value = false;
  }
};
</script>

<style lang="scss" scoped>
@use "@/styles/tokens/colors.scss" as colors;

// 覆盖hyt-page的默认滚动行为
:deep(.hyt-page__content) {
  overflow: hidden !important; /* 禁用hyt-page的滚动 */
  height: 100%;
}

.guide-sheet-container {
  height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden; /* 防止整个容器滚动 */
}

.header {
  flex-shrink: 0;
  margin-bottom: 24rpx;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许flex子项收缩 */
  overflow: hidden; /* 防止内容区域滚动 */
}

.guide-sheet-list {
  flex: 1;
  width: 100%;
  overflow: hidden; /* 确保只有scroll-view内部滚动 */
}

.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;

  .load-text {
    font-size: 26rpx;
    color: colors.$text-secondary;
  }
}

.empty-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40rpx 20rpx;
  box-sizing: border-box;
}

.empty-action {
  padding: 20rpx 40rpx;
  background-color: colors.$color-primary;
  color: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin-top: 24rpx;

  &:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
}
</style>
