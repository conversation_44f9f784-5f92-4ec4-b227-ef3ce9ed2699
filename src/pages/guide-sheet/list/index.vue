<template>
  <hyt-page navbarTitle="导诊单" class="guide-sheet-page">
    <view class="guide-sheet-container">
      <!-- 患者卡片区域 - 固定在顶部 -->
      <view class="header" v-if="patientInfo">
        <PatientCardDisplay
          :patientInfo="patientInfo"
          @change="handlePatientChange"
        />
      </view>

      <!-- 内容区域 -->
      <view class="content-area">
        <!-- 初始加载状态 -->
        <view class="loading-container" v-if="loading">
          <view class="loading-content">
            <view class="loading-spinner"></view>
            <text class="loading-text">加载中...</text>
          </view>
        </view>

        <!-- 导诊单列表 -->
        <scroll-view
          class="guide-sheet-list"
          scroll-y
          enable-back-to-top
          refresher-enabled
          :refresher-triggered="refreshing"
          @refresherrefresh="handleRefresh"
          @scrolltolower="loadMore"
          v-if="!loading && guideSheetList.length > 0"
        >
          <GuideSheetItem
            v-for="item in guideSheetList"
            :key="item.guideInfoId"
            :item="item"
            @click="handleItemClick"
          />

          <!-- 加载更多提示 -->
          <view class="load-more" v-if="isLoading">
            <text class="load-text">加载中...</text>
          </view>
          <view
            class="load-more"
            v-else-if="!hasMore && guideSheetList.length > 0"
          >
            <text class="load-text">没有更多了</text>
          </view>
        </scroll-view>

        <!-- 调试信息 -->
        <view class="debug-info" v-if="true">
          <text>loading: {{ loading }}</text>
          <text>guideSheetList.length: {{ guideSheetList.length }}</text>
          <text>patientInfo: {{ patientInfo ? "有患者" : "无患者" }}</text>
          <text
            >显示条件1:
            {{ !loading && guideSheetList.length === 0 && patientInfo }}</text
          >
          <text>显示条件2: {{ !loading && !patientInfo }}</text>
        </view>

        <!-- 测试hyt-empty组件 -->
        <view class="test-empty" v-if="true">
          <text>测试hyt-empty组件:</text>
          <hyt-empty
            text="测试文本"
            description="测试描述"
            :show-border="false"
          />

          <!-- 手动创建简化版empty组件测试 -->
          <view class="manual-empty">
            <view class="manual-empty-container">
              <view class="manual-image">📄</view>
              <text class="manual-text">手动测试文本</text>
              <text class="manual-desc">手动测试描述</text>
            </view>
          </view>
        </view>

        <!-- 空状态：有患者但无导诊单 -->
        <view
          class="empty-container"
          v-if="!loading && guideSheetList.length === 0 && patientInfo"
        >
          <!-- 简单的空状态测试 -->
          <view class="simple-empty">
            <text class="empty-title">暂无导诊单</text>
            <text class="empty-desc">当前就诊人暂无导诊单记录</text>
          </view>

          <!-- hyt-empty组件 -->
          <hyt-empty
            text="暂无导诊单"
            description="当前就诊人暂无导诊单记录"
            :show-border="false"
          />
        </view>

        <!-- 无患者卡状态 -->
        <view class="empty-container" v-if="!loading && !patientInfo">
          <!-- 简单的空状态测试 -->
          <view class="simple-empty">
            <text class="empty-title">请先选择就诊人</text>
            <text class="empty-desc">选择就诊人后可查看导诊单</text>
            <view class="empty-action" @click="selectPatient">选择就诊人</view>
          </view>

          <!-- hyt-empty组件 -->
          <hyt-empty
            text="请先选择就诊人"
            description="选择就诊人后可查看导诊单"
            :show-border="false"
          >
            <view class="empty-action" @click="selectPatient">选择就诊人</view>
          </hyt-empty>
        </view>
      </view>
    </view>
  </hyt-page>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { useGuideSheetList } from "./hooks";
import PatientCardDisplay from "./components/patient-card-display.vue";
import GuideSheetItem from "./components/guide-sheet-item.vue";
import { HytEmpty } from "@/components/hyt-empty";

// 刷新状态
const refreshing = ref(false);

// 使用导诊单列表 Hook
const {
  // 响应式数据
  guideSheetList,
  patientInfo,
  loading,
  isLoading,
  hasMore,

  // 方法
  handleItemClick,
  handlePatientChange,
  selectPatient,
  loadMore,
  onRefresh,
} = useGuideSheetList();

// 下拉刷新处理
const handleRefresh = async () => {
  refreshing.value = true;
  try {
    await onRefresh();
  } finally {
    refreshing.value = false;
  }
};
</script>

<style lang="scss" scoped>
@use "@/styles/tokens/colors.scss" as colors;

// 覆盖hyt-page的默认滚动行为
:deep(.hyt-page__content) {
  overflow: hidden !important; /* 禁用hyt-page的滚动 */
  height: 100%;
}

.guide-sheet-container {
  height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden; /* 防止整个容器滚动 */
}

.header {
  flex-shrink: 0;
  margin-bottom: 24rpx;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许flex子项收缩 */
  overflow: hidden; /* 防止内容区域滚动 */
}

.guide-sheet-list {
  flex: 1;
  width: 100%;
  overflow: hidden; /* 确保只有scroll-view内部滚动 */
}

.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40rpx 20rpx;
  box-sizing: border-box;

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .loading-spinner {
      width: 60rpx;
      height: 60rpx;
      border: 4rpx solid #f0f0f0;
      border-top: 4rpx solid colors.$color-primary;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 24rpx;
    }

    .loading-text {
      font-size: 28rpx;
      color: colors.$text-secondary;
    }
  }
}

.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;

  .load-text {
    font-size: 26rpx;
    color: colors.$text-secondary;
  }
}

.empty-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40rpx 20rpx;
  box-sizing: border-box;
}

.simple-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  padding: 40rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  border: 2rpx dashed #ddd;

  .empty-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
    margin-bottom: 16rpx;
  }

  .empty-desc {
    font-size: 26rpx;
    color: #666;
    text-align: center;
    margin-bottom: 24rpx;
  }
}

.empty-action {
  padding: 20rpx 40rpx;
  background-color: colors.$color-primary;
  color: white;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin-top: 24rpx;

  &:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
}

// 调试信息样式
.debug-info {
  position: fixed;
  top: 100rpx;
  left: 20rpx;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx;
  border-radius: 8rpx;
  z-index: 9999;
  font-size: 24rpx;

  text {
    display: block;
    margin-bottom: 10rpx;
  }
}

// 测试组件样式
.test-empty {
  position: fixed;
  top: 400rpx;
  left: 20rpx;
  right: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid #007aff;
  border-radius: 8rpx;
  z-index: 9998;
  padding: 20rpx;

  text {
    color: #007aff;
    font-size: 24rpx;
    margin-bottom: 20rpx;
  }
}

// 手动empty组件样式
.manual-empty {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 1rpx solid #ddd;

  .manual-empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;

    .manual-image {
      font-size: 60rpx;
      margin-bottom: 16rpx;
    }

    .manual-text {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 8rpx;
    }

    .manual-desc {
      font-size: 24rpx;
      color: #666;
    }
  }
}

// Loading动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
