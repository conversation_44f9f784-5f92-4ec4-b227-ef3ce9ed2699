<template>
  <hyt-page navbarTitle="导诊单" :loading="loading">
    <view class="guide-sheet-container">
      <!-- 患者卡片区域 -->
      <view class="header" v-if="patientInfo">
        <PatientCardDisplay
          :patientInfo="patientInfo"
          @change="handlePatientChange"
        />
      </view>

      <!-- 导诊单列表 -->
      <scroll-view 
        class="guide-sheet-list"
        scroll-y
        enable-back-to-top
        @scrolltolower="loadMore"
        v-if="!loading && guideSheetList.length > 0"
      >
        <GuideSheetItem
          v-for="item in guideSheetList"
          :key="item.guideInfoId"
          :item="item"
          @click="handleItemClick"
        />
        
        <!-- 加载更多提示 -->
        <view class="load-more" v-if="isLoading">
          <text class="load-text">加载中...</text>
        </view>
        <view class="load-more" v-else-if="!hasMore && guideSheetList.length > 0">
          <text class="load-text">没有更多了</text>
        </view>
      </scroll-view>

      <!-- 空状态 -->
      <view
        class="empty-state"
        v-if="!loading && guideSheetList.length === 0 && patientInfo"
      >
        <view class="empty-icon">📋</view>
        <view class="empty-text">暂无导诊单</view>
        <view class="empty-desc">当前就诊人暂无导诊单记录</view>
      </view>

      <!-- 无患者卡状态 -->
      <view class="no-patient-state" v-if="!loading && !patientInfo">
        <view class="empty-icon">👤</view>
        <view class="empty-text">请先选择就诊人</view>
        <view class="empty-desc">选择就诊人后可查看导诊单</view>
        <view class="empty-action" @click="selectPatient">选择就诊人</view>
      </view>
    </view>
  </hyt-page>
</template>

<script setup lang="ts">
import { useGuideSheetList } from "./hooks";
import PatientCardDisplay from "./components/patient-card-display.vue";
import GuideSheetItem from "./components/guide-sheet-item.vue";

// 使用导诊单列表 Hook
const {
  // 响应式数据
  guideSheetList,
  patientInfo,
  loading,
  isLoading,
  hasMore,

  // 方法
  handleItemClick,
  handlePatientChange,
  selectPatient,
  loadMore,
} = useGuideSheetList();
</script>

<style lang="scss" scoped>
@use "@/styles/tokens/colors.scss" as colors;

.guide-sheet-container {
  min-height: 100vh;
  background-color: colors.$color-background;
  padding: 21rpx 30rpx;
}

.header {
  margin-bottom: 24rpx;
}

.guide-sheet-list {
  flex: 1;
  height: calc(100vh - 200rpx);
}

.load-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;

  .load-text {
    font-size: 26rpx;
    color: colors.$text-secondary;
  }
}

.empty-state,
.no-patient-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;

  .empty-icon {
    font-size: 80rpx;
    margin-bottom: 24rpx;
    opacity: 0.6;
  }

  .empty-text {
    font-size: 32rpx;
    color: colors.$text-primary;
    margin-bottom: 12rpx;
    font-weight: 500;
  }

  .empty-desc {
    font-size: 26rpx;
    color: colors.$text-secondary;
    margin-bottom: 32rpx;
    text-align: center;
    line-height: 1.4;
  }

  .empty-action {
    padding: 20rpx 40rpx;
    background-color: colors.$color-primary;
    color: white;
    border-radius: 12rpx;
    font-size: 28rpx;
    font-weight: 500;

    &:active {
      transform: scale(0.95);
      transition: transform 0.1s ease;
    }
  }
}
</style>
