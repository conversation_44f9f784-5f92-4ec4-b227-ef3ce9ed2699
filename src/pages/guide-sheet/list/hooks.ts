/**
 * 导诊单列表页面 Hooks
 */
import { ref, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { getGuideSheetList, getCardList } from "../shared/services";
import type {
  GuideSheetContent,
  PatientCard,
  GuideSheetListParams,
} from "../shared/types";

/**
 * 导诊单列表管理 Hook
 */
export const useGuideSheetList = () => {
  // 响应式数据
  const guideSheetList = ref<GuideSheetContent[]>([]);
  const patientInfo = ref<PatientCard | null>(null);
  const loading = ref(false);
  
  // 分页参数
  const pageNum = ref(1);
  const pageSize = ref(10);
  const hasMore = ref(true);
  const isLoading = ref(false);

  // 页面参数
  const pageParams = ref<{
    data?: string;
    bizDealSeq?: string;
  }>({});

  /**
   * 页面加载时处理参数
   */
  onLoad((options: any) => {
    console.log("导诊单列表页面参数:", options);
    pageParams.value = {
      data: options.data || "",
      bizDealSeq: options.bizDealSeq || "",
    };
  });

  /**
   * 组件挂载时初始化
   */
  onMounted(async () => {
    await initializeData();
  });

  /**
   * 初始化数据
   */
  const initializeData = async () => {
    const { data = "", bizDealSeq = "" } = pageParams.value;
    
    if (bizDealSeq) {
      // 支付回调逻辑 - 暂时跳过，直接初始化患者卡
      await initializePatientCard();
    } else if (data) {
      // 有传入数据，解析并设置患者信息
      try {
        const parsedData = JSON.parse(data);
        const { cardId = "", pmi = "", papmi = "" } = parsedData;
        if (cardId) {
          await queryCardData(cardId);
        } else {
          await initializePatientCard();
        }
      } catch (error) {
        console.error("解析页面参数失败:", error);
        await initializePatientCard();
      }
    } else {
      // 没有参数，初始化患者卡
      await initializePatientCard();
    }
  };

  /**
   * 初始化患者卡
   */
  const initializePatientCard = async () => {
    try {
      loading.value = true;
      const response = await getCardList({});
      
      if (response.code === "1" && response.data.userCardList?.length > 0) {
        const firstCard = response.data.userCardList[0];
        await setPatientInfo(firstCard);
      } else {
        // 没有患者卡，设置为空状态
        patientInfo.value = null;
      }
    } catch (error) {
      console.error("获取患者卡列表失败:", error);
      uni.showToast({
        title: "获取患者信息失败",
        icon: "none",
      });
    } finally {
      loading.value = false;
    }
  };

  /**
   * 根据卡ID查询患者信息
   */
  const queryCardData = async (cardId: string) => {
    try {
      loading.value = true;
      const response = await getCardList({});
      
      if (response.code === "1" && response.data.userCardList?.length > 0) {
        const targetCard = response.data.userCardList.find(
          (card: PatientCard) => card.cardId === cardId
        );
        
        if (targetCard) {
          await setPatientInfo(targetCard);
        } else {
          uni.showToast({
            title: "暂无此就诊卡",
            icon: "none",
          });
          // 设置为空状态，清空旧数据
          patientInfo.value = null;
          guideSheetList.value = [];
        }
      }
    } catch (error) {
      console.error("查询患者信息失败:", error);
      uni.showToast({
        title: "查询患者信息失败",
        icon: "none",
      });
    } finally {
      loading.value = false;
    }
  };

  /**
   * 设置患者信息并获取导诊单列表
   */
  const setPatientInfo = async (card: PatientCard) => {
    patientInfo.value = card;

    // 重置分页数据
    guideSheetList.value = [];
    pageNum.value = 1;
    hasMore.value = true;

    // 获取导诊单列表，这里不需要设置loading，因为外层已经管理了
    await getGuideList(1, card.cardId, card.pmi || card.pmiNo || "", false);
  };

  /**
   * 获取导诊单列表
   */
  const getGuideList = async (page: number, cardId: string, pAPMI: string, showLoading: boolean = true) => {
    if (isLoading.value) return;

    try {
      if (showLoading) {
        isLoading.value = true;
      }
      
      const params: GuideSheetListParams = {
        cardId,
        pAPMI,
        pageNum: page,
        pageSize: pageSize.value,
      };

      const response = await getGuideSheetList(params);
      
      if (response.content) {
        const { content = [], total = 0 } = response;
        
        if (page === 1) {
          guideSheetList.value = content;
        } else {
          guideSheetList.value = [...guideSheetList.value, ...content];
        }
        
        pageNum.value = page;
        
        // 判断是否还有更多数据
        if (guideSheetList.value.length >= total) {
          hasMore.value = false;
        }
      }
    } catch (error) {
      console.error("获取导诊单列表失败:", error);
      uni.showToast({
        title: "获取导诊单列表失败",
        icon: "none",
      });
    } finally {
      if (showLoading) {
        isLoading.value = false;
      }
    }
  };

  /**
   * 加载更多
   */
  const loadMore = () => {
    if (isLoading.value || !hasMore.value || !patientInfo.value) {
      return;
    }
    
    const nextPage = pageNum.value + 1;
    getGuideList(nextPage, patientInfo.value.cardId, patientInfo.value.pmi || patientInfo.value.pmiNo || "");
  };

  /**
   * 处理导诊单项点击
   */
  const handleItemClick = (item: GuideSheetContent) => {
    const { guideInfoId = "" } = item;
    const { cardId = "" } = patientInfo.value || {};
    const { data = "{}" } = pageParams.value;
    
    let pmiNo = "";
    try {
      const parsedData = JSON.parse(data);
      pmiNo = parsedData.pmiNo || "";
    } catch (error) {
      console.error("解析pmiNo失败:", error);
    }

    uni.navigateTo({
      url: `/pages/guide-sheet/detail/index?guideInfoId=${guideInfoId}&cardId=${cardId}&pmiNo=${pmiNo}`,
    });
  };

  /**
   * 处理患者切换
   */
  const handlePatientChange = async (card: PatientCard) => {
    await setPatientInfo(card);
  };

  /**
   * 选择患者
   */
  const selectPatient = () => {
    uni.navigateTo({
      url: "/pages/patient-card/list/index",
    });
  };

  /**
   * 下拉刷新
   */
  const onRefresh = async () => {
    if (!patientInfo.value) return;
    
    pageNum.value = 1;
    hasMore.value = true;
    await getGuideList(1, patientInfo.value.cardId, patientInfo.value.pmi || patientInfo.value.pmiNo || "");
  };

  return {
    // 响应式数据
    guideSheetList,
    patientInfo,
    loading,
    isLoading,
    hasMore,

    // 方法
    handleItemClick,
    handlePatientChange,
    selectPatient,
    loadMore,
    onRefresh,
  };
};
