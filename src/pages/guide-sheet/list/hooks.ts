/**
 * 导诊单列表页面 Hooks
 */
import { ref, onMounted } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import {
  getGuideSheetList,
  PatientCardService
} from "../shared/services";
import type {
  GuideSheetContent,
  PatientInfo,
  GuideSheetListParams,
  PatientCard
} from "../shared/types";

/**
 * 导诊单列表管理 Hook
 */
export const useGuideSheetList = () => {
  // 响应式数据
  const guideSheetList = ref<GuideSheetContent[]>([]);
  const patientInfo = ref<PatientInfo | null>(null);
  const loading = ref(false);
  const paging = ref<any>(null);
  
  // 分页参数
  const pageNum = ref(1);
  const hasMore = ref(true);

  // 页面参数
  const pageParams = ref<{
    cardId?: string;
    pmiNo?: string;
  }>({});

  /**
   * 页面加载时处理参数
   */
  onLoad((options: any) => {
    console.log("导诊单列表页面参数:", options);
    
    if (options.cardId && options.pmiNo) {
      pageParams.value = {
        cardId: options.cardId,
        pmiNo: options.pmiNo,
      };
    }
  });

  /**
   * 组件挂载时初始化
   */
  onMounted(async () => {
    if (pageParams.value.cardId && pageParams.value.pmiNo) {
      // 如果有传入参数，直接查询对应患者的导诊单
      await queryPatientByCardId(pageParams.value.cardId);
    } else {
      // 否则获取患者卡列表，选择第一个
      await fetchPatientCards();
    }
  });

  /**
   * 获取患者卡列表
   */
  const fetchPatientCards = async () => {
    try {
      loading.value = true;
      const response = await PatientCardService.getCardList({
        guidance: "0",
        businessCode: "1",
      });

      if (response.code === "1" && response.data.userCardList?.length > 0) {
        const firstCard = response.data.userCardList[0];
        await setPatientInfo(firstCard);
      }
    } catch (error) {
      console.error("获取患者卡列表失败:", error);
      uni.showToast({
        title: "获取患者信息失败",
        icon: "none",
      });
    } finally {
      loading.value = false;
    }
  };

  /**
   * 根据卡ID查询患者信息
   */
  const queryPatientByCardId = async (cardId: string) => {
    try {
      loading.value = true;
      const response = await PatientCardService.getCardList({
        guidance: "0",
        businessCode: "1",
      });

      if (response.code === "1" && response.data.userCardList?.length > 0) {
        const targetCard = response.data.userCardList.find(
          (card: PatientCard) => card.cardId === cardId
        );
        
        if (targetCard) {
          await setPatientInfo(targetCard);
        } else {
          uni.showToast({
            title: "暂无此就诊卡",
            icon: "none",
          });
        }
      }
    } catch (error) {
      console.error("查询患者信息失败:", error);
      uni.showToast({
        title: "查询患者信息失败",
        icon: "none",
      });
    } finally {
      loading.value = false;
    }
  };

  /**
   * 设置患者信息并获取导诊单列表
   */
  const setPatientInfo = async (card: PatientCard) => {
    patientInfo.value = {
      cardId: card.cardId,
      patientName: card.patientName,
      pmi: card.pmi || card.pmiNo || card.cardId, // 优先使用pmi，其次pmiNo，最后cardId
      gender: card.gender,
      age: card.age?.toString(),
      cardNo: card.cardNo,
      cardTypeCode: card.cardTypeCode || card.cardType,
    };

    // 重置分页数据
    guideSheetList.value = [];
    pageNum.value = 1;
    hasMore.value = true;

    // 触发分页组件查询
    if (paging.value) {
      paging.value.reload();
    }
  };

  /**
   * 分页查询导诊单列表
   */
  const queryList = async (pageNo: number, pageSize: number) => {
    if (!patientInfo.value) {
      return;
    }

    try {
      const params: GuideSheetListParams = {
        cardId: patientInfo.value.cardId,
        pAPMI: patientInfo.value.pmi,
        pageNum: pageNo,
        pageSize: pageSize,
      };

      const response = await getGuideSheetList(params);
      
      if (response.content) {
        const { content = [], total = 0 } = response;
        
        // 完成分页组件的数据更新
        paging.value?.complete(content);
        
        // 判断是否还有更多数据
        const currentTotal = (pageNo - 1) * pageSize + content.length;
        if (currentTotal >= total) {
          hasMore.value = false;
        }
      } else {
        paging.value?.complete([]);
      }
    } catch (error) {
      console.error("获取导诊单列表失败:", error);
      paging.value?.complete(false);
      uni.showToast({
        title: "获取导诊单列表失败",
        icon: "none",
      });
    }
  };

  /**
   * 处理导诊单项点击
   */
  const handleItemClick = (item: GuideSheetContent) => {
    const { guideInfoId = "" } = item;
    const { cardId = "" } = patientInfo.value || {};
    const { pmiNo = "" } = pageParams.value;

    uni.navigateTo({
      url: `/pages/guide-sheet/detail/index?guideInfoId=${guideInfoId}&cardId=${cardId}&pmiNo=${pmiNo}`,
    });
  };

  /**
   * 处理患者切换
   */
  const handlePatientChange = async (card: PatientCard) => {
    await setPatientInfo(card);
  };

  /**
   * 选择患者
   */
  const selectPatient = () => {
    uni.navigateTo({
      url: "/pages/patient-card/list/index",
    });
  };

  return {
    // 响应式数据
    guideSheetList,
    patientInfo,
    loading,
    paging,

    // 方法
    queryList,
    handleItemClick,
    handlePatientChange,
    selectPatient,
  };
};
