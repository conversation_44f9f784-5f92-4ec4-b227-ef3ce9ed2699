/**
 * 导诊单相关API服务
 */
import request from "@/utils/request";
import type {
  GuideSheetListParams,
  GuideSheetListResponse,
  GuideSheetDetailParams,
  GuideSheetDetailResponse,
  CardListParams,
  CardListData
} from "../types";

/**
 * 查询导诊单列表
 * @param params 查询参数
 * @returns 导诊单列表响应
 */
export const getGuideSheetList = async (
  params: GuideSheetListParams
): Promise<GuideSheetListResponse> => {
  const response = await request.post<GuideSheetListResponse>(
    "/cloud/hosplatcustomer/medical/op/list/guideDetail",
    params
  );
  return response.data;
};

/**
 * 查询导诊单详情
 * @param params 查询参数
 * @returns 导诊单详情响应
 */
export const getGuideSheetDetail = async (
  params: GuideSheetDetailParams
): Promise<GuideSheetDetailResponse> => {
  const response = await request.post<GuideSheetDetailResponse>(
    "/cloud/hosplatcustomer/medical/op/query/guideDetail",
    params
  );
  return response.data;
};

/**
 * 获取就诊卡列表
 * @param params 查询参数
 * @returns 就诊卡列表响应
 */
export const getCardList = async (
  params: Partial<CardListParams> = {}
): Promise<{ code: string; data: CardListData }> => {
  const response = await request.post<CardListData>(
    "/cloud/cardservice/home/<USER>",
    params
  );
  return {
    code: response.code,
    data: response.data
  };
};