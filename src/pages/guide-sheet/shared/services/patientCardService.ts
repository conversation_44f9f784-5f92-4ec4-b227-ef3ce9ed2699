/**
 * 就诊卡服务层 - guide-sheet分包专用
 */
import request from "@/utils/request";
import type {
  CardListParams,
  CardListData,
  IPatientCardTypeItem,
} from "../types";

/**
 * 就诊卡服务
 */
export const PatientCardService = {
  /**
   * 获取就诊卡列表
   * @param params 查询参数
   * @returns 就诊卡列表
   */
  getCardList: (params: CardListParams) => {
    return request.post<CardListData>(
      "/cloud/cardservice/home/<USER>",
      params
    );
  },
  /**
   * 注册就诊卡方式
   */
  getOrganCardList: () => {
    return request.post<IPatientCardTypeItem[]>(
      "/cloud/hosplatcustomer/cardservice/getorgancardlist",
      {}
    );
  },
};
