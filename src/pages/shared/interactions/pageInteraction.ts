/**
 * Tabs页面交互逻辑
 */
import { navigateToWebview, isHttpsUrl } from "@/pages/webview/utils";
import type { MenuItemData, BannerItem } from "../types";

/**
 * 页面交互处理器
 */
export class PageInteractionHandler {
  /**
   * 处理服务点击事件
   */
  static handleServiceClick(service: MenuItemData): void {
    console.log("点击服务:", service.name);

    if (!service.path) {
      uni.showToast({
        title: "功能暂未开放",
        icon: "none",
      });
      return;
    }

    // 特殊路径处理
    const specialPaths = this.getSpecialPathHandlers();
    const handler = specialPaths[service.path];
    if (handler) {
      handler(service);
      return;
    }

    // 判断是否为 HTTPS 链接
    if (isHttpsUrl(service.path)) {
      // HTTPS 链接跳转到 webview 页面
      try {
        navigateToWebview({
          url: service.path,
          title: service.name,
        });
      } catch (err) {
        console.error("Webview 跳转失败:", err);
        uni.showToast({
          title: "页面跳转失败",
          icon: "none",
        });
      }
    } else {
      // 小程序内部页面跳转
      uni
        .navigateTo({
          url: service.path,
        })
        .catch((err) => {
          console.error("页面跳转失败:", err);
          uni.showToast({
            title: "页面跳转失败",
            icon: "none",
          });
        });
    }
  }

  /**
   * 获取特殊路径处理器
   */
  private static getSpecialPathHandlers(): Record<string, (service: MenuItemData) => void> {
    return {
      // 导诊单路径处理
      "/guidesheet/index": (service: MenuItemData) => {
        this.handleGuideSheetNavigation(service);
      },
      "/guidesheet/home": (service: MenuItemData) => {
        this.handleGuideSheetNavigation(service);
      },
    };
  }

  /**
   * 处理导诊单导航
   */
  private static handleGuideSheetNavigation(service: MenuItemData): void {
    console.log("导航到导诊单页面:", service.name);

    // 跳转到导诊单列表页面
    uni.navigateTo({
      url: "/pages/guide-sheet/list/index",
    }).catch((err) => {
      console.error("导诊单页面跳转失败:", err);
      uni.showToast({
        title: "页面跳转失败",
        icon: "none",
      });
    });
  }

  /**
   * 处理轮播图点击事件
   */
  static handleBannerClick(banner: BannerItem): void {
    console.log("点击轮播图:", banner.title);

    if (!banner.linkUrl) {
      return;
    }

    // 判断是否为 HTTP/HTTPS 链接
    if (isHttpsUrl(banner.linkUrl)) {
      // HTTP/HTTPS 链接跳转到 webview 页面
      try {
        navigateToWebview({
          url: banner.linkUrl,
          title: banner.title || "页面",
        });
      } catch (err) {
        console.error("轮播图 Webview 跳转失败:", err);
        uni.showToast({
          title: "页面跳转失败",
          icon: "none",
        });
      }
    } else {
      // 小程序内部页面跳转
      uni
        .navigateTo({
          url: banner.linkUrl,
        })
        .catch((err) => {
          console.error("轮播图跳转失败:", err);
          uni.showToast({
            title: "页面跳转失败",
            icon: "none",
          });
        });
    }
  }
}
