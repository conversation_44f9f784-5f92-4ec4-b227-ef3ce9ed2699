<template>
  <hyt-page navbarTitle="绑定就诊卡">
    <view class="bind-visit-card">
      <view class="form">
        <!-- 基础信息 -->
        <view class="card">
          <view class="card-header">
            <text class="card-title">基础信息</text>
          </view>
          <view class="card-body">
            <!-- 卡类型 -->
            <view class="form-item">
              <view class="form-label">卡类型</view>
              <view class="form-radio-group">
                <view
                  v-for="item in cardTypeList"
                  :key="item.value"
                  class="radio-item"
                  :class="{ active: cardType === item.value }"
                  @click="onCardTypeChange(item.value)"
                >
                  <image
                    :src="cardType === item.value ? checkedIcon : uncheckIcon"
                    class="radio-icon"
                  />
                  <text class="radio-label">{{ item.label }}</text>
                </view>
              </view>
            </view>

            <!-- 姓名 -->
            <view class="form-item">
              <view class="form-label">姓名</view>
              <input
                v-model="formData.patientName"
                placeholder="请输入办卡人真实姓名"
                placeholder-style="color: #b0b3bf"
                maxlength="20"
                class="form-input"
              />
            </view>

            <!-- 就诊卡号（实体卡时显示） -->
            <view v-if="cardType === '2'" class="form-item">
              <view class="form-label">就诊卡号</view>
              <input
                v-model="formData.cardNo"
                placeholder="请输入实体就诊卡号"
                placeholder-style="color: #b0b3bf"
                type="number"
                maxlength="20"
                class="form-input"
              />
            </view>

            <!-- 证件类型 -->
            <view class="form-item">
              <view class="form-label">证件类型</view>
              <picker
                :value="credTypePickerValue"
                :range="credTypeList"
                range-key="label"
                @change="onCredTypeChange"
                class="form-picker"
              >
                <view class="form-value">
                  <text
                    :class="formData.credTypeName ? 'selected' : 'placeholder'"
                  >
                    {{ formData.credTypeName || "请选择证件类型" }}
                  </text>
                  <image :src="arrowRightIcon" class="arrow" />
                </view>
              </picker>
            </view>

            <!-- 证件号码 -->
            <view class="form-item">
              <view class="form-label">证件号码</view>
              <view class="form-input-group">
                <input
                  v-model="formData.credNo"
                  placeholder="请输入就诊人证件号码"
                  placeholder-style="color: #b0b3bf"
                  maxlength="18"
                  class="form-input"
                  @input="onCredNoInput"
                />
                <!-- OCR识别按钮 -->
                <image
                  v-if="showOcrButton"
                  :src="cameraIcon"
                  class="camera-icon"
                  @click="onOcrClick"
                />
              </view>
            </view>
          </view>
        </view>

        <view class="line"></view>

        <!-- 其他信息 -->
        <view class="card">
          <view class="card-header">
            <text class="card-title">其他信息</text>
          </view>
          <view class="card-body">
            <!-- 关系（未绑定本人卡时显示） -->
            <view v-if="!hasSelf" class="form-item">
              <view class="form-label">关系</view>
              <picker
                :value="relationPickerValue"
                :range="selfList"
                range-key="label"
                @change="onRelationChange"
                class="form-picker"
              >
                <view class="form-value">
                  <text
                    :class="getRelationLabel() ? 'selected' : 'placeholder'"
                  >
                    {{ getRelationLabel() || "请选择关系" }}
                  </text>
                  <image :src="arrowRightIcon" class="arrow" />
                </view>
              </picker>
            </view>

            <!-- 联系电话 -->
            <view class="form-item no-border">
              <view class="form-label">联系电话</view>
              <input
                v-model="formData.tel"
                placeholder="请输入接收通知的手机号码"
                placeholder-style="color: #b0b3bf"
                type="number"
                maxlength="11"
                class="form-input"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 底部按钮 -->
      <view class="footer">
        <view
          class="submit-btn primary"
          :class="{ disabled: !canSubmit }"
          :disabled="!canSubmit"
          @click="handleSubmit"
        >
          下一步
        </view>
        <view class="submit-btn outline" @click="goToSyncHospitals">
          跨院一键同步
        </view>
      </view>
    </view>
  </hyt-page>
</template>

<script setup lang="ts">
import { onMounted } from "vue";
import { useBindVisitCard } from "./hooks/useBindVisitCard";

// 导入图标
import arrowRightIcon from "./assets/arrow-right.png";
import cameraIcon from "./assets/camera.png";
import checkedIcon from "./assets/checked.png";
import uncheckIcon from "./assets/uncheck.png";

// 使用 hooks
const {
  // 响应式数据
  formData,
  cardType,
  cardTypeList,
  credTypeList,
  selfList,
  hasSelf,

  // 计算属性
  credTypePickerValue,
  relationPickerValue,
  showOcrButton,
  canSubmit,

  // 方法
  onCardTypeChange,
  onCredTypeChange,
  onCredNoInput,
  onRelationChange,
  onOcrClick,
  handleSubmit,
  goToSyncHospitals,
  getRelationLabel,
  initPageData,
} = useBindVisitCard();

// 页面初始化
onMounted(() => {
  initPageData();
});
</script>

<style lang="scss" scoped>
@use "../../../styles/tokens/colors.scss" as colors;

.bind-visit-card {
  padding: 24rpx;
  width: 100%;
  max-height: 100vh;
  overflow-y: auto;

  .form {
    .line {
      width: 100%;
      height: 24rpx;
      display: block;
    }
  }

  .card {
    background: #ffffff;
    border-radius: 24rpx;
    color: #03081a;
    margin-bottom: 24rpx;

    .card-header {
      padding: 32rpx 32rpx 0 32rpx;

      .card-title {
        font-size: 36rpx;
        font-weight: 600;
        color: #03081a;
        line-height: 36rpx;
      }
    }

    .card-body {
      padding: 32rpx;
      padding-top: 16rpx;
    }
  }

  .form-item {
    display: flex;
    align-items: center;
    height: 100rpx;
    border-bottom: 1rpx solid #eee;
    box-sizing: border-box;

    &.no-border {
      border-bottom: none;
    }

    .form-label {
      width: 160rpx;
      font-size: 28rpx;
      color: #989eb4;
      flex-shrink: 0;
    }

    .form-input {
      flex: 1;
      height: 100%;
      font-size: 28rpx;
      color: #03081a;
      background: transparent;
      border: none;
      text-align: right;
      padding: 16rpx 0;
    }

    .form-input-group {
      flex: 1;
      display: flex;
      align-items: center;
      height: 100%;

      .form-input {
        flex: 1;
        height: 100%;
      }

      .camera-icon {
        width: 40rpx;
        height: 40rpx;
        margin-left: 8rpx;
      }
    }

    .form-picker {
      flex: 1;
    }

    .form-value {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 100%;
      height: 100%;
      border-radius: 12rpx;
      box-sizing: border-box;
      background-color: transparent;
      font-size: 28rpx;

      .selected {
        color: #03081a;
      }

      .placeholder {
        color: #b0b3bf;
      }

      .arrow {
        width: 32rpx;
        height: 32rpx;
        flex-shrink: 0;
        margin-left: 8rpx;
      }
    }

    .form-radio-group {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      gap: 32rpx;
      height: 100%;

      .radio-item {
        display: flex;
        align-items: center;
        cursor: pointer;

        .radio-icon {
          width: 40rpx;
          height: 40rpx;
          margin-right: 16rpx;
        }

        .radio-label {
          font-size: 28rpx;
          color: #03081a;
        }
      }
    }
  }

  .footer {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 24rpx;
    flex-direction: column;

    .submit-btn {
      width: 100%;
      height: 96rpx;
      background-color: colors.$color-primary;
      color: #ffffff;
      border: none;
      border-radius: 48rpx;
      font-size: 32rpx;
      font-weight: 600;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 48rpx;

      &:first-child {
        margin-top: 0;
      }

      &.disabled {
        background-color: #d9d9d9;
        color: #999999;
      }

      &.outline {
        background: transparent;
        color: colors.$color-primary;
        border: 2rpx solid colors.$color-primary;
      }
    }
  }
}
</style>
