import { ref, computed, reactive } from "vue";
import { getOrganCode, getChannelCode } from "@/utils/platform";
import { idCardNoUtil } from "@/utils/idCardNoUtil";
import { PatientCardService } from "../../shared/services/patientCardService";
import { getOrganName } from "@/services/organService";
import { uploadImage } from "@/utils/upload";

// 表单数据类型
interface FormData {
  patientName: string;
  cardNo: string;
  credTypeCode: string;
  credTypeName: string;
  credNo: string;
  tel: string;
  relation: number[];
  gender?: number;
  birthday?: string;
}

// 卡类型选项
const cardTypeList = [
  { label: "电子健康卡", value: "1" },
  { label: "实体就诊卡", value: "2" },
];

// 关系选项
const selfList = [
  { label: "本人", value: 0 },
  { label: "其他", value: 1 },
];

// 工具函数：从身份证号码获取出生日期
const getBirthdayFromIdCard = (idCard: string): string => {
  if (!idCard || !idCardNoUtil.checkIdCardNo(idCard)) {
    return "";
  }

  let birthday = "";
  if (idCard.length === 15) {
    // 15位身份证
    birthday = "19" + idCard.substring(6, 12);
  } else if (idCard.length === 18) {
    // 18位身份证
    birthday = idCard.substring(6, 14);
  }

  // 格式化为 YYYY-MM-DD
  if (birthday.length === 8) {
    return `${birthday.substring(0, 4)}-${birthday.substring(
      4,
      6
    )}-${birthday.substring(6, 8)}`;
  }

  return "";
};

// 工具函数：从身份证号码获取性别
const getGenderFromIdCard = (idCard: string): string => {
  if (!idCard || !idCardNoUtil.checkIdCardNo(idCard)) {
    return "";
  }

  let genderCode = "";
  if (idCard.length === 15) {
    // 15位身份证
    genderCode = idCard.substring(14, 15);
  } else if (idCard.length === 18) {
    // 18位身份证
    genderCode = idCard.substring(16, 17);
  }

  // 奇数为男，偶数为女
  return parseInt(genderCode) % 2 === 1 ? "1" : "2";
};

/**
 * 绑定就诊卡页面逻辑
 */
export const useBindVisitCard = () => {
  // 响应式数据
  const formData = reactive<FormData>({
    patientName: "",
    cardNo: "",
    credTypeCode: "",
    credTypeName: "",
    credNo: "",
    tel: "",
    relation: [],
  });

  const cardType = ref<string>("1"); // 默认电子健康卡
  const credTypeList = ref<any[]>([]);
  const ocrList = ref<string[]>([]);
  const hasSelf = ref<boolean>(true); // 是否已绑定本人卡
  const loading = ref<boolean>(false);

  // 计算属性
  const credTypePickerValue = computed(() => {
    return credTypeList.value.findIndex(
      (item) => item.value === formData.credTypeCode
    );
  });

  const relationPickerValue = computed(() => {
    if (!formData.relation || formData.relation.length === 0) return -1;
    return selfList.findIndex((item) => item.value === formData.relation[0]);
  });

  const showOcrButton = computed(() => {
    return (
      formData.credTypeCode && ocrList.value.includes(formData.credTypeCode)
    );
  });

  const canSubmit = computed(() => {
    const { patientName, credTypeCode, credNo, tel, cardNo, relation } =
      formData;

    // 基础字段验证
    if (!patientName || !credTypeCode || !credNo || !tel) {
      return false;
    }

    // 实体卡需要卡号
    if (cardType.value === "2" && !cardNo) {
      return false;
    }

    // 未绑定本人卡需要选择关系
    if (!hasSelf.value && (!relation || relation.length === 0)) {
      return false;
    }

    return true;
  });

  // 方法
  const onCardTypeChange = (value: string) => {
    cardType.value = value;
    // 重置证件类型为身份证
    formData.credTypeCode = "01";
    formData.credTypeName = "居民身份证";
    // 重新获取证件类型列表
    getCredTypeList(value);
  };

  const onCredTypeChange = (e: any) => {
    const index = e.detail.value;
    const selectedType = credTypeList.value[index];
    if (selectedType) {
      formData.credTypeCode = selectedType.value;
      formData.credTypeName = selectedType.label;
    }
  };

  const onCredNoInput = (e: any) => {
    const value = e.detail.value;
    formData.credNo = value;

    // 身份证自动解析
    if (
      formData.credTypeCode === "01" &&
      value.length > 14 &&
      idCardNoUtil.checkIdCardNo(value)
    ) {
      // 解析生日
      const birthday = getBirthdayFromIdCard(value);
      if (birthday) {
        formData.birthday = birthday;
      }

      // 解析性别
      const gender = getGenderFromIdCard(value);
      if (gender) {
        formData.gender = parseInt(gender);
      }
    }
  };

  const onRelationChange = (e: any) => {
    const index = e.detail.value;
    const selectedRelation = selfList[index];
    if (selectedRelation) {
      formData.relation = [selectedRelation.value];
    }
  };

  const onOcrClick = () => {
    console.log("开始OCR扫描");

    uni.chooseImage({
      count: 1,
      sizeType: ["compressed"],
      sourceType: ["camera", "album"],
      success: async (res) => {
        try {
          uni.showLoading({ title: "上传中..." });

          const tempFilePath = res.tempFilePaths[0];
          console.log("选择的图片路径:", tempFilePath);

          // 第一步：上传图片到OSS获取URL
          const ossUrl = await uploadImage(tempFilePath, {
            prefix: "ocr",
            businessId: "hxgy-hyt-certificate",
          });
          console.log("OSS上传成功，URL:", ossUrl);

          uni.showLoading({ title: "识别中..." });

          // 第二步：使用OSS URL调用OCR识别接口
          const ocrResult = await PatientCardService.ocrRecognition({
            businessCode: "card-register",
            ocrType: "HX_HW_OCR",
            channelCode: getChannelCode(),
            url: ossUrl,
            organCode: getOrganCode(),
            credTypeCode: formData.credTypeCode,
          });

          console.log("OCR识别结果:", ocrResult);

          if (ocrResult.code === "1" && ocrResult.data) {
            const { name, number, birth, sex } = ocrResult.data;

            // 填充识别到的信息
            if (name) {
              formData.patientName = name;
            }
            if (number) {
              formData.credNo = number;
            }
            if (birth) {
              formData.birthday = birth;
            }
            if (sex) {
              formData.gender = parseInt(sex);
            }

            uni.showToast({
              title: "识别成功",
              icon: "success",
            });
          } else {
            uni.showToast({
              title: ocrResult.msg || "识别失败",
              icon: "none",
            });
          }
        } catch (error: any) {
          console.error("OCR识别失败:", error);
          uni.showToast({
            title: error.message || "识别失败，请重试",
            icon: "none",
          });
        } finally {
          uni.hideLoading();
        }
      },
      fail: () => {
        uni.showToast({
          title: "选择图片失败",
          icon: "none",
        });
      },
    });
  };

  const getRelationLabel = () => {
    if (!formData.relation || formData.relation.length === 0) return "";
    const relation = selfList.find(
      (item) => item.value === formData.relation[0]
    );
    return relation?.label || "";
  };

  // 验证表单
  const validateForm = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      const { credNo, patientName, tel, credTypeCode, relation, cardNo } =
        formData;

      // 关系验证
      if (!hasSelf.value && (!relation || relation.length === 0)) {
        reject(new Error("请选择患者关系"));
        return;
      }

      // 实体卡卡号验证
      if (cardType.value === "2" && !cardNo) {
        reject(new Error("请输入就诊卡卡号"));
        return;
      }

      // 手机号验证
      const phonePattern = /^1\d{10}$/;
      if (!phonePattern.test(tel)) {
        reject(new Error("您输入的手机号有误，请重新输入"));
        return;
      }

      // 姓名验证
      if (patientName.length < 2) {
        reject(new Error("您输入姓名有误，请重新输入"));
        return;
      }

      // 必填字段验证
      if (!credNo) {
        reject(new Error("请填写证件号"));
        return;
      }
      if (!patientName) {
        reject(new Error("请填写患者姓名"));
        return;
      }
      if (!tel) {
        reject(new Error("请填写手机号码"));
        return;
      }
      if (!credTypeCode) {
        reject(new Error("请选择证件类型"));
        return;
      }

      resolve();
    });
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      await validateForm();
    } catch (error: any) {
      uni.showToast({
        title: error.message || "验证失败",
        icon: "none",
      });
      return;
    }

    loading.value = true;

    try {
      // 获取机构信息
      const organCode = getOrganCode();
      const organName = await getOrganName(organCode);

      // 构建提交参数
      const payload = {
        cardNo: cardType.value === "1" ? "" : formData.cardNo,
        credTypeCode: formData.credTypeCode,
        credTypeName: formData.credTypeName,
        credNo: formData.credNo,
        tel: formData.tel,
        patientName: formData.patientName,
        relation: Array.isArray(formData.relation) ? formData.relation[0] : 1,
        birthday: formData.birthday,
        gender: formData.gender,
        organName,
      };

      console.log("🚀 提交绑定就诊卡请求:", payload);

      const result = await PatientCardService.bindCard(payload);

      if (result.code === "1") {
        const { cardId, successMsg, successTittle } = result.data || {};
        if (cardId) {
          // 保存成功信息
          uni.setStorageSync("CARD_BIND_DESC", {
            successMsg,
            successTittle,
          });

          // 跳转到绑定结果页
          uni.redirectTo({
            url: `/pages/patient-card/bindresult/index?bindResultType=BIND_SUCCESS`,
          });
        }
      } else {
        uni.showModal({
          title: "提示",
          content: result.msg || "绑定失败",
          showCancel: false,
        });
      }
    } catch (error: any) {
      console.error("❌ 绑定就诊卡失败:", error);
      uni.showToast({
        title: error.message || "绑定失败",
        icon: "none",
      });
    } finally {
      loading.value = false;
    }
  };

  // 跳转到跨院同步页面
  const goToSyncHospitals = () => {
    uni.navigateTo({
      url: "/pages/patient-card/sync-hospitals/index",
      fail: () => {
        uni.showToast({
          title: "页面暂未开放",
          icon: "none",
        });
      },
    });
  };

  // 获取证件类型列表
  const getCredTypeList = async (type: string) => {
    try {
      const useType = type === "1" ? 1 : 2; // 注册1绑定2
      const result = await PatientCardService.getCredTypeList({ useType });

      if (result.code === "1" && result.data) {
        credTypeList.value = result.data.map((item: any) => ({
          value: item.credTypeCode,
          label: item.credTypeName,
        }));

        // 默认选择身份证
        if (credTypeList.value.length > 0) {
          const idCard = credTypeList.value.find((item) => item.value === "01");
          if (idCard) {
            formData.credTypeCode = idCard.value;
            formData.credTypeName = idCard.label;
          }
        }
      }
    } catch (error) {
      console.error("❌ 获取证件类型列表失败:", error);
    }
  };

  // 获取机构配置
  const getOrganConfig = async () => {
    try {
      const result = await PatientCardService.getOrganConfig({
        channelCode: getChannelCode(),
        organCode: getOrganCode(),
      });

      if (result.code === "1" && result.data) {
        ocrList.value = result.data.credOcrList || [];
      }
    } catch (error) {
      console.error("❌ 获取机构配置失败:", error);
    }
  };

  // 查询是否绑过本人卡
  const checkMyselfCard = async () => {
    try {
      const result = await PatientCardService.queryMyselfCard();

      if (result.code === "1" && result.data) {
        hasSelf.value = result.data.myselfCardExist || false;
      }
    } catch (error) {
      console.error("❌ 查询本人卡状态失败:", error);
    }
  };

  // 页面初始化
  const initPageData = async () => {
    await Promise.all([
      getOrganConfig(),
      getCredTypeList("1"),
      checkMyselfCard(),
    ]);
  };

  return {
    // 响应式数据
    formData,
    cardType,
    cardTypeList,
    credTypeList,
    selfList,
    hasSelf,
    ocrList,
    loading,

    // 计算属性
    credTypePickerValue,
    relationPickerValue,
    showOcrButton,
    canSubmit,

    // 方法
    onCardTypeChange,
    onCredTypeChange,
    onCredNoInput,
    onRelationChange,
    onOcrClick,
    handleSubmit,
    goToSyncHospitals,
    getRelationLabel,
    initPageData,
  };
};
