<template>
  <hyt-page :navbarTitle="pageTitle">
    <view class="patient-card-container">
      <view class="operation">
        <view class="residueCard">
          还可以绑定 <text>{{ canBindCount }}</text> 张卡
        </view>
      </view>
      <!-- <scroll-view class="patient-card-list" scroll-y> </scroll-view> -->
      <view class="patient-card-list">
        <z-paging
          ref="paging"
          refresher-only
          @onRefresh="onRefresh"
          :fixed="false"
          :use-page-scroll="true"
        >
          <!-- 页面内容 -->
          <PatientCardItem
            v-for="normalCard in cardList"
            :key="normalCard.cardId"
            :item="normalCard"
            :isSelectionMode="isSelectionMode"
            @click="handleCardClick(normalCard)"
          />
        </z-paging>
      </view>
      <view class="add-card-container" v-if="!isSelectionMode">
        <view class="add-card" @click="addNewCard">添加就诊卡</view>
      </view>
    </view>
  </hyt-page>
</template>

<script setup lang="ts">
import { usePatientCardList } from "./hooks";
import PatientCardItem from "./components/patient-card-item.vue";
import { ref } from "vue";
import type { PatientCard } from "../shared/types";

const paging = ref<any>(null);

// 使用hooks
const {
  // 状态
  cardList,
  canBindCount,
  isSelectionMode,
  pageTitle,

  // 方法
  fetchCardList,
  addNewCard,
  selectCard,
} = usePatientCardList();

const onRefresh = async () => {
  await fetchCardList();
  // 刷新列表
  paging.value?.complete();
};

// 处理卡片点击
const handleCardClick = (card: PatientCard) => {
  if (isSelectionMode.value) {
    // 选择模式下直接选择并返回
    selectCard(card);
  } else {
    // 卡列表模式下跳转到卡详情页面
    console.log("卡列表模式点击卡片:", card);

    if (!card.cardId) {
      uni.showToast({
        title: "卡片信息异常",
        icon: "none",
      });
      return;
    }

    // 只传递 cardId
    uni.navigateTo({
      url: `/pages/patient-card/card-detail/index?cardId=${card.cardId}`,
      fail: (error) => {
        console.error("跳转卡详情页面失败:", error);
        uni.showToast({
          title: "页面跳转失败",
          icon: "none",
        });
      },
    });
  }
};
</script>

<style lang="scss" scoped>
@use "../../../styles/tokens/colors.scss" as colors;

.patient-card-container {
  display: flex;
  flex-direction: column;
  flex: 1; /* 关键：让 login-page 也具有 flex: 1 属性 */
  height: 100%; /* 确保高度撑满父级 */
  min-height: 0; /* 允许flex子元素收缩 */
  view,
  text {
    box-sizing: border-box;
  }
}
.patient-card-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  overflow: auto;
  padding-bottom: 24rpx;
}
.operation {
  display: flex;
  flex-direction: row;
  padding: 30rpx 30rpx 30rpx 40rpx;

  .img {
    width: 6rpx;
    height: 30rpx;
  }

  .residueCard {
    color: #222;
    font-size: 24rpx;

    text {
      padding: 0 5rpx 0 5rpx;
      color: #fab319;
    }
  }
}
.add-card-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding-top: 24rpx;
  background-color: #fff;
  .add-card {
    width: 690rpx;
    max-height: 90rpx;
    min-height: 90rpx;
    color: #fff;
    font-size: 32rpx;
    background: colors.$color-primary;
    border-radius: 48px;
    margin: 0 auto;
    text-align: center;
    line-height: 90rpx;
    &:active {
      opacity: 0.7;
    }
  }
}
</style>
