<template>
  <hyt-page navbarTitle="绑卡结果">
    <view class="bind-wrap">
      <image :src="resultIcon" alt="" class="icon" />
      <view class="status">{{ successTitle }}</view>
      <view class="desc">{{ successMsg }}</view>

      <!-- 功能菜单（绑卡失败时不显示） -->
      <view
        v-if="bindResultType !== BIND_RESULT_TYPE.BIND_AUTH_FAIL"
        class="menu-list"
      >
        <view
          v-for="item in menuList"
          :key="item.id"
          class="item"
          @click="goTo(item.id)"
        >
          <image :src="item.url" alt="" />
          <text>{{ item.menuName }}</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="btn-wrap">
        <!-- 返回首页按钮（老年专区不显示） -->
        <view
          v-if="oldCode !== 'oldManCardChannel'"
          class="btn btn-outline"
          @click="goHome"
        >
          返回首页
        </view>

        <!-- 就诊卡列表按钮 -->
        <view class="btn btn-primary" @click="goToCardList"> 就诊卡列表 </view>
      </view>
    </view>
  </hyt-page>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";

// 图片资源（使用网络图片，与 react-old 项目保持一致）
const bindResultIcon =
  "https://cdnhyt.cd120.com/person/assets/patient-card/bind_result.png";
const realNameIcon =
  "https://cdnhyt.cd120.com/person/assets/patient-card/real_name_result.png";
const mzjfIcon = "https://cdnhyt.cd120.com/person/assets/patient-card/mzjf.png";
const yyghIcon = "https://cdnhyt.cd120.com/person/assets/patient-card/yygh.png";
const bgcxIcon = "https://cdnhyt.cd120.com/person/assets/patient-card/bgcx.png";

// 业务类型枚举（参考 react-old 项目）
const BIND_RESULT_TYPE = {
  BIND_SUCCESS: "1", // 直接绑定就诊卡成功
  BIND_AUTH_SUCCESS: "2", // 上传证件照注册、人脸识别注册绑卡成功
  BIND_AUTH_FAIL: "3", // 上传证件照绑卡失败-走审核
};

// 存储键名
const CARD_STORAGE = {
  CARD_BIND_DESC: "cardBindDesc", // 绑卡成功接口返回的描述
};

const BUSINESS_STORAGE = {
  HOME_SEARCH: "HOME_SEARCH",
};

// 响应式数据
const bindResultType = ref("");
const oldCode = ref("");
const successTitle = ref("");
const successMsg = ref("");

// 功能菜单配置
const menuList = ref([
  {
    id: 0,
    menuName: "门诊缴费",
    status: 1,
    url: mzjfIcon,
  },
  {
    id: 1,
    menuName: "预约挂号",
    status: 1,
    url: yyghIcon,
  },
  {
    id: 2,
    menuName: "报告查询",
    status: 1,
    url: bgcxIcon,
  },
]);

// 计算结果图标
const resultIcon = computed(() => {
  return bindResultType.value === BIND_RESULT_TYPE.BIND_SUCCESS
    ? bindResultIcon
    : realNameIcon;
});

// 返回首页
const goHome = () => {
  uni.reLaunch({
    url: "/pages/index/index",
  });
};

// 跳转到就诊卡列表
const goToCardList = () => {
  try {
    // 清除搜索信息
    uni.removeStorageSync(BUSINESS_STORAGE.HOME_SEARCH);

    uni.navigateTo({
      url: "/pages/patient-card/list/index",
    });
  } catch (error) {
    console.error("跳转就诊卡列表失败:", error);
    // 如果跳转失败，则返回上一页
    uni.navigateBack();
  }
};

// 功能菜单跳转
const goTo = (id: number) => {
  const menuItem = menuList.value.find((item) => item.id === id);

  if (menuItem?.status === 0) {
    uni.showToast({
      title: "暂未开放",
      icon: "none",
    });
    return;
  }

  let targetUrl = "";

  switch (id) {
    case 0: // 门诊缴费
      targetUrl = "/pages/outpatient-payment/index";
      break;
    case 1: // 预约挂号
      targetUrl = "/pages/appointment/index";
      break;
    case 2: // 报告查询
      targetUrl = "/pages/report-query/index";
      break;
    default:
      // 默认跳转首页
      goHome();
      return;
  }

  // 检查页面是否存在，如果不存在则提示暂未开放
  uni.navigateTo({
    url: targetUrl,
    fail: () => {
      uni.showToast({
        title: "功能暂未开放",
        icon: "none",
      });
    },
  });
};

// 页面加载
onLoad((options: any) => {
  console.log("绑卡结果页面参数:", options);

  // 获取页面参数
  bindResultType.value = options.bindResultType || "";
  oldCode.value = options.oldCode || "";

  // 从本地存储获取绑卡描述信息
  try {
    const cardBindDesc = uni.getStorageSync(CARD_STORAGE.CARD_BIND_DESC);
    if (cardBindDesc) {
      successTitle.value =
        cardBindDesc.successTittle || cardBindDesc.successTitle || "";
      successMsg.value = cardBindDesc.successMsg || "";

      console.log("获取绑卡描述信息:", cardBindDesc);
    }
  } catch (error) {
    console.error("获取绑卡描述信息失败:", error);
  }

  // 清理存储数据，避免影响下次使用
  try {
    uni.removeStorageSync(CARD_STORAGE.CARD_BIND_DESC);
  } catch (error) {
    console.error("清理存储数据失败:", error);
  }
});
</script>

<style lang="scss" scoped>
@use "../../../styles/tokens/colors.scss" as colors;

.bind-wrap {
  width: 100%;
  height: 100vh;
  text-align: center;
  padding: 0 72rpx;
  background-color: #fff;

  .icon {
    margin-top: 80rpx;
    width: 300rpx;
    height: 300rpx;
  }

  .status {
    margin-top: 48rpx;
    font-size: 36rpx;
    font-weight: 500;
    color: colors.$text-primary;
  }

  .desc {
    font-size: 28rpx;
    font-weight: 400;
    color: colors.$text-primary;
    line-height: 44rpx;
    text-align: left;
    margin-top: 24rpx;
    padding: 0 26rpx;
  }

  .menu-list {
    width: 100%;
    padding: 0 26rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 48rpx;

    .item {
      width: 168rpx;
      height: 168rpx;
      background: #f5f6fa;
      border-radius: 12rpx;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      image {
        width: 60rpx;
        height: 60rpx;
        flex-shrink: 0;
      }

      text {
        margin-top: 20rpx;
        font-size: 28rpx;
        font-weight: 400;
        color: colors.$text-primary;
      }
    }
  }

  .btn-wrap {
    width: 100%;
    margin-top: 128rpx;
    display: flex;
    flex-direction: column;
    gap: 48rpx;
  }

  .btn {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    border-radius: 44rpx;
    font-size: 32rpx;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;

    &.btn-outline {
      background-color: transparent;
      color: colors.$color-primary;
      border: 2rpx solid colors.$color-primary;
    }

    &.btn-primary {
      background-color: colors.$color-primary;
      color: #fff;
    }
  }
}
</style>
