/**
 * 卡详情页面 Hooks
 */
import { ref, computed } from "vue";
import type { PatientCard } from "../shared/types";
import { CardDetailService } from "./service";

/**
 * 扩展的卡片信息类型（兼容 react-old 项目的字段）
 */
interface ExtendedPatientCard extends PatientCard {
  // react-old 项目中的字段
  cardTypeCode?: string; // 卡片类型代码
  credNo?: string; // 证件号码
  birthday?: string; // 生日
  tel?: string; // 电话
  provinceName?: string; // 省份名称
  cityName?: string; // 城市名称
  cityAreaName?: string; // 区域名称
  detailAddress?: string; // 详细地址
  relation?: number; // 关系 0-本人，其他-非本人
  realname?: boolean; // 是否实名
  listDefault?: boolean; // 是否为默认卡
  cardInsChannel?: Array<{ name: string }>; // 医保渠道
  qrCode?: string; // 二维码数据
  childNewCardId?: string; // 子卡ID
  cardReleaseTimeStr?: string; // 卡释放时间
  slotReleaseTimeStr?: string; // 槽释放时间
  allBindCount?: number; // 总绑定次数
  leftUnBindCount?: number; // 剩余解绑次数
  cardStatusDesc?: string; // 卡状态描述
  cardTypeName?: string; // 卡类型名称
}

/**
 * 卡详情管理 Hook
 */
export const useCardDetail = () => {
  // 响应式数据
  const cardInfo = ref<ExtendedPatientCard>({} as ExtendedPatientCard);
  const loading = ref(false);
  const isClosed = ref(false);
  const qrcodeSize = ref(200);

  // 计算属性
  const showUpgradeButton = computed(() => {
    return (
      cardInfo.value.cardTypeCode === "YYEZTJZK" &&
      !cardInfo.value.childNewCardId
      // 这里可以根据实际的机构代码判断
    );
  });

  // 获取卡片信息样式类
  const getCardInfoClass = () => {
    const baseClass = "card-info";
    if (cardInfo.value.cardTypeCode === "EHCard") {
      return isClosed.value
        ? `${baseClass} eh-card closed`
        : `${baseClass} eh-card`;
    }
    return isClosed.value ? `${baseClass} closed` : baseClass;
  };

  // 获取显示的卡号
  const getDisplayCardNumber = () => {
    const { cardTypeCode, credNo, cardNo } = cardInfo.value;
    const number = cardTypeCode === "EHCard" ? credNo : cardNo;
    if (!number) return "";
    return `${number.slice(0, 4)}****${number.slice(number.length - 4)}`;
  };

  // 获取生日显示
  const getBirthdayDisplay = () => {
    const { birthday } = cardInfo.value;
    if (!birthday) return "";
    const month = Number(birthday.substring(5, 7));
    const day = Number(birthday.substring(8, 10));
    return `${month}月${day}日`;
  };

  // 获取显示的手机号
  const getDisplayPhone = () => {
    const { tel } = cardInfo.value;
    if (!tel) return "";
    return `${tel.slice(0, 3)}****${tel.slice(tel.length - 4)}`;
  };

  // 获取显示的地址
  const getDisplayAddress = () => {
    const { provinceName, cityName, cityAreaName, detailAddress } =
      cardInfo.value;
    if (!provinceName) return "";

    let address = "";
    if (provinceName === cityName) {
      address = `${provinceName}${cityAreaName}${detailAddress}`;
    } else {
      address = `${provinceName}${cityName}${cityAreaName}${detailAddress}`;
    }
    return address;
  };

  // 切换二维码显示状态
  const toggleQrcode = () => {
    isClosed.value = !isClosed.value;
  };

  // 刷新卡片信息
  const refreshCardInfo = async () => {
    if (!cardInfo.value.cardId) return;

    loading.value = true;
    try {
      const isChildCard = cardInfo.value.cardTypeCode === "YYEZTJZK";
      const response = isChildCard
        ? await CardDetailService.getChildCardInfo(cardInfo.value.cardId)
        : await CardDetailService.getCardInfo(cardInfo.value.cardId);

      if (response.code === "1" && response.data) {
        cardInfo.value = { ...cardInfo.value, ...response.data };
        console.log("刷新卡片信息成功:", cardInfo.value);
      } else {
        console.warn("刷新卡片信息失败:", response);
        uni.showToast({
          title: response.msg || "获取卡片信息失败",
          icon: "none",
        });
      }
    } catch (error) {
      console.error("刷新卡片信息异常:", error);
      uni.showToast({
        title: "网络错误，请稍后重试",
        icon: "none",
      });
    } finally {
      loading.value = false;
    }
  };

  // 设置默认卡
  const setDefaultCard = async () => {
    if (!cardInfo.value.cardId) return;

    try {
      uni.showModal({
        title: "提示",
        content: "是否设置为默认就诊卡",
        success: async (res) => {
          if (res.confirm) {
            loading.value = true;
            try {
              const response = await CardDetailService.setDefaultCard(
                cardInfo.value.cardId
              );
              if (response.code === "1") {
                uni.showToast({
                  title: "设置成功",
                  icon: "success",
                });
                // 更新卡片信息
                if (response.data) {
                  cardInfo.value = { ...cardInfo.value, ...response.data };
                }
              } else {
                uni.showToast({
                  title: response.msg || "设置失败",
                  icon: "none",
                });
              }
            } catch (error) {
              console.error("设置默认卡失败:", error);
              uni.showToast({
                title: "网络错误，请稍后重试",
                icon: "none",
              });
            } finally {
              loading.value = false;
            }
          }
        },
      });
    } catch (error) {
      console.error("显示确认弹窗失败:", error);
    }
  };

  // 解绑卡片
  const unbindCard = async () => {
    if (!cardInfo.value.cardId) return;

    const {
      cardReleaseTimeStr = "",
      slotReleaseTimeStr = "",
      allBindCount = 12,
      leftUnBindCount = 0,
    } = cardInfo.value;

    const content = `解绑后相关的就诊、住院、缴费记录将不再显示，如果持此卡人在就诊、缴费、住院、咨询、预约的流程中，解绑将导致业务无法查看与使用。解绑后${cardReleaseTimeStr}返回可绑卡数量，本卡也将在${slotReleaseTimeStr}后才可再次绑定,每个账户一年仅支持${
      allBindCount || 12
    }次解绑。您当前还有[${leftUnBindCount}]次解绑次数。`;

    try {
      uni.showModal({
        title: "确认解绑",
        content,
        success: async (res) => {
          if (res.confirm) {
            loading.value = true;
            try {
              const response = await CardDetailService.unbindCard(
                cardInfo.value.cardId
              );
              if (response.code === "1" && response.data?.cardId) {
                uni.showToast({
                  title: "解绑成功",
                  icon: "success",
                });
                // 返回列表页面
                setTimeout(() => {
                  // 获取页面栈
                  const pages = getCurrentPages();
                  if (pages.length > 1) {
                    // 如果有上一页，则返回
                    uni.navigateBack();
                  } else {
                    // 如果没有上一页，则跳转到卡列表页面
                    uni.redirectTo({
                      url: "/pages/patient-card/list/index",
                    });
                  }
                }, 1500);
              } else {
                uni.showToast({
                  title: response.msg || "解绑失败",
                  icon: "none",
                });
              }
            } catch (error) {
              console.error("解绑卡片失败:", error);
              uni.showToast({
                title: "网络错误，请稍后重试",
                icon: "none",
              });
            } finally {
              loading.value = false;
            }
          }
        },
      });
    } catch (error) {
      console.error("显示确认弹窗失败:", error);
    }
  };

  // 修改资料
  const modifyData = () => {
    // 根据卡片类型和机构配置跳转到不同的编辑页面
    const { cardId } = cardInfo.value;

    if (!cardId) {
      uni.showToast({
        title: "卡片信息异常",
        icon: "none",
      });
      return;
    }

    // 跳转到申请卡片页面进行编辑
    uni.navigateTo({
      url: `/pages/patient-card/applycard/index?businessType=EDIT_CARD&cardId=${cardId}`,
      fail: (error) => {
        console.error("跳转编辑页面失败:", error);
        uni.showToast({
          title: "页面跳转失败",
          icon: "none",
        });
      },
    });
  };

  // 更换就诊卡
  const changeCard = () => {
    uni.showModal({
      title: "提示",
      content: "仅支持当前卡为同一人的就诊卡更换",
      success: (res) => {
        if (res.confirm) {
          // TODO: 实现更换就诊卡逻辑
          uni.showToast({
            title: "功能开发中",
            icon: "none",
          });
        }
      },
    });
  };

  // 升级卡片
  const upgradeCard = async () => {
    if (!cardInfo.value.cardId) return;

    try {
      uni.showModal({
        title: "提示",
        content:
          "1、若您确定升级为电子健康卡，系统将自动为你注册电子健康卡，并绑定至本账户\n2、升级为电子健康卡后，已绑定的此卡将被切换为电子健康卡",
        success: async (res) => {
          if (res.confirm) {
            loading.value = true;
            try {
              const response = await CardDetailService.upgradeCard(
                cardInfo.value.cardId
              );
              if (response.code === "1") {
                uni.showToast({
                  title: "升级成功",
                  icon: "success",
                });
                // 刷新卡片信息
                await refreshCardInfo();
              } else {
                uni.showToast({
                  title: response.msg || "升级失败",
                  icon: "none",
                });
              }
            } catch (error) {
              console.error("升级卡片失败:", error);
              uni.showToast({
                title: "网络错误，请稍后重试",
                icon: "none",
              });
            } finally {
              loading.value = false;
            }
          }
        },
      });
    } catch (error) {
      console.error("显示确认弹窗失败:", error);
    }
  };

  return {
    // 响应式数据
    cardInfo,
    loading,
    isClosed,
    qrcodeSize,

    // 计算属性
    showUpgradeButton,

    // 方法
    getCardInfoClass,
    getDisplayCardNumber,
    getBirthdayDisplay,
    getDisplayPhone,
    getDisplayAddress,
    toggleQrcode,
    refreshCardInfo,
    setDefaultCard,
    unbindCard,
    modifyData,
    changeCard,
    upgradeCard,
  };
};

export default useCardDetail;
