/**
 * 卡详情页面服务
 */
import request from "@/utils/request";

/**
 * 卡详情服务类
 */
export class CardDetailService {
  /**
   * 获取卡详情信息
   */
  static async getCardInfo(cardId: string): Promise<any> {
    return request.post("/cloud/hosplatcustomer/cardservice/cardinfo", {
      cardId,
    });
  }

  /**
   * 获取儿童卡详情信息
   */
  static async getChildCardInfo(cardId: string): Promise<any> {
    return request.post("/cloud/hosplatcustomer/cardservice/childCardInfo", {
      cardId,
    });
  }

  /**
   * 设置默认卡
   */
  static async setDefaultCard(cardId: string): Promise<any> {
    return request.post("/cloud/hosplatcustomer/cardservice/setDefaultCard", {
      cardId,
    });
  }

  /**
   * 解绑卡片
   */
  static async unbindCard(cardId: string): Promise<any> {
    return request.post("/cloud/hosplatcustomer/cardservice/unbindCard", {
      cardId,
    });
  }

  /**
   * 升级卡片
   */
  static async upgradeCard(cardId: string): Promise<any> {
    return request.post("/cloud/hosplatcustomer/cardservice/upgradeCard", {
      cardId,
    });
  }

  /**
   * 更新卡片信息
   */
  static async updateCardInfo(params: {
    cardId: string;
    cityAreaCode?: string;
    cityAreaName?: string;
    cityCode?: string;
    cityName?: string;
    occupationCode?: string;
    occupationName?: string;
    provinceCode?: string;
    provinceName?: string;
    tel?: string;
    detailAddress?: string;
    relation?: number;
  }): Promise<any> {
    return request.post(
      "/cloud/hosplatcustomer/cardservice/updateCardInfo",
      params
    );
  }

  /**
   * 获取机构配置
   */
  static async getOrganConfig(params: {
    channelCode: string;
    organCode: string;
  }): Promise<any> {
    return request.post(
      "/cloud/hosplatcustomer/cardservice/getOrganConfig",
      params
    );
  }

  /**
   * 医保绑定
   */
  static async insBind(params: {
    aliCode?: string;
    cardId: string;
    insChannel: string;
    openId: string;
  }): Promise<any> {
    return request.post("/cloud/hosplatcustomer/cardservice/insBind", params);
  }

  /**
   * 获取医保绑定链接
   */
  static async getInsBindingUrl(params: {
    hospitalCode: string;
    insChannel: string;
    openId: string;
    returnUrl: string;
  }): Promise<any> {
    return request.post(
      "/cloud/hosplatcustomer/cardservice/insBindingurl",
      params
    );
  }
}

export default CardDetailService;
