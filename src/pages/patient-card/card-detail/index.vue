<template>
  <hyt-page navbarTitle="就诊卡详情">
    <view class="container" :class="{ containerVip: isVip }">
      <!-- 卡片头部区域 -->
      <view class="cardDetaiHead">
        <!-- 无二维码状态 -->
        <view class="noQRcodeDetail" v-if="qrCode === ''">
          <image :src="cardDetailInfoIcon" alt="" />
          <view class="noQRcodeDetailText">暂无健康卡二维码</view>
          <view class="noQRcodeDetailText"
            >如需使用请点击下方按钮升级为电子健康卡</view
          >
          <view class="updateCard" @click="upgradeCard">
            升级为电子健康卡
          </view>
        </view>

        <!-- 有二维码状态 -->
        <view class="QRcodeDetail" :class="{ QRcodeDetailVip: isVip }" v-else>
          <view class="code">
            <uqrcode
              ref="qrcodeRef"
              canvas-id="qrcode"
              :value="qrCode"
              :size="360"
              size-unit="rpx"
              :options="qrcodeOptions"
              @complete="onQrcodeComplete"
              class="QRCode"
            />
          </view>
          <view class="registrationMark">登记号：{{ organPmino }}</view>
          <!-- 升级按钮（院内卡显示） -->
          <view
            v-if="cardTypeCode === 'YYEZTJZK'"
            class="updateCard"
            @click="upgradeCard"
          >
            升级为电子健康卡
          </view>
        </view>
      </view>

      <!-- 卡片详情主体 -->
      <view class="cardDetaiBody">
        <view class="cardDetaiBodyTitle">
          <view>
            <text>就诊卡信息</text>
            <text v-if="realname">已实名</text>
          </view>
          <view v-if="!listDefault">
            <text>设为默认</text>
            <switch
              :checked="listDefault"
              @change="onSwitchChange"
              color="#3AD3C1"
            />
          </view>
          <view v-else>
            <text>默认就诊卡</text>
          </view>
        </view>

        <view class="cardDetaiBodyList">
          <view>
            <text>姓名</text>
            <text>{{ patientName }}</text>
          </view>
          <view>
            <text>证件类型</text>
            <text>{{ credTypeName }}</text>
          </view>
          <view>
            <text>证件号码</text>
            <text>{{ getCredNoDisplay() }}</text>
          </view>
          <view v-if="cardTypeCode !== 'EHCard'">
            <text>就诊卡号</text>
            <text>{{ cardNo }}</text>
          </view>
          <view>
            <text>出生日期</text>
            <text>{{ birthday }}</text>
          </view>
          <view>
            <text>民族</text>
            <text>{{ nationName }}</text>
          </view>
          <!-- 儿童卡监护人信息 -->
          <template v-if="cardTypeCode === 'YYEZTJZK'">
            <view>
              <text>监护人</text>
              <text>{{ guarderName }}</text>
            </view>
            <view>
              <text>监护人关系</text>
              <text>{{ getGuarderRelationName() }}</text>
            </view>
          </template>
          <view>
            <text>关系</text>
            <text>{{ relation === 0 ? "本人" : "其他" }}</text>
          </view>
          <view>
            <text>联系电话</text>
            <text>{{ tel }}</text>
          </view>
          <view v-if="cardTypeCode !== 'YYEZTJZK'">
            <text>职业</text>
            <text>{{ occupationName }}</text>
          </view>
          <view>
            <text>联系地址</text>
            <text>{{ getAddressDisplay() }}</text>
          </view>
          <view>
            <text>详细地址</text>
            <text>{{ detailAddress }}</text>
          </view>
          <view
            v-if="getOrganCode() === 'HID0101' && cardTypeCode !== 'YYEZTJZK'"
          >
            <text>工作单位</text>
            <text>{{ company }}</text>
          </view>
        </view>
      </view>

      <!-- 底部操作按钮 -->
      <view class="cardDetailFooter">
        <view @click="unbundleCard">解除绑定</view>
        <view @click="modifiedData">修改资料</view>
      </view>
    </view>
  </hyt-page>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { onLoad, onShow } from "@dcloudio/uni-app";
import { getOrganCode } from "@/utils/platform";
import { CardDetailService } from "./service";
import uqrcode from "@/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue";

// 图片资源
import logoIcon from "./assets/patientCardNew/logo.png";
import cardDetailInfoIcon from "./assets/patientCardNew/cardDetailInfo.png";

// 响应式数据
const cardInfo = ref<any>({});
const realtionList = ref<any[]>([]);

// 从 cardInfo 中解构数据（与 React 版本保持一致）
const organPmino = computed(() => cardInfo.value.organPmino || "");
const listDefault = computed(() => cardInfo.value.listDefault || false);
const realname = computed(() => cardInfo.value.realname || false);
const patientName = computed(() => cardInfo.value.patientName || "");
const cardTypeCode = computed(() => cardInfo.value.cardTypeCode || "");
const credNo = computed(() => cardInfo.value.credNo || "");
const birthday = computed(() => cardInfo.value.birthday || "");
const nationName = computed(() => cardInfo.value.nationName || "");
const tel = computed(() => cardInfo.value.tel || "");
const occupationName = computed(() => cardInfo.value.occupationName || "");
const provinceName = computed(() => cardInfo.value.provinceName || "");
const cityName = computed(() => cardInfo.value.cityName || "");
const cityAreaName = computed(() => cardInfo.value.cityAreaName || "");
const credTypeName = computed(() => cardInfo.value.credTypeName || "");
const detailAddress = computed(() => cardInfo.value.detailAddress || "");
const cardNo = computed(() => cardInfo.value.cardNo || "");
const relation = computed(() => cardInfo.value.relation);
const guarderName = computed(() => cardInfo.value.guarderName || "");
const guarderRelation = computed(() => cardInfo.value.guarderRelation || "");
const vipType = computed(() => cardInfo.value.vipType || "");
const company = computed(() => cardInfo.value.company || "");
const streetName = computed(() => cardInfo.value.streetName || "");

// 二维码处理
const qrCode = computed(() => {
  let qr = cardInfo.value.qrCode;
  if (!qr) {
    if (cardTypeCode.value === "YYEZTJZK") qr = cardNo.value;
    else qr = "";
  }
  return qr;
});

// VIP 状态
const isVip = computed(() => vipType.value === "1");

// 二维码相关
const qrcodeRef = ref();
const qrcodeOptions = computed(() => {
  const baseOptions = {
    margin: 10,
    backgroundColor: "#ffffff",
    foregroundColor: "#000000",
    errorCorrectionLevel: "M",
  };

  // 当不是院内卡时，添加 logo 到二维码中心
  if (cardTypeCode.value !== "YYEZTJZK") {
    return {
      ...baseOptions,
      foregroundImageSrc: logoIcon,
      foregroundImageWidth: 44,
      foregroundImageHeight: 44,
      foregroundImageBackgroundColor: "#ffffff",
      foregroundImageBorderRadius: 0, // 设置为 0 保持正方形
    };
  }

  return baseOptions;
});

// 二维码生成完成回调
const onQrcodeComplete = (res: any) => {
  console.log("二维码生成完成:", res);
};

// 方法
const getCredNoDisplay = () => {
  const cred = credNo.value;
  if (!cred || cred.length < 8) return cred;
  return `${cred.slice(0, 4)}****${cred.slice(cred.length - 4)}`;
};

const getGuarderRelationName = () => {
  if (!realtionList.value || realtionList.value.length === 0) return "";
  const relation = realtionList.value.find(
    (item) => guarderRelation.value === item.dicCode
  );
  return relation?.dicName || "";
};

const getAddressDisplay = () => {
  return `${provinceName.value}${cityName.value}${cityAreaName.value}${streetName.value}`;
};

// Switch 切换事件
const onSwitchChange = (event: any) => {
  if (event.detail.value) {
    setDefaultCard();
  }
};

// 设置默认卡
const setDefaultCard = async () => {
  try {
    const response = await CardDetailService.setDefaultCard(
      cardInfo.value.cardId
    );

    if (response.code === "1") {
      uni.showToast({
        title: "设置成功",
        icon: "success",
      });
      cardInfo.value.listDefault = true;
    } else {
      uni.showToast({
        title: response.msg || "设置失败",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("设置默认卡失败:", error);
    uni.showToast({
      title: "设置失败",
      icon: "none",
    });
  }
};

// 升级卡片
const upgradeCard = async () => {
  // 将卡片信息存储到缓存中，供验证页面使用（参考React老项目逻辑）
  try {
    const cachedCardInfo = {
      cardId: cardInfo.value.cardId,
      patientName: cardInfo.value.patientName,
      credNo: cardInfo.value.credNo,
      cardTypeCode: cardInfo.value.cardTypeCode,
      credTypeCode: cardInfo.value.credTypeCode,
      credTypeName: cardInfo.value.credTypeName,
      // 添加出生日期等关键字段
      birthday: cardInfo.value.birthday,
      gender: cardInfo.value.gender,
      nation: cardInfo.value.nation,
      profession: cardInfo.value.profession,
      phone: cardInfo.value.phone,
      tel: cardInfo.value.tel, // 添加tel字段
      // 地址信息
      provinceCode: cardInfo.value.provinceCode,
      provinceName: cardInfo.value.provinceName,
      cityCode: cardInfo.value.cityCode,
      cityName: cardInfo.value.cityName,
      cityAreaCode: cardInfo.value.cityAreaCode,
      cityAreaName: cardInfo.value.cityAreaName,
      streetCode: cardInfo.value.streetCode,
      streetName: cardInfo.value.streetName,
      detailAddress: cardInfo.value.detailAddress,
      // 监护人信息（如果有）
      guarderName: cardInfo.value.guarderName,
      guarderCredTypeCode: cardInfo.value.guarderCredTypeCode,
      guarderCredNo: cardInfo.value.guarderCredNo,
      guarderRelation: cardInfo.value.guarderRelation,
      // 患者关系信息
      patientRelation:
        cardInfo.value.patientRelation || cardInfo.value.relation,
    };

    uni.setStorageSync("cached_card_info", cachedCardInfo);
    console.log("缓存卡片信息成功:", cachedCardInfo);
  } catch (error) {
    console.error("缓存卡片信息失败:", error);
  }

  // 跳转到验证页面进行升级（参考React老项目逻辑）
  uni.navigateTo({
    url: `/pages/patient-card/verify-card/index?businessType=2&cardId=${cardInfo.value.cardId}`,
  });
};

// 解除绑定
const unbundleCard = async () => {
  if (!cardInfo.value.cardId) {
    uni.showToast({
      title: "卡片信息异常",
      icon: "none",
    });
    return;
  }

  const {
    cardReleaseTimeStr = "",
    slotReleaseTimeStr = "",
    allBindCount = 12,
    leftUnBindCount = 0,
  } = cardInfo.value;

  const content = `解绑后相关的就诊、住院、缴费记录将不再显示，如果持此卡人在就诊、缴费、住院、咨询、预约的流程中，解绑将导致业务无法查看与使用。解绑后${cardReleaseTimeStr}返回可绑卡数量，本卡也将在${slotReleaseTimeStr}后才可再次绑定,每个账户一年仅支持${
    allBindCount || 12
  }次解绑。您当前还有[${leftUnBindCount}]次解绑次数。`;

  try {
    uni.showModal({
      title: "确认解绑",
      content,
      success: async (res) => {
        if (res.confirm) {
          try {
            uni.showLoading({
              title: "解绑中...",
            });

            const response = await CardDetailService.unbindCard(
              cardInfo.value.cardId
            );

            if (response.code === "1" && response.data?.cardId) {
              uni.showToast({
                title: "解绑成功",
                icon: "success",
              });
              // 返回列表页面
              setTimeout(() => {
                // 直接返回上一页，如果没有上一页会自动处理
                uni.navigateBack({
                  fail: () => {
                    // 如果返回失败，则跳转到卡列表页面
                    uni.redirectTo({
                      url: "/pages/patient-card/list/index",
                    });
                  },
                });
              }, 1500);
            } else {
              uni.showToast({
                title: response.msg || "解绑失败",
                icon: "none",
              });
            }
          } catch (error) {
            console.error("解绑卡片失败:", error);
            uni.showToast({
              title: "网络错误，请稍后重试",
              icon: "none",
            });
          } finally {
            uni.hideLoading();
          }
        }
      },
    });
  } catch (error) {
    console.error("显示确认弹窗失败:", error);
  }
};

// 修改资料
const modifiedData = () => {
  // 根据卡片类型和机构配置跳转到不同的编辑页面
  const { cardId } = cardInfo.value;

  if (!cardId) {
    uni.showToast({
      title: "卡片信息异常",
      icon: "none",
    });
    return;
  }

  // 跳转到申请卡片页面进行编辑
  uni.navigateTo({
    url: `/pages/patient-card/applycard/index?businessType=3&cardId=${cardId}`,
    fail: (error) => {
      console.error("跳转编辑页面失败:", error);
      uni.showToast({
        title: "页面跳转失败",
        icon: "none",
      });
    },
  });
};

// 获取关系列表数据
const getRelationList = () => {
  try {
    const list = uni.getStorageSync("base_data_relation");
    realtionList.value = list || [];
  } catch (error) {
    console.error("获取关系列表失败:", error);
  }
};

// 获取卡片详情信息
const fetchCardInfo = async (cardId: string) => {
  try {
    uni.showLoading({
      title: "加载中...",
    });

    const response = await CardDetailService.getCardInfo(cardId);

    if (response.code === "1") {
      cardInfo.value = response.data;
      console.log("获取卡片信息成功:", cardInfo.value);
    } else {
      uni.showToast({
        title: response.msg || "获取卡片信息失败",
        icon: "none",
      });
    }
  } catch (error) {
    console.error("获取卡片信息失败:", error);
    uni.showToast({
      title: "获取卡片信息失败",
      icon: "none",
    });
  } finally {
    uni.hideLoading();
  }
};

// 存储当前卡片ID，用于页面显示时刷新
let currentCardId = "";
let isFirstShow = true; // 标记是否是首次显示

// 页面加载
onLoad(async (options: any) => {
  console.log("卡详情页面参数:", options);

  // 从参数中获取卡片ID
  if (options.cardId) {
    console.log("卡片ID:", options.cardId);
    currentCardId = options.cardId; // 保存卡片ID
    await fetchCardInfo(options.cardId);
  } else {
    console.error("缺少卡片ID参数");
    uni.showToast({
      title: "参数错误",
      icon: "none",
    });
  }

  // 获取关系列表
  getRelationList();
});

// 页面显示时刷新数据（从编辑页面返回时会触发）
onShow(async () => {
  console.log("卡详情页面显示，isFirstShow:", isFirstShow);

  // 首次显示时不需要刷新（onLoad已经加载过了）
  if (isFirstShow) {
    isFirstShow = false;
    return;
  }

  // 非首次显示（比如从编辑页面返回），刷新卡片信息
  if (currentCardId) {
    console.log("从其他页面返回，刷新卡片信息，卡片ID:", currentCardId);
    await fetchCardInfo(currentCardId);
  }
});

onMounted(() => {
  // uqrcode 组件会自动生成二维码，无需手动调用
  console.log("卡详情页面已挂载");
});
</script>

<style lang="scss" scoped>
.container {
  height: calc(100vh - 128rpx - constant(safe-area-inset-bottom));
  height: calc(100vh - 128rpx - env(safe-area-inset-bottom));
  overflow: auto;
  padding: 24rpx;
  padding-bottom: calc(128rpx + 24rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(128rpx + 24rpx + env(safe-area-inset-bottom));
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;

  .cardDetaiHead {
    width: 702rpx;
    background: #ffffff;
    border-radius: 24rpx;

    .noQRcodeDetail {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-bottom: 60rpx;

      > image {
        width: 144rpx;
        height: 144rpx;
        margin-top: 60rpx;
      }

      .noQRcodeDetailText:nth-of-type(1) {
        margin-top: 60rpx;
      }

      .noQRcodeDetailText {
        font-size: 28rpx;
        font-weight: 400;
        color: #989eb4;
      }

      .updateCard {
        margin-top: 24rpx;
        width: 574rpx;
        height: 88rpx;
        background: #3ad3c1;
        border-radius: 52rpx;
        line-height: 88rpx;
        text-align: center;
        font-size: 32rpx;
        font-weight: 500;
        color: #ffffff;
      }
    }

    .QRcodeDetail {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: 60rpx;
      padding-bottom: 40rpx;

      .code {
        position: relative;
        height: 360rpx;

        .QRCode {
          width: 360rpx !important;
          height: 360rpx !important;
        }

        > image {
          width: 88rpx;
          height: 88rpx;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
      }

      .registrationMark {
        font-size: 28rpx;
        font-weight: 500;
        color: #989eb4;
        margin-top: 40rpx;
      }

      .updateCard {
        margin-top: 24rpx;
        width: 574rpx;
        height: 88rpx;
        background: #3ad3c1;
        border-radius: 52rpx;
        line-height: 88rpx;
        text-align: center;
        font-size: 32rpx;
        font-weight: 500;
        color: #ffffff;
      }
    }

    .QRcodeDetailVip {
      background-image: url("./assets/patientCardNew/vipBg.png");
      background-size: cover;

      .registrationMark {
        color: #cb9d62;
      }
    }
  }

  .cardDetaiBody {
    margin-top: 24rpx;
    width: 702rpx;
    background: #ffffff;
    border-radius: 24rpx;

    .cardDetaiBodyTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 88rpx;
      padding: 0 24rpx;
      margin-bottom: 24rpx;

      > view:nth-child(1) {
        display: flex;
        align-items: center;

        > text:nth-child(1) {
          font-size: 36rpx;
          font-weight: 600;
          color: #03081a;
        }

        > text:nth-child(2) {
          font-size: 20rpx;
          font-weight: 400;
          color: #ffffff;
          width: 76rpx;
          height: 36rpx;
          line-height: 36rpx;
          text-align: center;
          background: #fbbc44;
          border-radius: 18rpx;
          margin-left: 16rpx;
        }
      }

      > view:nth-child(2) {
        display: flex;
        align-items: center;
        font-size: 28rpx;
        font-weight: 400;
        color: #989eb4;

        > text:nth-child(1) {
          font-size: 28rpx;
          font-weight: 400;
          color: #989eb4;
          margin-right: 16rpx;
        }
      }
    }

    .cardDetaiBodyList {
      padding-bottom: 24rpx;

      > view {
        box-sizing: border-box;
        padding: 0 24rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        > text:nth-child(1) {
          font-size: 28rpx;
          font-weight: 400;
          color: #989eb4;
        }

        > text:nth-child(2) {
          font-size: 28rpx;
          font-weight: 400;
          color: #03081a;
          display: inline-block;
          max-width: 400rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }

      > view + view {
        margin-top: 32rpx;
      }
    }
  }

  .cardDetailFooter {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: calc(128rpx + constant(safe-area-inset-bottom));
    height: calc(128rpx + env(safe-area-inset-bottom));
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 16rpx 24rpx;

    > view {
      width: 326rpx;
      height: 96rpx;
      border-radius: 52rpx;
      border: 2rpx solid #fc4553;
      color: #fc4553;
      font-size: 32rpx;
      font-weight: 500;
      text-align: center;
      line-height: 96rpx;
    }

    > view:nth-child(2) {
      border: 2rpx solid #3ad3c1;
      color: #3ad3c1;
    }
  }
}

.containerVip {
  .cardDetaiHead {
    .QRcodeDetail {
      background-image: url("./assets/patientCardNew/vipBg.png");
      background-size: cover;

      .registrationMark {
        color: #cb9d62;
      }
    }
  }

  .cardDetailFooter {
    > view:nth-child(2) {
      border: 2rpx solid #cb9d62;
      color: #cb9d62;
    }
  }
}
</style>
