<template>
  <view class="test-container">
    <view class="test-title">卡详情页面测试</view>

    <view class="test-buttons">
      <button @click="testWithQRCode" class="test-btn">测试有二维码卡片</button>
      <button @click="testWithoutQRCode" class="test-btn">
        测试无二维码卡片
      </button>
      <button @click="testVipCard" class="test-btn">测试VIP卡片</button>
      <button @click="testChildCard" class="test-btn">测试儿童卡</button>
    </view>
  </view>
</template>

<script setup lang="ts">
// 测试数据
const testCardWithQR = {
  organPmino: "*********",
  listDefault: false,
  realname: true,
  patientName: "张三",
  cardTypeCode: "EHCard",
  credNo: "******************",
  birthday: "1990-01-01",
  nationName: "汉族",
  tel: "13800138000",
  occupationName: "工程师",
  provinceName: "广东省",
  cityName: "广州市",
  cityAreaName: "天河区",
  credTypeName: "身份证",
  detailAddress: "天河路123号",
  cardNo: "**********",
  relation: 0,
  vipType: "0",
  company: "某某公司",
  streetName: "天河街道",
  qrCode: "test_qr_code_123456",
};

const testCardWithoutQR = {
  ...testCardWithQR,
  qrCode: "",
};

const testVipCard = {
  ...testCardWithQR,
  vipType: "1",
};

const testChildCard = {
  ...testCardWithQR,
  cardTypeCode: "YYEZTJZK",
  guarderName: "李四",
  guarderRelation: "5",
};

const testWithQRCode = () => {
  uni.navigateTo({
    url: `/pages/patient-card/card-detail/index?cardId=test_card_1`,
  });
};

const testWithoutQRCode = () => {
  uni.navigateTo({
    url: `/pages/patient-card/card-detail/index?cardId=test_card_2`,
  });
};

const testVipCard = () => {
  uni.navigateTo({
    url: `/pages/patient-card/card-detail/index?cardId=test_card_3`,
  });
};

const testChildCard = () => {
  uni.navigateTo({
    url: `/pages/patient-card/card-detail/index?cardId=test_card_4`,
  });
};
</script>

<style lang="scss" scoped>
.test-container {
  padding: 40rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.test-title {
  font-size: 36rpx;
  font-weight: 600;
  text-align: center;
  margin-bottom: 60rpx;
  color: #333;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.test-btn {
  width: 100%;
  height: 88rpx;
  background: #3ad3c1;
  color: white;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
}

.test-btn:active {
  background: #2bb3a1;
}
</style>
