<template>
  <hyt-page navbarTitle="申领电子健康卡">
    <view class="apply-container">
      <!-- 基础信息卡片 -->
      <view class="info-card">
        <view class="card-title">基础信息</view>
        <view class="form-container">
          <!-- 证件类型选择 -->

          <view v-if="credTypeArr.length > 0" class="form-item">
            <view class="form-label">证件类型</view>
            <picker
              :value="credTypePickerValue"
              :range="credTypeArr"
              range-key="label"
              @change="onCredTypeChangeHandler"
              class="form-picker"
            >
              <view class="form-value">
                <text :class="credType.length ? 'selected' : 'placeholder'">
                  {{ getCredTypeLabel() || "请选择证件类型" }}
                </text>
                <image :src="arrowRightIcon" class="arrow" />
              </view>
            </picker>
          </view>

          <!-- 认证方式选择 -->
          <view
            v-if="checktypeList.length > 0 && !isChildCard"
            class="form-item"
          >
            <view class="form-label">认证方式</view>
            <view class="radio-group">
              <view
                v-for="item in checktypeList"
                :key="item.value"
                class="radio-item"
                :class="{ disabled: item.disabled }"
                @click="selectCheckType(item.value)"
              >
                <image
                  :src="
                    checktypeValue === item.value ? checkedIcon : uncheckIcon
                  "
                  class="radio-icon"
                />
                <text class="radio-text">{{ item.label }}</text>
              </view>
            </view>
          </view>

          <!-- 证件上传 -->
          <view v-if="showUploadCard" class="form-item upload-card-item">
            <view class="form-label">上传证件</view>
            <view class="upload-container">
              <view v-if="cardImg" class="uploaded-image">
                <image :src="cardImg" class="card-image" mode="aspectFit" />
                <view class="image-actions">
                  <text class="action-btn" @click="uploadCardImage">上传</text>
                  <text class="action-btn delete" @click="deleteCardImage"
                    >删除</text
                  >
                </view>
              </view>
              <image
                v-else
                :src="uploadIcon"
                class="upload-placeholder"
                @click="uploadCardImage"
              />
            </view>
          </view>

          <!-- 姓名输入 -->
          <view class="form-item">
            <view class="form-label">姓名</view>
            <input
              v-model="patientName"
              class="form-input"
              :class="{ disabled: isNameDisabled }"
              :disabled="isNameDisabled"
              placeholder="请输入就诊人真实姓名"
              placeholder-style="color: #D6DAE6;"
              maxlength="20"
            />
          </view>

          <!-- 证件号码输入 -->
          <view v-if="credType[0] !== '100'" class="form-item no-border">
            <view class="form-label">证件号码</view>
            <view class="input-container">
              <input
                v-model="credNo"
                class="form-input"
                :class="{ disabled: isCredNoDisabled }"
                :disabled="isCredNoDisabled"
                placeholder="请输入就诊人证件号码"
                placeholder-style="color: #D6DAE6;"
              />
              <image
                v-if="useOcr"
                :src="cameraIcon"
                class="camera-icon"
                @click="handleOcrScan"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 协议阅读 -->
      <view class="protocol-container">
        <view class="protocol-title">
          <text>阅读并同意以下内容</text>
          <image
            :src="hasRead ? checkedIcon : uncheckIcon"
            class="checkbox-icon"
            @click="toggleRead"
          />
        </view>
        <view class="protocol-line"></view>
        <view class="protocol-content">
          <view v-if="credType[0] !== '100'" class="normal-content">
            您已知晓您在华医通的身份证件等个人信息，将用于您申领电子健康卡、预约挂号等所有需要实名制的场景，并在使用时进行验证，请确保此信息的真实有效。华医通将通过加密等方式保护此信息，且仅在具体业务流程中授权提供给第三方医疗卫生监管单位。
          </view>
          <view v-else class="child-content">
            <view class="notice-item">
              <image :src="alertIcon" class="notice-icon" />
              <text class="notice-title">注意事项</text>
            </view>
            <view class="notice-text">{{ getChildNotice() }}</view>
            <view v-if="getAddressLines().length > 0">
              <view class="address-item">
                <image :src="addressIcon" class="address-icon" />
                <text class="address-title">办理地点：</text>
              </view>
              <view class="address-text">
                <view
                  v-for="(line, index) in getAddressLines()"
                  :key="index"
                  class="address-line"
                >
                  {{ line }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="footer">
      <view
        class="submit-btn"
        :class="{ disabled: !canSubmit }"
        :disabled="!canSubmit"
        @click="handleSubmit"
      >
        下一步
      </view>
    </view>
  </hyt-page>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";
import { useVerifyCard } from "./hooks/index";

// 导入图片资源
import cameraIcon from "./assets/camera.png";
import checkedIcon from "./assets/checked.png";
import uncheckIcon from "./assets/uncheck.png";
import alertIcon from "./assets/alert.png";
import addressIcon from "./assets/address.png";
import uploadIcon from "./assets/upload-icon.png";
import arrowRightIcon from "./assets/arrow-right.png";

// 现在可以通过变量动态控制图片显示
// 例如：可以根据主题、状态等条件切换不同的图片
// const currentCameraIcon = computed(() => isDarkMode.value ? cameraIconDark : cameraIcon)

// 使用页面主 Hook
const {
  // 响应式数据
  patientName,
  credNo,
  credType,
  credTypeArr,
  checktypeList,
  checktypeValue,
  hasRead,
  cardImg,

  // 计算属性
  isChildCard,
  isNameDisabled,
  isCredNoDisabled,
  useOcr,
  useUploadOcr,
  canSubmit,
  showUploadCard,

  // 方法
  getCredTypeLabel,
  getChildNotice,
  getAddressLines,
  selectCheckType,
  uploadCardImage,
  deleteCardImage,
  handleOcrScan,
  toggleRead,
  handleSubmit,
  onCredTypeChange,
} = useVerifyCard();

// 选择器相关状态
const credTypePickerValue = ref(0);

// 监听credType变化，同步更新picker的显示值
watch(
  credType,
  (newCredType) => {
    if (newCredType.length > 0 && credTypeArr.value.length > 0) {
      const index = credTypeArr.value.findIndex(
        (item) => item.value === newCredType[0]
      );
      if (index !== -1) {
        credTypePickerValue.value = index;
      }
    }
  },
  { immediate: true }
);

// 证件类型选择变化
const onCredTypeChangeHandler = (e: any) => {
  const index = e.detail.value;
  const selectedType = credTypeArr.value[index];
  if (selectedType) {
    // 更新picker的显示值
    credTypePickerValue.value = index;
    // 调用hooks中的处理方法
    onCredTypeChange([selectedType.value]);
  }
};
</script>

<style lang="scss" scoped>
@use "../../../styles/tokens/colors.scss" as colors;

/* 全局占位符样式 - 兼容多种小程序 */
input::-webkit-input-placeholder {
  color: #d6dae6;
}
input::placeholder {
  color: #d6dae6;
}
input:placeholder-shown {
  color: #d6dae6;
}

.apply-container {
  padding: 24rpx;
  width: 100%;
  flex: 1;
  overflow: auto;
  padding-bottom: calc(24rpx + 128rpx);
  padding-bottom: calc(24rpx + 128rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(24rpx + 128rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

.info-card {
  width: 702rpx;
  background: #ffffff;
  border-radius: 24rpx;
  margin: 0 auto;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-sizing: border-box;

  .card-title {
    font-size: 36rpx;
    font-weight: 600;
    color: colors.$text-primary;
    margin-bottom: 24rpx;
  }
}

.form-container {
  width: 100%;

  .form-picker {
    flex: 1;
    width: 486rpx;
  }

  .form-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    height: 100rpx;
    padding: 0;
    border-bottom: 2rpx solid #f6f8ff;
    margin-bottom: 0;
    width: 100%;
    box-sizing: border-box;

    &.upload-card-item {
      align-items: flex-start;
      height: auto;
      padding: 32rpx 0;
    }

    &.no-border {
      border-bottom: none;
    }

    .form-label {
      font-size: 28rpx;
      color: colors.$text-secondary;
      font-weight: 400;
      min-width: 160rpx;
      margin-bottom: 0;
    }

    .form-value {
      display: flex;
      align-items: center;
      width: 486rpx;
      height: auto;
      // margin-left: 20rpx;
      justify-content: space-between;
      padding-right: 16rpx;

      .selected {
        color: colors.$text-primary;
        font-size: 28rpx;
      }

      .placeholder {
        color: #d6dae6;
        font-size: 28rpx;
      }

      .arrow {
        width: 32rpx;
        height: 32rpx;
        margin-left: 8rpx;
      }
    }

    .form-input {
      width: 486rpx;
      height: auto;
      font-size: 28rpx;
      color: colors.$text-primary;
      background: transparent;
      border: none;
      padding: 0;

      &.disabled {
        color: colors.$text-placeholder;
      }

      &::placeholder {
        color: #d6dae6;
      }
    }

    .input-container {
      display: flex;
      align-items: center;
      width: 486rpx;
      height: auto;
      justify-content: space-between;
      padding-right: 16rpx;

      .form-input {
        margin-left: 0;
        width: calc(100% - 48rpx); // 减去相机图标的空间
      }

      .camera-icon {
        width: 40rpx;
        height: 40rpx;
        margin-left: 8rpx;
      }
    }
  }
}

.radio-group {
  display: flex;
  flex-direction: row;
  gap: 32rpx;
  justify-content: flex-start;
  // margin-left: 20rpx;
  width: 486rpx;

  .radio-item {
    display: flex;
    align-items: center;

    &.disabled {
      cursor: not-allowed;

      .radio-icon {
        border-color: colors.$color-divider-1;
        background-color: colors.$color-divider-1;
        opacity: 0.5;

        &.checked {
          border-color: colors.$color-divider-1;
          background-color: colors.$color-divider-1;

          &::after {
            background-color: colors.$text-placeholder;
          }
        }
      }
    }

    .radio-icon {
      width: 36rpx;
      height: 36rpx;
      margin-right: 12rpx;
    }

    .radio-text {
      font-size: 28rpx;
      color: colors.$text-primary;
    }
  }
}

.upload-container {
  flex: 1;
  position: relative;

  .uploaded-image {
    position: relative;

    .card-image {
      display: block;
      width: 486rpx;
      height: 300rpx;
      border-radius: 8rpx;
    }

    .image-actions {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 64rpx;
      background: rgba(0, 0, 0, 0.5);
      border-radius: 0 16rpx 16rpx 0;
      display: flex;

      .action-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        flex: 1;
        font-size: 24rpx;
        color: #ffffff;

        &.delete {
          color: #ffffff;
        }
      }

      &::after {
        content: "";
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 2rpx;
        height: 48rpx;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2rpx;
      }
    }
  }

  .upload-placeholder {
    display: block;
    width: 486rpx;
    height: 300rpx;
    border: 1rpx dashed colors.$color-divider-1;
    border-radius: 8rpx;
    background: colors.$color-background;
  }
}

.protocol-container {
  width: 702rpx;
  background: #ffffff;
  border-radius: 24rpx;
  margin: 0 auto;
  padding: 32rpx;
  margin-top: 24rpx;

  .protocol-title {
    display: flex;
    align-items: center;
    justify-content: space-between;

    text {
      height: 32rpx;
      font-size: 32rpx;
      font-weight: 600;
      color: colors.$text-primary;
      line-height: 32rpx;
    }

    .checkbox-icon {
      width: 40rpx;
      height: 40rpx;
    }
  }

  .protocol-line {
    content: "";
    display: block;
    width: 638rpx;
    margin: 24rpx 0;
    height: 2rpx;
    background: #f6f8ff;
  }

  .protocol-content {
    font-size: 24rpx;
    line-height: 48rpx;
    color: colors.$text-secondary;
    text-align: justify;

    .child-content {
      .notice-item {
        display: flex;
        align-items: center;
        margin-bottom: 22rpx;

        .notice-icon {
          width: 40rpx;
          height: 40rpx;
          margin-right: 16rpx;
        }

        .notice-title {
          font-size: 32rpx;
          font-weight: 600;
          color: colors.$text-primary;
        }
      }

      .notice-text {
        font-size: 28rpx;
        color: colors.$text-secondary;
        line-height: 48rpx;
        margin-bottom: 16rpx;
      }

      .address-item {
        display: flex;
        align-items: center;
        margin: 16rpx 0 8rpx;

        .address-icon {
          width: 32rpx;
          height: 32rpx;
          margin-right: 4rpx;
        }

        .address-title {
          font-size: 28rpx;
          color: colors.$text-secondary;
        }
      }

      .address-text {
        .address-line {
          font-size: 28rpx;
          color: colors.$text-secondary;
          line-height: 44rpx;
        }
      }
    }
  }
}

.footer {
  position: fixed;
  padding-top: 24rpx;
  padding-bottom: 24rpx;
  padding-bottom: calc(24rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fff;

  .submit-btn {
    height: 96rpx;
    width: 686rpx;
    background-color: colors.$color-primary;
    color: white;
    border: none;
    border-radius: 48rpx;
    font-size: 32rpx;
    font-weight: 600;
    /* 使用flex布局确保文字居中 */
    display: flex;
    align-items: center;
    justify-content: center;
    /* 移除line-height，避免与flex冲突 */
    line-height: 1;
    /* 添加过渡动画 */
    transition: all 0.3s ease;
    /* 添加阴影效果，与其他按钮保持一致 */
    // box-shadow: 0 8rpx 24rpx rgba(58, 211, 193, 0.3);

    /* 点击效果 */
    &:active {
      transform: scale(0.98);
      transition: transform 0.1s;
    }

    /* 禁用状态 */
    &.disabled {
      background: colors.$color-divider-1;
      color: colors.$text-regular;
      /* 禁用时移除阴影和点击效果 */
      box-shadow: none;
      opacity: 0.7;
      pointer-events: none;

      &:active {
        transform: none;
      }
    }
  }
}
</style>
