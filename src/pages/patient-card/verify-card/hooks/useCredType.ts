import { ref, computed } from "vue";
import { CRED_TYPE } from "./constants";
import type { CredType } from "./types";
import { applyCardService } from "../service";

export function useCredType() {
  const credType = ref<string[]>([]);
  const credTypeArr = ref<CredType[]>([]);

  // 是否是儿童卡
  const isChildCard = computed(() => {
    const selectedCredType = credType.value[0];
    // 根据React代码逻辑：只有"其他有效证件"(100)才是儿童卡
    // 出生证明(03)不是儿童卡，仍然需要显示认证方式
    return selectedCredType === CRED_TYPE.NO_VALID_CERT;
  });

  // 获取证件类型标签
  const getCredTypeLabel = () => {
    if (credType.value.length === 0) return "";
    const selectedValue = credType.value[0];
    const found = credTypeArr.value.find(
      (item) => item.value === selectedValue
    );
    return found ? found.label : "";
  };

  // 初始化证件类型列表
  const initCredTypeList = async (
    businessType?: string,
    cachedCardInfo?: any
  ) => {
    try {
      const credTypeResult = await applyCardService.getCredTypeList({
        useType: 1,
      });

      if (credTypeResult.code === "1" && credTypeResult.data) {
        // 转换API返回的数据格式为组件需要的格式
        let credTypeList = credTypeResult.data.map((item: any) => ({
          label: item.credTypeName,
          value: item.credTypeCode,
          disabled: false,
        }));

        // 参考React老项目逻辑：小儿卡升级时过滤掉"其他有效证件"
        const isCardUpdate = businessType === "2";
        const isChildCardType =
          isCardUpdate && cachedCardInfo?.cardTypeCode === "YYEZTJZK";

        if (isChildCardType) {
          // 小儿卡升级时过滤掉"其他有效证件"（证件类型代码100）
          credTypeList = credTypeList.filter(
            (item: any) => item.value !== "100"
          );
          console.log("小儿卡升级：已过滤掉其他有效证件选项");
        }

        credTypeArr.value = credTypeList;

        // 默认选择第一个证件类型
        if (credTypeArr.value.length > 0) {
          credType.value = [credTypeArr.value[0].value];
        }
      }
    } catch (error) {
      console.error("获取证件类型列表失败:", error);
      throw error;
    }
  };

  // 证件类型变化处理
  const onCredTypeChange = (
    values: string[],
    buildCheckTypeList: (selectedCredType: string) => void
  ) => {
    const oldCredType = credType.value[0];
    const newCredType = values[0];

    credType.value = values;

    // 如果证件类型发生变化，需要重置相关字段
    if (oldCredType !== newCredType) {
      // 根据React代码逻辑，选择不同证件类型时需要处理以下逻辑：
      console.log(`证件类型从 ${oldCredType} 变更为 ${newCredType}`);

      // 更新认证方式列表（不重新请求接口，只更新禁用状态）
      buildCheckTypeList(newCredType);

      // 根据不同证件类型的特殊处理
      if (newCredType === CRED_TYPE.ID_CARD) {
        // 身份证：启用自动解析功能
        console.log("选择了身份证，启用自动解析功能");
      } else if (newCredType === CRED_TYPE.NO_VALID_CERT) {
        // 无任何有效证件：特殊的儿童卡逻辑
        console.log("选择了无任何有效证件，启用儿童卡模式");
      } else {
        // 其他证件类型：需要手动输入相关信息
        console.log("选择了其他证件类型，需要手动输入相关信息");
      }
    }
  };

  return {
    credType,
    credTypeArr,
    isChildCard,
    getCredTypeLabel,
    initCredTypeList,
    onCredTypeChange,
  };
}
