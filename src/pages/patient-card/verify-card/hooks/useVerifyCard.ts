import { onLoad } from "@dcloudio/uni-app";
import { ref, computed } from "vue";
import { applyCardService } from "../service";
import { applyCardService as applyCardServiceForCardInfo } from "../../applycard/service";
import { CRED_TYPE, REALNAME_TYPE } from "./constants";
import { useCredType } from "./useCredType";
import { useCheckType } from "./useCheckType";
import { useFormValidation } from "./useFormValidation";
import { ApplyCardStorageKeys } from "../../shared/constants";
import { getOrganCode, getChannelCode } from "@/utils/platform";
import { uploadImage } from "@/utils/upload";
import { FaceVerificationUtils } from "@/pages/shared/face-recognition/utils";
import type { OrganConfig } from "./types";

// 工具函数
const showToast = (title: string) => {
  uni.showToast({
    title,
    icon: "none",
  });
};

export function useVerifyCard() {
  // 基础状态
  const loading = ref(false);
  const patientName = ref("");
  const credNo = ref("");
  const hasRead = ref(false);
  const cardImg = ref(""); // 用于显示的图片路径（可能是本地路径或OSS链接）
  const cardImgOssUrl = ref(""); // OSS链接，用于提交数据
  const organConfig = ref<OrganConfig | null>(null);
  const ocrCardInfo = ref<any>({});

  // 页面参数
  const businessType = ref("");
  const cardId = ref("");

  // 缓存的卡片信息（用于升级业务）
  const cachedCardInfo = ref<any>({});

  // 使用模块化的证件类型管理
  const {
    credType,
    credTypeArr,
    isChildCard,
    getCredTypeLabel,
    initCredTypeList,
    onCredTypeChange: handleCredTypeChange,
  } = useCredType();

  // 使用模块化的认证方式管理
  const {
    checktypeList,
    checktypeValue,
    showUploadCard,
    isFaceCheckType,
    updateCheckTypeList,
    buildCheckTypeList,
    selectCheckType,
  } = useCheckType(credType);

  // 使用表单验证
  const { validateForm } = useFormValidation();

  // 计算属性
  const isNameDisabled = computed(() => {
    // 如果证件类型是100，允许输入姓名
    if (credType.value[0] === "100") {
      return false;
    }

    // 判断是否为升级卡片业务
    const isCardUpdate = businessType.value === "2"; // UPDATE_CARD

    // 参考React老项目逻辑：
    // disabled={checktypeValue === CHECK_TYPE.MANUAL_CHECK ? isOcrDisabled || isCardUpdate : isCardUpdate}
    if (checktypeValue.value === "MANUAL_CHECK") {
      // 上传证件方式：OCR禁用或卡片升级时禁用姓名
      return useUploadOcr.value || isCardUpdate;
    } else {
      // 人脸识别方式：卡片升级时禁用姓名
      return isCardUpdate;
    }
  });

  const isCredNoDisabled = computed(() => {
    // 1. 无任何有效证件时禁用
    if (credType.value[0] === CRED_TYPE.NO_VALID_CERT) {
      return true;
    }

    // 判断是否为升级卡片业务和小儿卡升级
    const isCardUpdate = businessType.value === "2"; // UPDATE_CARD
    const isChildUpdate =
      isCardUpdate && cachedCardInfo.value?.cardTypeCode === "YYEZTJZK";

    // 参考React老项目逻辑
    if (checktypeValue.value === "MANUAL_CHECK") {
      // 上传证件方式
      if (isCardUpdate) {
        if (isChildUpdate) {
          // 小儿卡升级：根据OCR状态决定
          return useUploadOcr.value;
        } else {
          // 非小儿卡升级：禁用
          return true;
        }
      } else {
        // 非升级业务：根据OCR状态决定
        return useUploadOcr.value;
      }
    } else {
      // 人脸识别方式：升级且非小儿卡时禁用
      return isCardUpdate && !isChildUpdate;
    }
  });

  // 证件号码输入框旁边的OCR图标显示逻辑（仅人脸识别时显示）
  const useOcr = computed(() => {
    const credOcrList = organConfig.value?.credOcrList || [];
    const currentCredType = credType.value[0];
    const organConfigEnabled = credOcrList.includes(currentCredType);

    // 只有选择人脸识别时，证件号码输入框旁边才显示OCR图标
    const isFaceCheck = isFaceCheckType.value;

    console.log("=== 证件号码OCR显示条件检查 ===");
    console.log("credOcrList:", credOcrList);
    console.log("当前证件类型:", currentCredType);
    console.log("organConfigEnabled:", organConfigEnabled);
    console.log("认证方式:", checktypeValue.value);
    console.log("isFaceCheck:", isFaceCheck);
    console.log("最终显示结果:", organConfigEnabled && isFaceCheck);
    console.log("================================");

    return organConfigEnabled && isFaceCheck;
  });

  // 上传证件区域的OCR功能显示逻辑（仅上传证件时可用）
  const useUploadOcr = computed(() => {
    const credOcrList = organConfig.value?.credOcrList || [];
    const currentCredType = credType.value[0];
    const organConfigEnabled = credOcrList.includes(currentCredType);

    // 只有选择上传证件时，上传区域才支持OCR
    const isManualCheck = checktypeValue.value === REALNAME_TYPE.MANUAL_CHECK;

    console.log("=== 上传证件OCR显示条件检查 ===");
    console.log("organConfigEnabled:", organConfigEnabled);
    console.log("isManualCheck:", isManualCheck);
    console.log("最终显示结果:", organConfigEnabled && isManualCheck);
    console.log("================================");

    return organConfigEnabled && isManualCheck;
  });

  const canSubmit = computed(() => {
    const selectedCredType = credType.value[0];

    // 参考React老项目的校验逻辑
    if (selectedCredType === "100" && !patientName.value.trim()) {
      return false; // 其他有效证件且无姓名时不能提交
    }

    // 基础条件：姓名、证件类型、已阅读协议
    let baseConditions =
      patientName.value.trim() !== "" &&
      credType.value.length > 0 &&
      hasRead.value;

    // 如果有认证方式列表，则需要校验认证方式是否已选择
    if (checktypeList.value.length > 0) {
      baseConditions = baseConditions && checktypeValue.value !== "";
    }

    // 无任何有效证件时不需要证件号码和证件图片
    if (selectedCredType === CRED_TYPE.NO_VALID_CERT) {
      return baseConditions;
    }

    // 其他证件类型的校验逻辑（参考React老项目）
    if (!["100"].includes(selectedCredType)) {
      if (checktypeValue.value === "MANUAL_CHECK") {
        // 上传证件方式：必须有姓名、证件号码、证件图片
        return (
          baseConditions &&
          credNo.value.trim() !== "" &&
          cardImg.value.trim() !== ""
        );
      } else {
        // 人脸识别方式：必须有姓名、证件号码
        return baseConditions && credNo.value.trim() !== "";
      }
    }

    // 其他有效证件（100）：只需要基础条件
    return baseConditions;
  });

  // 初始化页面数据
  const initPageData = async () => {
    try {
      loading.value = true;

      // 初始化证件类型列表（传递业务类型和缓存信息用于过滤）
      await initCredTypeList(businessType.value, cachedCardInfo.value);

      // 更新认证方式列表
      await updateCheckTypeList();

      // 获取机构配置
      const organResult: any = await applyCardService.getOrganConfig({
        channelCode: getChannelCode(),
        organCode: getOrganCode(),
      });
      console.log("机构配置获取结果:", organResult);
      console.log(
        "机构配置原始数据结构:",
        JSON.stringify(organResult, null, 2)
      );

      if (organResult.code === "1" && organResult.data) {
        // 处理机构配置数据
        const configData = organResult.data;

        // 处理地址信息 - 如果childAddress是字符串，转换为数组
        if (
          configData.childAddress &&
          typeof configData.childAddress === "string"
        ) {
          configData.childAddressList = configData.childAddress
            .split("\n")
            .filter((line: string) => line.trim());
        }

        organConfig.value = configData;
        console.log("机构配置设置成功:", organConfig.value);
        console.log("useOcr字段值:", configData.useOcr);
        console.log("childNoice字段值:", configData.childNoice);
        console.log("addressNoice字段值:", configData.addressNoice);
        console.log("childAddress字段值:", configData.childAddress);
      } else {
        console.log("机构配置获取失败或数据为空，使用默认配置");
        // 设置默认配置，暂时启用OCR用于测试
        organConfig.value = { useOcr: true };
      }

      // 如果是升级业务，获取原卡片的完整信息（参考React老项目逻辑）
      if (businessType.value === "2" && cardId.value) {
        try {
          console.log("升级业务：开始获取原卡片信息，cardId:", cardId.value);

          // 首先尝试从缓存获取
          const cachedInfo = uni.getStorageSync("cached_card_info");
          if (cachedInfo && cachedInfo.cardId === cardId.value) {
            console.log("从缓存获取卡片信息:", cachedInfo);
            cachedCardInfo.value = cachedInfo;
          } else {
            // 缓存不存在或不匹配，调用接口获取完整的卡片信息
            console.log("缓存不存在，调用接口获取卡片信息");
            const cardInfoResponse =
              await applyCardServiceForCardInfo.getCardInfo(cardId.value);

            if (cardInfoResponse.code === "1" && cardInfoResponse.data) {
              console.log("获取原卡片信息成功:", cardInfoResponse.data);
              cachedCardInfo.value = cardInfoResponse.data;

              // 更新缓存
              uni.setStorageSync("cached_card_info", cardInfoResponse.data);
            } else {
              console.warn("获取原卡片信息失败:", cardInfoResponse.msg);
              // 如果接口失败，尝试使用缓存的信息
              if (cachedInfo) {
                cachedCardInfo.value = cachedInfo;
              }
            }
          }

          // 预填充验证页面的字段
          if (cachedCardInfo.value) {
            const isChildCardType =
              cachedCardInfo.value?.cardTypeCode === "YYEZTJZK";

            // 姓名：始终回填
            if (cachedCardInfo.value.patientName) {
              patientName.value = cachedCardInfo.value.patientName;
            }

            // 证件号码：小儿卡升级时不回填（参考React老项目逻辑）
            if (cachedCardInfo.value.credNo && !isChildCardType) {
              credNo.value = cachedCardInfo.value.credNo;
            }

            // 卡片图片：回填
            if (cachedCardInfo.value.cardImg) {
              cardImg.value = cachedCardInfo.value.cardImg;
            }
          }
        } catch (error) {
          console.error("获取原卡片信息失败:", error);
          // 如果出错，尝试使用缓存的信息
          const cachedInfo = uni.getStorageSync("cached_card_info");
          if (cachedInfo && cachedInfo.cardId === cardId.value) {
            cachedCardInfo.value = cachedInfo;
          }
        }
      }
    } catch (error) {
      console.error("初始化页面数据失败:", error);
      showToast("初始化失败");
    } finally {
      loading.value = false;
    }
  };

  // 证件类型变化处理
  const onCredTypeChange = (values: string[]) => {
    handleCredTypeChange(values, buildCheckTypeList);

    // 参考React老项目逻辑：根据业务类型决定清空哪些字段
    const isCardUpdate = businessType.value === "2";
    const isChildUpdate =
      isCardUpdate && cachedCardInfo.value?.cardTypeCode === "YYEZTJZK";

    // 清空图片和OCR相关数据
    cardImg.value = "";
    cardImgOssUrl.value = "";
    ocrCardInfo.value = {};

    if (isCardUpdate) {
      if (isChildUpdate) {
        // 小儿卡升级：只清空证件号码，不清空姓名（参考React逻辑）
        credNo.value = "";
        console.log("小儿卡升级 - 证件类型变化，只清空证件号码");
      } else {
        // 非小儿卡升级：根据实际需要处理
        credNo.value = "";
        console.log("非小儿卡升级 - 证件类型变化，清空证件号码");
      }
    } else {
      // 非升级业务：清空姓名和证件号码（参考React逻辑）
      credNo.value = "";
      patientName.value = "";
      console.log("非升级业务 - 证件类型变化，清空姓名和证件号码");
    }
  };

  // 获取注意事项内容
  const getChildNotice = () => {
    return organConfig.value?.childNoice || "请仔细阅读注意事项";
  };

  // 获取地址行数组
  const getAddressLines = () => {
    // 优先使用 addressNoice 字段
    if (organConfig.value?.addressNoice) {
      return organConfig.value.addressNoice
        .split("\n")
        .filter((line) => line.trim());
    }

    // 兼容旧的 childAddressList 字段
    if (
      organConfig.value?.childAddressList &&
      organConfig.value.childAddressList.length > 0
    ) {
      return organConfig.value.childAddressList;
    }

    // 兼容旧的 childAddress 字段
    if (organConfig.value?.childAddress) {
      return organConfig.value.childAddress
        .split("\n")
        .filter((line) => line.trim());
    }

    // 默认返回空数组
    return [];
  };

  // 上传证件图片（支持OCR识别）
  const uploadCardImage = async () => {
    uni.chooseImage({
      count: 1,
      sizeType: ["compressed"],
      sourceType: ["camera", "album"],
      success: async (res) => {
        try {
          const tempFilePath = res.tempFilePaths[0];
          // 先显示本地图片，提升用户体验
          cardImg.value = tempFilePath;

          // 显示上传进度
          uni.showLoading({ title: "上传中..." });

          try {
            // 无论是否需要OCR，都先上传图片到OSS获取链接
            const ossUrl = await uploadImage(tempFilePath, {
              prefix: "card",
              businessId: "hxgy-hyt-certificate",
            });

            console.log("证件图片上传到OSS成功:", ossUrl);

            // 保存OSS链接，用于后续提交
            cardImgOssUrl.value = ossUrl;
            cardImg.value = ossUrl;

            // 如果支持OCR且选择的是上传证件认证方式，进行OCR识别
            if (useUploadOcr.value) {
              uni.showLoading({ title: "识别中..." });

              try {
                // 调用OCR识别接口
                const ocrResult = await applyCardService.ocrRecognition({
                  businessCode: "card-register",
                  ocrType: "HX_HW_OCR",
                  channelCode: getChannelCode(),
                  url: ossUrl,
                  organCode: getOrganCode(),
                  credTypeCode: credType.value[0],
                });

                if (ocrResult.code === "1" && ocrResult.data) {
                  const { name, number } = ocrResult.data;

                  // 参考React老项目逻辑：根据业务类型决定填充哪些字段
                  const isCardUpdate = businessType.value === "2";
                  const isChildUpdate =
                    isCardUpdate &&
                    cachedCardInfo.value?.cardTypeCode === "YYEZTJZK";

                  if (isCardUpdate) {
                    if (isChildUpdate) {
                      // 小儿卡升级：只设置证件号码，不设置姓名
                      if (number) {
                        credNo.value = number;
                      }
                    }
                    // 非小儿卡升级：根据React项目，这种情况下不应该进入OCR流程
                  } else {
                    // 非升级业务：设置姓名和证件号码
                    if (name) patientName.value = name;
                    if (number) credNo.value = number;
                  }

                  // 保存OCR识别的信息
                  ocrCardInfo.value = ocrResult.data;

                  showToast("识别成功");
                } else {
                  showToast("识别失败，请手动输入");
                }
              } catch (ocrError) {
                console.error("OCR识别失败:", ocrError);
                showToast("识别失败，请手动输入");
              }
            } else {
              // 不需要OCR识别，直接提示上传成功
              showToast("上传成功");
            }
          } catch (uploadError) {
            console.error("上传到OSS失败:", uploadError);
            showToast("上传失败，请重试");
            // 上传失败时，恢复为本地路径用于显示
            cardImg.value = tempFilePath;
          } finally {
            uni.hideLoading();
          }
        } catch (error) {
          console.error("上传失败:", error);
          showToast("上传失败");
        }
      },
      fail: () => {
        showToast("选择图片失败");
      },
    });
  };

  // 删除证件图片
  const deleteCardImage = () => {
    uni.showModal({
      title: "确认删除",
      content: "确定要删除已上传的证件图片吗？",
      confirmText: "删除",
      cancelText: "取消",
      success: (res) => {
        if (res.confirm) {
          // 用户确认删除
          cardImg.value = "";
          cardImgOssUrl.value = "";

          // 删除图片时，清空OCR识别的数据（重要的用户体验逻辑）
          // 因为图片被删除了，之前基于该图片的OCR识别结果就不再有效
          if (ocrCardInfo.value) {
            // 清空OCR识别信息
            ocrCardInfo.value = null;

            // 根据业务类型决定清空哪些字段（参考React老项目逻辑）
            const isCardUpdate = businessType.value === "2";
            const isChildUpdate =
              isCardUpdate && cachedCardInfo.value?.cardTypeCode === "YYEZTJZK";

            if (isCardUpdate) {
              if (isChildUpdate) {
                // 小儿卡升级：只清空证件号码，保留姓名
                credNo.value = "";
              } else {
                // 非小儿卡升级：清空证件号码，姓名通常已被禁用
                credNo.value = "";
              }
            } else {
              // 非升级业务：清空通过OCR填充的姓名和证件号码
              patientName.value = "";
              credNo.value = "";
            }

            console.log("删除证件图片，已清空OCR识别数据");
            showToast("已删除图片，请重新输入信息");
          } else {
            showToast("已删除图片");
          }
        }
        // 用户取消删除，不执行任何操作
      },
      fail: () => {
        // 弹窗显示失败，不执行删除操作
        console.log("删除确认弹窗显示失败");
      },
    });
  };

  // OCR扫描处理（按React项目逻辑：先上传OSS，再调用OCR接口）
  const handleOcrScan = () => {
    console.log("开始OCR扫描");

    uni.chooseImage({
      count: 1,
      sizeType: ["compressed"],
      sourceType: ["camera", "album"],
      success: async (res) => {
        try {
          uni.showLoading({ title: "上传中..." });

          const tempFilePath = res.tempFilePaths[0];
          console.log("选择的图片路径:", tempFilePath);

          // 第一步：上传图片到OSS获取URL（使用通用OSS上传工具）
          const ossUrl = await uploadImage(tempFilePath, {
            prefix: "ocr",
            businessId: "hxgy-hyt-certificate",
          });
          console.log("OSS上传成功，URL:", ossUrl);

          uni.showLoading({ title: "识别中..." });

          // 第二步：使用OSS URL调用OCR识别接口（React项目真实接口）
          const ocrResult = await applyCardService.ocrRecognition({
            businessCode: "card-register",
            ocrType: "HX_HW_OCR",
            channelCode: getChannelCode(),
            url: ossUrl,
            organCode: getOrganCode(),
            credTypeCode: credType.value[0],
          });

          console.log("OCR识别结果:", ocrResult);

          if (ocrResult.code === "1" && ocrResult.data) {
            const { name, number } = ocrResult.data;

            // 参考React老项目逻辑：根据业务类型决定填充哪些字段
            const isCardUpdate = businessType.value === "2";
            const isChildUpdate =
              isCardUpdate && cachedCardInfo.value?.cardTypeCode === "YYEZTJZK";

            if (isCardUpdate) {
              if (isChildUpdate) {
                // 小儿卡升级：只设置证件号码，不设置姓名
                if (number) {
                  credNo.value = number;
                }
              }
              // 非小儿卡升级：根据React项目，这种情况下不应该进入OCR流程
            } else {
              // 非升级业务：设置姓名和证件号码
              if (name) {
                patientName.value = name;
              }
              if (number) {
                credNo.value = number;
              }
            }

            // 保存OCR识别的信息
            ocrCardInfo.value = ocrResult.data;

            showToast("识别成功");
          } else {
            showToast((ocrResult as any).message || "识别失败");
          }
        } catch (error) {
          console.error("OCR识别失败:", error);
          showToast("识别失败，请重试");
        } finally {
          uni.hideLoading();
        }
      },
      fail: () => {
        showToast("选择图片失败");
      },
    });
  };

  // 切换阅读状态
  const toggleRead = () => {
    hasRead.value = !hasRead.value;
  };

  // 处理人脸验证
  const handleFaceVerification = async () => {
    try {
      console.log("开始人脸验证");

      // 调用人脸验证工具类
      const result = await FaceVerificationUtils.navigateToFaceVerification({
        businessCode: "hxgy-card-manager",
        credNo: credNo.value,
        name: patientName.value,
        type: checktypeValue.value,
        maxAttempts: 3, // 设置最大尝试次数为3次
      });

      console.log("人脸验证结果:", result);
      if (result.success) {
        // 人脸验证成功，跳转到申领页面
        console.log("人脸验证成功，获取到后台数据:", result.backendData);

        const verifyCardInfo = {
          patientName: patientName.value,
          credType: credType.value,
          credNo: credNo.value,
          credTypeCode: credType.value[0],
          credTypeName: credTypeArr.value.find(
            (i) => i.value === credType.value[0]
          )?.label,
          cardImg: cardImg.value,
          checktypeValue: checktypeValue.value,
          faceVerificationResult: result, // 包含完整的人脸验证结果
          // 可以单独提取后台验证数据用于后续业务
          faceVerificationData: result.backendData,
          ...ocrCardInfo.value,
        };

        console.log("准备跳转到申领页面，携带数据:", {
          faceId: result.backendData?.faceId,
          token: result.backendData?.token,
          agreementNo: result.backendData?.agreementNo,
        });

        // 存储数据到本地
        uni.setStorageSync(
          ApplyCardStorageKeys.VERIFY_TO_APPLY_DATA,
          verifyCardInfo
        );

        // 升级业务时，需要将原卡片信息合并到registerCardInfo中（参考React老项目逻辑）
        let registerCardInfo = verifyCardInfo;
        if (businessType.value === "2" && cachedCardInfo.value) {
          registerCardInfo = {
            // 先使用原卡片的完整信息作为基础
            ...cachedCardInfo.value,
            // 再覆盖验证页面修改的字段
            patientName: patientName.value,
            credType: credType.value,
            credNo: credNo.value,
            credTypeCode: credType.value[0],
            credTypeName: credTypeArr.value.find(
              (i) => i.value === credType.value[0]
            )?.label,
            cardImg: cardImg.value,
            checktypeValue: checktypeValue.value,
            // 确保保留原卡片的其他重要信息
            tel: cachedCardInfo.value.tel || cachedCardInfo.value.phone,
            phone: cachedCardInfo.value.tel || cachedCardInfo.value.phone,
            detailAddress: cachedCardInfo.value.detailAddress,
            // 地址信息
            provinceCode: cachedCardInfo.value.provinceCode,
            provinceName: cachedCardInfo.value.provinceName,
            cityCode: cachedCardInfo.value.cityCode,
            cityName: cachedCardInfo.value.cityName,
            cityAreaCode: cachedCardInfo.value.cityAreaCode,
            cityAreaName: cachedCardInfo.value.cityAreaName,
            streetCode: cachedCardInfo.value.streetCode,
            streetName: cachedCardInfo.value.streetName,
            // 监护人信息
            guarderName: cachedCardInfo.value.guarderName,
            guarderCredTypeCode: cachedCardInfo.value.guarderCredTypeCode,
            guarderCredNo: cachedCardInfo.value.guarderCredNo,
            guarderRelation: cachedCardInfo.value.guarderRelation,
            // 患者关系信息
            patientRelation:
              cachedCardInfo.value.patientRelation ||
              cachedCardInfo.value.relation,
          };
          console.log(
            "升级业务：合并原卡片信息到registerCardInfo",
            registerCardInfo
          );
        }

        uni.setStorageSync(
          ApplyCardStorageKeys.REGISTER_CARD_INFO,
          registerCardInfo
        );

        // 跳转到申领页面，携带人脸验证结果
        console.log("跳转到申领页面");

        // 根据业务类型决定跳转参数
        const urlParams = [
          `fromVerify=1`,
          `faceVerified=1`,
          `timestamp=${Date.now()}`,
        ];
        if (businessType.value === "2" && cardId.value) {
          // 升级卡片业务：添加checkCard参数（参考React老项目）
          urlParams.push(`businessType=${businessType.value}`);
          urlParams.push(`cardId=${cardId.value}`);
          urlParams.push(`checkCard=1`);
        }

        uni.redirectTo({
          url: `/pages/patient-card/applycard/index?${urlParams.join("&")}`,
        });
      } else {
        // 人脸验证失败
        showToast(result.errorMessage || "人脸验证失败，请重试");
      }
    } catch (error: any) {
      console.error("人脸验证异常:", error);
      showToast(error.message || "人脸验证失败，请重试");
    }
  };

  // 验证表单
  const validateFormData = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (!hasRead.value) {
        reject(new Error("请阅读并同意以下内容"));
        return;
      }

      if (!patientName.value) {
        reject(new Error("请填写姓名"));
        return;
      }

      if (!isChildCard.value) {
        if (!credType.value || !credType.value.length) {
          reject(new Error("选择证件类型"));
          return;
        }

        if (
          checktypeValue.value === REALNAME_TYPE.MANUAL_CHECK &&
          !cardImg.value
        ) {
          reject(new Error("请上传证件照"));
          return;
        }

        if (!credNo.value) {
          reject(new Error("请输入证件号码"));
          return;
        }
      }

      resolve();
    });
  };

  // 检查卡片
  const checkCard = (): Promise<any> => {
    return new Promise((resolve, reject) => {
      // 如果是"无任何有效证件"类型，直接返回成功
      if (credType.value[0] === CRED_TYPE.NO_VALID_CERT) {
        resolve({
          checkStatus: "1",
        });
        return;
      }

      // 否则，调用checkCard API
      const params = {
        credNo: credNo.value,
        patientName: patientName.value,
        credTypeCode: credType.value[0],
        businessCode: "register",
        credTypeName: credTypeArr.value.find(
          (i) => i.value === credType.value[0]
        )?.label,
      };

      applyCardService
        .checkCard(params)
        .then((response: any) => {
          if (response.code === "1") {
            resolve(response.data || {});
          } else {
            reject(new Error(response.msg || "验证失败"));
          }
        })
        .catch((error) => {
          reject(error);
        });
    });
  };

  // 提交申请
  const handleSubmit = async () => {
    try {
      // 验证表单
      await validateFormData();

      // 显示加载
      uni.showLoading({
        title: "加载中...",
        mask: true,
      });

      // 清除本地存储
      uni.removeStorageSync(ApplyCardStorageKeys.ALI_CERTIFICATION_COUNT);

      // 验证卡片
      const data = await checkCard();
      const { checkStatus, failMsg = "", failTitle = "" } = data;

      // 关闭加载
      uni.hideLoading();

      // 将数据存入缓存
      const cardInfo = {
        patientName: patientName.value,
        cardImg: cardImg.value,
        credNo: credNo.value,
        credType: credType.value,
        credTypeCode: credType.value[0],
        credTypeName: credTypeArr.value.find(
          (i) => i.value === credType.value[0]
        )?.label,
        realnameType: checktypeValue.value,
        // address: getAddressLines(),
        picFront: cardImg.value,
      };

      uni.setStorageSync(ApplyCardStorageKeys.CACHE_CARD_INFO_KEY, cardInfo);

      // 升级业务时，需要将原卡片信息合并到registerCardInfo中（参考React老项目逻辑）
      let registerCardInfo = cardInfo;
      if (businessType.value === "2" && cachedCardInfo.value) {
        registerCardInfo = {
          // 先使用原卡片的完整信息作为基础
          ...cachedCardInfo.value,
          // 再覆盖验证页面修改的字段
          patientName: patientName.value,
          credType: credType.value,
          credNo: credNo.value,
          credTypeCode: credType.value[0],
          credTypeName: credTypeArr.value.find(
            (i) => i.value === credType.value[0]
          )?.label,
          cardImg: cardImg.value,
          checktypeValue: checktypeValue.value,
          realnameType: checktypeValue.value,
          picFront: cardImg.value,
          // 确保保留原卡片的其他重要信息
          tel: cachedCardInfo.value.tel || cachedCardInfo.value.phone,
          phone: cachedCardInfo.value.tel || cachedCardInfo.value.phone,
          detailAddress: cachedCardInfo.value.detailAddress,
          // 地址信息
          provinceCode: cachedCardInfo.value.provinceCode,
          provinceName: cachedCardInfo.value.provinceName,
          cityCode: cachedCardInfo.value.cityCode,
          cityName: cachedCardInfo.value.cityName,
          cityAreaCode: cachedCardInfo.value.cityAreaCode,
          cityAreaName: cachedCardInfo.value.cityAreaName,
          streetCode: cachedCardInfo.value.streetCode,
          streetName: cachedCardInfo.value.streetName,
          // 监护人信息
          guarderName: cachedCardInfo.value.guarderName,
          guarderCredTypeCode: cachedCardInfo.value.guarderCredTypeCode,
          guarderCredNo: cachedCardInfo.value.guarderCredNo,
          guarderRelation: cachedCardInfo.value.guarderRelation,
        };
        console.log(
          "升级业务：合并原卡片信息到registerCardInfo",
          registerCardInfo
        );
      }

      uni.setStorageSync(
        ApplyCardStorageKeys.REGISTER_CARD_INFO,
        registerCardInfo
      );

      // 根据验证状态处理后续流程
      if (checkStatus === "0") {
        // 验证失败，跳转到结果页面
        uni.setStorageSync(ApplyCardStorageKeys.CARD_CHECK_DESC, {
          failMsg,
          failTitle,
        });
        uni.navigateTo({
          url: `/pages/patient-card/query-result/index?checkStatus=${checkStatus}&checkCard=1`,
        });
      } else {
        // 验证成功，根据认证方式处理后续流程
        if (checktypeList.value.length === 0) {
          // 没有认证方式，直接跳转到申领页面
          const verifyCardInfo = {
            patientName: patientName.value,
            credType: credType.value,
            credNo: credNo.value,
            credTypeCode: credType.value[0],
            credTypeName: credTypeArr.value.find(
              (i) => i.value === credType.value[0]
            )?.label,
            cardImg: cardImg.value,
            checktypeValue: checktypeValue.value,
            ...ocrCardInfo.value,
          };

          // 存储数据到本地
          uni.setStorageSync(
            ApplyCardStorageKeys.VERIFY_TO_APPLY_DATA,
            verifyCardInfo
          );

          // 升级业务时，需要将原卡片信息合并到registerCardInfo中（参考React老项目逻辑）
          let registerCardInfo = verifyCardInfo;
          if (businessType.value === "2" && cachedCardInfo.value) {
            registerCardInfo = {
              // 先使用原卡片的完整信息作为基础
              ...cachedCardInfo.value,
              // 再覆盖验证页面修改的字段
              patientName: patientName.value,
              credType: credType.value,
              credNo: credNo.value,
              credTypeCode: credType.value[0],
              credTypeName: credTypeArr.value.find(
                (i) => i.value === credType.value[0]
              )?.label,
              cardImg: cardImg.value,
              checktypeValue: checktypeValue.value,
              // 确保保留原卡片的其他重要信息
              tel: cachedCardInfo.value.tel || cachedCardInfo.value.phone,
              phone: cachedCardInfo.value.tel || cachedCardInfo.value.phone,
              detailAddress: cachedCardInfo.value.detailAddress,
              // 地址信息
              provinceCode: cachedCardInfo.value.provinceCode,
              provinceName: cachedCardInfo.value.provinceName,
              cityCode: cachedCardInfo.value.cityCode,
              cityName: cachedCardInfo.value.cityName,
              cityAreaCode: cachedCardInfo.value.cityAreaCode,
              cityAreaName: cachedCardInfo.value.cityAreaName,
              streetCode: cachedCardInfo.value.streetCode,
              streetName: cachedCardInfo.value.streetName,
              // 监护人信息
              guarderName: cachedCardInfo.value.guarderName,
              guarderCredTypeCode: cachedCardInfo.value.guarderCredTypeCode,
              guarderCredNo: cachedCardInfo.value.guarderCredNo,
              guarderRelation: cachedCardInfo.value.guarderRelation,
              // 患者关系信息
              patientRelation:
                cachedCardInfo.value.patientRelation ||
                cachedCardInfo.value.relation,
            };
            console.log(
              "升级业务：合并原卡片信息到registerCardInfo",
              registerCardInfo
            );
          }

          uni.setStorageSync(
            ApplyCardStorageKeys.REGISTER_CARD_INFO,
            registerCardInfo
          );

          // 根据业务类型决定跳转参数
          const urlParams = [`fromVerify=1`, `timestamp=${Date.now()}`];
          if (businessType.value === "2" && cardId.value) {
            // 升级卡片业务：添加checkCard参数（参考React老项目）
            urlParams.push(`businessType=${businessType.value}`);
            urlParams.push(`cardId=${cardId.value}`);
            urlParams.push(`checkCard=1`);
          }

          uni.navigateTo({
            url: `/pages/patient-card/applycard/index?${urlParams.join("&")}`,
          });
        } else if (checktypeValue.value === REALNAME_TYPE.MANUAL_CHECK) {
          // 上传证件方式，跳转到申领页面
          const verifyCardInfo = {
            patientName: patientName.value,
            credType: credType.value,
            credNo: credNo.value,
            credTypeCode: credType.value[0],
            credTypeName: credTypeArr.value.find(
              (i) => i.value === credType.value[0]
            )?.label,
            cardImg: cardImg.value,
            checktypeValue: checktypeValue.value,
            ...ocrCardInfo.value,
          };

          // 存储数据到本地
          uni.setStorageSync(
            ApplyCardStorageKeys.VERIFY_TO_APPLY_DATA,
            verifyCardInfo
          );

          // 根据业务类型决定跳转参数
          const urlParams = [`fromVerify=1`, `timestamp=${Date.now()}`];
          if (businessType.value === "2" && cardId.value) {
            // 升级卡片业务：添加checkCard参数（参考React老项目）
            urlParams.push(`businessType=${businessType.value}`);
            urlParams.push(`cardId=${cardId.value}`);
            urlParams.push(`checkCard=1`);
          }

          uni.navigateTo({
            url: `/pages/patient-card/applycard/index?${urlParams.join("&")}`,
          });
          console.log("上传证件方式，跳转到申领页面");
        } else {
          // 人脸认证方式，调用人脸验证处理方法
          await handleFaceVerification();
        }
      }
    } catch (error: any) {
      uni.hideLoading();
      showToast(error.message || "处理失败");
      console.error("申请处理失败:", error);
    }
  };

  // 页面初始化
  onLoad((options: any) => {
    console.log("验证页面参数:", options);

    // 处理页面参数
    if (options.businessType) {
      businessType.value = options.businessType;
      console.log("业务类型:", businessType.value);
    }

    if (options.cardId) {
      cardId.value = options.cardId;
      console.log("卡片ID:", cardId.value);
    }

    initPageData();
  });

  return {
    // 响应式数据
    loading,
    patientName,
    credNo,
    credType,
    credTypeArr,
    checktypeList,
    checktypeValue,
    hasRead,
    cardImg,
    cardImgOssUrl,
    organConfig,
    businessType,
    cardId,
    cachedCardInfo,

    // 计算属性
    isChildCard,
    isNameDisabled,
    isCredNoDisabled,
    useOcr,
    useUploadOcr,
    canSubmit,
    showUploadCard,

    // 常量
    REALNAME_TYPE,
    CRED_TYPE,

    // 方法
    getCredTypeLabel,
    getChildNotice,
    getAddressLines,
    selectCheckType,
    onCredTypeChange,
    uploadCardImage,
    deleteCardImage,
    handleOcrScan,
    toggleRead,
    handleSubmit,
  };
}
