/**
 * 就诊卡相关类型定义
 */

/**
 * 就诊卡列表请求参数
 */
export interface CardListParams {
  /** 指导标识 */
  guidance: string;
  /** 业务代码 */
  businessCode: string;
}

/**
 * 就诊卡信息
 */
export interface PatientCard {
  /** 卡片ID */
  cardId: string;
  /** 卡片类型 */
  cardType: string;
  /** 卡片名称 */
  cardName: string;
  /** 患者姓名 */
  patientName: string;
  /** 卡号（脱敏显示） */
  cardNo: string;
  /** 完整卡号 */
  fullCardNo?: string;
  /** 身份证号（脱敏显示） */
  idCard: string;
  /** 性别 */
  gender: string;
  /** 年龄 */
  age?: number;
  /** 出生日期 */
  birthDate?: string;
  /** 手机号 */
  phone?: string;
  /** 地址 */
  address?: string;
  /** 卡片状态 */
  status: number;
  /** 是否为默认卡 */
  isDefault: boolean;
  /** 是否为本人 */
  isSelf: boolean;
  /** 创建时间 */
  createTime?: string;
  /** 更新时间 */
  updateTime?: string;
  /** 卡片标签 */
  tags?: string[];
  /** 二维码数据 */
  qrCodeData?: string;
  /** 患者登记号 */
  pmi?: string;
  /** 患者登记号（备用字段） */
  pmiNo?: string;
  /** 卡类型编码 */
  cardTypeCode?: string;
}

/**
 * 就诊卡列表响应数据的实际数据部分
 */
export interface CardListData {
  /** 用户卡片列表 */
  userCardList: PatientCard[];
  /** 可绑定卡片数量 */
  canBindCount: number;
}

/**
 * 就诊卡列表响应数据
 */
export interface CardListResponse {
  /** 响应码 */
  code: string;
  /** 响应消息 */
  msg: string;
  /** 卡片列表数据 */
  data: CardListData;
  /** 错误码 */
  errCode: string;
}

export interface IPatientCardTypeItem {
  /** 机构编码 */
  organCode: string;
  /** 绑卡方式编码 */
  cardMethodCode: string;
  /** 绑卡方式描述 */
  cardMethodName: string;
  /** 卡描述 */
  cardDesc: string;
  /** 卡图标 */
  cardIcon: string;
  /** 卡提示 */
  notice: string;
}

/**
 * 通用API响应结构
 */
export interface ApiResponse<T = any> {
  /** 响应码 */
  code: number;
  /** 响应消息 */
  message: string;
  /** 响应数据 */
  data?: T;
  /** 是否成功 */
  success: boolean;
}

/**
 * 卡片操作类型
 */
export enum CardActionType {
  /** 查看详情 */
  VIEW_DETAIL = "view_detail",
  /** 设为默认 */
  SET_DEFAULT = "set_default",
  /** 编辑信息 */
  EDIT_INFO = "edit_info",
  /** 删除卡片 */
  DELETE_CARD = "delete_card",
  /** 生成二维码 */
  GENERATE_QR = "generate_qr",
}

/**
 * 卡片状态枚举
 */
export enum CardStatus {
  /** 正常 */
  NORMAL = "normal",
  /** 冻结 */
  FROZEN = "frozen",
  /** 注销 */
  CANCELLED = "cancelled",
}

/**
 * 卡片类型枚举
 */
export enum CardType {
  /** 电子健康卡 */
  HEALTH_CARD = "health_card",
  /** 就诊卡 */
  MEDICAL_CARD = "medical_card",
  /** 市民卡 */
  CITIZEN_CARD = "citizen_card",
}
