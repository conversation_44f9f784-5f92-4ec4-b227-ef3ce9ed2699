/**
 * 就诊卡服务层
 */
import request from "@/utils/request";
import type {
  CardListParams,
  CardListData,
  IPatientCardTypeItem,
} from "../types";

/**
 * 就诊卡服务
 */
export const PatientCardService = {
  /**
   * 获取就诊卡列表
   * @param params 查询参数
   * @returns 就诊卡列表
   */
  getCardList: (params: CardListParams) => {
    return request.post<CardListData>(
      "/cloud/cardservice/home/<USER>",
      params
    );
  },
  /**
   * 注册就诊卡方式
   */
  getOrganCardList: () => {
    return request.post<IPatientCardTypeItem[]>(
      "/cloud/hosplatcustomer/cardservice/getorgancardlist",
      {}
    );
  },

  /**
   * 绑定就诊卡
   * @param params 绑定参数
   */
  bindCard: (params: any) => {
    return request.post<any>("/cloud/hosplatcustomer/cardservice/bindcard", {
      ...params,
      showOriginData: true,
      skipError: true,
    });
  },

  /**
   * 获取证件类型列表
   * @param params 查询参数
   */
  getCredTypeList: (params: { useType: number }) => {
    return request.post<any[]>(
      "/cloud/hosplatcustomer/cardservice/getCredTypeList",
      params
    );
  },

  /**
   * 获取机构配置
   * @param params 查询参数
   */
  getOrganConfig: (params: { channelCode: string; organCode: string }) => {
    return request.post<any>(
      "/cloud/cardservice/organConfig/getClientOrganConfig",
      params
    );
  },

  /**
   * 查询是否绑过本人卡
   */
  queryMyselfCard: () => {
    return request.post<any>(
      "/cloud/hosplatcustomer/cardservice/querymyselfcard",
      {}
    );
  },

  /**
   * OCR识别证件信息
   * @param params OCR参数
   */
  ocrRecognition: (params: any) => {
    return request.post<any>(
      "/cloud/hosplatcustomer/realname/getOcrInfo",
      params
    );
  },
};
