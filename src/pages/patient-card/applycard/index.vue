<template>
  <hyt-page :navbarTitle="pageTitle">
    <view class="apply-container">
      <!-- 基础信息卡片 -->
      <view class="info-card">
        <view class="card-title">
          <text>基础信息</text>
          <view class="card-tite-tips">
            <image :src="tipsIcon" />
            <text class="warning-text">提交后不能修改</text>
          </view>
        </view>
        <view class="form-container">
          <!-- 姓名 -->
          <view class="form-item">
            <view class="form-label">姓名</view>
            <view class="form-value-readonly">{{ formData.patientName }}</view>
          </view>

          <!-- 证件类型 -->
          <view class="form-item">
            <view class="form-label">证件类型</view>
            <view class="form-value-readonly">{{ formData.credTypeName }}</view>
          </view>

          <!-- 证件号码 -->
          <view class="form-item" v-if="showCredNo">
            <view class="form-label">证件号码</view>
            <view class="form-value-readonly">{{ getCredNoDisplay() }}</view>
          </view>

          <!-- 出生日期 -->
          <view class="form-item">
            <view class="form-label">出生日期</view>

            <picker
              mode="date"
              :value="formData.birthday"
              :start="birthdayDateRange.start"
              :end="birthdayDateRange.end"
              fields="day"
              @change="onBirthdayChange"
              class="form-picker"
              :key="`date-picker-${formData.credType}`"
              :disabled="birthdayDisabled"
            >
              <view class="form-value">
                <text :class="formData.birthday ? 'selected' : 'placeholder'">
                  {{ formData.birthday || "请选择出生日期" }}
                </text>
                <image
                  :src="arrowRightIcon"
                  class="arrow"
                  v-if="!birthdayDisabled"
                />
              </view>
            </picker>
          </view>

          <!-- 民族 -->
          <view class="form-item">
            <view class="form-label">民族</view>
            <picker
              :value="nationPickerValue"
              :range="nationList"
              range-key="label"
              @change="onNationChange"
              class="form-picker"
              :disabled="nationDisabled"
            >
              <view class="form-value">
                <text :class="formData.nation ? 'selected' : 'placeholder'">
                  {{ formData.nation || "汉族" }}
                </text>
                <image
                  :src="arrowRightIcon"
                  class="arrow"
                  v-if="!nationDisabled"
                />
              </view>
            </picker>
          </view>

          <!-- 性别 -->
          <view class="form-item" v-if="showGender">
            <view class="form-label">性别</view>
            <view class="gender-container">
              <view
                class="gender-option"
                :class="{ active: formData.gender === '1' }"
                @click="selectGender('1')"
              >
                <image
                  :src="formData.gender === '1' ? checkedIcon : uncheckIcon"
                  class="gender-icon"
                />
                <text>男</text>
              </view>
              <view
                class="gender-option"
                :class="{ active: formData.gender === '2' }"
                @click="selectGender('2')"
              >
                <image
                  :src="formData.gender === '2' ? checkedIcon : uncheckIcon"
                  class="gender-icon"
                />
                <text>女</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 监护人信息卡片 -->
      <view class="info-card" v-if="showGuardianInfo">
        <view class="card-title">
          监护人信息
          <view class="import-btn-wrapper" @click="importGuardian">
            <image :src="importIcon" class="import-icon" />
            <text class="import-btn">从就诊卡导入</text>
          </view>
        </view>
        <view class="form-container">
          <!-- 监护人姓名 -->
          <view class="form-item">
            <view class="form-label">姓名</view>
            <input
              v-model="formData.guardianName"
              placeholder="请输入监护人真实姓名"
              placeholder-style="color: #b0b3bf"
              class="form-input"
              :disabled="guardianDisabled"
              maxlength="11"
            />
          </view>

          <!-- 监护人证件类型 -->
          <view class="form-item">
            <view class="form-label">证件类型</view>
            <picker
              :value="guardianCredTypePickerValue"
              :range="credTypeArr"
              range-key="label"
              @change="onGuardianCredTypeChange"
              class="form-picker"
              :disabled="guardianDisabled"
            >
              <view class="form-value">
                <text
                  :class="
                    formData.guardianCredType ? 'selected' : 'placeholder'
                  "
                >
                  {{ getGuardianCredTypeLabel() || "居民身份证" }}
                </text>
                <image
                  :src="arrowRightIcon"
                  class="arrow"
                  v-if="!guardianDisabled"
                />
              </view>
            </picker>
          </view>

          <!-- 监护人证件号码 -->
          <view class="form-item">
            <view class="form-label">证件号码</view>
            <input
              v-model="formData.guardianCredNo"
              placeholder="请输入监护人证件号码"
              placeholder-style="color: #b0b3bf"
              class="form-input"
              :disabled="guardianDisabled"
            />
            <image
              :src="scanIcon"
              class="scan-icon"
              @click="scanGuardianCard"
              v-if="showOcrIcon"
            />
          </view>

          <!-- 监护人关系 -->
          <view class="form-item">
            <view class="form-label">监护人关系</view>
            <picker
              :value="relationPickerValue"
              :range="relationList"
              range-key="label"
              @change="onRelationChange"
              class="form-picker"
              :disabled="guardianDisabled"
            >
              <view class="form-value">
                <text
                  :class="
                    formData.guardianRelation ? 'selected' : 'placeholder'
                  "
                >
                  {{
                    getRelationLabel(formData.guardianRelation) ||
                    "请选择监护人关系"
                  }}
                </text>
                <image
                  :src="arrowRightIcon"
                  class="arrow"
                  v-if="!guardianDisabled"
                />
              </view>
            </picker>
          </view>
        </view>
      </view>

      <!-- 其他信息卡片 -->
      <view class="info-card">
        <view class="card-title">其他信息</view>
        <view class="form-container">
          <!-- 患者关系 -->
          <view class="form-item" v-if="showPatientRelation">
            <view class="form-label">关系</view>
            <picker
              :value="patientRelationPickerValue"
              :range="patientRelationList"
              range-key="label"
              @change="onPatientRelationChange"
              class="form-picker"
            >
              <view class="form-value">
                <text
                  :class="formData.patientRelation ? 'selected' : 'placeholder'"
                >
                  {{
                    getPatientRelationLabel(formData.patientRelation) ||
                    "请选择关系"
                  }}
                </text>
                <image :src="arrowRightIcon" class="arrow" />
              </view>
            </picker>
          </view>

          <!-- 联系电话 -->
          <view class="form-item">
            <view class="form-label">联系电话</view>
            <input
              v-model="formData.phone"
              placeholder="请输入联系电话"
              placeholder-style="color: #b0b3bf"
              type="number"
              class="form-input"
            />
          </view>

          <!-- 职业 -->
          <view v-if="showProfession" class="form-item">
            <view class="form-label">职业</view>
            <picker
              :value="professionPickerValue"
              :range="professionList"
              range-key="label"
              @change="onProfessionChange"
              class="form-picker"
              :disabled="professionDisabled"
            >
              <view class="form-value">
                <text :class="formData.profession ? 'selected' : 'placeholder'">
                  {{ getProfessionLabel(formData.profession) || "请选择职业" }}
                </text>
                <image
                  :src="arrowRightIcon"
                  class="arrow"
                  v-if="!professionDisabled"
                />
              </view>
            </picker>
          </view>

          <!-- 所在地区 -->
          <view class="form-item address-item">
            <view class="form-label">所在地区</view>
            <view class="address-select-wrapper">
              <hyt-select
                ref="addressSelect"
                v-model="formData.regionValues"
                :options="regionData"
                title="所在地区"
                placeholder="请选择所在地区"
                :level-config="regionLevelConfig"
                :field-names="{
                  label: 'dicName',
                  value: 'dicCode',
                  children: 'children',
                }"
                @change="onRegionValuesChange"
                class="form-cascade"
              />
            </view>
          </view>

          <!-- 详细地址 -->
          <view class="form-item">
            <view class="form-label">详细地址</view>
            <input
              v-model="formData.address"
              placeholder="小区/街道门牌号"
              placeholder-style="color: #b0b3bf"
              class="form-input"
            />
          </view>

          <!-- 工作单位 -->
          <view class="form-item" v-if="showCompany">
            <view class="form-label">工作单位</view>
            <input
              v-model="formData.company"
              placeholder="请输入工作单位"
              placeholder-style="color: #b0b3bf"
              class="form-input"
            />
          </view>
        </view>
      </view>
    </view>

    <!-- 底部提交按钮 -->
    <view class="submit-container">
      <view
        class="submit-btn"
        :class="{ disabled: !canSubmit }"
        @click="validateAndSubmit"
      >
        {{ submitButtonText }}
      </view>
    </view>
  </hyt-page>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import { useApplyCard } from "./hooks/useApplyCard";

// 导入图片资源
import checkedIcon from "./assets/checked.png";
import uncheckIcon from "./assets/uncheck.png";
import arrowRightIcon from "./assets/arrow-right.png";
import scanIcon from "./assets/scan.png";
import tipsIcon from "./assets/提示***********";
import importIcon from "./assets/import.png";

// 使用页面主 Hook
const {
  // 响应式数据
  formData,
  credTypeArr,
  nationList,
  professionList,
  relationList,
  patientRelationList,
  regionData,
  regionLevelConfig,
  businessType,
  cardId,

  // 计算属性
  canSubmit,
  showGuardianInfo,
  showProfession,
  showCompany,
  showCredNo,
  showGender,
  showPatientRelation,
  guardianDisabled,
  showOcrIcon,
  birthdayDateRange,

  // 方法
  getGuardianCredTypeLabel,
  getPatientRelationLabel,
  onPatientRelationChange,
  selectGender,
  handleBirthdayChange,
  importGuardian,
  scanGuardianCard,
  handleSubmit,
  initPageData,
  onRegionValuesChange,
  validateForm,

  // picker值
  patientRelationPickerValue,
  professionPickerValue,
  nationPickerValue,
  relationPickerValue,
} = useApplyCard();

// 导入业务类型常量
import { BUSINESS_TYPE } from "./hooks/constants";

// 页面标题计算属性
const pageTitle = computed(() => {
  switch (businessType.value) {
    case BUSINESS_TYPE.EDIT_CARD:
    case BUSINESS_TYPE.EDIT_AUDIT_CARD:
      return "修改资料";
    case BUSINESS_TYPE.UPDATE_CARD:
      return "升级电子健康卡";
    default:
      return "申领电子健康卡";
  }
});

// 提交按钮文本计算属性
const submitButtonText = computed(() => {
  switch (businessType.value) {
    case BUSINESS_TYPE.EDIT_CARD:
    case BUSINESS_TYPE.EDIT_AUDIT_CARD:
      return "提交";
    case BUSINESS_TYPE.UPDATE_CARD:
      return "确认升级";
    default:
      return "确认申领";
  }
});

// 字段禁用状态计算属性（参考 react-old 项目逻辑）
const isEditMode = computed(
  () =>
    businessType.value === BUSINESS_TYPE.EDIT_CARD ||
    businessType.value === BUSINESS_TYPE.EDIT_AUDIT_CARD
);

const isUpdateMode = computed(
  () => businessType.value === BUSINESS_TYPE.UPDATE_CARD
);

// 基础信息字段禁用状态
const nameDisabled = computed(() => isEditMode.value);
const credTypeDisabled = computed(() => isEditMode.value || isUpdateMode.value);
const credNoDisabled = computed(() => isEditMode.value || isUpdateMode.value);
const birthdayDisabled = computed(() => isEditMode.value || isUpdateMode.value);

// 其他字段禁用状态
const nationDisabled = computed(() => {
  // 根据实际业务逻辑，民族字段在编辑模式下通常是禁用的
  // 因为民族信息是基础身份信息，一旦确定后不应随意修改

  // 编辑模式和升级模式下，民族字段禁用
  if (isEditMode.value || isUpdateMode.value) {
    return true;
  }

  // 新申请模式下，民族字段可以编辑
  return false;
});

const professionDisabled = computed(() => {
  // 职业字段在编辑模式下通常可以修改
  return false;
});

// 选择器相关状态
const guardianCredTypePickerValue = ref(0);

// 地址选择器引用
const addressSelect = ref(null);

// 页面加载
onLoad((options: any) => {
  console.log("📱 页面加载，接收到的参数:", options);
  // 确保regionValues初始化为空数组
  formData.value.regionValues = [];
  initPageData(options);
});

// 出生日期选择
const onBirthdayChange = (e: any) => {
  handleBirthdayChange(e.detail.value);
};

// 民族选择
const onNationChange = (e: any) => {
  const index = e.detail.value;
  const selectedNation = nationList.value[index];
  if (selectedNation) {
    formData.value.nation = selectedNation.label;
  }
};

// 职业选择
const onProfessionChange = (e: any) => {
  const index = e.detail.value;
  const selectedProfession = professionList.value[index];
  if (selectedProfession) {
    formData.value.profession = selectedProfession.value;
  }
};

// 监护人证件类型选择
const onGuardianCredTypeChange = (e: any) => {
  const index = e.detail.value;
  const selectedType = credTypeArr.value[index];
  if (selectedType) {
    guardianCredTypePickerValue.value = index;
    formData.value.guardianCredType = selectedType.value;
    formData.value.guardianCredTypeName = selectedType.label;
  }
};

// 关系选择
const onRelationChange = (e: any) => {
  const index = e.detail.value;
  const selectedRelation = relationList.value[index];
  if (selectedRelation) {
    formData.value.guardianRelation = selectedRelation.value; // 存储代码而不是标签
  }
};

// 获取关系标签
const getRelationLabel = (relationValue: string) => {
  if (!relationValue) return "";
  const relation = relationList.value.find(
    (item) => item.value === relationValue
  );
  return relation ? relation.label : relationValue;
};

// 获取职业标签
const getProfessionLabel = (professionValue: string) => {
  if (!professionValue) return "";
  const profession = professionList.value.find(
    (item) => item.value === professionValue
  );
  return profession ? profession.label : professionValue;
};

// 获取证件号码显示（参考 react-old 项目逻辑）
const getCredNoDisplay = () => {
  const credNo = formData.value.credNo;
  if (!credNo || credNo.length < 8) return credNo;
  // 显示前4位和后4位，中间用****代替
  return `${credNo.slice(0, 4)}****${credNo.slice(credNo.length - 4)}`;
};

// 验证并提交
const validateAndSubmit = async () => {
  if (!canSubmit.value) return;

  // 调用表单验证
  if (validateForm()) {
    try {
      uni.showLoading({ title: "提交中..." });
      await handleSubmit();
    } catch (error) {
      console.error("提交失败:", error);
      uni.showToast({
        title: "提交失败，请重试",
        icon: "none",
      });
    } finally {
      uni.hideLoading();
    }
  }
};
</script>

<style lang="scss" scoped>
@use "../../../styles/tokens/colors.scss" as colors;
view,
text {
  box-sizing: border-box;
}
.apply-container {
  padding: 24rpx;
  width: 100%;
  flex: 1;
  overflow: auto;
  padding-bottom: calc(24rpx + 128rpx);
  padding-bottom: calc(24rpx + 128rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(24rpx + 128rpx + env(safe-area-inset-bottom));
  box-sizing: border-box;
}

.info-card {
  width: 702rpx;
  background: #ffffff;
  border-radius: 24rpx;
  margin: 0 auto;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-sizing: border-box;

  .card-title {
    font-size: 36rpx;
    font-weight: 600;
    color: #03081a;
    margin-bottom: 24rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .card-tite-tips {
      display: flex;
      align-items: center;
      justify-self: inherit;
      flex: 1;
      margin-left: 24rpx;
      > image {
        width: 28rpx;
        height: 28rpx;
      }
      > text {
        font-size: 28rpx;
        color: #ffab0b;
        margin-left: 8rpx;
      }
    }

    .warning-text {
      font-size: 28rpx;
      font-weight: 400;
    }

    .import-btn-wrapper {
      display: flex;
      align-items: center;
      gap: 8rpx;

      .import-icon {
        width: 32rpx;
        height: 32rpx;
      }

      .import-btn {
        font-size: 28rpx;
        color: colors.$color-primary;
        font-weight: 400;
      }
    }
  }
}

.form-container {
  width: 100%;

  .form-item {
    display: flex;
    align-items: center;
    height: 100rpx;
    border-bottom: 1rpx solid #eee;
    box-sizing: border-box;

    &:last-child {
      border-bottom: none;
    }

    .form-label {
      width: 160rpx;
      font-size: 28rpx;
      color: #989eb4;
      flex-shrink: 0;
    }

    .form-value-readonly {
      flex: 1;
      font-size: 28rpx;
      color: colors.$text-regular;
      padding: 16rpx 0;
    }

    .form-input {
      flex: 1;
      height: 100%;
      font-size: 28rpx;
      color: #03081a;
      background: transparent;
      border-radius: 12rpx;
      border: none;
      box-sizing: border-box;
    }

    .form-picker {
      flex: 1;
    }

    .form-value {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100%;
      border-radius: 12rpx;
      box-sizing: border-box;
      background-color: transparent;
      font-size: 28rpx;
      .selected {
        color: #03081a;
      }

      .placeholder {
        color: #b0b3bf;
      }

      .arrow {
        width: 32rpx;
        height: 32rpx;
        flex-shrink: 0;
      }
    }

    .scan-icon {
      width: 48rpx;
      height: 48rpx;
      margin-left: 16rpx;
      flex-shrink: 0;
    }
  }
}

.address-select-wrapper {
  flex: 1;
}

.form-cascade {
  width: 100%;
}

.gender-container {
  display: flex;
  gap: 48rpx;

  .gender-option {
    display: flex;
    align-items: center;
    gap: 16rpx;
    font-size: 28rpx;
    color: colors.$text-regular;

    &.active {
      color: colors.$color-primary;
    }

    .gender-icon {
      width: 40rpx;
      height: 40rpx;
    }
  }
}

.submit-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 32rpx;
  padding-bottom: calc(32rpx + constant(safe-area-inset-bottom));
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  background: #ffffff;
  border-top: 2rpx solid #f0f0f0;
  z-index: 100;

  .submit-btn {
    height: 96rpx;
    width: 100%;
    background-color: colors.$color-primary;
    color: white;
    border: none;
    border-radius: 48rpx;
    font-size: 32rpx;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
    transition: all 0.3s ease;
    box-shadow: 0 8rpx 24rpx rgba(58, 211, 193, 0.3);

    &:active {
      transform: scale(0.98);
      transition: transform 0.1s;
    }

    &.disabled {
      background: colors.$color-divider-1;
      color: colors.$text-regular;
      box-shadow: none;
      opacity: 0.7;
      pointer-events: none;

      &:active {
        transform: none;
      }
    }
  }
}
</style>
