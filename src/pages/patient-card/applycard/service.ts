/**
 * 申领电子健康卡服务层
 */
import request from "@/utils/request";
import type { SubmitApplicationData, ApiResponse } from "./hooks/types";

/**
 * 申领电子健康卡服务
 */
export const ApplyCardService = {
  /**
   * 提交申请
   * @param params 申请参数
   * @returns 申请结果
   */
  submitApplication: (
    params: SubmitApplicationData
  ): Promise<ApiResponse<any>> => {
    return request.post("/cloud/hosplatcustomer/cardservice/registercard", {
      ...params,
    });
  },

  /**
   * 获取监护人列表
   * @returns 监护人列表
   */
  getGuardianList: (): Promise<ApiResponse<any[]>> => {
    return request.post("/cloud/hosplatcustomer/guardian/list", {});
  },

  /**
   * OCR识别监护人证件
   * @param params OCR参数
   * @returns 识别结果
   */
  ocrGuardianCard: (params: any): Promise<ApiResponse<any>> => {
    return request.post("/cloud/hosplatcustomer/realname/getOcrInfo", {
      ...params,
    });
  },

  /**
   * 验证手机号格式
   */
  validatePhone: (phone: string): boolean => {
    const phoneReg = /^1[3-9]\d{9}$/;
    return phoneReg.test(phone);
  },

  /**
   * 验证身份证号码
   */
  validateIdCard: (idCard: string): boolean => {
    const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
    return reg.test(idCard);
  },

  /**
   * 验证姓名格式
   */
  validateName: (name: string): boolean => {
    if (!name || name.trim().length === 0) return false;
    if (name.length > 20) return false;

    // 中文姓名验证
    const chineseReg = /^[\u4e00-\u9fa5]{2,20}$/;
    // 英文姓名验证
    const englishReg = /^[a-zA-Z\s]{2,20}$/;

    return chineseReg.test(name) || englishReg.test(name);
  },

  /**
   * 根据身份证号码获取出生日期
   */
  getBirthdayFromIdCard: (idCard: string): string => {
    if (!ApplyCardService.validateIdCard(idCard)) {
      return "";
    }

    let birthday = "";
    if (idCard.length === 15) {
      // 15位身份证
      birthday = "19" + idCard.substring(6, 12);
    } else if (idCard.length === 18) {
      // 18位身份证
      birthday = idCard.substring(6, 14);
    }

    // 格式化为 YYYY-MM-DD
    if (birthday.length === 8) {
      return `${birthday.substring(0, 4)}-${birthday.substring(
        4,
        6
      )}-${birthday.substring(6, 8)}`;
    }

    return "";
  },

  /**
   * 根据身份证号码获取性别
   */
  getGenderFromIdCard: (idCard: string): string => {
    if (!ApplyCardService.validateIdCard(idCard)) {
      return "";
    }

    let genderCode = "";
    if (idCard.length === 15) {
      // 15位身份证
      genderCode = idCard.substring(14, 15);
    } else if (idCard.length === 18) {
      // 18位身份证
      genderCode = idCard.substring(16, 17);
    }

    // 奇数为男，偶数为女
    return parseInt(genderCode) % 2 === 1 ? "1" : "2";
  },
  fetchStreetData: async (districtCode: string) => {
    try {
      const response = await request.post(
        "/cloud/resource/dictionary/querySupplyList",
        {
          typeCode: "street",
          parentId: districtCode,
        }
      );
      return (response?.data || []).map((street: any) => ({
        dicCode: street.dicCode,
        dicName: street.dicName,
        children: [],
      }));
    } catch (error) {
      console.error("获取街道数据失败:", error);
      return [];
    }
  },

  /**
   * 获取卡信息（编辑模式）
   * @param cardId 卡ID
   * @returns 卡信息
   */
  getCardInfo: (cardId: string): Promise<ApiResponse<any>> => {
    return request.post("/cloud/hosplatcustomer/cardservice/cardinfo", {
      cardId,
    });
  },

  /**
   * 编辑卡信息
   * @param params 编辑参数
   * @returns 编辑结果
   */
  editCard: (params: SubmitApplicationData): Promise<ApiResponse<any>> => {
    return request.post("/cloud/hosplatcustomer/cardservice/updatecardinfo", {
      ...params,
    });
  },
};

// 导出实例，保持与其他页面的导入一致
export const applyCardService = ApplyCardService;
