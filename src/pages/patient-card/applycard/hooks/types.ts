// 表单数据类型
export interface FormData {
  // 基础信息（从上一页面传入，不可修改）
  patientName: string; // 患者姓名
  credType: string; // 证件类型代码
  credTypeName: string; // 证件类型名称
  credNo: string; // 证件号码

  // 可编辑的基础信息
  birthday: string; // 出生日期
  nation: string; // 民族
  gender: string; // 性别 1-男，2-女
  profession: string; // 职业

  // 监护人信息
  guardianName: string; // 监护人姓名
  guardianCredType: string; // 监护人证件类型
  guardianCredTypeName: string; // 监护人证件类型名称
  guardianCredNo: string; // 监护人证件号码
  guardianRelation: string; // 与监护人关系

  // 其他信息
  phone: string; // 联系电话
  region: string[]; // 所在地区 [省, 市, 区]
  address: string; // 详细地址
  patientRelation: string; // 患者与申请人的关系
  realnameType: string; // 认证方式（从验证页面传递）
  company: string; // 工作单位

  // 图片信息（从验证页面传递）
  cardImg: string; // 证件图片URL（用于显示）
  picFront: string; // 证件图片URL（用于提交）

  // 级联选择器用的地区值 ID
  regionValues: (string | number)[]; // 所在地区对应的值 [省ID, 市ID, 区ID]
}

// 证件类型
export interface CredType {
  label: string; // 显示名称
  value: string; // 证件类型代码
}

// 民族类型
export interface Nation {
  label: string; // 民族名称
  value: string; // 民族代码
}

// 关系类型
export interface Relation {
  label: string; // 关系名称
  value: string; // 关系代码
}

// 提交申请数据（参考 react-old 项目的 IApplyCard 接口）
export interface SubmitApplicationData {
  // 基础信息
  patientName: string;
  credType: string;
  credTypeCode: string;
  credTypeName: string;
  credNo: string;
  birthday: string;
  nationName: string;
  nationCode: string;
  gender: string;
  occupationName: string;
  occupationCode: string;

  // 联系信息
  tel: string;

  // 患者与申请人的关系
  relation: number; // 0=本人，1=其他

  // 认证方式
  realnameType: string; // 认证方式（如 "MANUAL_CHECK"）

  // 升级相关字段
  oldCardId?: string; // 升级时的原卡片ID

  // 机构信息
  organCode: string;
  organName: string;

  // 地址信息（完整的编码和名称）
  provinceCode: string;
  provinceName: string;
  cityCode: string;
  cityName: string;
  cityAreaCode: string;
  cityAreaName: string;
  streetCode: string;
  streetName: string;
  detailAddress: string;

  // 监护人信息（可选）
  guarderName?: string;
  guarderCredTypeCode?: string;
  guarderCredNo?: string;
  guarderRelation?: string;
}

// API响应基础类型
export interface ApiResponse<T = any> {
  code: string; // 业务状态码
  msg: string; // 响应消息
  data: T; // 业务数据
  errCode: string; // 错误码
}

// 验证结果类型
export interface ValidationResult {
  valid: boolean;
  message: string;
}

// 页面参数类型
export interface PageOptions {
  verifyCardInfo?: string; // 从上一页面传入的验证信息（JSON字符串）
}

// 监护人信息类型
export interface GuardianInfo {
  name: string;
  credType: string;
  credNo: string;
  relation: string;
}

// OCR识别结果类型
export interface OcrResult {
  name?: string;
  credNo?: string;
  birthday?: string;
  gender?: string;
  nation?: string;
  address?: string;
}

// 地区数据选项类型
export interface RegionOption {
  label: string;
  value: string | number;
  children?: RegionOption[];
}
