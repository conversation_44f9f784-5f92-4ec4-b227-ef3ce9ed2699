import { ref, computed } from "vue";
// 移除 dayjs 依赖，使用原生 Date 对象

// 日期工具函数
const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const subtractYears = (date: Date, years: number): Date => {
  const newDate = new Date(date);
  newDate.setFullYear(newDate.getFullYear() - years);
  return newDate;
};

const parseDate = (dateString: string): Date => {
  return new Date(dateString);
};

const diffInYears = (date1: Date, date2: Date): number => {
  const diffTime = Math.abs(date1.getTime() - date2.getTime());
  const diffYears = diffTime / (1000 * 60 * 60 * 24 * 365.25);
  return Math.floor(diffYears);
};

const isDateBefore = (date1: Date, date2: Date): boolean => {
  return date1.getTime() < date2.getTime();
};

const isDateAfter = (date1: Date, date2: Date): boolean => {
  return date1.getTime() > date2.getTime();
};

import { applyCardService } from "../service";
import { applyCardService as verifyCardService } from "../../verify-card/service";
import {
  RELATION_LIST,
  PATIENT_RELATION_LIST,
  BUSINESS_TYPE,
} from "./constants";
import { ApplyCardStorageKeys } from "../../shared/constants";
import type { FormData, CredType } from "./types";
import type { LevelConfig } from "../../../../components/hyt-select/types";
import { idCardNoUtil } from "../../../../utils/idCardNoUtil";
import {
  PatientCardListUtils,
  type CardValidationFunction,
} from "../../list/utils";
import { getChannelCode, getOrganCode } from "@/utils/platform";
import { findDictionaryByTypeCode } from "@/services/dictionary";
import { getOrganName } from "@/services/organService";

// 工具函数
const showToast = (title: string) => {
  uni.showToast({
    title,
    icon: "none",
  });
};

// 创建年龄校验函数
const createAgeValidator = (
  minAge: number,
  message: string = `请选择大于${minAge}岁的就诊卡`
): CardValidationFunction => {
  return (card: any) => {
    // 如果没有生日信息，尝试从身份证号获取
    let birthday = card.birthday;
    if (!birthday && card.credTypeCode === "01" && card.credNo) {
      // 从身份证号解析生日
      try {
        if (card.credNo.length === 18) {
          const year = card.credNo.substring(6, 10);
          const month = card.credNo.substring(10, 12);
          const day = card.credNo.substring(12, 14);
          birthday = `${year}-${month}-${day}`;
        } else if (card.credNo.length === 15) {
          const year = "19" + card.credNo.substring(6, 8);
          const month = card.credNo.substring(8, 10);
          const day = card.credNo.substring(10, 12);
          birthday = `${year}-${month}-${day}`;
        }
      } catch (error) {
        console.warn("解析身份证号生日失败:", error);
      }
    }

    if (!birthday) {
      return {
        valid: false,
        message: "无法获取年龄信息，请选择其他卡片",
      };
    }

    // 计算年龄
    const birthDate = new Date(birthday);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (
      monthDiff < 0 ||
      (monthDiff === 0 && today.getDate() < birthDate.getDate())
    ) {
      age--;
    }

    return {
      valid: age >= minAge,
      message: age >= minAge ? undefined : message,
    };
  };
};

export function useApplyCard() {
  // 业务类型状态
  const businessType = ref<string>(BUSINESS_TYPE.REGISTER_CARD);
  const cardId = ref<string>("");

  // 原始卡片信息（用于编辑模式判断）
  const originalCardInfo = ref<any>({});

  // 表单数据
  const formData = ref<FormData>({
    // 基础信息（从上一页面传入，不可修改）
    patientName: "",
    credType: "",
    credTypeName: "",
    credNo: "",

    // 可编辑的基础信息
    birthday: "",
    nation: "汉族",
    gender: "1", // 1-男，2-女
    profession: "", // 职业

    // 监护人信息
    guardianName: "",
    guardianCredType: "01", // 默认身份证
    guardianCredTypeName: "居民身份证",
    guardianCredNo: "",
    guardianRelation: "", // 监护人关系

    // 其他信息
    phone: "",
    region: [] as string[],
    address: "",
    patientRelation: "", // 患者与申请人的关系
    realnameType: "", // 认证方式（从验证页面传递）
    company: "",

    // 图片信息（从验证页面传递）
    cardImg: "", // 证件图片URL（用于显示）
    picFront: "", // 证件图片URL（用于提交）

    // 级联地址选择器使用
    regionValues: [] as (string | number)[],
  });

  // 是否已经绑定监护人卡（对应React版本的ishasBindCard）
  const ishasBindCard = ref(false);

  // 儿童卡状态（对应React版本的childCardStatus）
  const childCardStatus = ref(false);

  // OCR支持的证件类型列表（对应React版本的credOcrList）
  const credOcrList = ref<string[]>([]);

  // 是否显示监护人信息（对应React版本的hasBindCard）
  const showGuardianInfo = computed(() => {
    // 参考React项目逻辑：监护人信息的显示主要由hasBindCard控制
    // 升级业务时，如果原卡片需要监护人（年龄小于14岁），仍然显示监护人信息
    return ishasBindCard.value && childCardStatus.value;
  });

  // 是否显示职业字段（参考React项目逻辑）
  const showProfession = computed(() => {
    // 参考 react-old 项目逻辑：(!isChildCard || businessType === BUSINESS_TYPE.UPDATE_CARD)
    // 小儿卡时，只有在升级模式下才显示职业字段，编辑模式下不显示
    return (
      !isChildCard.value || businessType.value === BUSINESS_TYPE.UPDATE_CARD
    );
  });

  // 是否显示工作单位字段（参考React项目逻辑）
  const showCompany = computed(() => {
    const organCode = getOrganCode();
    // 参考 react-old 项目逻辑：getOrganCode() === 'HID0101' && (!isChildCard || businessType === BUSINESS_TYPE.UPDATE_CARD)
    // 小儿卡时，只有在升级模式下才显示工作单位字段，编辑模式下不显示
    return (
      organCode === "HID0101" &&
      (!isChildCard.value || businessType.value === BUSINESS_TYPE.UPDATE_CARD)
    );
  });

  // 是否显示患者关系字段（参考React项目逻辑）
  const showPatientRelation = computed(() => {
    // 升级业务时，始终显示患者关系字段（参考React项目逻辑）
    if (businessType.value === BUSINESS_TYPE.UPDATE_CARD) {
      return true;
    }

    // 编辑模式时，显示患者关系字段
    if (
      businessType.value === BUSINESS_TYPE.EDIT_CARD ||
      businessType.value === BUSINESS_TYPE.EDIT_AUDIT_CARD
    ) {
      return true;
    }

    // 参考 react-old 项目逻辑：非儿童卡时显示患者关系字段
    // 儿童卡有监护人关系，不需要患者关系
    return !isChildCard.value;
  });

  // 是否显示证件号码字段（参考React老项目逻辑）
  const showCredNo = computed(() => {
    // 升级模式下始终显示证件号码（参考 react-old 项目逻辑）
    if (businessType.value === BUSINESS_TYPE.UPDATE_CARD) {
      return true;
    }

    // 编辑模式下显示证件号码
    if (businessType.value === BUSINESS_TYPE.EDIT_CARD) {
      return true;
    }

    // 其他模式下，如果证件类型不是"无任何有效证件"，则显示证件号码
    return formData.value.credType !== "100";
  });

  // 是否显示性别字段（参考React老项目逻辑）
  const showGender = computed(() => {
    // 1. 证件类型必须不是身份证
    const isNotIdCard =
      !formData.value.credType || formData.value.credType !== "01";

    if (!isNotIdCard) {
      return false;
    }

    // 2. 根据业务类型判断
    if (businessType.value === BUSINESS_TYPE.EDIT_CARD) {
      // 编辑模式：只有当原始性别信息为空时才显示性别字段
      // 参考React逻辑：!![null, ''].includes(initCardInfo?.gender)
      const originalGender = originalCardInfo.value?.gender;
      return (
        originalGender === null ||
        originalGender === "" ||
        originalGender === undefined
      );
    } else {
      // 非编辑模式（申领、升级等）：始终显示性别字段
      return true;
    }
  });

  // 监护人信息是否禁用（对应React版本的canEditJhr）
  // React版本中：canEditJhr = !hasBindCard，当hasBindCard为true时，canEditJhr为false（禁用）
  // 所以这里：当显示监护人信息时，监护人信息应该被禁用
  const guardianDisabled = computed(() => {
    return !showGuardianInfo.value;
  });

  // OCR图标是否显示（对应React版本的useOcr逻辑）
  const showOcrIcon = computed(() => {
    // 只有在监护人信息未禁用且当前证件类型支持OCR时才显示
    return (
      !guardianDisabled.value &&
      credOcrList.value.includes(formData.value.guardianCredType)
    );
  });

  // 出生日期选择范围限制（基于证件类型）
  const birthdayDateRange = computed(() => {
    const today = formatDate(new Date());
    const credType = formData.value.credType;

    // 根据证件类型设置不同的日期范围限制
    switch (credType) {
      case "01": // 身份证
        // 身份证：支持 0-110 岁的年龄范围（参考 react-old 项目）
        return {
          start: formatDate(subtractYears(new Date(), 110)),
          end: today,
        };

      case "100": // 出生证明/无任何有效证件
        // 出生证明：通常用于新生儿，限制为 0-3 岁
        return {
          start: formatDate(subtractYears(new Date(), 3)),
          end: today,
        };

      case "02": // 军官证
      case "03": // 护照
      case "04": // 港澳通行证
      case "05": // 台湾通行证
      case "99": // 其他
      case "": // 未选择证件类型时的默认范围
      default:
        // 其他证件类型或未选择时：支持 0-110 岁的年龄范围
        return {
          start: formatDate(subtractYears(new Date(), 110)),
          end: today,
        };
    }
  });

  // 省市区数据 - 使用API数据
  const regionData = ref<any[]>([]);

  // 地区选择器级别配置
  const regionLevelConfig = ref<LevelConfig[]>([
    { label: "请选择省份" },
    { label: "请选择城市" },
    { label: "请选择区县" },
    {
      label: "请选择街道",
      loadData: async (districtCode: string | number) => {
        return await loadStreetData(districtCode);
      },
    },
  ]);

  // 获取省市区数据
  const getRegionData = async () => {
    try {
      console.log("🌍 开始获取省市区数据...");
      console.time("1111");
      const data = await findDictionaryByTypeCode("province");
      if (data && data.length > 0) {
        regionData.value = data;
        console.log("✅ 字典服务获取的省市区数据:", data);
      } else {
        // 如果字典服务没返回数据，使用备用数据
        console.warn("⚠️ 字典服务未返回数据，使用备用数据");
      }
      console.timeEnd("1111");
    } catch (error) {
      console.error("❌ 获取省市区数据失败，使用备用数据", error);
    }
  };

  // 获取民族数据
  const getNationData = async () => {
    try {
      console.log("🏛️ 开始获取民族数据...");
      const data = await findDictionaryByTypeCode("ethnic");
      if (data && data.length > 0) {
        // 转换数据格式以匹配组件需要的格式
        nationList.value = data.map((item: any) => ({
          label: item.dicName,
          value: item.dicCode,
        }));
        console.log("✅ 字典服务获取的民族数据:", nationList.value);
      } else {
        console.warn("⚠️ 字典服务未返回民族数据");
      }
    } catch (error) {
      console.error("❌ 获取民族数据失败", error);
    }
  };

  // 获取职业数据
  const getProfessionData = async () => {
    try {
      console.log("💼 开始获取职业数据...");
      const data = await findDictionaryByTypeCode("profession");
      if (data && data.length > 0) {
        // 转换数据格式以匹配组件需要的格式
        professionList.value = data.map((item: any) => ({
          label: item.dicName,
          value: item.dicCode,
        }));
        console.log("✅ 字典服务获取的职业数据:", professionList.value);
      } else {
        console.warn("⚠️ 字典服务未返回职业数据");
      }
    } catch (error) {
      console.error("❌ 获取职业数据失败", error);
    }
  };

  // 获取证件类型列表
  const getCredTypeList = async () => {
    try {
      console.log("📄 开始获取证件类型列表...");
      const credTypeResult = await verifyCardService.getCredTypeList({
        useType: 1, // 注册类型
      });

      if (credTypeResult.code === "1" && credTypeResult.data) {
        // 转换API返回的数据格式为组件需要的格式
        credTypeArr.value = credTypeResult.data.map((item: any) => ({
          label: item.credTypeName,
          value: item.credTypeCode,
          disabled: false,
        }));
        console.log("✅ 获取证件类型列表成功:", credTypeArr.value);
      } else {
        console.warn("⚠️ 获取证件类型列表失败:", credTypeResult.msg);
      }
    } catch (error) {
      console.error("❌ 获取证件类型列表异常:", error);
    }
  };

  // 加载街道数据
  const loadStreetData = async (districtCode: string | number) => {
    try {
      console.log("🏘️ 开始加载街道数据:", districtCode);
      console.log("🏘️ 参数类型:", typeof districtCode);

      const data = await applyCardService.fetchStreetData(
        districtCode.toString()
      );
      console.log("✅ 街道数据加载完成，数量:", data.length);
      console.log("✅ 街道数据内容:", data);

      return data || [];
    } catch (error) {
      console.error("❌ 加载街道数据失败:", error);
      console.error("❌ 错误详情:", error);
      return [];
    }
  };

  // 证件类型列表 - 从接口获取
  const credTypeArr = ref<CredType[]>([]);

  // 民族列表 - 从字典服务获取
  const nationList = ref<any[]>([]);

  // 职业列表 - 从字典服务获取
  const professionList = ref<any[]>([]);

  // 关系列表
  const relationList = ref(RELATION_LIST);

  // 患者关系列表
  const patientRelationList = ref(PATIENT_RELATION_LIST);

  // 关系选择器的值
  const relationPickerValue = computed(() => {
    return relationList.value.findIndex(
      (item) => item.value === formData.value.guardianRelation
    );
  });

  // 患者关系选择器的值
  const patientRelationPickerValue = computed(() => {
    return patientRelationList.value.findIndex(
      (item) => item.value === formData.value.patientRelation
    );
  });

  // 职业选择器的值
  const professionPickerValue = computed(() => {
    return professionList.value.findIndex(
      (item) => item.value === formData.value.profession
    );
  });

  // 民族选择器的值
  const nationPickerValue = computed(() => {
    return nationList.value.findIndex(
      (item) => item.label === formData.value.nation
    );
  });

  // 获取关系标签
  const getRelationLabel = (value: string) => {
    const relation = relationList.value.find((item) => item.value === value);
    return relation?.label || "";
  };

  // 获取患者关系标签
  const getPatientRelationLabel = (value: string) => {
    const relation = patientRelationList.value.find(
      (item) => item.value === value
    );
    return relation?.label || "";
  };

  // 获取患者关系代码（参考 react-old 项目逻辑）
  const getPatientRelationCode = (relationValue: string): number => {
    // 根据患者关系值转换为数字代码
    // 0=本人，1=其他
    if (relationValue === "0" || relationValue === "本人") {
      return 0;
    }
    // 如果没有选择关系或选择了其他关系，默认为"其他"
    return 1;
  };

  // 关系选择变化
  const onRelationChange = (e: any) => {
    const index = e.detail.value;
    const selectedRelation = relationList.value[index];
    if (selectedRelation) {
      formData.value.guardianRelation = selectedRelation.value;
    }
  };

  // 患者关系选择变化
  const onPatientRelationChange = (e: any) => {
    const index = e.detail.value;
    const selectedRelation = patientRelationList.value[index];
    if (selectedRelation) {
      formData.value.patientRelation = selectedRelation.value;
    }
  };

  // 计算属性
  const canSubmit = computed(() => {
    const {
      patientName,
      credNo,
      birthday,
      gender,
      phone,
      address,
      regionValues,
      profession,
    } = formData.value;

    // 调试信息
    console.log("=== canSubmit 检查 ===");
    console.log("基本信息:", {
      patientName: !!patientName,
      credNo: !!credNo,
      birthday: !!birthday,
      gender: !!gender,
      phone: !!phone,
    });
    console.log("地址信息:", {
      regionValues: regionValues?.length || 0,
      address: !!address?.trim(),
    });
    console.log("职业信息:", {
      isChildCard: isChildCard.value,
      profession: !!profession,
      showProfession: !isChildCard.value,
    });
    if (isChildCard.value) {
      const { guardianName, guardianCredNo, guardianRelation } = formData.value;
      console.log("监护人信息:", {
        guardianName: !!guardianName,
        guardianCredNo: !!guardianCredNo,
        guardianRelation: !!guardianRelation,
      });
    }

    // 基本信息必填
    if (!patientName || !birthday || !gender || !phone) {
      console.log("❌ 基本信息不完整");
      return false;
    }

    // 证件号码验证（非儿童卡时必填）
    if (!isChildCard.value && !credNo) {
      console.log("❌ 非儿童卡需要填写证件号码");
      return false;
    }

    // 验证地址选择 - 至少需要选择省市区三级
    if (!regionValues || regionValues.length < 3) {
      console.log("❌ 地址选择不完整，需要至少3级");
      return false;
    }

    // 验证详细地址
    if (!address || !address.trim()) {
      console.log("❌ 详细地址为空");
      return false;
    }

    // 验证职业（非儿童卡时必填）
    if (!isChildCard.value && !profession) {
      console.log("❌ 非儿童卡需要选择职业");
      return false;
    }

    // 如果是儿童卡（需要监护人信息）
    if (isChildCard.value) {
      const { guardianName, guardianCredNo, guardianRelation } = formData.value;
      if (!guardianName || !guardianCredNo || !guardianRelation) {
        console.log("❌ 儿童卡监护人信息不完整");
        return false;
      }
    }

    console.log("✅ 所有验证通过，可以提交");
    return true;
  });

  // 是否为儿童卡（对应React版本的isChildCard）
  // 这个状态用于控制UI显示和验证逻辑，与showGuardianInfo不同
  const isChildCard = ref(false);

  // 获取监护人证件类型标签
  const getGuardianCredTypeLabel = () => {
    const credType = credTypeArr.value.find(
      (item: CredType) => item.value === formData.value.guardianCredType
    );
    return credType?.label || "";
  };

  // 选择性别
  const selectGender = (gender: string) => {
    formData.value.gender = gender;
  };

  // 处理证件号码变化，自动解析性别
  const handleCredNoChange = (credNo: string) => {
    formData.value.credNo = credNo;

    // 如果是身份证，自动解析性别
    if (formData.value.credType === "01" && credNo && credNo.length >= 17) {
      try {
        // 身份证第17位（倒数第2位）表示性别，奇数为男，偶数为女
        const genderDigit = parseInt(credNo.charAt(16));
        formData.value.gender = genderDigit % 2 === 1 ? "1" : "2";
        console.log(
          "从身份证号码自动解析性别:",
          formData.value.gender === "1" ? "男" : "女"
        );
      } catch (error) {
        console.warn("解析身份证性别失败:", error);
      }
    }
  };

  // 处理证件类型变化（对应React版本的getCredType）
  const handleCredTypeChange = (credTypeCode: string) => {
    // 更新证件类型
    formData.value.credType = credTypeCode;
    const credType = credTypeArr.value.find(
      (item) => item.value === credTypeCode
    );
    if (credType) {
      formData.value.credTypeName = credType.label;
    }

    // 检查当前出生日期是否在新证件类型的允许范围内
    if (formData.value.birthday) {
      const currentBirthday = parseDate(formData.value.birthday);
      const dateRange = birthdayDateRange.value;
      const startDate = parseDate(dateRange.start);
      const endDate = parseDate(dateRange.end);

      // 如果当前出生日期超出了新证件类型的允许范围，则清空出生日期
      if (
        isDateBefore(currentBirthday, startDate) ||
        isDateAfter(currentBirthday, endDate)
      ) {
        formData.value.birthday = "";
        uni.showToast({
          title: "证件类型变更，请重新选择出生日期",
          icon: "none",
          duration: 2000,
        });
      }
    }

    // 根据证件类型设置isChildCard和ishasBindCard
    if (credTypeCode === "100") {
      // 出生证明，一定是儿童卡，一定需要监护人
      isChildCard.value = true;
      ishasBindCard.value = true;
      childCardStatus.value = true; // 设置儿童卡状态
    } else {
      // 其他证件类型，根据年龄判断
      initGuardianStatus();
    }
  };

  // 处理生日变化（对应React版本的getBirthday）
  const handleBirthdayChange = (birthday: string) => {
    formData.value.birthday = birthday;

    // 根据生日重新计算是否需要监护人
    if (birthday) {
      const age = diffInYears(new Date(), parseDate(birthday));
      const orgCode = uni.getStorageSync("organCode") || "HID0101";

      // 根据医院配置判断年龄限制
      const needGuardian =
        (orgCode === "HID0101" || orgCode === "HID0102") && age < 14;
      isChildCard.value = needGuardian;
      ishasBindCard.value = needGuardian;
      childCardStatus.value = needGuardian; // 设置儿童卡状态
    }
  };

  // 从就诊卡导入（对应React版本的从就诊卡导入功能）
  const importGuardian = async () => {
    try {
      // 保存当前表单数据到本地存储
      uni.setStorageSync("registerCardInfo", formData.value);

      // 使用工具类跳转到就诊卡列表页面进行选择，带年龄校验
      const result = await PatientCardListUtils.navigateToSelectCard({
        guardianOnly: true,
        maxSelection: 1,
        backType: "7",
        title: "选择监护人卡",
        filter: {
          relation: ["父亲", "母亲", "监护人"], // 只显示监护人关系的卡
        },
        validator: createAgeValidator(18, "请选择大于18岁的就诊卡"),
      });

      if (result.success && result.selectedCards.length > 0) {
        const guardianCard = result.selectedCards[0];

        // 更新监护人信息
        formData.value.guardianName = guardianCard.patientName || "";
        formData.value.guardianCredType = guardianCard.credTypeCode || "01";
        formData.value.guardianCredTypeName =
          guardianCard.credTypeName || "居民身份证";
        formData.value.guardianCredNo = guardianCard.credNo || "";
        formData.value.guardianRelation = guardianCard.relation || "";

        uni.showToast({
          title: "监护人信息导入成功",
          icon: "success",
        });
      }
    } catch (error) {
      console.error("导入监护人信息失败:", error);
      uni.showToast({
        title: "导入失败，请重试",
        icon: "none",
      });
    }
  };

  // 扫描监护人证件
  const scanGuardianCard = () => {
    uni.chooseImage({
      count: 1,
      sizeType: ["compressed"],
      sourceType: ["camera", "album"],
      success: async (res) => {
        try {
          uni.showLoading({ title: "识别中..." });

          const tempFilePath = res.tempFilePaths[0];
          console.log("选择的监护人证件图片:", tempFilePath);

          // 这里可以调用OCR识别接口
          // const ocrResult = await applyCardService.ocrGuardianCard({...});
          // 暂时模拟OCR结果
          setTimeout(() => {
            uni.hideLoading();
            showToast("OCR识别功能开发中");
          }, 1000);
        } catch (error) {
          uni.hideLoading();
          console.error("OCR识别失败:", error);
          showToast("识别失败，请重试");
        }
      },
      fail: () => {
        showToast("选择图片失败");
      },
    });
  };

  // 获取机构配置（对应React版本的getOrganConfig）
  const getOrganConfig = async () => {
    try {
      // 调用真实的API获取机构配置
      const organResult: any = await verifyCardService.getOrganConfig({
        channelCode: getChannelCode(),
        organCode: getOrganCode(),
      });

      console.log("机构配置获取结果:", organResult);

      if (organResult.code === "1" && organResult.data) {
        const configData = organResult.data;

        // 设置儿童卡状态
        childCardStatus.value = configData.childCardStatus !== false; // 默认支持儿童卡

        // 处理OCR支持的证件类型列表
        if (configData.credOcrList && Array.isArray(configData.credOcrList)) {
          credOcrList.value = configData.credOcrList;
        } else if (configData.credOcr) {
          // 如果是JSON字符串，尝试解析
          try {
            credOcrList.value = JSON.parse(configData.credOcr);
          } catch (parseError) {
            console.warn("解析credOcr字段失败:", parseError);
            credOcrList.value = ["01"]; // 默认支持身份证
          }
        } else {
          // 默认支持身份证OCR
          credOcrList.value = ["01"];
        }

        console.log("机构配置设置成功:", {
          childCardStatus: childCardStatus.value,
          credOcrList: credOcrList.value,
        });
      } else {
        console.log("机构配置获取失败或数据为空，使用默认配置");
        // 设置默认配置
        childCardStatus.value = true;
        credOcrList.value = ["01"];
      }
    } catch (error) {
      console.error("获取机构配置失败:", error);
      // 默认支持儿童卡功能和身份证OCR
      childCardStatus.value = true;
      credOcrList.value = ["01"];
    }
  };

  // 初始化监护人状态
  const initGuardianStatus = () => {
    // 根据证件类型和年龄判断是否需要监护人信息
    if (formData.value.credType === "100") {
      // 出生证明，一定是儿童卡，一定需要监护人
      isChildCard.value = true;
      ishasBindCard.value = true;
      childCardStatus.value = true; // 设置儿童卡状态
      return;
    }

    // 根据年龄判断
    let age = 999; // 默认成年
    if (formData.value.credType === "01" && formData.value.credNo) {
      // 身份证，从证件号计算年龄
      age = idCardNoUtil.getAge(formData.value.credNo);
    } else if (formData.value.birthday) {
      // 其他证件类型，从生日计算年龄
      age = diffInYears(new Date(), parseDate(formData.value.birthday));
    }

    const orgCode = uni.getStorageSync("organCode") || "HID0101";

    // 根据医院配置判断年龄限制
    const needGuardian =
      (orgCode === "HID0101" || orgCode === "HID0102") && age < 14;
    isChildCard.value = needGuardian;
    ishasBindCard.value = needGuardian;
    childCardStatus.value = needGuardian; // 设置儿童卡状态
  };

  // 初始化页面数据
  const initPageData = async (options: any) => {
    console.log("🚀 初始化页面数据，参数:", options);

    // 设置业务类型和卡ID
    businessType.value = options.businessType || BUSINESS_TYPE.REGISTER_CARD;
    cardId.value = options.cardId || "";

    console.log("📋 业务类型:", businessType.value);
    console.log("🆔 卡ID:", cardId.value);
    console.log("🔍 checkCard参数:", options.checkCard);
    console.log(
      "🔍 是否为编辑模式:",
      businessType.value === BUSINESS_TYPE.EDIT_CARD
    );
    console.log(
      "🔍 是否为升级模式:",
      businessType.value === BUSINESS_TYPE.UPDATE_CARD
    );
    console.log("🔍 卡ID是否存在:", !!cardId.value);

    // 确保regionValues初始化为空数组
    formData.value.regionValues = [];
    formData.value.region = [];

    // 获取证件类型列表
    await getCredTypeList();

    // 获取省市区数据
    await getRegionData();

    // 获取民族数据
    await getNationData();

    // 获取职业数据
    await getProfessionData();

    // 获取机构配置
    await getOrganConfig();

    try {
      // 根据业务类型处理数据
      if (businessType.value === BUSINESS_TYPE.EDIT_CARD && cardId.value) {
        // 编辑卡：获取卡信息
        console.log("🎯 检测到编辑模式，开始获取卡信息");
        await fetchCardInfo();
        return;
      } else if (businessType.value === BUSINESS_TYPE.UPDATE_CARD) {
        // 升级卡：从本地存储获取数据
        const registerCardInfo = uni.getStorageSync("registerCardInfo") || {};
        // 确保字典数据加载完成后再初始化表单
        await Promise.all([
          getNationData(),
          getProfessionData(),
          getCredTypeList(),
        ]);
        initFormInfo(registerCardInfo);
        return;
      } else if (businessType.value === BUSINESS_TYPE.EDIT_AUDIT_CARD) {
        // 编辑待审核卡：从本地存储获取数据
        const registerCardInfo = uni.getStorageSync("registerCardInfo") || {};
        // 确保字典数据加载完成后再初始化表单
        await Promise.all([
          getNationData(),
          getProfessionData(),
          getCredTypeList(),
        ]);
        initFormInfo(registerCardInfo);
        return;
      }

      // 正常注册：从本地存储获取验证页面传递的数据
      if (options.fromVerify) {
        const verifyCardInfo = uni.getStorageSync(
          ApplyCardStorageKeys.VERIFY_TO_APPLY_DATA
        );

        if (verifyCardInfo) {
          console.log("从本地存储获取验证数据:", verifyCardInfo);

          // 使用数据后立即清除，避免数据残留
          // uni.removeStorageSync(ApplyCardStorageKeys.VERIFY_TO_APPLY_DATA);

          // 设置基础信息（不可修改）
          formData.value.patientName = verifyCardInfo.patientName || "";
          formData.value.credType = verifyCardInfo.credTypeCode || "";
          formData.value.credTypeName = verifyCardInfo.credTypeName || "";
          formData.value.credNo = verifyCardInfo.credNo || "";

          // 设置认证方式（重要：后台需要这个字段）
          formData.value.realnameType =
            verifyCardInfo.realnameType || verifyCardInfo.checktypeValue || "";
          console.log(
            "✅ 从验证页面设置认证方式:",
            formData.value.realnameType
          );

          // 设置图片信息（重要：后台需要这些字段）
          formData.value.cardImg =
            verifyCardInfo.cardImg || verifyCardInfo.picFront || "";
          formData.value.picFront =
            verifyCardInfo.cardImg || verifyCardInfo.picFront || "";
          console.log("✅ 从验证页面设置图片信息:", {
            cardImg: formData.value.cardImg,
            picFront: formData.value.picFront,
          });

          // 如果有OCR识别的信息，可以预填一些字段
          if (verifyCardInfo.birthday) {
            formData.value.birthday = verifyCardInfo.birthday;
          }
          if (verifyCardInfo.gender) {
            formData.value.gender = verifyCardInfo.gender;
          }
          if (verifyCardInfo.nation) {
            formData.value.nation = verifyCardInfo.nation;
          }
          if (verifyCardInfo.address) {
            formData.value.address = verifyCardInfo.address;
          }

          // 初始化监护人状态
          initGuardianStatus();

          // 如果是人脸验证成功的情况，可以获取人脸验证数据
          if (options.faceVerified && verifyCardInfo.faceVerificationData) {
            console.log("人脸验证数据:", verifyCardInfo.faceVerificationData);
            // 这里可以保存人脸验证相关数据，用于后续提交
          }

          console.log("初始化页面数据:", formData.value);
        } else {
          console.warn("未找到验证页面传递的数据");
          showToast("页面数据获取失败");
        }
      } else {
        // 兼容旧的URL参数方式（如果需要）
        if (options.verifyCardInfo) {
          const verifyCardInfo = JSON.parse(
            decodeURIComponent(options.verifyCardInfo)
          );

          // 设置基础信息
          formData.value.patientName = verifyCardInfo.patientName || "";
          formData.value.credType = verifyCardInfo.credTypeCode || "";
          formData.value.credTypeName = verifyCardInfo.credTypeName || "";
          formData.value.credNo = verifyCardInfo.credNo || "";

          // 设置认证方式（兼容旧URL参数方式）
          formData.value.realnameType =
            verifyCardInfo.realnameType || verifyCardInfo.checktypeValue || "";
          console.log("✅ 从URL参数设置认证方式:", formData.value.realnameType);

          // 设置图片信息（兼容旧URL参数方式）
          formData.value.cardImg =
            verifyCardInfo.cardImg || verifyCardInfo.picFront || "";
          formData.value.picFront =
            verifyCardInfo.cardImg || verifyCardInfo.picFront || "";
          console.log("✅ 从URL参数设置图片信息:", {
            cardImg: formData.value.cardImg,
            picFront: formData.value.picFront,
          });

          // 如果有OCR识别的信息，可以预填一些字段
          if (verifyCardInfo.birthday) {
            formData.value.birthday = verifyCardInfo.birthday;
          }
          if (verifyCardInfo.gender) {
            formData.value.gender = verifyCardInfo.gender;
          }

          // 初始化监护人状态
          initGuardianStatus();

          console.log("从URL参数初始化页面数据:", formData.value);
        }
      }
    } catch (error) {
      console.error("初始化页面数据失败:", error);
      showToast("页面数据初始化失败");
    }
  };

  // 级联地址选择处理
  const onRegionValuesChange = (values: (string | number)[]) => {
    formData.value.regionValues = values;

    // 获取选择的地址标签
    const regionLabels = getRegionLabels(values);
    formData.value.region = regionLabels;

    console.log("级联地址选择:", values, regionLabels);
  };

  // 根据选择的值获取地址标签
  const getRegionLabels = (values: (string | number)[]) => {
    const labels: string[] = [];
    let currentOptions = regionData.value;

    for (const value of values) {
      const option = currentOptions.find((item) => item.dicCode === value);
      if (option) {
        labels.push(option.dicName);
        currentOptions = option.children || [];
      }
    }

    return labels;
  };

  // 获取地区详细信息（编码和名称）
  const getRegionDetails = (values: (string | number)[]) => {
    if (!values || values.length === 0) {
      return {
        provinceCode: "",
        provinceName: "",
        cityCode: "",
        cityName: "",
        cityAreaCode: "",
        cityAreaName: "",
        streetCode: "",
        streetName: "",
      };
    }

    const details = {
      provinceCode: "",
      provinceName: "",
      cityCode: "",
      cityName: "",
      cityAreaCode: "",
      cityAreaName: "",
      streetCode: "",
      streetName: "",
    };

    let currentData = regionData.value;

    // 省份
    if (values.length > 0) {
      const province = currentData.find((d: any) => d.dicCode === values[0]);
      if (province) {
        details.provinceCode = province.dicCode;
        details.provinceName = province.dicName;
        currentData = province.children || [];
      }
    }

    // 城市
    if (values.length > 1) {
      const city = currentData.find((d: any) => d.dicCode === values[1]);
      if (city) {
        details.cityCode = city.dicCode;
        details.cityName = city.dicName;
        currentData = city.children || [];
      }
    }

    // 区县
    if (values.length > 2) {
      const district = currentData.find((d: any) => d.dicCode === values[2]);
      if (district) {
        details.cityAreaCode = district.dicCode;
        details.cityAreaName = district.dicName;
        currentData = district.children || [];
      }
    }

    // 街道
    if (values.length > 3) {
      const street = currentData.find((d: any) => d.dicCode === values[3]);
      if (street) {
        details.streetCode = street.dicCode;
        details.streetName = street.dicName;
      }
    }

    return details;
  };

  // 验证表单
  const validateForm = () => {
    // 验证姓名
    if (!formData.value.patientName) {
      showToast("请输入患者姓名");
      return false;
    }

    // 验证证件号码（非儿童卡时必填）
    if (!isChildCard.value && !formData.value.credNo) {
      showToast("请输入证件号码");
      return false;
    }

    // 验证出生日期
    if (!formData.value.birthday) {
      showToast("请选择出生日期");
      return false;
    }

    // 验证联系电话
    if (!formData.value.phone) {
      showToast("请输入联系电话");
      return false;
    } else if (!/^1[3456789]\d{9}$/.test(formData.value.phone)) {
      showToast("请输入正确的手机号码");
      return false;
    }

    // 验证地址选择 - 至少需要选择省市区三级
    if (formData.value.regionValues.length < 3) {
      showToast("请至少选择省市区三级地址");
      return false;
    }

    // 验证详细地址
    if (!formData.value.address) {
      showToast("请输入详细地址");
      return false;
    }

    // 验证职业（非儿童卡时必填）
    if (!isChildCard.value && !formData.value.profession) {
      showToast("请选择职业");
      return false;
    }

    // 如果是儿童卡，验证监护人信息
    if (isChildCard.value) {
      // 验证监护人姓名
      if (!formData.value.guardianName) {
        showToast("请输入监护人姓名");
        return false;
      }

      // 验证监护人证件号码
      if (!formData.value.guardianCredNo) {
        showToast("请输入监护人证件号码");
        return false;
      }

      // 验证与患者关系
      if (!formData.value.guardianRelation) {
        showToast("请选择与患者关系");
        return false;
      }
    }

    return true;
  };

  // 获取卡信息（编辑模式）
  const fetchCardInfo = async () => {
    if (!cardId.value) {
      console.warn("缺少卡ID，无法获取卡信息");
      showToast("缺少卡片ID");
      return;
    }

    try {
      console.log("🔄 开始获取卡信息，卡ID:", cardId.value);

      uni.showLoading({
        title: "加载中...",
      });

      // 调用获取卡信息的接口
      const response = await applyCardService.getCardInfo(cardId.value);

      console.log("📡 API响应:", response);

      if (response.code === "1" && response.data) {
        console.log("✅ 获取卡信息成功:", response.data);
        initFormInfo(response.data);
      } else {
        console.warn("❌ 获取卡信息失败:", response.msg || response);
        showToast(response.msg || "获取卡信息失败");
      }
    } catch (error) {
      console.error("💥 获取卡信息异常:", error);
      showToast("网络错误，请稍后重试");
    } finally {
      uni.hideLoading();
    }
  };

  // 初始化表单信息
  const initFormInfo = (cardInfo: any) => {
    if (!cardInfo) {
      console.warn("⚠️ 卡信息为空，无法初始化表单");
      return;
    }

    console.log("🔄 开始初始化表单信息:", cardInfo);

    // 保存原始卡片信息（用于编辑模式的字段显示判断）
    originalCardInfo.value = { ...cardInfo };

    // 基础信息
    formData.value.patientName = cardInfo.patientName || "";
    formData.value.credType = cardInfo.credTypeCode || cardInfo.credType || "";
    formData.value.credTypeName = cardInfo.credTypeName || "";
    formData.value.credNo = cardInfo.credNo || "";
    formData.value.birthday = cardInfo.birthday || "";
    formData.value.nation = cardInfo.nationName || cardInfo.nation || "汉族";
    formData.value.gender = cardInfo.gender || "";

    // 职业信息处理（参考React项目逻辑）
    // React项目中使用occupationCode作为值，occupationName作为显示名称
    formData.value.profession = cardInfo.occupationCode || "";

    // 联系信息
    formData.value.phone = cardInfo.tel || cardInfo.phone || "";
    formData.value.address = cardInfo.detailAddress || cardInfo.address || "";
    formData.value.company = cardInfo.company || "";

    // 图片信息（编辑模式可能需要）
    formData.value.cardImg = cardInfo.cardImg || cardInfo.picFront || "";
    formData.value.picFront = cardInfo.cardImg || cardInfo.picFront || "";

    console.log("📝 基础信息已设置:", {
      patientName: formData.value.patientName,
      credType: formData.value.credType,
      credNo: formData.value.credNo,
      birthday: formData.value.birthday,
      nation: formData.value.nation,
      gender: formData.value.gender,
      profession: formData.value.profession,
      occupationCode: cardInfo.occupationCode,
      occupationName: cardInfo.occupationName,
      phone: formData.value.phone,
      address: formData.value.address,
      company: formData.value.company,
      cardImg: formData.value.cardImg,
      picFront: formData.value.picFront,
    });

    // 地址信息
    console.log("🏠 处理地址信息:", {
      provinceCode: cardInfo.provinceCode,
      provinceName: cardInfo.provinceName,
      cityCode: cardInfo.cityCode,
      cityName: cardInfo.cityName,
      cityAreaCode: cardInfo.cityAreaCode,
      cityAreaName: cardInfo.cityAreaName,
      streetCode: cardInfo.streetCode,
      streetName: cardInfo.streetName,
    });

    if (cardInfo.provinceCode && cardInfo.cityCode && cardInfo.cityAreaCode) {
      const regionValues = [
        cardInfo.provinceCode,
        cardInfo.cityCode,
        cardInfo.cityAreaCode,
      ];
      if (cardInfo.streetCode) {
        regionValues.push(cardInfo.streetCode);
      }
      formData.value.regionValues = regionValues;

      // 设置地址标签
      const regionLabels = [
        cardInfo.provinceName || "",
        cardInfo.cityName || "",
        cardInfo.cityAreaName || "",
      ];
      if (cardInfo.streetName) {
        regionLabels.push(cardInfo.streetName);
      }
      formData.value.region = regionLabels.filter((label) => label);

      console.log("✅ 地址信息已设置:", {
        regionValues: formData.value.regionValues,
        region: formData.value.region,
      });
    } else {
      console.log("⚠️ 地址信息不完整，跳过地址设置");
    }

    // 监护人信息
    console.log("👨‍👩‍👧‍👦 处理监护人信息:", {
      guarderName: cardInfo.guarderName,
      guarderCredTypeCode: cardInfo.guarderCredTypeCode,
      guarderCredNo: cardInfo.guarderCredNo,
      guarderRelation: cardInfo.guarderRelation,
    });

    if (cardInfo.guarderName) {
      formData.value.guardianName = cardInfo.guarderName;
      formData.value.guardianCredType = cardInfo.guarderCredTypeCode || "";
      formData.value.guardianCredNo = cardInfo.guarderCredNo || "";
      formData.value.guardianRelation = cardInfo.guarderRelation || "";

      console.log("✅ 监护人信息已设置:", {
        guardianName: formData.value.guardianName,
        guardianCredType: formData.value.guardianCredType,
        guardianCredNo: formData.value.guardianCredNo,
        guardianRelation: formData.value.guardianRelation,
      });
    } else {
      console.log("⚠️ 无监护人信息");
    }

    // 患者关系信息（参考React项目逻辑）
    // React项目中使用relation字段，需要转换为字符串格式
    if (cardInfo.relation !== null && cardInfo.relation !== undefined) {
      // React项目中relation是数字：0=本人，1=其他
      formData.value.patientRelation = String(cardInfo.relation);
      console.log(
        "✅ 患者关系已设置:",
        formData.value.patientRelation,
        "(来自relation字段)"
      );
    } else if (cardInfo.patientRelation) {
      // 如果有patientRelation字段，直接使用
      formData.value.patientRelation = String(cardInfo.patientRelation);
      console.log(
        "✅ 患者关系已设置:",
        formData.value.patientRelation,
        "(来自patientRelation字段)"
      );
    } else {
      // 如果没有关系信息，默认设置为"其他"（参考 react-old 项目逻辑）
      formData.value.patientRelation = "1";
      console.log("⚠️ 无患者关系信息，设置默认值为'其他'");
    }

    // 认证方式信息（参考React项目逻辑）
    if (cardInfo.realnameType || cardInfo.checktypeValue) {
      // 优先使用realnameType，如果没有则使用checktypeValue（兼容老数据）
      formData.value.realnameType =
        cardInfo.realnameType || cardInfo.checktypeValue || "";
      console.log("✅ 认证方式已设置:", formData.value.realnameType);
    } else {
      console.log("⚠️ 无认证方式信息");
    }

    // 检查是否需要监护人信息
    initGuardianStatus();

    console.log("🎯 表单初始化完成，最终数据:", formData.value);
    console.log("🔍 升级模式状态检查:", {
      businessType: businessType.value,
      isChildCard: isChildCard.value,
      showProfession: showProfession.value,
      showCompany: showCompany.value,
      showPatientRelation: showPatientRelation.value,
      profession: formData.value.profession,
      company: formData.value.company,
      patientRelation: formData.value.patientRelation,
    });
  };

  // 提交申请
  const handleSubmit = async () => {
    try {
      uni.showLoading({ title: "提交中..." });

      // 表单验证
      if (!validateForm()) {
        uni.hideLoading();
        return;
      }

      // 获取地区详细信息
      const regionDetails = getRegionDetails(formData.value.regionValues);

      // 获取民族编码
      const selectedNation = nationList.value.find(
        (item) => item.label === formData.value.nation
      );
      const nationCode = selectedNation ? selectedNation.value : "";

      // 获取职业名称
      const selectedProfession = professionList.value.find(
        (item) => item.value === formData.value.profession
      );
      const occupationName = selectedProfession ? selectedProfession.label : "";

      // 获取机构名称
      const organCode = getOrganCode();
      const organName = await getOrganName(organCode);
      debugger;
      // 构建提交数据（参考 react-old 项目的数据结构）
      const submitData = {
        // 基础信息
        patientName: formData.value.patientName,
        credType: formData.value.credType,
        credTypeCode: formData.value.credType,
        credTypeName: formData.value.credTypeName,
        credNo: formData.value.credType === "100" ? "" : formData.value.credNo, // 无任何有效证件时证件号为空
        birthday: formData.value.birthday,
        nationName: formData.value.nation,
        nationCode: nationCode,
        gender: formData.value.gender,
        occupationName: occupationName,
        occupationCode: formData.value.profession,

        // 联系信息
        tel: formData.value.phone,
        company: formData.value.company,

        // 机构信息
        organCode: organCode,
        organName: organName,

        // 地址信息（完整的编码和名称）
        provinceCode: regionDetails.provinceCode,
        provinceName: regionDetails.provinceName,
        cityCode: regionDetails.cityCode,
        cityName: regionDetails.cityName,
        cityAreaCode: regionDetails.cityAreaCode,
        cityAreaName: regionDetails.cityAreaName,
        streetCode: regionDetails.streetCode,
        streetName: regionDetails.streetName,
        detailAddress: formData.value.address,

        // 患者与申请人的关系（参考 react-old 项目逻辑）
        relation: getPatientRelationCode(formData.value.patientRelation),

        // 认证方式（参考 react-old 项目逻辑）
        realnameType: formData.value.realnameType,

        // 图片信息（参考 react-old 项目逻辑）
        cardImg: formData.value.cardImg,
        picFront: formData.value.picFront,

        // 编辑相关字段
        ...(businessType.value === BUSINESS_TYPE.EDIT_CARD && {
          cardId: cardId.value,
        }),

        // 升级相关字段（参考 react-old 项目逻辑）
        ...(businessType.value === BUSINESS_TYPE.UPDATE_CARD && {
          oldCardId: cardId.value,
        }),

        // 监护人信息（如果是儿童卡）
        ...(isChildCard.value && {
          guarderName: formData.value.guardianName,
          guarderCredTypeCode: formData.value.guardianCredType,
          guarderCredNo: formData.value.guardianCredNo,
          guarderRelation: formData.value.guardianRelation,
        }),
      };

      console.log("提交申请数据:", submitData);

      let response;
      let successMessage = "申请成功";

      // 根据业务类型调用不同的接口
      if (businessType.value === BUSINESS_TYPE.EDIT_CARD) {
        // 编辑卡
        response = await applyCardService.editCard(submitData);
        successMessage = "修改成功";
      } else {
        // 注册、升级卡、编辑待审核卡都使用注册接口
        response = await applyCardService.submitApplication(submitData);
        if (businessType.value === BUSINESS_TYPE.EDIT_AUDIT_CARD) {
          successMessage = "修改成功";
        }
      }

      uni.hideLoading();

      if (response.code === "1") {
        // 提交成功，跳转到结果页面
        showToast(successMessage);

        // 保存绑卡描述信息到本地存储（参考 react-old 项目）
        const { data = {} } = response;
        const cardBindDesc = {
          // 优先使用接口返回的标题和描述，如果没有则使用默认值
          successTittle: data.successTittle || successMessage, // 注意：保持与 react-old 一致的拼写
          successTitle:
            data.successTitle || data.successTittle || successMessage, // 兼容两种拼写
          successMsg: data.successMsg || response.msg || "操作成功",
        };
        uni.setStorageSync("cardBindDesc", cardBindDesc);

        setTimeout(() => {
          if (businessType.value === BUSINESS_TYPE.EDIT_CARD) {
            // 编辑卡成功后返回上一页
            uni.navigateBack();
          } else {
            // 其他情况跳转到绑卡结果页面（使用 BIND_AUTH_SUCCESS = "2"）
            uni.redirectTo({
              url: `/pages/patient-card/bindresult/index?bindResultType=2`,
            });
          }
        }, 1500);
      } else {
        // 提交失败，显示错误信息
        showToast(response.msg || "操作失败，请重试");
      }
    } catch (error: any) {
      uni.hideLoading();
      console.error("提交申请失败:", error);
      showToast(error.message || "申请失败，请重试");
    }
  };

  return {
    // 响应式数据
    formData,
    credTypeArr,
    nationList,
    professionList,
    relationList,
    patientRelationList,
    regionData,
    regionLevelConfig,
    businessType,
    cardId,
    originalCardInfo,

    // 计算属性
    canSubmit,
    showGuardianInfo,
    showProfession,
    showCompany,
    showCredNo,
    showGender,
    showPatientRelation,
    guardianDisabled,
    showOcrIcon,
    isChildCard,
    birthdayDateRange,

    // 方法
    getGuardianCredTypeLabel,
    getRelationLabel,
    getPatientRelationLabel,
    onRelationChange,
    onPatientRelationChange,
    selectGender,
    handleCredTypeChange,
    handleBirthdayChange,
    handleCredNoChange,
    importGuardian,
    scanGuardianCard,
    initPageData,
    handleSubmit,
    onRegionValuesChange,
    loadStreetData,
    validateForm,

    // picker值
    relationPickerValue,
    patientRelationPickerValue,
    professionPickerValue,
    nationPickerValue,
  };
}
