// 证件类型常量
export const CRED_TYPE = {
  ID_CARD: "01", // 身份证
  MILITARY_CARD: "02", // 军官证
  PASSPORT: "03", // 护照
  HK_MACAU_PASS: "04", // 港澳通行证
  TAIWAN_PASS: "05", // 台湾通行证
  NO_VALID_CERT: "99", // 无有效证件（儿童证件类型）
  OTHER: "99", // 其他
} as const;

// 性别常量
export const GENDER = {
  MALE: "1", // 男
  FEMALE: "2", // 女
} as const;

// 民族列表
export const NATION_LIST = [
  { label: "汉族", value: "01" },
  { label: "蒙古族", value: "02" },
  { label: "回族", value: "03" },
  { label: "藏族", value: "04" },
  { label: "维吾尔族", value: "05" },
  { label: "苗族", value: "06" },
  { label: "彝族", value: "07" },
  { label: "壮族", value: "08" },
  { label: "布依族", value: "09" },
  { label: "朝鲜族", value: "10" },
  { label: "满族", value: "11" },
  { label: "侗族", value: "12" },
  { label: "瑶族", value: "13" },
  { label: "白族", value: "14" },
  { label: "土家族", value: "15" },
  { label: "哈尼族", value: "16" },
  { label: "哈萨克族", value: "17" },
  { label: "傣族", value: "18" },
  { label: "黎族", value: "19" },
  { label: "傈僳族", value: "20" },
  { label: "佤族", value: "21" },
  { label: "畲族", value: "22" },
  { label: "高山族", value: "23" },
  { label: "拉祜族", value: "24" },
  { label: "水族", value: "25" },
  { label: "东乡族", value: "26" },
  { label: "纳西族", value: "27" },
  { label: "景颇族", value: "28" },
  { label: "柯尔克孜族", value: "29" },
  { label: "土族", value: "30" },
  { label: "达斡尔族", value: "31" },
  { label: "仫佬族", value: "32" },
  { label: "羌族", value: "33" },
  { label: "布朗族", value: "34" },
  { label: "撒拉族", value: "35" },
  { label: "毛南族", value: "36" },
  { label: "仡佬族", value: "37" },
  { label: "锡伯族", value: "38" },
  { label: "阿昌族", value: "39" },
  { label: "普米族", value: "40" },
  { label: "塔吉克族", value: "41" },
  { label: "怒族", value: "42" },
  { label: "乌孜别克族", value: "43" },
  { label: "俄罗斯族", value: "44" },
  { label: "鄂温克族", value: "45" },
  { label: "德昂族", value: "46" },
  { label: "保安族", value: "47" },
  { label: "裕固族", value: "48" },
  { label: "京族", value: "49" },
  { label: "塔塔尔族", value: "50" },
  { label: "独龙族", value: "51" },
  { label: "鄂伦春族", value: "52" },
  { label: "赫哲族", value: "53" },
  { label: "门巴族", value: "54" },
  { label: "珞巴族", value: "55" },
  { label: "基诺族", value: "56" },
  { label: "其他", value: "99" },
];

// 关系列表（监护人关系）
export const RELATION_LIST = [
  { label: "父母", value: "5" },
  { label: "祖父母或外祖父母", value: "6" },
  { label: "兄、弟、姐、妹", value: "7" },
  { label: "其他", value: "8" },
];

// 患者关系列表（患者与申请人的关系）
export const PATIENT_RELATION_LIST = [
  { label: "本人", value: "0" },
  { label: "其他", value: "1" },
];

// 表单验证规则
export const VALIDATION_RULES = {
  // 姓名验证
  NAME: {
    required: true,
    minLength: 2,
    maxLength: 20,
    pattern: /^[\u4e00-\u9fa5a-zA-Z\s]{2,20}$/,
    message: "请输入2-20位中文或英文姓名",
  },

  // 身份证验证
  ID_CARD: {
    required: true,
    pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
    message: "请输入正确的身份证号码",
  },

  // 手机号验证
  PHONE: {
    required: true,
    pattern: /^1[3-9]\d{9}$/,
    message: "请输入正确的手机号码",
  },
} as const;

// 业务类型枚举（参考 react-old 项目）
export const BUSINESS_TYPE = {
  REGISTER_CARD: "1", // 正常注册电子健康卡
  UPDATE_CARD: "2", // 升级卡
  EDIT_CARD: "3", // 编辑卡
  EDIT_AUDIT_CARD: "4", // 编辑待审核卡
} as const;
