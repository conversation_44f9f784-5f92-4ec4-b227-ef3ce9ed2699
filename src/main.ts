import { createSSRApp } from "vue";
import { createPinia } from "pinia";
import App from "./App.vue";
import { handleVueError } from "./utils/error-handler";
import { initRouter } from "./router";
import { HytEmpty } from "./components/hyt-empty";

export function createApp() {
  const app = createSSRApp(App);
  const pinia = createPinia();

  // 注册全局错误处理
  app.config.errorHandler = handleVueError;

  // 注册全局组件
  app.component("hyt-empty", HytEmpty);
  app.use(pinia); // 先注册 pinia，因为全局服务依赖于它

  // 初始化路由系统
  initRouter();

  return {
    app,
  };
}
