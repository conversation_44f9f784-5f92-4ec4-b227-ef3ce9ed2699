import { loadEnv, PluginOption, ResolvedConfig } from "vite";
import OSS from "ali-oss";
import path from "path";
import {
  readDirSync,
  OSSClient,
  replaceLocalImagesWithCDN,
} from "../shared/oss";
import { readFileSync, writeFileSync } from "fs";
import { execSync } from "child_process";
import pinyin from "pinyin";

/**
 * 替换目录下所有样式文件中的本地图片路径为 CDN 前缀
 * @param dirPath 目标目录路径
 * @param cdnPrefix CDN 前缀（如 "https://cdn.example.com"）
 */

const getDevNameCode = (): string => {
  try {
    let username = execSync("git config user.name").toString().trim();
    // 替换非法文件夹字符：替换空格为-，删除其他特殊字符
    const converted = username
      .replace(/\s+/g, "-")
      .replace(/[^\u4e00-\u9fa5a-zA-Z0-9-]/g, "")
      .replace(/[\u4e00-\u9fa5]/g, (char) => {
        return pinyin(char, { style: pinyin.STYLE_NORMAL }).join("");
      });
    return converted.replace(/[^a-zA-Z0-9-]/g, "");
  } catch (error) {
    console.warn("无法获取 Git 用户名:", error);
    return "unknown-user";
  }
};

export function ImageDevViteOss({
  mode,
  cacheValidity = 7 * 24 * 60 * 60 * 1000,
}): PluginOption {
  if (process.env.UNI_NODE_ENV === "development") {
    const type = "dev";
    const env = loadEnv(mode, process.cwd(), "VITE_");
    const devName = getDevNameCode();
    const remoteDir = env.VITE_OSS_REMOTE_PATH + "dev/" + devName;
    const cdnBaseUrl = env.VITE_OSS_CDNPREFIX + remoteDir + "/";
    const assetsDir = path.resolve(
      process.cwd(),
      `dist/dev/${process.env.UNI_UTS_PLATFORM}/assets`
    );
    return {
      name: "vite-dev-plugin-image-oss",
      apply: "build",
      // 生产环境构建后处理
      generateBundle(_, bundle) {
        for (const [fileName, chunk] of Object.entries(bundle)) {
          // 处理 JS 文件
          if (chunk.type === "chunk" && chunk.code.includes("/assets/")) {
            // 先保护完整的 HTTP/HTTPS URL，然后替换本地路径，最后恢复
            chunk.code = chunk.code
              .replace(
                /(https?:\/\/[^\s"']*?)\/assets\//g,
                "$1__TEMP_ASSETS__/"
              )
              .replace(/\/assets\//g, `${cdnBaseUrl}`)
              .replace(/__TEMP_ASSETS__\//g, "/assets/");
          }
        }
      },
      async closeBundle() {
        const ossClient = new OSSClient({
          region: env.VITE_OSS_REGION,
          accessKeyId: env.VITE_OSS_ACCESS_KEY_ID!,
          accessKeySecret: env.VITE_OSS_ACCESS_KEY_SECRET!,
          bucket: env.VITE_OSS_BUCKET!,
        });

        replaceLocalImagesWithCDN(type, cdnBaseUrl);

        await ossClient.uploadDirectory(assetsDir, remoteDir, {
          skipCache: false, // 开发环境启用缓存
          deleteLocalFiles: true, // 删除本地文件
        });
      },
    };
  }
}
