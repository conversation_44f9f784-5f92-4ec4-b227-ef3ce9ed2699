import { loadEnv, PluginOption, ResolvedConfig } from "vite";
import OSS from "ali-oss";
import path from "path";
import {
  readDirSync,
  OSSClient,
  replaceLocalImagesWithCDN,
} from "../shared/oss";
import { readFileSync, writeFileSync } from "fs";
import { execSync } from "child_process";

// 获取当前Git分支名
const getCurrentBranch = (): string => {
  try {
    return execSync("git rev-parse --abbrev-ref HEAD").toString().trim();
  } catch (error) {
    console.warn("无法获取 Git 分支:", error);
    return "unknown-branch";
  }
};

// 获取当前Git标签名
const getCurrentTag = (): string => {
  try {
    // 严格检查HEAD是否为标签
    const isTag = execSync(
      "git describe --exact-match --tags HEAD 2>/dev/null || echo ''"
    )
      .toString()
      .trim();
    // 额外验证HEAD不是分支
    const isBranch = execSync("git symbolic-ref -q HEAD 2>/dev/null || echo ''")
      .toString()
      .trim();
    return isTag && !isBranch ? isTag : "";
  } catch (error) {
    console.warn("无法获取 Git 标签:", error);
    return "";
  }
};

// 验证构建环境
const validateBuildEnvironment = (mode: string): void => {
  if (mode === "staging") {
    const branch = getCurrentBranch();
    if (branch !== "release-test") {
      throw new Error(
        `预发布环境构建必须使用release-test分支，当前分支: ${branch}`
      );
    }
  } else if (mode === "prod") {
    // 如果.env中已配置版本号，则直接使用
    if (process.env.VITE_MINI_PROGRAM_VERSION) {
      console.log(
        `✅ 使用.env配置的版本号: ${process.env.VITE_MINI_PROGRAM_VERSION}`
      );
      return;
    }

    // 获取远程tag列表
    const remoteTags = new Set(
      execSync("git ls-remote --tags origin")
        .toString()
        .split("\n")
        .filter((line) => line.includes("refs/tags/"))
        .map((line) => line.split("refs/tags/")[1])
    );

    // 获取本地最新tag
    const localTag = getCurrentTag();
    if (!localTag) {
      throw new Error("生产环境构建必须使用标签");
    }

    // 检查tag是否存在远程
    if (!remoteTags.has(localTag)) {
      throw new Error(`标签${localTag}不存在于远程仓库`);
    }

    // 检查tag格式
    if (!/^prod-v(\d+\.\d+\.\d+)-(\d{8})-([a-z]+)$/.test(localTag)) {
      throw new Error(
        `标签格式错误: ${localTag}，必须为prod-vX.Y.Z-YYYYMMDD-xxx格式（xxx为任意长度字母）`
      );
    }

    console.log(`✅ 标签校验通过: ${localTag}`);
    const version = localTag.match(/^prod-v(\d+\.\d+\.\d+)-/)?.[1];
    if (!version) throw new Error("版本号提取失败");
    process.env.VITE_MINI_PROGRAM_VERSION = version;
  }
};

// 获取最新的 Git commit 值
// 修改后的代码（增加哈希长度并附加时间戳）
const getVersionCode = (): string => {
  try {
    // 使用 12 位哈希 + 时间戳（避免冲突）
    const hash = execSync("git rev-parse --short=12 HEAD").toString().trim();
    const timestamp = new Date().toISOString().split("T")[0].replace(/-/g, ""); // 格式：YYYYMMDD
    return `${hash}-${timestamp}`;
  } catch (error) {
    console.warn("无法获取 Git commit 值:", error);
    return "unknown-commit";
  }
};

export function ImageViteOss({ mode }): PluginOption {
  const type = process.env.UNI_NODE_ENV === "production" ? "build" : "dev";
  const env = loadEnv(mode, process.cwd(), "VITE_");
  const commitHash = getVersionCode();
  const assetsDir = path.resolve(
    process.cwd(),
    `dist/${type}/${process.env.UNI_UTS_PLATFORM}/assets`
  );
  const hashFolder =
    env.VITE_NODE_ENV === "prod"
      ? `/${commitHash}/${process.env.UNI_UTS_PLATFORM}`
      : "";
  const remoteDir = env.VITE_OSS_REMOTE_PATH + mode + hashFolder;
  const cdnBaseUrl = env.VITE_OSS_CDNPREFIX + remoteDir + "/";
  if (process.env.UNI_NODE_ENV === "production") {
    // 验证构建环境
    validateBuildEnvironment(mode);
    return {
      name: "vite-plugin-image-oss",
      apply: "build",
      // 生产环境构建后处理
      generateBundle(_, bundle) {
        for (const [fileName, chunk] of Object.entries(bundle)) {
          // 处理 JS 文件
          if (chunk.type === "chunk" && chunk.code.includes("/assets/")) {
            // 先保护完整的 HTTP/HTTPS URL，然后替换本地路径，最后恢复
            chunk.code = chunk.code
              .replace(
                /(https?:\/\/[^\s"']*?)\/assets\//g,
                "$1__TEMP_ASSETS__/"
              )
              .replace(/\/assets\//g, `${cdnBaseUrl}`)
              .replace(/__TEMP_ASSETS__\//g, "/assets/");
          }
        }
      },
      async closeBundle() {
        const ossClient = new OSSClient({
          region: env.VITE_OSS_REGION,
          accessKeyId: env.VITE_OSS_ACCESS_KEY_ID!,
          accessKeySecret: env.VITE_OSS_ACCESS_KEY_SECRET!,
          bucket: env.VITE_OSS_BUCKET!,
        });

        replaceLocalImagesWithCDN(type, cdnBaseUrl);

        await ossClient.uploadDirectory(assetsDir, remoteDir, {
          skipCache: true, // 生产环境跳过缓存，强制上传所有文件
          deleteLocalFiles: true, // 删除本地文件
        });
      },
    };
  }
}
