import { readFileSync, writeFileSync } from "fs";
import { readDirSync } from "./client";
import path from "path";

/**
 * 替换本地图片路径为CDN路径
 * @param type 构建类型
 * @param cdnBaseUrl CDN基础URL
 */
export async function replaceLocalImagesWithCDN(
  type: string,
  cdnBaseUrl: string
): Promise<void> {
  const assetsDir = path.resolve(
    process.cwd(),
    `dist/${type}/${process.env.UNI_UTS_PLATFORM}`
  );

  const files = readDirSync(assetsDir);

  for (const fileName of files) {
    if (/\.(wxss|acss|ttss)$/.test(fileName)) {
      try {
        // 读取文件内容
        const data = readFileSync(fileName, "utf8");

        // 替换本地资源路径为CDN路径，但保护完整的 HTTP/HTTPS URL
        // 先保护完整的 HTTP/HTTPS URL，然后替换本地路径，最后恢复
        const regex = /(\.\.\/)*assets\//g;
        const newData = data
          .replace(/(https?:\/\/[^\s"']*?)\/assets\//g, "$1__TEMP_ASSETS__/")
          .replace(regex, cdnBaseUrl)
          .replace(/__TEMP_ASSETS__\//g, "/assets/");

        // 写回文件
        writeFileSync(fileName, newData);

        console.log(`✅ 已替换: ${path.relative(process.cwd(), fileName)}`);
      } catch (error) {
        console.error(`❌ 替换失败: ${fileName}`, error);
      }
    }
  }
}

/**
 * 获取文件的MIME类型
 * @param filePath 文件路径
 * @returns MIME类型
 */
export function getMimeType(filePath: string): string {
  const ext = path.extname(filePath).toLowerCase();
  const mimeTypes: Record<string, string> = {
    ".jpg": "image/jpeg",
    ".jpeg": "image/jpeg",
    ".png": "image/png",
    ".gif": "image/gif",
    ".webp": "image/webp",
    ".svg": "image/svg+xml",
    ".css": "text/css",
    ".js": "application/javascript",
    ".json": "application/json",
    ".html": "text/html",
    ".txt": "text/plain",
  };

  return mimeTypes[ext] || "application/octet-stream";
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return "0 B";

  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

/**
 * 生成随机字符串
 * @param length 长度
 * @returns 随机字符串
 */
export function generateRandomString(length: number = 8): string {
  const chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  let result = "";
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/**
 * 检查文件是否为图片
 * @param filePath 文件路径
 * @returns 是否为图片
 */
export function isImageFile(filePath: string): boolean {
  const imageExts = [
    ".jpg",
    ".jpeg",
    ".png",
    ".gif",
    ".webp",
    ".svg",
    ".bmp",
    ".ico",
  ];
  const ext = path.extname(filePath).toLowerCase();
  return imageExts.includes(ext);
}

/**
 * 获取文件的相对路径
 * @param filePath 文件路径
 * @param basePath 基础路径
 * @returns 相对路径
 */
export function getRelativePath(filePath: string, basePath: string): string {
  return path.relative(basePath, filePath).replace(/\\/g, "/");
}
