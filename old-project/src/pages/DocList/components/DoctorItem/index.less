.doctorItem {
  //position: relative;
  padding: 24px;
  background-color: #ffffff;
  border-radius: 12px;
  position: relative;

  .item {
    display: flex;
    border-bottom: 2px solid rgba(235, 237, 245, 0.3);
  }

  .posterBox {
    position: relative;
    width: 100px;
    height: 108px;
    margin-right: 24px;

    &::after {
      position: absolute;
      left: 50%;
      bottom: -8px;
      transform: translate(-50%);
      text-align: center;
      display: block;

      //line-height: 0.38px;
      //background: linear-gradient(175deg, #A9C1EC 0%, #568DF2 100%);
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      width: 92px;
      height: 28px;
      background: linear-gradient(175deg, #a9c1ec 0%, #568df2 100%);
      border-radius: 16px;
      font-size: 24px;
    }

    .poster_active {
      position: absolute;
      top: 9.3px;
      border: 3px solid transparent;
    }
  }

  /*hasJisuTag*/

  .posterBox2 {
    // &::after {
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    //   content: '';
    //   //background: linear-gradient(175deg, #A9C1EC 0%, #568DF2 100%);
    //   //transform: scale(0.75);
    //   background: url('../../../../assets/sitForConsultation.png') no-repeat;
    //   background-size: cover;
    // }

    .poster_active {
      border: 3px solid #568df2;
    }
  }

  .poster {
    width: 100%;
    height: 100px;
    border-radius: 50%;
    object-fit: cover;
  }

  .tags {
    position: absolute;
    height: 46px;
    width: 65px;
    display: flex;
    flex-direction: column;
    right: 0;
    top: 16px;

    .rapid {
      width: 92px;
      margin-top: 16px;
    }

    .hasNum {
      font-size: 24px;
      color: rgba(49, 185, 170, 1);
      // line-height: 0.46px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      background: rgba(234, 248, 246, 1);
      border-top-left-radius: 20px;
      border-bottom-left-radius: 20px;
    }

    .hasNoNum {
      color: #bbb;
      background: #f3f7ff;
    }
  }

  .doctorInfo {
    .basicInfo {
      display: flex;
      align-items: center;

      .doctorName {
        font-size: 36px;
        color: #03081a;
        font-weight: 600;
        margin-right: 16px;
      }

      .titleNmae {
        font-size: 28px;
        color: #03081a;
        margin-right: 24px;
      }

      .rank {
        background-color: #568df2;
        padding: 0 12px;
        color: #ffffff;
        border-radius: 8px;
        font-size: 18px;
        height: 32px;
        line-height: 32px;
        text-align: center;
      }

      .usableNone {
        font-size: 20px;
        color: #989eb4;
        padding: 10px 8px;
        background: #eff0f6;
        border-radius: 12px;
      }
    }

    .workInfo {
      font-size: 28px;
      color: #03081a;
      margin: 12px 0;

      .organName {
        margin-right: 16px;
      }
    }

    .introduce {
      font-size: 24px;
      width: 502px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      text-indent: 55px;
      position: relative;

      .goodAt {
        width: 50px;
        height: 24px;
        position: absolute;
        left: 0;
        top: 0 4.5px;
        // float: left;
        // margin-top: 0.06px;
        // margin-right: 0.07px;
      }

      .introContent {
        font-size: 24px;
        color: #989eb4;
      }
    }

    .commont {
      font-size: 24px;
      color: #989eb4;
      margin: 14.9px 0 16.7px 0;

      span {
        font-size: 32px;
        color: #3ad3c1;
        font-weight: bold;
      }
    }

    .medicalLabel {
      display: flex;
      flex-direction: row;
      overflow-x: hidden;
      font-size: 18.6px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #5c6176;
      margin-bottom: 24px;

      li {
        flex-shrink: 0;
        padding: 0 12px;
        background: #ffffff;
        border-radius: 3.7px;
        border: 2px solid #c5c9da;
      }

      li:nth-child(n + 2) {
        margin-left: 11px;
      }
    }

    ul,
    ol {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    .price {
      font-size: 32px;
      color: #fbbc44;
      font-weight: bold;
      padding-bottom: 24px;
    }

    .priceBox {
      font-size: 28px;
      color: #03081a;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      padding-bottom: 24px;
      margin-top: 31.6px;

      .picPrice {
        margin-left: 22.3px;
      }

      .line {
        width: 2px;
        height: 28px;
        background-color: #ebedf5;
        margin: 0 16px;
      }

      span {
        font-size: 28px;
        color: #fbbc44;
        font-weight: bold;
      }
    }
  }
}

.fastLabel {
  width: 92px;
  height: 44px;
  position: absolute;
  top: 0;
  right: 0;
}
