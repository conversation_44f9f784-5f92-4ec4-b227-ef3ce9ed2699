import React from 'react';
import styles from './index.less';
import classNames from 'classnames';

const avater = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/default-doctor-logo.png';
const goodAt = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/shanchang.png';
const DoctorItem = (props) => {
  const { toDetail, item = {} } = props;
  const {
    doctorName = '',
    titelName = '',
    organName = '',
    deptName = '',
    headPortraits = '',
    profession = '',
    usable = 0,
    satisfaction = '',
    price = 0,
    servTimes = 0,
    organLevel = '',
    receptionLevel = 0,
    docTags,
    gdpbType,
    inquiryServiceConfigExpandVo,
  } = item;
  let gdpbType_;
  if (gdpbType) {
    gdpbType_ = gdpbType.split(',');
  } else {
    gdpbType_ = new Array();
  }
  const screeningTime = sessionStorage.getItem('screeningTime');
  const thatDay = sessionStorage.getItem('thatDay');
  /* 新增需求-6.6.9 */
  /* 正在坐诊：早上8点到中午12点，下午2点到下午6点 */
  const isTimeBetween = (start, end) => {
    let now = new Date();
    return now.getHours() >= start && now.getHours() < end;
  };
  const timeInner = isTimeBetween(8, 12) || isTimeBetween(14, 18);

  return (
    <div className={styles.doctorItem}>
      <div className={styles.item} onClick={() => toDetail(item)}>
        <div
          className={`${styles.posterBox} ${
            screeningTime == thatDay && timeInner ? styles.posterBox2 : timeInner && styles.posterBox2
          }  `}
        >
          <img src={headPortraits || avater} alt="" className={[styles.poster, styles.poster_active].join(' ')} />
        </div>

        <div className={styles.doctorInfo}>
          <div className={styles.basicInfo}>
            <div className={styles.doctorName}>{doctorName}</div>
            <div className={styles.titleNmae}>{titelName}</div>
            {organLevel && <div className={styles.rank}>{organLevel}</div>}

            {/* <div className={styles.usableNone}>{ usable === 0 ? '无号' : '有号' }</div> */}
          </div>
          <div className={styles.workInfo}>
            <span className={styles.organName}>{organName}</span>
            <span>{deptName}</span>
          </div>
          <div className={styles.introduce}>
            <img src={goodAt} alt="" className={styles.goodAt} />
            <div className={styles.introContent}>{profession}</div>
          </div>
          <div className={styles.commont}>
            好评率<span> {satisfaction}% </span> / 问诊量
            <span> {servTimes}</span>
          </div>

          <div className={styles.priceBox}>
            <div>
              图文<span>￥{price}</span>
            </div>
          </div>
        </div>
      </div>
      <div className={styles.tags}>
        <div className={usable === 0 ? classNames(styles.hasNum, styles.hasNoNum) : classNames(styles.hasNum)}>
          {usable === 0 ? '无号' : '有号'}
        </div>
      </div>
    </div>
  );
};

export default DoctorItem;
