import React, { FC, useEffect, useState } from 'react';
import { Toast } from 'antd-mobile';
import { connect, IDocListModelState, Dispatch, Loading, history } from 'umi';
import DoctorItem from './components/DoctorItem';
import styles from './index.less';
import { HxEmpty, HxIndicator, HxListView } from '@/components';
import { HxParameter } from '@/utils/parameter';

interface IProps {
  dispatch: Dispatch;
  docListmodel: IDocListModelState;
  loading: Loading;
  location: {
    query: {
      type: string;
    };
  };
}

const DocListHome: FC<IProps> = (props) => {
  const { dispatch, loading, docListmodel, location } = props;
  const {
    type = 'kjczq', //开检查专区-kjczq；开药专区-kyzq
    dCode = '', //科室code
  }: any = location?.query;
  const { docList = {} }: any = docListmodel;
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [pageNum, setPageNum] = useState<number>(1);
  useEffect(() => {
    document.title = type === 'kjczq' ? '开检查专区' : '开药专区';
  });
  useEffect(() => {
    fetDoctorList();
  }, []);
  /* 数据请求 */
  const fetDoctorList = (num = 1) => {
    // dispatch({
    //   type: 'docListmodel/patientLtxDoctors',
    //   payload: {
    //     pageNum: 1,
    //     pageSize: 10,
    //     randomDocListType: type,
    //   },
    //   callback(res: any) {
    //     const { list = [], total = 0 } = res;
    //     console.log(5555, res);
    //     setPageNum(num);
    //     if (num * 10 >= total) {
    //       setHasMore(false);
    //     }
    //   },
    // });
    Toast.loading('加载中...', 0);
    dispatch({
      type: 'docListmodel/getAllDoctors',
      payload: {
        pageNum: 1,
        pageSize: 99,
        servCode: 'zxmz',
        businessCode: `${type === 'kjczq' ? 'jczq' : 'kyzq'}_${dCode}`,
      },
      callback: (result: any) => {
        console.log('22222', result);
        const { list = [], total = 0 } = result?.data;
        setPageNum(num);
        if (num * 10 >= +total) {
          setHasMore(false);
        }
        Toast.hide();
      },
    });
  };
  /** 上拉至底触发 */
  const onEndReached = () => {
    loadMore();
  };
  /** 加载后一页 */
  const loadMore = () => {
    if (!hasMore) {
      return;
    }
    setTimeout(() => {
      fetDoctorList(pageNum + 1);
    }, 200);
  };
  /** 跳转到医生详情 */
  const toDetail = (item) => {
    const { doctorId = '', workOrganCode = '', workOrganId = '', servCode = '', deptName = '' } = item || {};
    const { channelCode, openId, organCode } = HxParameter;
    let _sku: any = '';
    if (dCode === '3500-HXNK') {
      //呼吸内科 sku16
      _sku = 16;
    } else if (dCode === 'MZ0036') {
      //结直肠肿瘤中心 sku21
      _sku = 21;
    }
    history.push({
      pathname: '/doctor/hxhome',
      query: {
        organCode,
        openId,
        doctorId,
        servCode,
        middleType: 1,
        channelCode,
        deptName,
        organId: workOrganId,
        // sku: type === 'kjczq' ? 19:16, // 开药：16；开检查19
        sku: _sku, // 埋点sku
      },
    });
  };

  const renderBow = (item) => {
    return (
      <div className={styles.listItem}>
        <DoctorItem item={item} toDetail={(item) => toDetail(item)} />
      </div>
    );
  };
  return (
    <div className={styles.container}>
      {loading ? (
        <HxIndicator />
      ) : +docList?.total > 0 ? (
        <>
          <HxListView
            dataSource={docList?.list}
            renderRow={renderBow}
            initialListSize={10}
            pageSize={10}
            onEndReached={() => onEndReached()}
            onEndReachedThreshold={10}
            isRenderFooter
            hasMore={hasMore}
          />
        </>
      ) : (
        <HxEmpty emptyMsg="暂无数据" canRefresh={false} emptyIcon="nodoctor" />
      )}
    </div>
  );
};

export default connect(({ docListmodel, loading }: { docListmodel: IDocListModelState; loading: Loading }) => ({
  docListmodel,
  loading: loading.effects['docListmodel/patientLtxDoctors'],
}))(DocListHome);
