import { Reducer } from 'redux';
import { Effect } from 'umi';
import { createAction } from '@/utils/common';
import { patientLtxDoctors, getAllDoctors } from './service';

export interface IDocListModelState {
  docList: any;
}

export interface IDocListModel {
  namespace: 'docListmodel';
  state: IDocListModelState;
  effects: {
    patientLtxDoctors: Effect;
    getAllDoctors: Effect;
  };
  reducers: {
    updateState: Reducer<IDocListModelState>;
  };
}

const DocListModel: IDocListModel = {
  namespace: 'docListmodel',
  state: {
    docList: {},
  },
  effects: {
    *patientLtxDoctors({ payload, callback }, { put }) {
      const res = yield patientLtxDoctors({ ...payload }) || {};
      yield put(
        createAction('updateState')({
          docList: res?.data,
        }),
      );
      callback && callback(res);
    },
    *getAllDoctors({ payload, callback }, { put }) {
      const res = yield getAllDoctors({ ...payload }) || {};
      const { list = [], total = 0 } = res?.data || {};
      yield put(
        createAction('updateState')({
          docList: res?.data,
        }),
      );
      callback && callback(res);
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
export default DocListModel;
