import request from '@/utils/request';

/**
 * 开检查专区-kjczq；开药专区-kyzq
 * @param data
 */
export const patientLtxDoctors = async (data: object): Promise<object> =>
  request(`${API_ONLINE}/netInquiry/patient/ltxDoctors`, {
    method: 'POST',
    data: {
      skipError: true,
      showOriginData: true,
      ...data,
    },
  });
/* 开检验检查专区推荐医生 */
export const getAllDoctors = async (data: object): Promise<object> =>
  request(`/cloud/doctorcenter/doctorRecommend/queryRecommendDoctorList`, {
    method: 'POST',
    data: {
      skipError: true,
      showOriginData: true,
      ...data,
    },
  });
