.container {
  min-height: 100vh;
  background: #f5f6f7;

  .operation {
    display: flex;
    flex-flow: wrap;
    justify-content: space-around;
    width: 750px;
    padding-top: 30px;
    background: #fff;

    .item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .title {
        font-weight: 600;
        font-size: 28px;
      }
      .line {
        width: 90px;
        height: 4px;
        margin-top: 26px;
        border-radius: 2px;
      }
    }
  }

  .listitem {
    box-sizing: border-box;
    width: 690px;
    margin: 20px 30px 0 30px;
    padding: 0 30px 20px 30px;
    background: #fff;
    border: 1px solid rgba(204, 204, 204, 1);
    border-radius: 16px;

    .head {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      box-sizing: border-box;
      width: 630px;
      padding: 20px 20px 20px 0;
      border-bottom: 1px solid #eee;

      .recipeId {
        max-width: 590px;
        white-space: break-all;
        word-wrap: break-word;
        color: #333;
        font-weight: 600;
        font-size: 28px;
      }

      .img {
        width: 15px;
        height: 26px;
        margin-left: 10px;
      }
    }

    .detail {
      color: #666;
      font-size: 26px;
      .daptName,
      .doctorName,
      .date {
        margin-top: 20px;
      }
    }
  }

  // }
  // 长列表滚动条高度
  :global(.am-list-view-scrollview) {
    height: calc(100vh - 46px);
  }
}
