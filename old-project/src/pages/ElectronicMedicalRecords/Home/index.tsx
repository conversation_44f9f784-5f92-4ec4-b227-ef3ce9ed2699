import React, { PureComponent } from 'react';
import { connect } from 'dva';
import queryString from 'query-string';
import { history } from 'umi';
import { HxListView } from '@/components';
import { HxParameter } from '@/utils/parameter';
import { Dispatch, AnyAction } from 'redux';

import styles from './index.less';

const next = require('@/assets/outpatientPharmacy/nextGray.png');

interface IProps {
  dispatch: Dispatch<AnyAction>;
  location: {
    search: string;
    query: any;
  };
}

interface IState {
  list: any;
  hasMore: any;
  dateScope: any;
  guideList: any;
  organCode: any;
  cardId: any; // 就诊卡id
  // isLoading: any;
}
class List extends PureComponent<IProps, IState> {
  state = {
    list: [],
    hasMore: true,
    dateScope: '1',
    organCode: '',
    cardId: '',
    guideList: [
      { id: '1', title: '近一个月', isChoice: true },
      { id: '3', title: '近三个月', isChoice: false },
      { id: '6', title: '近六个月', isChoice: false },
      { id: '0', title: '近一年', isChoice: false },
    ],
  };

  componentDidMount() {
    const {
      location: { search = '' },
    } = this.props;
    const { data = '' }: any = queryString.parse(search) || {};
    const { cardId = '', organCode = '' } = JSON.parse(data) || {};
    this.setState(
      {
        cardId,
        organCode,
      },
      () => {
        this.getList();
      },
    );
  }

  // 处方查询
  getList = () => {
    const { cardId, organCode, dateScope } = this.state;
    const { list: oldList = [] } = this.state;
    const { channelCode } = HxParameter;
    const userInfo = localStorage.getItem('userInfo') || '';
    const userId = JSON.parse(userInfo).userId || '';
    this.props.dispatch({
      type: 'electronicMedical/queryEleMedicalRecordList',
      payload: {
        hospitalCode: organCode,
        cardId,
        dateScope,
        channelCode,
        userId,
      },
      callback: (res: any) => {
        if (res.length === 0) {
          this.setState({ hasMore: false });
        }
        if (res) {
          this.setState({
            list: [...oldList, ...res],
          });
          this.setState({ hasMore: false });
        }
      },
    });
  };

  fetchData = () => {
    const { cardId, organCode, dateScope } = this.state;
    const { channelCode } = HxParameter;
    const userInfo = localStorage.getItem('userInfo') || '';
    const userId = JSON.parse(userInfo).userId || '';
    this.props.dispatch({
      type: 'electronicMedical/queryEleMedicalRecordList',
      payload: {
        hospitalCode: organCode,
        cardId,
        orderBy: '',
        dateScope,
        channelCode,
        userId,
      },
      callback: (res: any) => {
        if (res) {
          this.setState({
            list: [...res],
          });
        }
      },
    });
  };

  choiceDate = (index: any, id: any) => {
    const list: any = [];
    const { guideList } = this.state;
    for (let i = 0; i < guideList.length; i += 1) {
      if (index === i) {
        guideList[i].isChoice = true;
        list.push(guideList[i]);
      } else {
        guideList[i].isChoice = false;
        list.push(guideList[i]);
      }
    }
    this.setState(
      {
        guideList: list,
        dateScope: id,
      },
      () => {
        this.fetchData();
      },
    );
  };

  // 跳转 出院带药详情
  goDetail = (item: any) => {
    const { emrId = '' } = item;
    history.push(`/electronicMedical/detail?emrId=${emrId}`);
  };

  render() {
    const { list, guideList, hasMore } = this.state;
    const a = '';
    const ListItem = (rowData: any): React.ReactElement => {
      const { emrId, admDate, admDoctor, mainSuit, deptName } = rowData;
      return (
        <div>
          <div className={styles.listitem} onClick={() => this.goDetail(rowData)} key={emrId}>
            <div className={styles.head}>
              <div className={styles.recipeId}>科室：{deptName}</div>
              <div>
                <span style={{ color: '#999999' }}>详情</span>
                <img src={next} alt="" className={styles.img} />
              </div>
            </div>
            <div className={styles.detail}>
              <div className={styles.daptName}>就诊医生：{admDoctor}</div>
              {/* 这里的mainSuit 对应的详情页的 diagnose  因为后端嫌麻烦不想多改动 */}
              <div className={styles.doctorName}>诊断：{mainSuit}</div>
              <div className={styles.date}>就诊时间：{admDate}</div>
            </div>
          </div>
        </div>
      );
    };

    return (
      <div className={styles.container}>
        <div className={styles.operation}>
          {guideList.map((item, index) => {
            const { id, title, isChoice } = item;
            return (
              <div className={styles.item} key={id} onClick={() => this.choiceDate(index, id)}>
                <div className={styles.title} style={{ color: isChoice ? '#32B9AA' : '#999999' }}>
                  {title}
                </div>
                <div className={styles.line} style={{ background: isChoice ? '#32B9AA' : '#FFFFFF' }}>
                  {a}
                </div>
              </div>
            );
          })}
        </div>
        <HxListView dataSource={list} renderRow={ListItem} isRenderFooter hasMore={hasMore} />
      </div>
    );
  }
}

export default connect()(List);
