import { Reducer } from 'redux';
import { Effect } from 'umi';
import { queryEleMedicalRecordDetails, queryEleMedicalRecordList } from './service';

export interface IPatientCardState {}
export interface IPatientCardModel {
  namespace: 'electronicMedical';
  state: {};
  effects: {
    queryEleMedicalRecordDetails: Effect;
    queryEleMedicalRecordList: Effect;
  };
  reducers: {
    updateState: Reducer<IPatientCardState>;
  };
}

const patientCardModel: IPatientCardModel = {
  namespace: 'electronicMedical',
  state: {},
  effects: {
    *queryEleMedicalRecordDetails({ payload, callback }, { call }) {
      const res = yield call(queryEleMedicalRecordDetails, payload);
      res && callback(res);
    },
    *queryEleMedicalRecordList({ payload, callback }, { call }) {
      const res = yield call(queryEleMedicalRecordList, payload);
      callback(res);
    },
  },
  reducers: {
    updateState(state: any, { payload }: any) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};

export default patientCardModel;
