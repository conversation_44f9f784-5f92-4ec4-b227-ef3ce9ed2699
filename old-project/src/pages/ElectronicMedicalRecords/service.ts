import request from '@/utils/request';

import { getOrganCode } from '@/utils/parameter';

let node = '/cloud';
if (APP_ENV === 'prod') {
  node = getOrganCode() === 'HID0101' || getOrganCode() === 'HYT' ? `${API_HXYY}/cloud` : `${API_BASE}/cloud`;
}

const handleOrganCode = (data: any = {}) => {
  const { hospitalCode = '' } = data;
  if (getOrganCode() === 'HYT' && hospitalCode === 'HYT') {
    return {
      ...data,
      hospitalCode: 'HID0101',
    };
  }
  return data;
};

/**
 * 处方查询
 */
export const queryEleMedicalRecordList = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/medicationservice/queryEleMedicalRecordList`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

/**
 * 电子病历详情查询
 */
export const queryEleMedicalRecordDetails = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/medicationservice/queryEleMedicalRecordDetails`, {
    method: 'POST',
    data: handleOrganCode(data),
  });
