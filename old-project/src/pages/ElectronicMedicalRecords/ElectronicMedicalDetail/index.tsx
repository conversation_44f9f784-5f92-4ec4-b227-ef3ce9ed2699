import React, { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import queryString from 'query-string';
import { HxParameter } from '@/utils/parameter';
import { Dispatch } from 'redux';
import styles from './index.less';

interface IProps {
  dispatch: Dispatch;
  location: {
    search: string;
    query: any;
  };
}

interface IState {
  info: any;
}
class Detail extends PureComponent<IProps, IState> {
  state = {
    info: {},
  };

  componentDidMount() {
    const { emrId } = queryString.parse(this.props.location.search) || {};
    this.setState({}, () => {
      this.getDetail(emrId);
    });
  }

  // 电子病历详情查询
  getDetail = (emrId: any) => {
    const { organCode } = HxParameter;
    this.props.dispatch({
      type: 'electronicMedical/queryEleMedicalRecordDetails',
      payload: {
        hospitalCode: organCode,
        emrId,
      },
      callback: (res: any) => {
        const data = res || {};
        this.setState({
          info: data,
        });
      },
    });
  };

  render() {
    const { info = {} } = this.state;
    const {
      admDate = '',
      admDoctor = '',
      allergyHistory = '',
      assistCk = '',
      diagnose = '',
      diseaseHistory = '',
      familyHistory = '',
      handlingSugg = '',
      mainSuit = '',
      pastHistory = '',
      patTo = '',
      physiqueCk = '',
      remark = '',
    }: any = info;

    const list = [
      {
        title: '主诉',
        value: mainSuit,
      },
      {
        title: '病史',
        value: diseaseHistory,
      },
      {
        title: '过敏史',
        value: allergyHistory,
      },
      {
        title: '既往史',
        value: pastHistory,
      },
      {
        title: '家族史',
        value: familyHistory,
      },
      {
        title: '体格检查',
        value: physiqueCk,
      },
      {
        title: '辅助检查',
        value: assistCk,
      },
      {
        title: '诊断',
        value: diagnose,
      },
      {
        title: '处理意见',
        value: handlingSugg,
      },
      {
        title: '备注',
        value: remark,
      },
      {
        title: '患者去向',
        value: patTo,
      },
      {
        title: '就诊医生',
        value: admDoctor,
      },
      {
        title: '就诊日期',
        value: admDate,
      },
    ];

    return (
      <div>
        <div className={styles.container}>
          {list.map((item) => {
            return (
              <Fragment key={item.title}>
                {!['', undefined, null].includes(item.value) && (
                  <div className={styles.organName}>
                    {item.title}: <span>{item.value}</span>
                  </div>
                )}
              </Fragment>
            );
          })}
        </div>
      </div>
    );
  }
}

export default connect()(Detail);
