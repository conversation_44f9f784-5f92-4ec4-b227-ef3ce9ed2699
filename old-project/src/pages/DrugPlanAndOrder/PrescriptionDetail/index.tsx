import React, { FC } from 'react';
import { connect, IDrugPlanAndOrderModelState, Dispatch } from 'umi';
import styles from './index.less';

interface IProps {
  drugPlanAndOrder: IDrugPlanAndOrderModelState;
  dispatch: Dispatch;
  location?: {
    query?: {
      prescUrl?: string;
      status?: number;
    };
  };
}

const invalidImg = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/invalid-icon.png';
const usedImg = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/used-icon.png';

const PrescriptionDetail: FC<IProps> = (props) => {
  const { location = {} } = props;
  const {
    query: { prescUrl = '', status = 1 },
  }: any = location;
  return (
    <div className={styles.container}>
      <div className={styles.wrapBox}>
        <iframe
          title=" "
          src={prescUrl}
          frameBorder="0"
          width="100%"
          height="100%"
          scrolling="no"
          style={{ minHeight: '100%' }}
        />
        <img src={Number(status) === 1 ? usedImg : invalidImg} alt="" className={styles.img} />
      </div>
    </div>
  );
};

export default connect(({ drugPlanAndOrder }: { drugPlanAndOrder: IDrugPlanAndOrderModelState }) => ({
  drugPlanAndOrder,
}))(PrescriptionDetail);
