/** 订单信息 */
export interface IOrderItem {
  /** 实际需要支付的金额 */
  actualPrice?: number;
  /** 新增时间 */
  addTime?: string;
  /** 新增地址 */
  address?: string;
  /** 确认收货时间 */
  confirmTime?: string;
  /** 收货人 */
  consignee?: string;
  /** 配送费用 */
  freightPrice?: number;
  /** 联系电话 */
  mobile?: string;
  /** 订单ID */
  orderId?: number;
  /** 订单号 */
  orderSn?: string;
  /** 订单状态 */
  orderStatus?: number;
  /** 快递单号 */
  payId?: number;
  /** 付款状态 */
  payStatus?: number;
  /** 付款时间 */
  payTime?: string;
  /** 快递公司id */
  shippingId?: number;
  /** 快递公司名称 */
  shippingName?: string;
  /** 发货状态 */
  shippingStatus?: number;
  /** 配送类型  1物流  2自取 */
  shippingType?: number;
}

/** 支付信息 */
export interface IPayParamsType {
  /** 银行流水号 */
  bankTradeNo?: string;
  /** 业务系统编号 */
  bizSysSeq?: string;
  /** 支付系统单号 */
  dealSeq?: string;
  /** 商户编号 */
  merchantSeq?: string;
  /** 微信openID */
  openid?: string;
  /** 支付渠道 */
  payMethod?: string;
  /** 支付时间 */
  payTime?: string;
}

export interface IProductType {
  /** 商品ID */
  goodsId?: number;
  /** 商品名称 */
  goodsName?: string;
  /** 商品单位 */
  goodsUnit?: string;
  /** 商品列表图 */
  listPicUrl?: string;
  /** 产品ID */
  productId?: number;
  /** 零售价格 */
  retailPrice?: number;
  /** 商品规格 */
  specificationValue?: string;
}

export interface IDistanceType {
  /** 地址 */
  address?: string;
  /** 标签 */
  label?: string;
  /** 纬度 */
  latitude?: string;
  /** 经度 */
  longitude?: string;
  /** 商店名称 */
  name?: string;
}

export interface IPriceType {
  /** 实际需要支付的价格 */
  actualPrice?: number;
  /** 优惠价格 */
  couponPrice?: number;
  /** 运费 */
  freightPrice?: number;
  /** 商品总价 */
  goodsPrice?: number;
  /** 订单总价 */
  orderPrice?: number;
}

/** 订单列表 */
export interface IOrderListType {
  content?: [
    {
      order?: IOrderItem;
      paymentOrder?: IPayParamsType;
      products?: IProductType[];
    },
  ];
  pageNum?: number;
  pageSize?: number;
  total?: number;
  totalPages?: number;
}

/** 订单详情 */
export interface IOrderDetailType {
  order?: IOrderItem;
  paymentOrder?: IPayParamsType;
  products?: IProductType[];
  shop?: IDistanceType;
}

/** 商品结算页信息 */
export interface IOrderCheckoutType {
  prices?: IPriceType;
  products?: IProductType[];
  shop?: IDistanceType;
}

/** 商品与商品信息列表 */
export interface IGoodsType {
  orderGoods?: IProductType;
  shop?: IDistanceType;
}

/** 购药方案列表 */
export interface IBuyMedicineItemType {
  content?: [
    {
      /** 就诊日期 */
      admDate?: string;
      /** 就诊id */
      admId?: string;
      /** 科室编号 */
      deptCode?: string;
      /** 科室名称 */
      deptName?: string;
      /** 就诊诊断 */
      diagnoseDesc?: string;
      /** 医生工号 */
      doctorCode?: string;
      /** 医生姓名 */
      doctorName?: string;
      /** 药品清单列表信息 */
      drugInfoList?: [
        {
          /** 用户选择标记 0-未选中 1-已选择 */
          chooseFlag?: number;
          drugList?: [
            {
              /** 审核备注 */
              authRemark?: string;
              /** 厂商 */
              company?: string;
              /** 医生备注 */
              doctorRemark?: string;
              /** 用法用量描述 */
              drugDesc?: string;
              /** 用药理由 */
              drugReason?: string;
              /** 药品名称 */
              itemDesc?: string;
              /** 药品医嘱id */
              itemId?: string;
              /** 药品图片URL */
              itemImageUrl?: string;
              /** 规格 */
              standards?: string;
              /** 总量 */
              totalQty?: string;
            },
          ];
          /** 药房id */
          drugStoreId?: string;
          /** 药房名称 */
          drugStoreName?: string;
          /** 方案id */
          schemeId?: string;
          /** 总价 */
          totalPrice?: number;
        },
      ];
      /** icd诊断 */
      icdDesc?: string;
      /** 方案id */
      id?: string;
      /** 关联通知id */
      noticeId?: string;
      /** 操作记录 */
      operateLogList?: [
        {
          /** 操作说明 */
          operateDesc?: string;
          /** 操作状态 */
          operateStatus?: string;
          /** 操作时间 */
          operateTime?: string;
          /** 操作人 */
          operator?: string;
        },
      ];
      /** 方案订单id */
      orderId?: string;
      /** 机构编码 */
      organCode?: string;
      /** 机构名称 */
      organName?: string;
      /** 患者pmi */
      patientId?: string;
      /** 患者姓名 */
      patientName?: string;
      /** 处方号 */
      prescNo?: string;
      /** 方案时间 */
      schemeDate?: string;
      /** 方案状态 0-待使用 1-已使用 2-已失效 */
      status?: string;
    },
  ];
  pageNum?: number;
  pageSize?: number;
  total?: number;
  totalPages?: number;
}
