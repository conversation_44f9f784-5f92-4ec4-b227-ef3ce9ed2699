import React, { FC, useEffect } from 'react';
import { ListView } from 'antd-mobile';
import { connect, Dispatch, Loading, ConnectProps, IAddressModelState, history } from 'umi';
import { HxIndicator, HxEmpty, HxIcon } from '@/components';
import { HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import { IAddrerssItem } from '@/pages/Address/data.d';
import styles from './index.less';

interface IProps extends ConnectProps {
  address: IAddressModelState;
  dispatch: Dispatch;
  loading?: boolean;
}

const MallDistributionAddress: FC<IProps> = ({ address, loading, dispatch }) => {
  const { addressList = [] } = address;

  const fetchData = () => {
    console.log('获取列表');
    dispatch({ type: 'address/findListAddress' });
  };

  useEffect(() => {
    fetchData();
  }, []);

  const dataSource = new ListView.DataSource({
    rowHasChanged: (row1: any, row2: any) => row1 !== row2,
  });

  const addAddress = () => {
    dispatch({ type: 'address/clearAddressDetail' });
    history.push('/address/detail');
  };

  const changeDetail = (item: IAddrerssItem) => {
    HxSessionStorage.set(StorageEnum.ADDRESS_DETAIL, item);
    history.push(`/address/detail?addressId=${item?.addressId}`);
  };

  const chooseAddress = (rowData: IAddrerssItem) => {
    console.log('dizhi99999', rowData);
    history.replace({
      pathname: '/drugplanandorder/distribution',
      query: {
        data: JSON.stringify(rowData),
        status: 5,
      },
    });
  };

  const renderRow = (item: IAddrerssItem): React.ReactElement => {
    const {
      contactUserName = '',
      phone = '',
      provinceName = '',
      cityName = '',
      areaName = '',
      defaultAddress = '0',
      addressId = '',
      detailAddress = '',
    } = item;
    return (
      <div className={styles.addressitem} key={addressId}>
        <div className={styles.detail} onClick={() => chooseAddress(item)}>
          <div className={styles.namePhone}>
            <div className={styles.contactUserName}>{contactUserName}</div>
            <div className={styles.phone}>{phone.replace(/^(\d{3})\d{4}(\d+)/, '$1****$2')}</div>
          </div>
          <div className={styles.address}>
            {defaultAddress === '1' && <span className={styles.defaultAddress}>默认</span>}
            {provinceName}
            {cityName}
            {areaName}
            {detailAddress}
          </div>
        </div>
        <div className={styles.img}>
          <HxIcon iconName="patientcard-update" className={styles.edit} onClick={() => changeDetail(item)} />
        </div>
      </div>
    );
  };

  return (
    <div className={styles.container}>
      <div className={styles.list}>
        {loading && <HxIndicator />}
        {!loading && !addressList.length && (
          <HxEmpty emptyIcon="patient-nocard" emptyMsg="暂未查询地址信息～" canRefresh={false} />
        )}
        {!!addressList.length && (
          <ListView
            useBodyScroll
            className={styles.listView}
            dataSource={dataSource.cloneWithRows(addressList || [])}
            renderRow={renderRow}
          />
        )}
      </div>
      <div className={styles.add} onClick={addAddress}>
        新增地址
      </div>
    </div>
  );
};

export default connect(({ address, loading }: { address: IAddressModelState; loading: Loading }) => ({
  address,
  loading: loading.global,
}))(MallDistributionAddress);
