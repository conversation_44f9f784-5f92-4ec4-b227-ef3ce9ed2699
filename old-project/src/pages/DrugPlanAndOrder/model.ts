import { Reducer } from 'redux';
import { Effect } from 'umi';
import {
  IOrderListType,
  IOrderDetailType,
  IOrderCheckoutType,
  IPayParamsType,
  IGoodsType,
  IBuyMedicineItemType,
} from './data.d';
import * as drugPlanAndOrderApi from './service';

export interface IDrugPlanAndOrderModelState {
  drugList: Array<any>;
  orderList: IOrderListType;
  orderDetail: IOrderDetailType;
  orderCheckoutData: IOrderCheckoutType;
  payParams: IPayParamsType;
  goodsDetail: IGoodsType;
  buyMedicineList: IBuyMedicineItemType;
  buyMedicineHistoryList: IBuyMedicineItemType;
  modelPhone: string;
  isFirstToPay: number;
  queryMedicinesList: object;
  timeQuery: object;
  logisticsInfo: object;
  isValueInsure: boolean;
}

export interface IDrugPlanAndOrderModel {
  namespace: 'drugPlanAndOrder';
  state: IDrugPlanAndOrderModelState;
  effects: {
    /** 订单列表 */
    fetchOrderList: Effect;
    /** 订单详情 */
    fetchOrderDetail: Effect;
    /** 结算页信息 */
    OrderCheckout: Effect;
    /** 下单 */
    createOrder: Effect;
    /** 确认收货 */
    comfirmReceipt: Effect;
    /** 获取商店与商品信息列表 */
    fetchGoodsDetail: Effect;
    /** 取消订单 */
    cancelOrder: Effect;
    /** 购药方案列表 */
    BuyMedicineScheme: Effect;
    /** 购药方案历史记录 */
    MedicineSchemeHistory: Effect;
    /** 查询清单详情 */
    schemeDetail: Effect;
    /** 定时定点派送 */
    timeQuery: Effect;
    /** 物流方式信息表 */
    logisticsInfo: Effect;
    /** 特药购药清单 */
    queryMedicinesList: Effect;
    /** 订单撤销 */
    revoke: Effect;
    /** 更新订单状态  */
    updataOrderStatus: Effect;
    /** 获取银海药房地址  */
    pharmacyUrl: Effect;
  };
  reducers: {
    updateState: Reducer<IDrugPlanAndOrderModelState>;
  };
}

const DrugPlanAndOrderModel: IDrugPlanAndOrderModel = {
  namespace: 'drugPlanAndOrder',
  state: {
    drugList: [],
    orderList: {},
    orderDetail: {},
    orderCheckoutData: {},
    payParams: {},
    goodsDetail: {},
    buyMedicineList: {},
    buyMedicineHistoryList: {},
    modelPhone: '',
    timeQuery: {},
    logisticsInfo: {},
    isValueInsure: false,
    queryMedicinesList: {},
  },

  effects: {
    *fetchOrderList({ payload, callback }, { call, put }) {
      const data = yield call(drugPlanAndOrderApi.fetchOrderList, payload);
      yield put({
        type: 'updateState',
        payload: {
          orderList: data || {},
        },
      });
      callback && callback(data);
    },
    *fetchOrderDetail({ payload, callback }, { call, put }) {
      const data = yield call(drugPlanAndOrderApi.fetchOrderDetail, payload);
      yield put({
        type: 'updateState',
        payload: {
          orderDetail: data || {},
        },
      });
      callback && callback(data);
    },
    *OrderCheckout({ payload, callback }, { call, put }) {
      const data = yield call(drugPlanAndOrderApi.OrderCheckout, payload);
      yield put({
        type: 'updateState',
        payload: {
          orderCheckoutData: data || {},
        },
      });
      callback && callback(data);
    },
    *createOrder({ payload, callback }, { call, put }) {
      const data = yield call(drugPlanAndOrderApi.createOrder, payload);
      yield put({
        type: 'updateState',
        payload: {
          payParams: data || {},
        },
      });
      callback && callback(data);
    },
    *comfirmReceipt({ payload, callback }, { call }) {
      const res = yield call(drugPlanAndOrderApi.comfirmReceipt, payload);
      callback && callback(res);
    },
    *fetchGoodsDetail({ payload, callback }, { call, put }) {
      const data = yield call(drugPlanAndOrderApi.fetchGoodsDetail, payload);
      yield put({
        type: 'updateState',
        payload: {
          goodsDetail: data || {},
        },
      });
      callback && callback(data);
    },
    *cancelOrder({ payload, callback }, { call }) {
      const res = yield call(drugPlanAndOrderApi.cancelOrder, payload);
      callback && callback(res);
    },
    *BuyMedicineScheme({ payload, callback }, { call, put }) {
      const data = yield call(drugPlanAndOrderApi.BuyMedicineScheme, payload);
      yield put({
        type: 'updateState',
        payload: {
          buyMedicineList: data || {},
        },
      });
      callback && callback(data);
    },
    *MedicineSchemeHistory({ payload, callback }, { call, put }) {
      const data = yield call(drugPlanAndOrderApi.MedicineSchemeHistory, payload);
      yield put({
        type: 'updateState',
        payload: {
          buyMedicineHistoryList: data || {},
        },
      });
      callback && callback(data);
    },
    *schemeDetail({ payload, callback }, { call, put }) {
      const data = yield call(drugPlanAndOrderApi.schemeDetail, payload);
      yield put({
        type: 'updateState',
        payload: {
          schemeDetail: data || {},
        },
      });
      callback && callback(data);
    },
    *timeQuery({ payload, callback }, { call, put }) {
      const data = yield call(drugPlanAndOrderApi.timeQuery, payload);
      yield put({
        type: 'updateState',
        payload: {
          timeQuery: data || {},
        },
      });
      callback && callback(data);
    },
    *logisticsInfo({ payload, callback }, { call, put }) {
      const data = yield call(drugPlanAndOrderApi.logisticsInfo, payload);
      yield put({
        type: 'updateState',
        payload: {
          logisticsInfo: data || {},
        },
      });
      callback && callback(data);
    },
    *queryMedicinesList({ payload, callback }, { call, put }) {
      const data = yield call(drugPlanAndOrderApi.queryMedicinesList, payload);
      yield put({
        type: 'updateState',
        payload: {
          queryMedicinesList: data || {},
        },
      });
      callback && callback(data);
    },
    *revoke({ payload, callback }, { call }) {
      console.log('revoke');
      const res = yield call(drugPlanAndOrderApi.revoke, payload);
      callback && callback(res);
    },
    *updataOrderStatus({ payload, callback }, { call }) {
      console.log('revoke');
      const res = yield call(drugPlanAndOrderApi.updataOrderStatus, payload);
      callback && callback(res);
    },
    *pharmacyUrl({ payload, callback }, { call }) {
      console.log('revoke');
      const res = yield call(drugPlanAndOrderApi.pharmacyUrl, payload);
      callback && callback(res);
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};

export default DrugPlanAndOrderModel;
