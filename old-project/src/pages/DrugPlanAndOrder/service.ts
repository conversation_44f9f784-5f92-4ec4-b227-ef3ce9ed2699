import request from '@/utils/request';

// const node = '/cloud/hxgy-mall';
const drug = '/cloud/hosplatcustomer';

/**
 * 订单列表
 * @param data
 */
export const fetchOrderList = async (data: object): Promise<object> =>
  request(`${drug}/mall/order/list`, {
    method: 'POST',
    data,
  });

/**
 * 订单详情      //预发部记得改回来，不能提交
 * @param data
 */
export const fetchOrderDetail = async (data: object): Promise<object> =>
  request('/cloud/hosplatcustomer/mall/order/detail', {
    method: 'POST',
    data,
  });
// export const fetchOrderDetail = async (data: object): Promise<object> =>
// request(`${API_HXYY_NEW}/cloud/hosplatcustomer/mall/order/detail`, {
//   method: 'POST',
//   data,
// });
/**
 * 结算页信息  改了接口域名，不能提交，
 * @param data
 */
// export const OrderCheckout = async (data: object): Promise<object> =>
//   request(`${API_YL}/hosplatcustomer/mall/order/checkout`, {
//     method: 'POST',
//     data,
//   });
// 预发部接口地址修改
export const OrderCheckout = async (data: object): Promise<object> =>
  request(`${API_HXYY_NEW}/cloud/hosplatcustomer/mall/order/checkout`, {
    method: 'POST',
    data,
  });
/**
 * 下单
 * @param data
 */
export const createOrder = async (data: object): Promise<object> =>
  request(`${drug}/mall/order/create`, {
    method: 'POST',
    data: {
      showOriginData: true,
      ...data,
    },
  });

/**
 * 确认收货
 * @param data
 */
export const comfirmReceipt = async (data: object): Promise<object> =>
  request(`${drug}/mall/order/receipt`, {
    method: 'POST',
    data: {
      showOriginData: true,
      ...data,
    },
  });

/**
 * 获取商店与商品信息列表
 * @param data
 */
export const fetchGoodsDetail = async (data: object): Promise<object> =>
  request(`${drug}/mall/goods/shop/detail`, {
    method: 'POST',
    data,
  });

/**
 * 取消订单
 * @param data
 */
export const cancelOrder = async (data: object): Promise<object> =>
  request(`${drug}/mall/order/cancel`, {
    method: 'POST',
    data: {
      showOriginData: true,
      ...data,
    },
  });

/**
 * 购药方案列表   特药要修改接口${drug}/prescription/schemeList
 * @param data
 */
export const BuyMedicineScheme = async (data: object): Promise<object> =>
  request(`${API_TEST}/prescription/outDrug/scheme/list`, {
    method: 'POST',
    data,
  });

/**
 * 购药方案历史记录
 * @param data
 */
export const MedicineSchemeHistory = async (data: object): Promise<object> =>
  request(`${API_TEST}/prescription/outDrug/scheme/history`, {
    method: 'POST',
    data,
  });

/** 查询清单详情 */
export const schemeDetail = async (data: object): Promise<object> =>
  request(`${API_HXYY_NEW}/cloud/prescription/outDrug/scheme/admission`, {
    method: 'POST',
    data,
  });

/** 物流方式信息表 */
export const logisticsInfo = async (data: object): Promise<object> =>
  request(`${API_YL}/hosplatcustomer/mall/ship/info`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
      // skipError: true,
    },
  });
/** 定时定点时间查询 */
export const timeQuery = async (data: object): Promise<object> =>
  request(`${API_YL}/hosplatcustomer/mall/ship/appoint/time`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
      // isSkipError: false,
      skipError: true,
    },
  });
/** 查询特药购药清单 */
export const queryMedicinesList = async (data: object): Promise<object> =>
  request(`${API_TEST}/prescription/drugs/purchase/detail`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
      // isSkipError: false,
      skipError: true,
    },
  });

/** 更新订单状态 */
export const updataOrderStatus = async (data: object): Promise<object> =>
  request(`${API_TEST}/prescription/drugs/purchase/updateSchemeSpecialDrugPurchaseStatus`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
      // isSkipError: false,
      skipError: true,
    },
  });
/** 撤销 */
export const revoke = async (data: object): Promise<object> =>
  request(`${API_TEST}/prescription/outDrug/scheme/revoke`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
      // isSkipError: false,
      skipError: true,
    },
  });

// 获取银海药房的地址
export const pharmacyUrl = async (data: object): Promise<object> =>
  request(`${API_TEST}/prescription/specialMedicine/masses/pharmacyUrl`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
      // isSkipError: false,
      skipError: true,
    },
  });
