import React, { FC, useEffect, useState } from 'react';
import { HxIcon } from '@/components';
import { Modal, Toast } from 'antd-mobile';
import { isHytPerson } from '@/utils/platform';
import AppScheme from '@/utils/AppScheme';
import { getOpenId, getToken } from '@/utils/parameter';
import { useSelector, Dispatch, connect, Loading, IDrugPlanAndOrderModelState } from 'umi';
import { StoreNameItem, DrugItem, VisitInfo, DrugPrice, OrderInfo, OrderTop } from '../components';
import { getCurEvnHref, openLocation, configWechatGetLocation } from '../tools';
import styles from './index.less';
import './index.css';

interface IProps {
  drugPlanAndOrder: IDrugPlanAndOrderModelState;
  dispatch: Dispatch;
  loading?: boolean;
  location?: {
    query?: {
      orderId?: string;
    };
  };
}

const position = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/mall-postion.png';
const positionGrey =
  'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/mall-position-grey.png';
const send = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/mall-send.png';
const connectImg = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/mall-connect.png';

const modalTitleStyle = {
  color: '#03081A',
  fontSize: '16px',
};

const modalContentStyle = {
  fontSize: '12px',
  color: '#989EB4',
  marginTop: '4px',
};
const OrderDetail: FC<IProps> = (props) => {
  // props
  const {
    dispatch,
    location: { query = {} },
    drugPlanAndOrder = {},
  }: any = props;
  const { orderId = '', tyOrderStatus = '', schemeId = '', drugStoreId = '' } = query;
  const { orderDetail = {}, schemeDetail = {} } = drugPlanAndOrder;

  const drugOrder = useSelector((store) => store.drugPlanAndOrder.orderDetail.order);
  console.log('drugPlanAndOrder', drugPlanAndOrder);
  const {
    itemDate = '',
    deptName = '',
    doctorName = '',
    patientName = '',
    diagnoseDesc = '',
    prescUrl = '',
    status = 1,
  } = schemeDetail;
  const { order = {}, paymentOrder = {}, products = [], shop = {}, customerService = {}, refundOrders = [] } =
    orderDetail || {};
  const {
    orderStatus = 0,
    consignee = '',
    actualPrice = 0,
    mobile = '',
    address: receiveAddress = '',
    shippingType = 1,
    freightPrice = 0,
    addTime = '',
    payTime = '',
    // payId = '',
    shippingNo = '',
    valueInsured = '',
    valueInsuredFee = '',
    remainingTime = 0,
    goodsPrice = 0,
    orderStatusNotice = '',
    orderStatusDesc = '',
  } = order || {};
  const { desc = '', telephone = '' } = customerService;
  const { bizSysSeq = '', dealSeq = '', merchantSeq = '', bankTradeNo = '', payMethod = '' } = paymentOrder;
  const { address: shopAddress = '', name = '' } = shop;
  const info = { itemDate, doctorName, deptName, patientName, diagnoseDesc, prescUrl };
  const [modalVisible, setModalVisible] = useState(false);
  const [wechatDistance, setWechatDistance] = useState<any>(0);
  //  const [visible, setVisible] = useState<boolean>(false);
  // 订单信息
  const orderInfo = {
    addTime,
    orderId,
    payTime,
    bankTradeNo,
    payMethod,
    orderStatus,
  };

  // 调用微信sdk，获取微信配置信息
  const fetchConfigData = (shopResInfo: any) => {
    dispatch({
      type: 'global/singWechatJSSDK',
      payload: {
        url: getCurEvnHref(),
      },
      callback: (data: any) => {
        const configData = {
          ...data,
          debug: false,
          jsApiList: ['getLocation'],
        };
        // configWechatGetLocation(configData, shopResInfo);
        configWechatGetLocation(configData, shopResInfo)
          .then(function (res) {
            setWechatDistance(res);
          })
          .catch(function (err) {
            console.log('promiseerr=====', err);
          });
      },
    });
  };

  // 查询购药方案
  const fetchDrugPlan = () => {
    console.log('购药方案');
    dispatch({
      type: 'drugPlanAndOrder/fetchOrderDetail',
      payload: {
        orderId,
      },
      callback: (data: any) => {
        const { shop: shopInfo = {} } = data || {};
        fetchConfigData(shopInfo);
      },
    });
  };
  useEffect(() => {
    if (drugOrder?.bizOrderCode) {
      dispatch({
        type: 'drugPlanAndOrder/schemeDetail',
        payload: {
          // schemeId: bizOrderCode,
          schemeId: drugOrder?.bizOrderCode,
        },
      });
      // fetchDetail(drugOrder?.bizOrderCode)
    }
  }, [drugOrder?.bizOrderCode]);

  /** 打开百度地图 */
  const getMap = () => {
    dispatch({
      type: 'global/singWechatJSSDK',
      payload: {
        url: getCurEvnHref(),
      },
      callback: (data: any) => {
        const configData = {
          ...data,
          debug: false,
          jsApiList: ['getLocation', 'openLocation'],
        };
        openLocation(configData, shop);
      },
    });
  };

  // 顶部状态展示信息
  const statusInfo = {
    orderStatus,
    prescUrl,
    payId: shippingNo,
    remainingTime,
    fetchDrugPlan,
    orderStatusNotice,
    orderStatusDesc,
    status,
  };

  const goodsInfo = [
    {
      title: '商品金额',
      price: goodsPrice,
      show: true,
      isRefundShow: refundOrders.filter((item: any) => item?.refundType === 3).length !== 0,
      id: 0,
    },
    {
      title: '运费',
      price: freightPrice,
      show: shippingType === 1,
      isRefundShow: refundOrders.filter((item: any) => item?.refundType === 2).length !== 0,
      id: 1,
    },
    {
      title: '物流保价费《保价说明》',
      price: valueInsuredFee,
      show: valueInsured === 1,
      isRefundShow: refundOrders.filter((item: any) => item?.refundType === 3).length !== 0,
      id: 2,
    },
  ];

  useEffect(() => {
    fetchDrugPlan();
  }, [wechatDistance]);
  // 更新订单状态
  const updateStatus = () => {
    const params = {
      schemeId,
      status: tyOrderStatus,
      drugStoreId,
    };
    dispatch({
      type: 'drugPlanAndOrder/updataOrderStatus',
      payload: params,
    });
  };

  /** 点击去支付按钮 */
  const toPayBtn = () => {
    if (isHytPerson()) {
      AppScheme.toPay({ dealSeq, bizSysSeq, merchantSeq });
      // 更改订单状态
      updateStatus();
    } else {
      // 更改订单状态
      updateStatus();
      window.location.href = `${API_BASE}/cloud/paygateway/getCashier?bizSysSeq=${bizSysSeq}&dealSeq=${dealSeq}&merchantSeq=${merchantSeq}&accessToken=${getToken()}&openid=${getOpenId()}`;
    }
    // 新需求去掉弹框-9.27号
    // Modal.alert(
    //   <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
    //     <HxIcon iconName="tips" style={{ width: '24px', height: '24px' }} />
    //     <div style={{ marginLeft: '4px', fontSize: '#03081A', fontWeight: 600 }}>确认支付</div>
    //   </div>,
    //   <div style={{ color: '#03081A' }}>本订单为外购药品，需要自行付费，是否可报销以实际报销情况为准。</div>,
    //   [
    //     {
    //       text: <div style={{ color: '#03081A' }}>再想一想</div>,
    //     },
    //     {
    //       text: <div style={{ color: '#3AD3C1' }}>确认</div>,
    //       onPress: () => {
    //         if (isHytPerson()) {
    //           AppScheme.toPay({ dealSeq, bizSysSeq, merchantSeq });
    //           // 更改订单状态
    //           updateStatus();
    //         } else {
    //           // 更改订单状态
    //           updateStatus();
    //           window.location.href = `${API_BASE}/cloud/paygateway/getCashier?bizSysSeq=${bizSysSeq}&dealSeq=${dealSeq}&merchantSeq=${merchantSeq}&accessToken=${getToken()}&openid=${getOpenId()}`;
    //         }
    //       },
    //     },
    //   ],
    // );
  };

  const cancelPress = () => {
    dispatch({
      type: 'drugPlanAndOrder/cancelOrder',
      payload: {
        orderId,
      },
      callback: (res: any) => {
        if (res?.code === '1') {
          Toast.info('取消订单成功', 1.5);
          setTimeout(() => {
            fetchDrugPlan();
          }, 1500);
        }
      },
    });
  };

  // 取消订单
  const cancel = () => {
    Modal.alert(
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        <HxIcon iconName="tips" style={{ width: '24px', height: '24px' }} />
        <div style={{ marginLeft: '4px', fontSize: '#03081A', fontWeight: 600 }}>取消订单</div>
      </div>,
      <div style={{ color: '#03081A' }}>您真的要取消这笔订单吗？</div>,
      [
        {
          text: <div style={{ color: '#3AD3C1' }}>再想一想</div>,
        },
        {
          text: <div style={{ color: '#03081A' }}>确认</div>,
          onPress: () => cancelPress(),
        },
      ],
    );
  };
  return (
    <div className={styles.container}>
      <OrderTop statusInfo={statusInfo} />
      {/* 用户信息部分 */}
      {/* 物流配送 */}
      {shippingType === 1 && (
        <div className={styles.userInfo}>
          <div className={styles.user}>
            <img src={position} alt="" className={styles.icon} />
            <div>
              <span>{consignee}</span>
              <span>{mobile.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')}</span>
            </div>
          </div>
          <div className={styles.address}>{receiveAddress}</div>
        </div>
      )}
      {/* 线下自取 */}
      {shippingType === 2 && (
        <div className={styles.userInfoOff}>
          <div className={styles.left}>
            {/* <div>{fetchCurrentLocation()}km</div> */}
            <div>{wechatDistance}km</div>
            <div>
              <img src={positionGrey} alt="" className={styles.leftIcon} />
              {shopAddress}
            </div>
          </div>
          <div className={styles.right} onClick={() => getMap()}>
            <img src={send} alt="" className={styles.rightIcon} />
            <div>导航去</div>
          </div>
        </div>
      )}
      {shippingType === 2 && (
        <div className={styles.method}>
          <div>预留电话</div>
          <div>{mobile}</div>
        </div>
      )}
      <StoreNameItem storeName={name} />
      {products.length > 0 &&
        products.map((item: any) => {
          return (
            <div className={styles.item} key={item?.productId}>
              <DrugItem drugInfo={item} />
              <div className={styles.line} />
            </div>
          );
        })}
      {/* {visible &&  */}
      <VisitInfo info={info} pageType="orderDetail" />
      {/* } */}
      {/* 说是暂时隐藏展开收起的功能，直接展示出来 */}
      {/* <div className={styles.expandBtn}>
        <div className={styles.btn} onClick={() => setVisible(!visible)}>
          <div>{visible ? '收起' : '展示诊断信息'}</div>
          <HxIcon iconName={visible ? 'appointment-arrow-up' : 'appointment-arrow-down'} className={styles.arrow} />
        </div>
      </div> */}
      <div className={styles.method}>
        <div>配送方式</div>
        <div>{shippingType === 1 ? '物流配送' : shippingType === 2 ? '到店自取' : '定点定时配送'}</div>
      </div>
      <DrugPrice goodsInfo={goodsInfo} totalPrice={actualPrice} fetchDrugPlan={fetchDrugPlan} />
      <OrderInfo orderInfo={orderInfo} />
      {/* 新加的需求 4.21 */}
      {refundOrders.length !== 0 && (
        <div className={styles.orderInfo}>
          {refundOrders.map((item: any) => {
            return (
              <div key={item?.refundBankTradeNo}>
                <div>退款时间：{item?.refundTime}</div>
                <div>退款金额：{item?.refundAmount}</div>
              </div>
            );
          })}
        </div>
      )}
      {/* {orderStatus !== 0 && ( */}
      <div className={styles.connectService} onClick={() => setModalVisible(true)}>
        <img src={connectImg} alt="" className={styles.connectIcon} />
        联系客服
      </div>
      {/*  )} */}
      <div style={{ height: '80px' }} />
      {orderStatus === 0 && (
        <div className={styles.bottom}>
          <div className={styles.payPrice}>
            <span>￥</span>
            {/* <span>{actualPrice}</span> */}

            {parseInt(actualPrice, 10) === actualPrice ? (
              actualPrice
            ) : (
              <>
                {parseInt(actualPrice, 10)}
                <span>.{actualPrice.toString().replace(/\d+\.(\d*)/, '$1')}</span>
              </>
            )}
            {/* 小数点之前或者不存在小数点的 */}
            {/* <span>{actualPrice.indexOf('.') !== -1 ? actualPrice.split('.')[0] : actualPrice}</span> */}
            {/* 小数点之后的 */}
            {/* <span>{actualPrice.indexOf('.') !== -1 && actualPrice.split('.')[1]}</span> */}
          </div>
          <div className={styles.btnBox}>
            <div className={styles.cancelBtn} onClick={() => cancel()}>
              取消订单
            </div>
            <div className={styles.payBtn} onClick={() => toPayBtn()}>
              去支付
            </div>
          </div>
        </div>
      )}
      {/* 定点配送 */}
      {/* <Modal
        popup
        className="selTimemodal"
        // visible={tipsModal}
        transparent
        maskClosable={false}
        animationType="slide-up"
        footer={[
          {
            text: '确定',
            onPress: () => {
              // goConfirm();
            },
          },
        ]}
      >
        <div className="modalContent">
          <div className="selectTime">
            <div> 选择时间</div>
            <div> <img src={tipsIcon} alt=""  /> </div>
          </div>
          <div className="distributionInfo sendAddress">
            <div className="title">配送点</div>
            <div>武侯区国学巷37号-华西医院南门</div>
          </div>
          <div className="distributionInfo sendTime">
            <div className="title">送货时间</div>
            <div className="timeList">
              <div className="date">
                <div className="activeDate">7月10日[周六]</div>
                <div>7月10日[周六]</div>
                <div>7月10日[周六]</div>
                <div>7月10日[周六]</div>
                <div>7月10日[周六]</div>
                <div>7月10日[周六]</div>
                <div>7月10日[周六]</div>
                <div>7月10日[周六]</div>
                <div>7月10日[周六]</div>
                <div>7月10日[周六]</div>
                <div>7月10日[周六]</div>
                <div>7月10日[周六]</div>
              </div>
              <div className="time">
                <div className="activeTime">09:00 - 10:00</div>
                <div>09:00 - 10:00</div>
                <div>09:00 - 10:00</div>
                <div>09:00 - 10:00</div>
              </div>
            </div>
          </div>
        </div>
      </Modal> */}

      <Modal
        visible={modalVisible}
        transparent
        maskClosable
        onClose={() => setModalVisible(false)}
        title="联系客服"
        footer={[
          {
            text: <div style={{ color: '#3AD3C1' }}>立即拨打</div>,
            onPress: () => {
              window.location.href = `tel: ${telephone}`;
            },
          },
        ]}
      >
        <div>
          <div style={modalTitleStyle}>客服电话：{telephone}</div>
          <div style={modalContentStyle}>{desc}</div>
        </div>
      </Modal>
    </div>
  );
};

export default connect(
  ({ drugPlanAndOrder, loading }: { drugPlanAndOrder: IDrugPlanAndOrderModelState; loading: Loading }) => ({
    drugPlanAndOrder,
    loading: loading.effects['drugPlanAndOrder/fetchOrderDetail'],
  }),
)(OrderDetail);
