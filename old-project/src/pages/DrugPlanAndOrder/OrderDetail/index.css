.selTimemodal {
  width: 100%;
}

.modalContent {
  display: flex;
  flex-direction: column;

}

.modalContent .selectTime {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
}

.selectTime div:first-child {
  flex: 1;
  font-size: 40px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  color: #03081A;
}

.distributionInfo {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-size: 28px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #03081A;
  line-height: 40px;
  padding-left: 24px;


}

.sendAddress {
  padding-bottom: 24px;
  border-bottom: 2px solid #EBEDF5;
  margin-bottom: 20px;
}


.distributionInfo .title {
  font-size: 24px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #989EB4;
  line-height: 34px;
  padding-bottom: 4px;
}

.modalContent .timeList {
  display: flex;
  margin-top: 24px;
  width: 100%;

}

.timeList .date,
.timeList .time {
  font-size: 28px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #03081A;
  background: #F5F6FA;

}

.timeList .time {
  background-color: #ffffff;
  width: 100%;
  flex: 1;
  overflow-y: scroll;
  height: 544px;
  margin-left: 14px;
}

.timeList .date div,
.timeList .time div {
  width: 232px;
  height: 80px;
  line-height: 80px;
  text-align: left;
}

.timeList .time div {
  font-weight: 600;
  padding-left: 24px;
  flex: 1;
  width: 100%;


}

.selTimemodal .am-modal-body,
.sendTime {
  padding-left: 0px !important;
  padding-bottom: 0px !important;
}

.selTimemodal .am-modal-content {
  border-radius: 16px 16px 0px 0px !important;

}

.sendTime .date {
  overflow-y: scroll;
  height: 544px;
  background: #F5F6FA;
  width: 232px;

}

.date div,
.sendTime .title {
  padding-left: 24px;
}

.selTimemodal .am-modal-button {
  font-size: 28px;
  font-family: PingFangSC-Semibold, PingFang SC;
  font-weight: 600;
  height: 72px;
  background: #3AD3C1;
  border-radius: 40px;
  color: #fff !important;
  line-height: 72px;
  border: none;
}

.selTimemodal .am-modal-footer {
  padding: 12px 22px;
  border-top: 2px solid #EBEDF5
}

.activeDate {
  font-weight: 600;
  color: #3AD3C1;
  background-color: #fff;
}

.activeTime {
  border: 1px solid #3AD3C1;
  color: #3AD3C1;
  border-radius: 4px;
  background: #F3FFFE;
  border-radius: 8px;
}
