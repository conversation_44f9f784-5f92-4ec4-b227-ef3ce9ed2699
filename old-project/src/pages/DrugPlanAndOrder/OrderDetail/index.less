.container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f6fa;
  .userInfoOff {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding: 46px 24px 24px 24px;
    background-color: #fff;
    .left {
      :nth-child(1) {
        margin-bottom: 6px;
        color: #03081a;
        font-weight: 600;
        font-size: 36px;
      }
      :nth-child(2) {
        display: flex;
        align-items: flex-end;
        color: #989eb4;
        font-size: 24px;
        .leftIcon {
          position: relative;
          top: 2px;
          width: 28px;
          height: 28px;
          margin-right: 4px;
        }
      }
    }
    .right {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 0 40px 0 62px;
      color: #03081a;
      font-size: 24px;
      border-left: 2px solid #ebedf5;
      .rightIcon {
        width: 48px;
        height: 48px;
      }
      div {
        margin-top: 8px;
      }
    }
  }
  .item {
    display: flex;
    flex-direction: column;
    background-color: #fff;
    .line {
      width: 702px;
      height: 1px;
      margin: auto;
      background-color: #ebedf5;
    }
  }
  .userInfo {
    margin-bottom: 24px;
    padding: 46px 24px 24px 24px;
    background-color: #fff;
    .user {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      color: #03081a;
      font-weight: 600;
      font-size: 36px;
      .icon {
        // width: 28px;
        height: 40px;
        margin-right: 10px;
      }
      div {
        :nth-child(1) {
          margin-right: 48px;
        }
      }
    }
    .address {
      margin-top: 20px;
      color: #989eb4;
      font-size: 28px;
    }
  }
  .expandBtn {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px 0;
    color: #03081a;
    font-size: 24px;
    background-color: #fff;
    .btn {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 4px 24px;
      border: 2px solid #b0b3bf;
      border-radius: 40px;
      .arrow {
        width: 28px;
        height: 28px;
        margin-left: 8px;
      }
    }
  }
  .method {
    display: flex;
    justify-content: space-between;
    margin: 24px 0;
    padding: 32px 24px;
    color: #03081a;
    font-size: 32px;
    background-color: #fff;
  }

  .connectService {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 24px 0;
    padding: 32px 0;
    color: #03081a;
    font-size: 32px;
    background-color: #fff;
    .connectIcon {
      width: 48px;
      height: 48px;
      margin-right: 8px;
    }
  }
  .bottom {
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 20px 24px;
    background-color: #fff;
    box-shadow: 0 0 8px 0 rgba(235, 237, 245, 1);
    .payPrice {
      color: #fc4553;
      font-weight: 600;
      font-size: 56px;
      :nth-child(1) {
        font-size: 36px;
      }
      :nth-child(2) {
        font-size: 40px;
      }
    }
    .btnBox {
      display: flex;
      justify-content: flex-start;
      div {
        width: 192px;
        height: 72px;
        font-size: 28px;
        line-height: 72px;
      }
      .payBtn {
        color: #fff;
        font-weight: 600;
        text-align: center;
        background-color: #3ad3c1;
        border-radius: 40px;
      }
      .cancelBtn {
        margin-right: 24px;
        color: #03081a;
        font-weight: 600;
        text-align: center;
        border: 2px solid #989eb4;
        border-radius: 40px;
      }
    }
  }
  .orderInfo {
    margin-top: 24px;
    padding: 1px 24px;
    padding-bottom: 28px;
    color: #03081a;
    font-size: 28px;
    background-color: #fff;
    div {
      margin-top: 28px;
    }
  }
}

.container::-webkit-scrollbar {
  display: none;
}
