import React, { FC } from 'react';
import { connect } from 'dva';
import styles from './index.less';
import { DrugItem } from '../components';

interface IProps {
  drugList: Array<any>;
}

const DrugList: FC<IProps> = ({ drugList = [] }) => {
  return (
    <div className={styles.container}>
      <div className={styles.num}>共{drugList.length}件</div>
      {drugList.map((item, index) => {
        return (
          <div key={index.toString()} className={styles.item}>
            <DrugItem drugInfo={item} />
          </div>
        );
      })}
    </div>
  );
};

export default connect(({ drugPlanAndOrder }: { drugPlanAndOrder: any }) => ({
  ...drugPlanAndOrder,
}))(DrugList);
