.container {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  background-color: #f5f6fa;

  .item {
    margin-bottom: 24px;
    padding: 0px 24px;

    &:first-child {
      padding-top: 24px;
    }
  }

  .btnView {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    height: 82px;
    padding: 12px 24px 0 0;
    background-color: #fff;
    border-radius: 16px;
    height: auto;

    .btn {
      width: 192px;
      height: 68px;
      margin-left: 24px;
      font-size: 28px;
      line-height: 68px;
      text-align: center;
      background-color: #fff;
      border-radius: 40px;
      padding-bottom: 24px;
    }

    .greenBtn {
      background: #3AD3C1;
    }

    .grayBtn {
      color: #03081a;
      border: 2px solid #b0b3bf;
    }
  }

  :global(.am-list-view-scrollview) {
    height: calc(100vh - 20px);
  }

  :global(.am-list-view-scrollview) {
    overflow-x: hidden !important;
  }
}

:global(.am-list-body) {
  background-color: #f5f6fa;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
}

// ::-webkit-scrollbar {
//   display: none;
// }
