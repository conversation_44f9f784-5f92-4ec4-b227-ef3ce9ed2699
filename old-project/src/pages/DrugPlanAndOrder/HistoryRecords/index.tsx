import React, { FC, useEffect, useState } from 'react';
import { connect } from 'dva';
import { Dispatch, IDrugPlanAndOrderModelState, Loading, history } from 'umi';
import { HxListView, HxIndicator } from '@/components';
import { Modal, Toast } from 'antd-mobile';
import { HxParameter } from '@/utils/parameter';
import styles from './index.less';
import { DrugItem, MoreDrug, VisitInfo, StoreNameItem, NoDataView } from '../components';
import DrugStories from '../components/DrugStories/index';

interface IProps {
  drugPlanAndOrder: IDrugPlanAndOrderModelState;
  dispatch: Dispatch;
  loading?: Boolean;
}
const noDrugPlan = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/no-drug-plan.png';

const HistoryRecords: FC<IProps> = (props) => {
  const { dispatch, loading } = props;
  const [list, setList] = useState<Array<any>>([]);
  const [pageNum, setPageNum] = useState(1);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [backModal, setRepealModal] = useState<boolean>(false);
  const { appCode = '', channelCode = '', token = '' } = HxParameter;
  const [schemeId, setSchemeId] = useState<any>();
  const [drugStoreId, setDrugStoreId] = useState<any>();

  const fetchList = (num: number) => {
    dispatch({
      type: 'drugPlanAndOrder/MedicineSchemeHistory',
      payload: {
        pageNum: num,
        pageSize: 5,
        query: {},
      },
      callback: (buyMedicineHistory: any) => {
        const { content = [], totalPages = 0 } = buyMedicineHistory || {};

        setList([...content, ...list]);
        setIsLoading(false);
        setPageNum(num);
        if (num >= totalPages) {
          setHasMore(false);
        }
      },
    });
  };

  useEffect(() => {
    fetchList(1);
  }, []);

  /** 加载后一页 */
  const loadMore = () => {
    if (isLoading || !hasMore) {
      return;
    }
    setIsLoading(false);
    setTimeout(() => {
      fetchList(pageNum + 1);
    }, 200);
  };
  const onLoadMore = () => {
    /** 上拉至底触发 */
    !isLoading && loadMore();
  };
  // 更新订单状态
  const updateStatus = (status) => {
    const params = {
      schemeId,
      status,
      drugStoreId,
    };
    dispatch({
      type: 'drugPlanAndOrder/updataOrderStatus',
      payload: params,
    });
  };

  // 撤销订单
  const confirm = () => {
    console.log(8888);
    // 跳转购药方案
    history.push('/drugplanandorder/home?tabPage=0');
    updateStatus(0);
  };

  const renderHistoryItem = (item: any = {}, index: any) => {
    console.log('item', item);
    const {
      admDate = '',
      doctorName = '',
      deptName = '',
      patientName = '',
      diagnoseDesc = '',
      drugInfoList = {},
      status = 0,
      orderId = '',
      prescType = '',
      drugList = [],
      expirationTime = '',
      itemDate,
      prescUrl,
    } = item;
    console.log('77777item[0]', item);

    // const {
    //   // drugList = [],
    //   // drugStoreName = '',
    //   // totalPrice = 0,
    // } = drugInfoList[0];
    const info = {
      itemDate: admDate || itemDate,
      doctorName,
      deptName,
      patientName,
      expirationTime,
      diagnoseDesc,
      prescUrl,
    };
    console.log('drugList[0]', item, drugList, drugList[0]);
    // const {
    // company = '',
    // doctorRemark = '',
    // drugDesc = '',
    //   itemDesc = '',
    //   standards = '',
    //   totalQty = '',
    //   itemImageUrl = '',
    //   drugPrice = '',
    // } = drugList[0];
    // const drugInfo = {
    //   specificationValue: standards,
    //   goodsName: itemDesc,
    //   goodsUnit: '',
    //   listPicUrl: itemImageUrl,
    //   number: totalQty,
    //   retailPrice: drugPrice,
    // };
    // 药房数量
    const size = drugInfoList?.length;
    const drugStoreInfo = {
      prescType,
      status,
      size,
      // drugStoreName,
      // totalPrice,
    };
    // 传递到配送方式页面的数据
    const toDistributeData = drugList.map((item: any) => {
      return { productId: item?.productId, amount: Number(item?.totalQty) };
    });
    const btnClick = (item) => {
      console.log(item, prescType, status, 'orderType');
      const drugStoreId = item?.drugInfoList[0]?.drugStoreId;
      const schemeId = item?.drugInfoList[0]?.schemeId;
      if (prescType === 2) {
        if (status === 1) {
          // 显示撤销弹框
          setRepealModal(true);
          setSchemeId(schemeId);
          setDrugStoreId(drugStoreId);
        } else if (status === 4) {
          // 填写订单页面
          const bizSysSeq = list[0]?.drugInfoList[0]?.bizSysSeq;
          window.location.href = `${
            window.location.origin
          }/person/drugplanandorder/distribution?products=${JSON.stringify(
            toDistributeData,
          )}&shopId=${drugStoreId}&bizSysSeq=${bizSysSeq}&schemeId=${schemeId}&drugStoreId=${drugStoreId}&status=5`;
        } else if (status === 2 || status === 3) {
          // 联系医生
          window.location.href = `${API_ZXMZ}/online/history?client=dzapp&type=yxzximage&organCode=HID0101&appCode=${appCode}&channelCode=${channelCode}&token=${token}`;
        } else if (status === 5 || prescType === 3) {
          // 查看订单
          window.location.href = `${window.location.origin}/person/drugplanandorder/detail?orderId=${orderId}`;
        }
      } else {
        // 查看订单
        window.location.href = `${window.location.origin}/person/drugplanandorder/detail?orderId=${orderId}`;
      }
    };

    return (
      <div key={index.toString()} className={styles.item}>
        <VisitInfo info={info} status={status} prescriptionDetail={drugList} />

        {/* 药房 */}
        <DrugStories drugStoreInfo={drugStoreInfo} btnClick={btnClick} type="1" />
      </div>
    );
  };

  return (
    <div className={styles.container}>
      {loading && list.length === 0 ? (
        <div className={styles.loading}>
          <HxIndicator />
        </div>
      ) : list.length === 0 ? (
        <NoDataView text="暂无历史记录" imgUrl={noDrugPlan} />
      ) : (
        <>
          <HxListView
            dataSource={list}
            renderRow={renderHistoryItem}
            initialListSize={10}
            pageSize={10}
            onEndReached={() => onLoadMore()}
            onEndReachedThreshold={50}
            isRenderFooter
            hasMore={hasMore}
          />
          <Modal
            className="tipsmodal"
            visible={backModal}
            transparent
            maskClosable={false}
            animationType="slide-up"
            title="购药撤销"
            footer={[
              {
                text: '确认',
                onPress: () => {
                  confirm();
                },
              },
              {
                text: '再想一想',
                onPress: () => {
                  setRepealModal(false);
                },
              },
            ]}
          >
            <div className={styles.explainContent}>请确认是撤销该药房的购药需求</div>
          </Modal>
        </>
      )}
    </div>
  );
};

export default connect(({ drugPlanAndOrder, loading }: { drugPlanAndOrder: any; loading: Loading }) => ({
  loading: loading.effects['drugPlanAndOrder/MedicineSchemeHistory'],
  ...drugPlanAndOrder,
}))(HistoryRecords);
