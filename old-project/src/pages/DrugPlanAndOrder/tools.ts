import { SingWechatJSSDKDataType } from '@/typings/global';

interface ConfigDataType extends SingWechatJSSDKDataType {
  /** 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。 */
  debug: boolean;

  /** 必填，需要使用的JS接口列表 (以逗号分割) */
  jsApiList: string[];
}

const { wx } = window;

/** 通过经纬度计算两地之间的距离 */
const getDistance = (lat1: number, lng1: number, lat2: number, lng2: number) => {
  console.log(lat1, lng1, lat2, lng2);
  const radLat1 = (lat1 * Math.PI) / 180.0;
  const radLat2 = (lat2 * Math.PI) / 180.0;
  const a = radLat1 - radLat2;
  const b = (lng1 * Math.PI) / 180.0 - (lng2 * Math.PI) / 180.0;
  let s =
    2 *
    Math.asin(
      // eslint-disable-next-line no-restricted-properties
      Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)),
    );
  s *= 6378.137;
  s = Math.round(s * 10000) / 10000;
  return s.toFixed(2); // 单位千米
};

const changebd09togcj02 = (bd_lon: any, bd_lat: any) => {
  const x_PI = (3.14159265358979324 * 3000.0) / 180.0;
  const x = bd_lon - 0.0065;
  const y = bd_lat - 0.006;
  const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_PI);
  const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_PI);
  const gg_lng = z * Math.cos(theta);
  const gg_lat = z * Math.sin(theta);
  return [gg_lng, gg_lat];
};

// 获取用户当前位置经纬度信息
const configWechatGetLocation = (configData: ConfigDataType, shop: any) => {
  const { debug, appId, timestamp, nonceStr, signature, jsApiList = [] } = configData;
  const { latitude: shopLatitude = 0, longitude: shopLongitude = 0 } = shop;
  return new Promise(function (resolve, reject) {
    wx.config({
      debug,
      appId,
      timestamp,
      nonceStr,
      signature,
      jsApiList,
    });
    wx.ready(() => {
      // 获取用户当前地理位置经纬度信息
      wx.getLocation({
        type: 'wgs84', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
        success: (res: any) => {
          const { latitude = 0, longitude = 0 } = res;
          const distance = getDistance(
            Number(latitude),
            Number(longitude),
            Number(shopLatitude),
            Number(shopLongitude),
          );
          resolve(distance);
          localStorage.setItem('weiChatCurrentLocation', JSON.stringify(res));
          localStorage.setItem('distance', distance);
        },
        cancel: (err: any) => {
          console.log('用户拒绝授权获取地理位置', err);
          reject(new Error('用户拒绝授权获取地理位置'));
        },
      });
    });
    wx.error((err: any) => {
      console.log('configWechatGetLocation-err', err);
    });
  });
};

// 打开地图
const openLocation = (configData: ConfigDataType, locationInfo: any) => {
  const { debug, appId, timestamp, nonceStr, signature, jsApiList = [] } = configData;
  const { name, address, province, city, district, latitude, longitude } = locationInfo;
  const adressArr = changebd09togcj02(Number(longitude), Number(latitude));
  wx.config({
    debug,
    appId,
    timestamp,
    nonceStr,
    signature,
    jsApiList,
  });
  wx.ready(() => {
    wx.getLocation({
      type: 'wgs84', // 默认为 wgs84 返回 gps 坐标，gcj02 返回可用于 wx.openLocation 的坐标
      success: (res: any) => {
        // success
        console.log(res);
        // 打开地图
        wx.openLocation({
          latitude: Number(adressArr[1]), // 纬度，浮点数，范围为90 ~ -90
          longitude: Number(adressArr[0]), // 经度，浮点数，范围为180 ~ -180。
          name: `${address}${name}`, // 位置名
          address: `${province}${city}${district}${address}`, // 地址详情说明
          scale: 16, // 地图缩放级别,整形值,范围从1~28。默认为最大
          infoUrl: '', // 在查看位置界面底部显示的超链接,可点击跳转
          cancel: (err: any) => {
            console.log('用户拒绝授权获取地理位置', err);
          },
        });
      },
    });
  });
  wx.error((err: any) => {
    console.log('configWechatGetLocation-err', err);
  });
};

/** 获取当前环境下的当前路由(过滤token) */
const getCurEvnHref = () => {
  // return THE_DOMAIN.split('/person')[0] + window.location.pathname + window.location.search;
  // return `${COMMON_DOMAIN}${window.location.pathname}${window.location.search}`;
  // return window.location.href.split('#')[0];

  // if (navigator.userAgent.match(/android/i)) {
  const thisPageUrl = THE_DOMAIN.split('/person')[0] + window.location.pathname + window.location.search;
  // return thisPageUrl.replaceAll('&', '%26');
  return thisPageUrl;
};

export { getDistance, configWechatGetLocation, getCurEvnHref, openLocation };
