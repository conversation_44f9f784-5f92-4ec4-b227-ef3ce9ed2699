import React, { <PERSON> } from 'react';
import styles from './index.less';

interface IProps {
  storeName: string;
  statusText?: string;
  textColor?: string;
}

const storeIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/store-icon.png';

const StoreNameItem: FC<IProps> = ({ storeName = '', statusText = '', textColor = '' }) => {
  return (
    <div className={styles.container}>
      <div className={styles.left}>
        <img src={storeIcon} alt="" />
        <div>{storeName}</div>
      </div>
      <div className={styles.right} style={{ color: textColor || '#989EB4' }}>
        {statusText}
      </div>
    </div>
  );
};

export default StoreNameItem;
