.mainContent {
  display: flex;
  flex-direction: column;
  position: relative;
  font-size: 24px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #989EB4;
  // border-top: 1px solid #D2D6E5;
  padding-top: 24px;

  .gapLine {
    position: absolute;
    height: 2px;
    background: #D2D6E5;
    left: 0px;
    top: 0px;
    width: 604px;

  }

  div .preicon {
    width: 28px;
    height: 28px;
    vertical-align: sub;
    padding-right: 4px;

  }

  .item {
    // display: flex;
    // justify-content: space-between;
    color: #03081A;
    margin-bottom: 0px !important;

    span {

      &:first-child {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 348px;

      }

      &:last-child {
        color: #989EB4;
        display: inline;
        float: right;

      }
    }
  }
}
