import React, { FC } from 'react';
import styles from './index.less';

interface IProps {
  prescriptionDetail: any;
}

const drugBottle = require('@/assets/speMedicine/drugBottle.png');

const Prescription: FC<IProps> = ({ prescriptionDetail = [], status }) => {
  console.log('555prescriptionDetail', prescriptionDetail);
  return (
    <div className={styles.mainContent}>
      <div className={styles.gapLine} />
      <div className={styles.drugIcon}>
        <img src={drugBottle} alt="" className={styles.preicon} />
        处方明细
      </div>
      {prescriptionDetail &&
        prescriptionDetail.map((item, index) => {
          return (
            <div className={styles.item} key={Number(index)}>
              <span>{item.drugName}</span>
              <span>
                {/* 规格 {item.strength} */}
                {item.specification}
                &nbsp;&nbsp;X{item.qty}
                {item.unit}
              </span>
            </div>
          );
        })}
    </div>
  );
};
export default Prescription;
