/* eslint-disable import/no-self-import */
import React, { FC, useEffect, useState } from 'react';
import { history, connect, Dispatch, IDrugPlanAndOrderModelState, Loading } from 'umi';
import { HxListView, HxIndicator } from '@/components';
import { Modal } from 'antd-mobile';
import { NoDataView, VisitInfo, StoreNameItem, DrugItem, MoreDrug } from '../index';

import styles from './index.less';
import DrugStories from '../DrugStories/index';

// import { getUrl } from '@/pages/ElectronReport/service';
// import Prescription from '@/pages/Prescription/model';
// import './index.tsx';

interface IProps {
  // planList: Array<any>;
  dispatch: Dispatch;
  loading?: boolean;
}

const noDrugPlan = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/no-drug-plan.png';
const tipsIcon = require('@/assets/speMedicine/tipsIcon.png');

const PlanList: FC<IProps> = (props) => {
  const { dispatch, loading } = props;
  const [planList, setPlanList] = useState<Array<any>>([]);
  const [pageNum, setPageNum] = useState(1);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasMore, setHasMore] = useState<boolean>(true);
  // const [orderType, setOrderType] = useState<string>(''); //  订单类型：1：普通 2：特药 3：外购药
  const [buyDrugModal, setBuyDrugModal] = useState<boolean>(false); // 特药购药弹窗
  const [schemeId, setSchemeId] = useState<string>('');
  const [drugStoreId, setDrugStoreId] = useState<string>('');
  const [backModal, setRepealModal] = useState<boolean>(false);
  const [underlDrugStore, setDrugStore] = useState<boolean>(false); //  线下药房模态框
  const [noticeId, setNoticeId] = useState<string>('');
  const [orderStatus, setOrderStatus] = useState<number>(1); //    订单状态

  // 查询购药方案
  const fetchDrugPlan = (num: number) => {
    dispatch({
      type: 'drugPlanAndOrder/BuyMedicineScheme',
      payload: {
        pageNum: num,
        pageSize: 5,
        query: {},
      },
      callback: (buyMedicineData: any) => {
        const { content = [], totalPages = 0 } = buyMedicineData || {};
        console.log('数据拼接buyMedicineData', planList, content);
        if (orderStatus === 0) {
          setPlanList([...content]);
        } else {
          console.log('order', orderStatus);
          setPlanList([...content, ...planList]);
        }

        setIsLoading(false);
        setPageNum(num);
        if (num >= totalPages) {
          setHasMore(false);
        }
      },
    });
  };

  /** 加载后一页 */
  const loadMore = () => {
    if (isLoading || !hasMore) {
      return;
    }
    setIsLoading(false);
    setTimeout(() => {
      fetchDrugPlan(pageNum + 1);
    }, 200);
  };

  const onLoadMore = () => {
    /** 上拉至底触发 */
    !isLoading && loadMore();
  };
  // 更新订单状态
  const updateStatus = (status) => {
    const params = {
      schemeId,
      status,
      drugStoreId,
    };
    dispatch({
      type: 'drugPlanAndOrder/updataOrderStatus',
      payload: params,
      callback: (res: any) => {
        const { code = '' } = res;
        if (code === '1') {
          setOrderStatus(0);
        }
      },
    });
  };
  // 购药
  const confirm = () => {
    // 跳转购药清单详情
    if (schemeId && drugStoreId) {
      history.push(`/drugplanandorder/buymedicinesingle?drugStoreId=${drugStoreId}&schemeId=${schemeId}`);
      setBuyDrugModal(false);
    }
  };

  useEffect(() => {
    fetchDrugPlan(1);
  }, []);
  useEffect(() => {
    fetchDrugPlan(1);
  }, [orderStatus]);

  const underDrugStore = () => {
    dispatch({
      type: 'drugPlanAndOrder/pharmacyUrl',
      payload: {
        groupId: noticeId,
      },
      callback: (res: any) => {
        // 获取银海药房地址
        if (res.code === '1') {
          const { data = '' } = res;
          window.location.href = `${data}`;
          setRepealModal(false);
        }
      },
    });
  };

  // 撤销
  const confBack = () => {
    if (underlDrugStore) {
      underDrugStore();
    } else {
      updateStatus(0);
      setRepealModal(false);
    }
  };

  const renderPlanItem = (item: any = {}, index: any) => {
    const {
      itemDate = '',
      doctorName = '',
      deptName = '',
      patientName = '',
      diagnoseDesc = '',
      drugInfoList = {},
      drugList = [],
      expirationTime = '',
      prescType,
      status,
      noticeId,
      prescUrl,
    } = item;
    const {
      drugStoreName = '',
      totalPrice = 0,
      drugStoreId = '',
      bizSysSeq = '',
      schemeId = '',
      logo = '',
      bizSubType = '', // 代表特药
    } = drugInfoList[0];
    const info = { itemDate, prescUrl, doctorName, deptName, patientName, diagnoseDesc, expirationTime };
    // const { itemDesc = '', standards = '', totalQty = '', itemImageUrl = '', drugPrice = '' } = drugList[0];
    // const drugInfo = {
    //   // specificationValue: standards,
    //   // goodsName: itemDesc,
    //   goodsUnit: '',
    //   // listPicUrl: itemImageUrl,
    //   // number: totalQty,
    //   // retailPrice: drugPrice,
    //   orderId,
    //   reviewStatus,
    //   status,
    //   prescType,
    // };
    // 药房数量
    const size = drugInfoList?.length;
    const drugStoreInfo = {
      prescType,
      status,
      size,
      drugStoreName,
      totalPrice,
      noticeId,
      drugInfoList,
      logo,
    };
    setNoticeId(noticeId);
    // 传递到配送方式页面的数据
    const toDistributeData = drugList.map((item: any) => {
      return { productId: item?.productId, amount: Number(item?.totalQty) };
    });
    // 去购买
    const btnClick = (selItem) => {
      console.log(prescType, 'orderType', status);
      if (selItem) {
        setSchemeId(selItem?.schemeId);
        setDrugStoreId(selItem?.drugStoreId);
      } else {
        setSchemeId(schemeId);
        setDrugStoreId(drugStoreId);
      }

      // 3是外购药，2特药
      if (prescType === 3) {
        window.location.href = `${
          window.location.origin
        }/person/drugplanandorder/distribution?products=${JSON.stringify(
          toDistributeData,
        )}&shopId=${drugStoreId}&bizSysSeq=${bizSysSeq}&schemeId=${schemeId}&bizSubType=${bizSubType}&drugStoreId=${drugStoreId}`;
      }
      // else if (prescType === 2 && status === 1) {
      //   // 撤销
      //   // setRepealModal(true);

      // }
      // 查看/去购买，都跳转药品清单详情
      else if (prescType === 2 && (status === 4 || status === 1)) {
        console.log(9999);
        // 特药去购买
        history.push(`/drugplanandorder/buymedicinesingle?drugStoreId=${drugStoreId}&schemeId=${schemeId}`);
      } else if (prescType === 2 && status === 0) {
        // 特药提交购药需求
        setBuyDrugModal(true);
      } else if (prescType === 2 && status === 7) {
        confirm();
      }
    };
    // 去线下药房
    const goOnline = () => {
      setDrugStore(true);
      setRepealModal(true);
    };
    return (
      <div key={index.toString()} className={styles.planItem}>
        <VisitInfo info={info} prescriptionDetail={drugList} />
        {/* <StoreNameItem storeName={drugStoreName} /> */}
        {/* {drugList.length === 1 ? (
          <DrugItem drugInfo={drugInfo} />
        ) : (
          <MoreDrug drugList={drugList} totalPrice={totalPrice} />
        )} */}
        {/* 药房 */}
        <DrugStories drugStoreInfo={drugStoreInfo} btnClick={btnClick} goOnline={goOnline} />
      </div>
    );
  };

  return (
    <div className={styles.planList}>
      <div className={styles.listView}>
        {loading && planList.length === 0 ? (
          <HxIndicator />
        ) : planList.length === 0 ? (
          <div>
            <NoDataView imgUrl={noDrugPlan} text="暂无购药方案" />
            <div className={styles.btnBox}>
              <div className={styles.btn} onClick={() => history.push('/drugplanandorder/historyrecords')}>
                历史记录
              </div>
            </div>
          </div>
        ) : (
          <>
            <HxListView
              dataSource={planList}
              renderRow={renderPlanItem}
              initialListSize={0}
              pageSize={10}
              onEndReached={() => onLoadMore()}
              onEndReachedThreshold={20}
              isRenderFooter={false}
              hasMore={hasMore}
            />
            <div className={styles.btnBox}>
              <div className={styles.btn} onClick={() => history.push('/drugplanandorder/historyrecords')}>
                历史记录
              </div>
            </div>
          </>
        )}
      </div>
      {/* <div className={styles.btnBox}>
        <div className={styles.btn} onClick={() => history.push('/drugplanandorder/historyrecords')}>
          历史记录
        </div>
      </div> */}
      <Modal
        className="tipsmodal"
        visible={buyDrugModal}
        transparent
        maskClosable={false}
        animationType="slide-up"
        footer={[
          {
            text: '再想一想',
            onPress: () => {
              setBuyDrugModal(false);
            },
          },
          {
            text: '确认',
            onPress: () => {
              confirm();
            },
          },
        ]}
      >
        <div className={styles.content}>
          <div className="tipsTitle">
            <img src={tipsIcon} alt="" />
            购药需求确认
          </div>
          提交后，药房将根据您的购药需求信息审方，请确认是否在该药房购药
        </div>
      </Modal>
      <Modal
        className="tipsmodal"
        visible={backModal}
        transparent
        maskClosable={false}
        animationType="slide-up"
        footer={[
          {
            text: '确认',
            onPress: () => {
              confBack();
            },
          },
          {
            text: '再想一想',
            onPress: () => {
              setRepealModal(false);
            },
          },
        ]}
      >
        <div className={styles.explainContent}>
          <div className="tipsTitle">
            <img src={tipsIcon} alt="" />
            {underlDrugStore ? '温馨提示' : '购药需求确认'}
          </div>
          {underlDrugStore
            ? '您还可以前往以下药店进行购买，购药前请自行联系药房询问药品实时供应情况，平台不为以下药店库存及药品品种提供保障，仅做信息展示。'
            : '请确认是撤销该药房的购药需求'}
        </div>
      </Modal>
    </div>
  );
};

export default connect(
  ({ drugPlanAndOrder, loading }: { drugPlanAndOrder: IDrugPlanAndOrderModelState; loading: Loading }) => ({
    drugPlanAndOrder,
    loading: loading.effects['drugPlanAndOrder/BuyMedicineScheme'],
  }),
)(PlanList);
