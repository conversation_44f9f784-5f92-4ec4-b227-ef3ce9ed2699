.planList {
  width: 100%;
  padding-bottom: 40px;
  background-color: #f5f6fa;

  .btnBox {
    display: flex;
    flex-direction: column;
    align-items: center;

    .btn {
      height: 68px;
      margin-top: 40px;
      color: #333333;
      font-size: 28px;
      line-height: 68px;
      text-align: center;
      border: 2px solid #989eb4;
      border-radius: 40px;
      padding: 0px 30px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;

    }
  }



  .planItem {
    width: 100vw;
    margin-bottom: 24px;
    padding: 24px;

    .btnView {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      box-sizing: border-box;
      // height: 82px;
      padding: 12px 0px 24px 0;
      background-color: #fff;
      flex: 2;

      .buyBtn {
        // width: 176px;
        height: 68px;
        padding: 0px 20px;
        color: #3ad3c1;
        font-size: 28px;
        line-height: 68px;
        text-align: center;
        border: 2px solid #3ad3c1;
        border-radius: 40px;
      }
    }
  }

  :global(.am-list-view-scrollview) {
    height: calc(100vh - 210px);
  }

  :global(.am-list-body::before) {
    background-color: transparent;
  }

  :global(.tipsmodal .am-modal-body),
  :global(.tipsmodal .am-modal-button) {
    font-size: 32px !important;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #03081a !important;
  }

  :global(.tipsmodal .am-modal-body .tipsTitle) {
    font-size: 36px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #03081a;
    padding-bottom: 30px;

    img {
      object-fit: contain;
      margin-right: 12px;
      position: relative;
      top: -1px;
    }
  }

  :global(.tipsmodal .title img) {
    width: 40px;
    height: 40px;

  }

  :global(.am-modal-transparent) {
    padding: 0px 40px;
    width: auto;
  }

  .tipsmodal {
    width: 100%;
    height: 346px;

  }

}

:global(.tipsmodal .am-modal-button) {
  &:first-child {
    color: #03081A !important;
  }
}
