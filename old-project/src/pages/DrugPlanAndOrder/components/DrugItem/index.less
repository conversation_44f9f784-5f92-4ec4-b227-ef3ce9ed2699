.item {
  display: flex;
  flex-direction: row;
  align-items: center;
  box-sizing: border-box;
  height: 240px;
  padding: 30px 24px;
  background-color: #fff;

  .left {
    width: 180px;
    height: 180px;
    margin-right: 24px;
    // background-color: #f5f6fa;
    border-radius: 8px;

    img {
      width: 180px;
      height: 180px;
      border-radius: 8px;
    }
  }

  .right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 100%;
    height: 100%;

    .name {
      display: -webkit-box;
      overflow: hidden;
      color: #03081a;
      font-weight: 400;
      font-size: 28px;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      width: 57%;
    }

    .space {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      color: #989eb4;
      font-size: 24px;
      position: relative;
    }

    .priceContent {
      position: relative;
      top: -58px;
    }

    .price {
      position: relative;
      top: 6px;
      color: #03081a;
      font-weight: bold;
      font-size: 40px;

      span {
        font-size: 28px;

        &:first-child {
          margin-right: 4px;
        }
      }
    }
  }
}

:global(.am-list-body) {
  background: #f5f6fa !important;
}

.drugContent {
  background: #fff;

  .paperWordTips {
    display: flex;
    justify-content: space-between;
    height: 36px;
    margin: 0px 24px;
    font-size: 24px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #FBBC44;
    padding-left: 8px;
    height: 72px;
    line-height: 72px;
    background: rgba(251, 188, 68, 0.1);
    border-radius: 36px;

    .wordTips {
      img {
        padding-left: 24px;
        padding-right: 8px;
        position: relative;
        top: -2px;
      }

    }

    .rightArrow {
      margin-right: 20px;
    }
  }


}
