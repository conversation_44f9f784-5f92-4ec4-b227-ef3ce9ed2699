import React, { FC, useState } from 'react';
import styles from './index.less';
import './index.css';

import { Modal } from 'antd-mobile';

const tips = require('@/assets/speMedicine/alert.png');
const rightArrow = require('@/assets/speMedicine/yellowArrow.png');
// const explain = require('@/assets/speMedicine/explain.jpg');
const explain = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/speMedicine/explain.jpg';

interface IProps {
  drugInfo: any;
  totalPrice?: string;
}

const DrugItem: FC<IProps> = ({ drugInfo = {}, totalPrice = '' }) => {
  const {
    specificationValue = '',
    goodsName = '',
    goodsUnit = '',
    listPicUrl = '',
    number = 0,
    retailPrice = 0,
    itemDesc = '',
    standards = '',
    totalQty = '',
    itemImageUrl = '',
    drugPrice = '',
    reviewStatus,
    prescType = '',
    // status,
  } = drugInfo;
  const [explainModal, setTipsModal] = useState<boolean>(false); // 流程提示框

  console.log('reviewStatus', reviewStatus);
  const tipsModal = () => {
    console.log('3333');
    setTipsModal(true);
  };
  const closemodal = () => {
    setTipsModal(false);
  };
  return (
    <div className={styles.drugContent}>
      {prescType === 2 ? (
        <div className={styles.paperWordTips} onClick={tipsModal}>
          <div className={styles.wordTips}>
            <img src={tips} alt="" />
            本方案需提交后，经药师审方后才可支付
          </div>
          <div className={styles.rightArrow}>
            <img src={rightArrow} alt="" />
          </div>
        </div>
      ) : (
        ''
      )}

      <div className={styles.item}>
        <div className={styles.left}>
          <img src={listPicUrl || itemImageUrl} alt="" />
        </div>
        <div className={styles.right}>
          <div className={styles.name}>{goodsName || itemDesc}</div>
          <div className={styles.space}>
            <span>
              {/* 规格： */}
              {specificationValue || standards}
              {!specificationValue && goodsUnit}
            </span>
            <div className={styles.priceContent}>
              {reviewStatus !== 1 ? (
                <div className={styles.price}>
                  <span>¥</span>
                  {console.log('22kkkk', totalPrice, retailPrice, drugPrice)}
                  {parseInt(totalPrice || retailPrice || drugPrice, 10) === (totalPrice || retailPrice || drugPrice) ? (
                    totalPrice || retailPrice || drugPrice
                  ) : (
                    <>
                      {parseInt(totalPrice || retailPrice || drugPrice, 10)}
                      <span>.{(totalPrice || retailPrice || drugPrice).toString().replace(/\d+\.(\d*)/, '$1')}</span>
                    </>
                  )}
                </div>
              ) : (
                ''
              )}
              共&nbsp;{number || totalQty} {goodsUnit}
            </div>
          </div>
        </div>
      </div>
      <Modal className="explainmodal" visible={explainModal} transparent maskClosable popup animationType="slide-up">
        <div className="explainContent">
          <img src={explain} alt="" style={{ width: '100%' }} />
          <div onClick={closemodal} className="close" />
        </div>
      </Modal>
    </div>
  );
};

export default DrugItem;
