import { progressTemp } from '@/pages/CliniCard/utils/hospitalizationServiceData';
import React, { FC } from 'react';
import styles from './index.less';

interface IProps {
  drugStoreInfo: any;
  /** 线下药房事件回调 */
  goOnline?: any;
  /** 订单事件回调 */
  btnClick: any;
  type?: string; //  type有值代表历史记录
}

const right = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/speMedicine/right.png';
// const storeName = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/storeName.png';
const Prescription: FC<IProps> = (props: IProps) => {
  const { drugStoreInfo, type, goOnline, btnClick, logo } = props;
  console.log('props', props);
  const { prescType, size, status, drugInfoList = [], drugStoreName, totalPrice } = drugStoreInfo;
  // 回调去线下药房
  const buyOnline = () => {
    goOnline && goOnline();
  };
  const orderClick = (item) => {
    console.log('item', item);
    btnClick && btnClick(item);
  };

  console.log('type', type);
  return (
    <div className={styles.drugStore}>
      <div>{prescType === 2 && status === 0 ? `在线配送药房  （${size}） ` : '已选择药房'}</div>
      {drugInfoList &&
        drugInfoList.map((item, index) => {
          return (
            <div className={styles.item} key={Number(index)}>
              <div className={styles.drugImg}>
                <img src={item.logo} alt="" />
              </div>
              <div className={styles.druginfo}>
                <div className={styles.drugName}>{item.drugStoreName}</div>
                <div className={styles.price}>
                  {item.totalPrice && (
                    <div>
                      <span>¥</span>
                      {item.totalPrice}
                    </div>
                  )}

                  {type ? (
                    <div className={styles.btnView}>
                      <div
                        className={`${styles.btn} ${styles.grayBtn}`}
                        onClick={() => {
                          orderClick(item);
                        }}
                      >
                        {prescType === 3 && status === 1
                          ? '查看订单'
                          : // : prescType === 2 && status === 1
                          // ? '重新选择'
                          status === 2 || status === 3
                          ? '联系医生'
                          : // : status === 4
                          // ? '去购买'
                          status === 5 && prescType !== 3
                          ? '查看订单'
                          : ''}
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className={styles.btnView}>
                        <div
                          className={status !== 1 || prescType === 3 ? (styles.btn, styles.gobuyBtn) : styles.btn}
                          onClick={() => {
                            orderClick(item);
                          }}
                        >
                          {/* 3外购药显示去购买，2特药显示提交购药需求 */}
                          {prescType === 2 && status === 0
                            ? '在线购药'
                            : prescType === 2 && status === 4
                            ? '去购买'
                            : prescType === 2 && status === 1
                            ? '查看'
                            : prescType === 3 && (status === 0 || status === 2)
                            ? '去购买'
                            : prescType === 3 && status === 1
                            ? '查看订单'
                            : prescType === 2 && status === 7
                            ? '查看进度'
                            : ''}
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          );
        })}

      {!type && (
        <div className={styles.offlineStore} onClick={buyOnline}>
          线下购药药房
          <img src={right} alt="" />
        </div>
      )}
    </div>
  );
};
export default Prescription;
