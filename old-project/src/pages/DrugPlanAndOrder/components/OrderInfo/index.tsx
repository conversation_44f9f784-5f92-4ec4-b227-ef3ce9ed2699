import React, { FC } from 'react';
import { PAY_CHANNEL } from '../../dictionary';
import styles from './index.less';

interface IProps {
  /** 商品信息 */
  orderInfo?: any;
}

const OrderInfo: FC<IProps> = (props) => {
  const { orderInfo } = props;
  const { addTime = '', orderId = '', payTime = '', bankTradeNo = '', payMethod = '', orderStatus = 0 } = orderInfo;

  const ORDER_INFO = [
    {
      label: '订单编号',
      value: orderId,
      show: true,
    },
    {
      label: '下单时间',
      value: addTime,
      show: true,
    },
    {
      label: '支付时间',
      value: payTime,
      show: orderStatus !== 0 && orderStatus !== 101, // 除了待支付、已取消，其他都显示
    },
    {
      label: '支付渠道',
      value: PAY_CHANNEL[payMethod],
      show: orderStatus !== 0 && orderStatus !== 101, // 除了待支付、已取消，其他都显示
    },
    {
      label: '支付流水',
      value: bankTradeNo,
      show: orderStatus !== 0 && orderStatus !== 101, // 除了待支付、已取消，其他都显示
    },
    // {
    //   label: '退款时间',
    //   value: 'DD23276482384',
    //   show: orderStatus === 401, // 只有已退款状态下显示
    // },
    // {
    //   label: '退款金额',
    //   value: 'DD23276482384',
    //   show: orderStatus === 401, // 只有已退款状态下显示
    // },
  ];
  return (
    <div className={styles.orderInfo}>
      {ORDER_INFO.map(
        (item: { label?: string; value?: string; show?: boolean }, index: number) =>
          item.show && (
            <div key={index.toString()}>
              <span>{item.label}：</span>
              <span>{item.value}</span>
            </div>
          ),
      )}
    </div>
  );
};

export default OrderInfo;
