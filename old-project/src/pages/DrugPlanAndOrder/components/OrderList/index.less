.container {
  height: 100%;
  background-color: #f5f6fa;
  .loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 80vh;
  }

  .listView {
    padding-bottom: 24px;

    .orderItem {
      margin-top: 24px;
      overflow: hidden;
    }
  }

  .btnView {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    height: 82px;
    padding: 0 24px 0 0;
    background-color: #fff;

    .btn {
      width: 192px;
      height: 56px;
      margin-left: 24px;
      font-size: 28px;
      line-height: 52px;
      text-align: center;
      background-color: #fff;
      border-radius: 40px;
    }

    .grayBtn {
      color: #03081a;
      border: 2px solid #b0b3bf;
    }

    .greenBtn {
      color: #3ad3c1;
      border: 2px solid #3ad3c1;
    }
  }
  :global(.am-list-view-scrollview) {
    height: calc(100vh - 80px);
  }

  :global {
    div.am-list-body::before {
      background-color: #fff;
    }
    div.am-list-body::after {
      background-color: #fff;
    }
    .am-tabs-default-bar-top .am-tabs-default-bar-tab::after {
      background-color: transparent !important;
    }
    .am-tabs-default-bar-tab-active {
      font-weight: 600;
    }
  }
}
