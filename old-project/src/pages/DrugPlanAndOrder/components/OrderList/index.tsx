import React, { FC, useState, useEffect } from 'react';
import { Dispatch, history, IDrugPlanAndOrderModelState, Loading } from 'umi';
import { Tabs, Toast, Modal } from 'antd-mobile';
import { isHytPerson } from '@/utils/platform';
import AppScheme from '@/utils/AppScheme';
import { getOpenId, getToken } from '@/utils/parameter';
import { HxListView, HxIcon, HxIndicator } from '@/components';
import { connect } from 'dva';
// import { getCurEvnHref, configWechatGetLocation } from '../../tools';
// import { getDistance } from '../../tools';
import { tabs, statusObj } from './data';
import { NoDataView, StoreNameItem, DrugItem, MoreDrug } from '../index';
import styles from './index.less';

interface IProps {
  dispatch: Dispatch;
  loading?: boolean;
}

const noDrugPlan = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/no-drug-order.png';

const OrderList: FC<IProps> = (props) => {
  const { dispatch, loading } = props;
  const [orderList, setOrderList] = useState<Array<any>>([]);
  const [pageNum, setPageNum] = useState(1);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [flag, setFlag] = useState(tabs[0].value);

  // 请求列表
  const fetchList = (param: any, type: string, num: number) => {
    dispatch({
      type: 'drugPlanAndOrder/fetchOrderList',
      payload: {
        pageNum: num,
        pageSize: 5,
        query: {
          orderStatusList: param,
          bizSysSeq: 'mall-drug',
        },
      },
      callback: (orderListData: any) => {
        const { content = [], totalPages = 0 } = orderListData || {};
        setOrderList(type === 'new' ? [...content] : [...orderList, ...content]);
        setIsLoading(false);
        setPageNum(num);
        if (num >= totalPages) {
          setHasMore(false);
        }
      },
    });
  };

  useEffect(() => {
    fetchList(tabs[0].value, 'new', 1);
  }, []);

  // tab切换
  const onTabChange = (tab: any) => {
    const { value } = tab;
    setFlag(value);
    setIsLoading(true);
    setHasMore(true);
    fetchList(value, 'new', 1);
  };

  // 取消订单
  const cancel = (orderId: string) => {
    dispatch({
      type: 'drugPlanAndOrder/cancelOrder',
      payload: {
        orderId,
      },
      callback: (res: any) => {
        if (res?.code === '1') {
          setIsLoading(true);
          setHasMore(true);
          Toast.info('取消订单成功', 1.5);
          setTimeout(() => {
            fetchList(flag, 'new', 1);
          }, 1500);
        }
      },
    });
  };

  /**
   * 订单按钮操作
   * @param type 按钮类型
   * @param item 列表项的数据
   */
  const doOperation = (e: any, type: string, item: any) => {
    e.stopPropagation();
    const { order = {}, paymentOrder = {} } = item;
    const { orderId = '' } = order;
    const { bizSysSeq = '', dealSeq = '', merchantSeq = '' } = paymentOrder;
    switch (type) {
      case 'cancel':
        Modal.alert(
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <HxIcon iconName="tips" style={{ width: '24px', height: '24px' }} />
            <div style={{ marginLeft: '4px', fontSize: '#03081A', fontWeight: 600 }}>取消订单</div>
          </div>,
          <div style={{ color: '#03081A' }}>您真的要取消这笔订单吗？</div>,
          [
            {
              text: <div style={{ color: '#3AD3C1' }}>再想一想</div>,
            },
            {
              text: <div style={{ color: '#03081A' }}>确认</div>,
              onPress: () => cancel(orderId),
            },
          ],
        );
        break;
      case 'pay':
        if (isHytPerson()) {
          AppScheme.toPay({ dealSeq, bizSysSeq, merchantSeq });
        } else {
          window.location.href = `${API_BASE}/cloud/paygateway/getCashier?bizSysSeq=${bizSysSeq}&dealSeq=${dealSeq}&merchantSeq=${merchantSeq}&accessToken=${getToken()}&openid=${getOpenId()}`;
        }

        // 新需求去掉弹框-9.27号
        // Modal.alert(
        //   <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        //     <HxIcon iconName="tips" style={{ width: '24px', height: '24px' }} />
        //     <div style={{ marginLeft: '4px', fontSize: '#03081A', fontWeight: 600 }}>确认支付</div>
        //   </div>,
        //   <div style={{ color: '#03081A' }}>本订单为外购药品，需要自行付费，是否可报销以实际报销情况为准。</div>,
        //   [
        //     {
        //       text: <div style={{ color: '#03081A' }}>再想一想</div>,
        //     },
        //     {
        //       text: <div style={{ color: '#3AD3C1' }}>确认</div>,
        //       onPress: () => {
        //         if (isHytPerson()) {
        //           AppScheme.toPay({ dealSeq, bizSysSeq, merchantSeq });
        //         } else {
        //           window.location.href = `${API_BASE}/cloud/paygateway/getCashier?bizSysSeq=${bizSysSeq}&dealSeq=${dealSeq}&merchantSeq=${merchantSeq}&accessToken=${getToken()}&openid=${getOpenId()}`;
        //         }
        //       }, // 跳转到支付页面
        //     },
        //   ],
        // );
        break;
      case 'see':
        history.push(`/drugplanandorder/detail?orderId=${orderId}`);
        break;
      case 'confirm':
        dispatch({
          type: 'drugPlanAndOrder/comfirmReceipt',
          payload: {
            orderId,
          },
          callback: (confimRes: any) => {
            const { code = '1' } = confimRes;
            if (code === '1') {
              setIsLoading(true);
              setHasMore(true);
              Toast.info('确认收货成功', 1.5);
              setTimeout(() => {
                fetchList(flag, 'new', 1);
              }, 1500);
            }
          },
        });
        break;
      default:
        break;
    }
  };

  /** 加载后一页 */
  const loadMore = () => {
    if (isLoading || !hasMore) {
      return;
    }
    setIsLoading(false);
    setTimeout(() => {
      fetchList(flag, 'add', pageNum + 1);
    }, 200);
  };

  const onLoadMore = () => {
    /** 上拉至底触发 */
    !isLoading && loadMore();
  };

  // 调用微信sdk，获取微信配置信息
  // const fetchConfigData = (shopResInfo: any) => {
  //   dispatch({
  //     type: 'global/singWechatJSSDK',
  //     payload: {
  //       url: getCurEvnHref(),
  //     },
  //     callback: (data: any) => {
  //       const configData = {
  //         ...data,
  //         debug: false,
  //         jsApiList: ['getLocation'],
  //       };
  //       configWechatGetLocation(configData, shopResInfo);
  //       // const wechatDistance = localStorage.getItem('distance');
  //       // setDistance(wechatDistance);
  //       // new Promise((resolve, reject) => {
  //       //   configWechatGetLocation(configData, shopResInfo);
  //       //   resolve(true);
  //       // }).then((err) => {
  //       //   const wechatDistance = localStorage.getItem('distance');

  //       // });
  //     },
  //   });
  // };

  /** 点击跳转到订单详情 */
  const toDetail = (orderId: string, bizOrderCode: string) => {
    // if (isWechat() && type === 2) {
    //   Object.keys(shop).length !== 0 && fetchConfigData(shop);
    // }
    // orderId && history.push(`/drugplanandorder/detail?orderId=${orderId}`);
    window.location.href = `${window.location.origin}/person/drugplanandorder/detail?orderId=${orderId}&bizOrderCode=${bizOrderCode}`;
  };

  const renderOrderItem = (item: any = {}, index: any) => {
    const { order = {}, products = [], shop = {} } = item;
    const { orderStatus = 0, actualPrice = 0, bizOrderCode = '' } = order;
    const { name = '' } = shop;
    const { text = '', color = '', btns = {} } = statusObj[orderStatus] || {};
    const { orderId = '' } = order;
    return (
      <div key={index.toString()} className={styles.orderItem} onClick={() => toDetail(orderId, bizOrderCode)}>
        <StoreNameItem storeName={name} statusText={text} textColor={color} />
        {products.length === 1 ? (
          <DrugItem drugInfo={products[0]} totalPrice={actualPrice} />
        ) : (
          <MoreDrug drugList={products} totalPrice={actualPrice} />
        )}
        <div className={styles.btnView}>
          {btns.cancel && (
            <div className={`${styles.btn} ${styles.grayBtn}`} onClick={(e) => doOperation(e, 'cancel', item)}>
              取消订单
            </div>
          )}
          {btns.pay && (
            <div className={`${styles.btn} ${styles.greenBtn}`} onClick={(e) => doOperation(e, 'pay', item)}>
              去支付
            </div>
          )}
          {btns.see && (
            <div className={`${styles.btn} ${styles.grayBtn}`} onClick={(e) => doOperation(e, 'see', item)}>
              查看订单
            </div>
          )}
          {btns.confirm && (
            <div className={`${styles.btn} ${styles.greenBtn}`} onClick={(e) => doOperation(e, 'confirm', item)}>
              确认收货
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={styles.container}>
      <Tabs
        animated={false}
        tabs={tabs}
        initialPage={0}
        onChange={(tab) => onTabChange(tab)}
        tabBarUnderlineStyle={{ borderWidth: 0 }}
        tabBarInactiveTextColor="#03081A"
        tabBarActiveTextColor="#3AD3C1"
        tabBarTextStyle={{ fontSize: '16px' }}
      >
        <div className={styles.listView}>
          {/* {orderList.length !== 0 ? (
            <HxListView
              dataSource={orderList}
              renderRow={renderOrderItem}
              initialListSize={10}
              pageSize={10}
              onEndReached={() => onLoadMore()}
              onEndReachedThreshold={20}
              isRenderFooter
              hasMore={hasMore}
            />
          ) : (
            // orderList.map((item, index) => renderOrderItem(item, index))
            <NoDataView text="暂无购药订单" imgUrl={noDrugPlan} />
          )} */}
          {loading && orderList.length === 0 ? (
            <div className={styles.loading}>
              <HxIndicator />
            </div>
          ) : orderList.length === 0 ? (
            <NoDataView text="暂无购药订单" imgUrl={noDrugPlan} />
          ) : (
            <HxListView
              dataSource={orderList}
              renderRow={renderOrderItem}
              initialListSize={10}
              pageSize={10}
              onEndReached={() => onLoadMore()}
              onEndReachedThreshold={20}
              isRenderFooter
              hasMore={hasMore}
            />
          )}
        </div>
      </Tabs>
    </div>
  );
};

export default connect(
  ({ drugPlanAndOrder, loading }: { drugPlanAndOrder: IDrugPlanAndOrderModelState; loading: Loading }) => ({
    drugPlanAndOrder,
    loading: loading.effects['drugPlanAndOrder/fetchOrderList'],
  }),
)(OrderList);
