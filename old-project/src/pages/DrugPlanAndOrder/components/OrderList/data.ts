/**
 * 0 待支付、201 待配货、202 待发货、301 待收货、203 待取货、302 已完成、401 已退款、101 已取消 、204 打包中
 */

const tabs = [
  // { title: '全部', value: [0, 201, 202, 301, 203, 204, 302, 401, 101] },
  { title: '全部', value: [] },
  { title: '待支付', value: [0] },
  { title: '待配货', value: [201] },
  { title: '待发货', value: [202, 204] },
  { title: '待收货', value: [301, 203] },
  { title: '已完成', value: [302] },
];

const statusObj = {
  0: {
    text: '待支付',
    color: '#FC4553',
    btns: { cancel: true, pay: true },
  },
  201: {
    text: '待配货',
    color: '#FC4553',
    btns: { see: true },
  },
  202: {
    text: '待发货',
    color: '#FC4553',
    btns: { see: true },
  },
  301: {
    text: '待收货',
    color: '#FC4553',
    btns: { see: true, confirm: true },
  },
  203: {
    text: '待取货',
    color: '#FC4553',
    btns: { see: true, confirm: true },
  },
  302: {
    text: '已完成',
    color: '#989EB4',
    btns: { see: true },
  },
  401: {
    text: '已退款',
    color: '#989EB4',
    btns: { see: true },
  },
  101: {
    text: '已取消',
    color: '#989EB4',
    btns: { see: true },
  },
  204: {
    text: '打包中',
    color: '#989EB4',
    btns: { see: true },
  },
};

export { tabs, statusObj };
