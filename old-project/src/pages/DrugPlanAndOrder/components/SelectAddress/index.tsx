import { InputItem } from 'antd-mobile';
import { createForm } from 'rc-form';
import React, { FC, useState, useEffect } from 'react';

// import { getDistance } from '../../tools';
import styles from './index.less';

interface IProps {
  /** 切换tab事件 */
  selectMethod?: (sub: number) => void;
  /** 选中tab的唯一标识 */
  alreadySelect?: number;
  /** input的onChange事件 */
  inputOnChange?: (val: string, type: string) => void;
  /** 跳转到选择地址页面 */
  toSelectAddress?: () => void;
  /** 物流配送地址信息 */
  addressInfo?: any;
  /** 打开百度地图 */
  getMap?: () => void;
  /** 线下地址 */
  offlineAddress?: any;
  /** 线下手机号 */
  offPhone?: string;
  wechatDistance?: number;
  /** 定点配送信息显示 */
  reservedInfo: boolean;
}

const tabs = [
  { title: '物流配送', sub: 1 },
  { title: '到店自取', sub: 2 },
];

const position = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/mall-postion.png';
const arrowRight = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/mall-arrow-right.png';
const edit = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/mall-edit.png';
const enter = require('@/assets/enter.png');

const SelectAddress = (props: IProps) => {
  // props
  const {
    selectMethod,
    alreadySelect,
    inputOnChange,
    toSelectAddress,
    addressInfo = {},
    getMap,
    offlineAddress,
    offPhone,
    wechatDistance,
    reservedInfo,
  } = props;

  // const addressData = addressInfo && JSON.parse(addressInfo);
  const { province = '', city = '', district = '', address = '' } = offlineAddress;
  const [name, setName] = useState<string>('');
  const [telephone, setTelephone] = useState<string>('');

  // 这是调用的微信api或者用户当前位置的信息
  // const weChatString = localStorage.getItem('weiChatCurrentLocation');

  // // 获取当前位置与目的地的距离
  // const fetchCurrentLocation = () => {
  //   let distance = '';
  //   if (weChatString) {
  //     const getWetChatCurrentLocation = JSON.parse(weChatString);
  //     const { latitude: currentLatitude = '', longitude: currentLongitude = '' } = getWetChatCurrentLocation;
  //     distance = getDistance(Number(latitude), Number(longitude), Number(currentLatitude), Number(currentLongitude));
  //   }
  //   return distance;
  // };

  // const wechartDis = localStorage.getItem('distance');

  // useEffect(() => {
  //   fetchCurrentLocation();
  // }, [weChatString]);

  const {
    defaultAddress = '',
    contactUserName = '',
    provinceName = '',
    cityName = '',
    areaName = '',
    detailAddress = '',
    phone = '',
  } = addressInfo;

  const change = (val: string, type: string) => {
    console.log('val', val, type);

    if (type === 'consignee') {
      setName(val?.replace(/\d/gi, ''));
      inputOnChange && inputOnChange(val, type);
    } else if (type === 'mobile') {
      setTelephone(val?.replace(/\s/gi, ''));
      inputOnChange && inputOnChange(val, type);
    }
  };

  return (
    <div className={styles.selectAddress}>
      <div className={styles.tabBox}>
        {tabs.map((tabItem) => (
          <div
            className={
              alreadySelect === 1 && alreadySelect === tabItem.sub
                ? styles.selectedLeft
                : alreadySelect === 2 && alreadySelect === tabItem.sub
                ? styles.selectedRight
                : ''
            }
            key={tabItem?.sub}
            onClick={() => selectMethod(tabItem?.sub)}
          >
            {tabItem.title}
          </div>
        ))}
      </div>
      {/* 没有收货地址的时间显示 */}
      {Object.keys(addressInfo).length === 0 && alreadySelect === 1 && (
        <div className={styles.noAddress}>
          <div className={styles.addressLeft}>
            <img src={position} alt="" className={styles.icon} />
            <div className={styles.addAddress}>添加收货地址</div>
          </div>
          <img src={arrowRight} alt="" className={styles.arrow} onClick={toSelectAddress} />
        </div>
      )}
      {Object.keys(addressInfo).length !== 0 && alreadySelect === 1 && (
        <div className={styles.offline}>
          <div className={styles.hasAddress}>
            {/* 这里需要区分，是物流配送显示的是送货地址，药店自取显示的是要放地址 */}
            {addressInfo && alreadySelect === 1 && (
              <div className={styles.defaultAddress}>
                {defaultAddress === '1' && <div className={styles.isdefault}>默认</div>}
                <div className={styles.province}>
                  {provinceName}
                  {cityName}
                  {areaName}
                </div>
              </div>
            )}
            <div className={styles.addressInfo}>
              <div className={styles.address}>{detailAddress}</div>
              <img src={arrowRight} alt="" className={styles.addressIcon} onClick={toSelectAddress} />
            </div>
            <div className={styles.userInfo}>
              {!reservedInfo ? `${contactUserName} ${phone.replace(/^(\d{3})\d{4}(\d{4})$/, '$1****$2')}` : ' '}
            </div>
            {reservedInfo ? (
              <div className={styles.reservedContent}>
                <div className={styles.reservedName}>
                  <div>预留姓名</div>
                  <div className={styles.connect}>
                    <InputItem
                      placeholder="请输入"
                      maxLength={11}
                      onChange={(val) => change(val, 'consignee')}
                      value={name}
                    />

                    <img src={enter} alt="" className={styles.arrowIcon} />
                  </div>
                </div>
                <div className={styles.reservedName}>
                  <div>预留电话</div>
                  <div className={styles.connect}>
                    <InputItem
                      type="number"
                      placeholder="请输入"
                      maxLength={11}
                      onChange={(val) => change(val, 'mobile')}
                      value={telephone}
                    />

                    <img src={enter} alt="" className={styles.arrowIcon} />
                  </div>
                </div>
              </div>
            ) : (
              ''
            )}
          </div>
        </div>
      )}
      {alreadySelect === 2 && (
        <div className={styles.offline}>
          <div className={styles.hasAddress}>
            <div className={styles.defaultAddress}>
              {defaultAddress === '1' && <div className={styles.isdefault}>默认</div>}
              <div className={styles.province}>
                {province}
                {city}
                {district}
              </div>
            </div>
            <div className={styles.addressInfo}>
              <div>{address}</div>
            </div>
            <div className={styles.connect}>
              <InputItem
                type="number"
                placeholder="请输入电话号码"
                maxLength={11}
                defaultValue={offPhone}
                onChange={(val) => change(val, '')}
              >
                预留电话:
              </InputItem>
              <img src={edit} alt="" className={styles.editIcon} />
            </div>
          </div>
          <div className={styles.mask} onClick={getMap}>
            {/* <div className={styles.distance}>距您{fetchCurrentLocation()}km</div> */}
            <div className={styles.distance}>距您{wechatDistance}km</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default createForm()(SelectAddress);
