.selectAddress {
  width: 100%;
  margin-bottom: 24px;
  background-color: #fff;
  border-radius: 16px 16px 0 0;

  .tabBox {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    height: 66px;
    color: #03081a;
    font-size: 28px;
    line-height: 66px;
    text-align: center;
    background-color: #d8f6f3;
    border-radius: 16px 16px 0 0;

    div {
      width: 50%;
      text-align: center;
    }

    .selectedLeft {
      display: flex;
      align-items: flex-end;
      justify-content: center;
      width: 396px;
      height: 90px;
      font-weight: 600;
      font-size: 32px;
      background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/logistics-distribution.png')
        no-repeat left bottom;
      background-size: 100%;
    }

    .selectedRight {
      display: flex;
      align-items: flex-end;
      justify-content: center;
      width: 396px;
      height: 90px;
      font-weight: 600;
      font-size: 32px;
      background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/invite.png')
        no-repeat right bottom;
      background-size: 100%;
    }
  }

  .noAddress {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 36px 24px;
    background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/stripe.png')
      no-repeat left bottom;
    background-size: 100%;

    .addressLeft {
      display: flex;
      color: #03081a;
      font-weight: 600;
      font-size: 36px;

      .icon {
        width: 48px;
        height: 48px;
        margin-right: 8px;
      }

      .addAddress {
        color: #03081a;
      }
    }

    .arrow {
      width: 48px;
      height: 48px;
    }
  }

  .offline {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px 24px 24px;
    background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/stripe.png')
      no-repeat left bottom;
    background-size: 100%;

    .mask {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 240px;
      height: 240px;
      background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/mask.png')
        no-repeat;
      background-size: 100% 100%;

      .distance {
        width: 162px;
        height: 70px;
        color: #03081a;
        font-size: 24px;
        line-height: 56px;
        text-align: center;
        background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/dialog.png')
          no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .hasAddress {
    // padding: 16px 24px 24px 24px;
    // background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/stripe.png')
    //   no-repeat left bottom;
    // background-size: 100%;
    .defaultAddress {
      display: flex;
      align-items: center;

      .isdefault {
        width: 56px;
        height: 28px;
        margin-right: 8px;
        color: #fff;
        font-size: 20px;
        line-height: 28px;
        text-align: center;
        background-color: #3ad3c1;
        border-radius: 8px;
      }

      .province {
        color: #03081a;
        font-size: 24px;
      }
    }

    .addressInfo {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      width: 100%;
      margin: 12px 0;
      color: #03081a;
      font-weight: 600;
      font-size: 36px;

      .address {
        width: 570px;
        margin-right: 50px;
      }

      .addressIcon {
        width: 48px;
        height: 48px;
        margin-top: 10px;
      }
    }

    .userInfo {
      color: #03081a;
      font-size: 24px;
    }

    .connect {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      :global {
        .am-list-item {
          padding: 0;
        }

        .am-list-item .am-input-label {
          width: 120px;
          color: #03081a;
          font-weight: 600;
          font-size: 28px;
        }

        .am-list-item .am-input-control input {
          width: 200px;
          color: #03081a;
          font-weight: 600;
          font-size: 28px;
        }

        div.am-list-line::after {
          background-color: transparent;
        }
      }

      .editIcon {
        width: 32px;
        height: 32px;
      }
    }
  }

  .reservedContent {
    display: flex;
    flex-direction: column;
    margin-top: 48px;

    .reservedName {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      font-size: 28px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #03081a;

      // line-height: 40px;
      &:first-child {
        padding-bottom: 32px;
      }

      :global .am-list-item {
        min-height: 20px !important;
        height: 40px !important;

        :global .am-input-control input {
          font-size: 28px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #989eb4;
          line-height: 40px;
          text-align: right;
        }
      }
    }
  }
}
