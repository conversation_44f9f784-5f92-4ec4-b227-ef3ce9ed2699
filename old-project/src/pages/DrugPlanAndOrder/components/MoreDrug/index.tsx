import React, { FC } from 'react';
import { history, Dispatch } from 'umi';
import { connect } from 'dva';
import styles from './index.less';

interface IProps {
  drugList: any;
  totalPrice: any;
  dispatch: Dispatch;
}

const arrowRight = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/mall-list-arrow.png';

const MoreDrug: FC<IProps> = ({ drugList = [], totalPrice = 0, dispatch }) => {
  const goDrugList = (e: any) => {
    e.stopPropagation();
    dispatch({
      type: 'drugPlanAndOrder/updateState',
      payload: {
        drugList,
      },
    });
    history.push('/drugplanandorder/druglist');
  };

  return (
    <div className={styles.container}>
      <div className={styles.drugList}>
        {drugList.map((item: any, index: any) => (
          <div className={styles.imgBox} key={index.toString()}>
            <img src={item?.listPicUrl || item?.itemImageUrl} alt="" />
          </div>
        ))}
      </div>
      <div className={styles.right} onClick={(e) => goDrugList(e)}>
        <div>
          <span>¥</span>
          {parseInt(totalPrice, 10) === totalPrice ? (
            totalPrice
          ) : (
            <>
              {parseInt(totalPrice, 10)}
              <span>.{totalPrice.toString().replace(/\d+\.(\d*)/, '$1')}</span>
            </>
          )}
        </div>
        <div>
          {`共${drugList.length}件 `}
          <img src={arrowRight} alt="" />
        </div>
      </div>
    </div>
  );
};

export default connect()(MoreDrug);
