.container {
  box-sizing: border-box;
  padding: 24px;
  background-color: #fff;
  border-radius: 16px 16px 0px 0px;

  .infoView {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding: 24px 0px 24px 24px;
    background-color: #f5f6fa;
    border-radius: 8px;
    position: relative;

    .sign {
      width: 96px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      background: #568DF2;
      border-radius: 0px 8px 0px 8px;
      font-size: 24px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #FFFFFF;
      position: absolute;
      right: 0;
      top: 0;
    }

    .left {
      flex: 1;
      margin-right: 24px;

      div {
        margin-bottom: 16px;

        span {
          color: #989eb4;
          font-size: 24px;
        }

        span:last-child {
          color: #03081a;
        }
      }

      div:last-child {
        margin-bottom: 0;
      }
    }

    .statusImg {
      img {
        width: 160px;
        height: 160px;
        right: 40px;
        top: 80px;
        position: absolute;


      }
    }
  }
}
