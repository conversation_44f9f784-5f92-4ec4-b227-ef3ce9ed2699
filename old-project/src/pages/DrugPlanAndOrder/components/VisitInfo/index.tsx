import React, { FC } from 'react';
import styles from './index.less';
import Prescription from '../Prescription/index';
import { history } from 'umi';
import { Toast } from 'antd-mobile';

interface IProps {
  info: any;
  prescriptionDetail?: any;
  hasInvalid?: boolean;
  hasUsed?: boolean;
  status?: number;
  pageType?: string; // 代表订单详情页面
}

const invalidImg = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/invalid-icon.png';
const usedImg = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/used-icon.png';
const haveorder = require('@/assets/haveorder.png');
const noneOrder = require('@/assets/noneOrder.png');

const VisitInfo: FC<IProps> = ({ info = {}, prescriptionDetail = [], pageType = '', status }) => {
  const {
    itemDate = '',
    doctorName = '',
    deptName = '',
    patientName = '',
    diagnoseDesc = '',
    regTitelName = '',
    prescUrl = '',
    expirationTime = '',
  } = info;
  console.log('prescriptionDetail', info, prescriptionDetail);
  // 查看处方笺
  const checkPrection = () => {
    if (!prescUrl) {
      Toast.info('暂无处方笺');
    } else {
      history.push({
        pathname: '/drugplanandorder/predetails',
        query: {
          prescUrl,
        },
      });
    }
  };
  return (
    <div className={styles.container}>
      <div className={styles.infoView}>
        <div className={styles.left}>
          <div>
            <span>就诊医生：</span>
            <span>
              {doctorName} {regTitelName}
            </span>
          </div>
          <div>
            <span>就诊科室：</span>
            <span>{deptName}</span>
          </div>
          <div>
            <span>就&nbsp;&nbsp;诊&nbsp;&nbsp;人：</span>
            <span>{patientName}</span>
          </div>
          <div>
            <span>诊断信息：</span>
            <span>{diagnoseDesc}</span>
          </div>
          <div>
            <span>开方时间：</span>
            <span>{}</span>
            <span>
              {itemDate} &nbsp;（{expirationTime}后失效）
            </span>
          </div>
          {!pageType && <Prescription prescriptionDetail={prescriptionDetail} />}
        </div>
        <div className={styles.statusImg}>
          {status === 1 && <img src={usedImg} alt="" />}
          {status === 2 && <img src={invalidImg} alt="" />}
          {status === 4 && <img src={noneOrder} alt="" />}
          {status === 5 && <img src={haveorder} alt="" />}
        </div>
        <div className={styles.sign} onClick={checkPrection}>
          处方笺
        </div>
      </div>
    </div>
  );
};

export default VisitInfo;
