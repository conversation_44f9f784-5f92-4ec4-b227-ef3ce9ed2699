.goodsInfo {
  margin: 24px 0;
  background-color: #fff;
  border-radius: 8px;
  .goodsItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32px 22px;
    color: #03081a;
    .title {
      display: flex;
      align-items: center;
      font-size: 32px;
      .refundLabel {
        margin-left: 16px;
        padding: 4px 8px;
        color: #fff;
        font-size: 20px;
        background-color: #b0b3bf;
        border-radius: 18px;
      }
    }
    .price {
      font-weight: bold;
      font-size: 40px;
      span {
        font-size: 28px;
      }
    }
    .emptyPrice {
      color: #989eb4;
      font-size: 32px;
    }
  }
  .goodsItemChildren {
    display: flex;
    justify-content: space-between;
    padding: 0 22px;
    color: #03081a;
    .subTitle {
      font-size: 28px;
      display: flex;
      align-items: center;
      img {
        width: 30px;
        height: 30px;
        margin-right: 5px;
      }
      .tips {
        color: #3ad3c1;
        text-decoration: underline;
      }
    }
    .subPrice {
      font-weight: bold;
      font-size: 30px;
      span {
        font-size: 28px;
      }
    }
  }
  .totalPrice {
    padding: 32px 22px;
    text-align: right;
    :nth-child(1) {
      color: #03081a;
      font-size: 32px;
    }
    :nth-child(2) {
      color: #fc4553;
      font-weight: bold;
      font-size: 28px;
    }
    :nth-child(3) {
      color: #fc4553;
      font-weight: bold;
      font-size: 40px;
    }
  }
}

.valueUnsure {
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 28px;
    height: 28px;
    margin-right: 6px;
  }
  .span {
    font-size: 20px;
    color: #03081a;
    font-weight: bold;
  }
}

.message {
  height: 300px;
  overflow-x: hidden;
}
