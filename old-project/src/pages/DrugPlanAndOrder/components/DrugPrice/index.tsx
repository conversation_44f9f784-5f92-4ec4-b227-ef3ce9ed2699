import React, { FC, useState, useEffect } from 'react';
import selected from '@/assets/select.png';
import noSelected from '@/assets/nonselect.png';
import tixingIcon from '@/assets/icon_弹窗提醒.png';
import { Modal } from 'antd-mobile';
import { connect, IDrugPlanAndOrderModelState, Dispatch } from 'umi';

import styles from './index.less';

const { alert } = Modal;
interface IProps {
  dispatch: Dispatch;
  drugPlanAndOrder: IDrugPlanAndOrderModelState;
  /** 商品信息 */
  goodsInfo?: any;
  /** 总价 */
  totalPrice?: number;
  onSelect?: (value: boolean) => void;
  repeatDetail?: any;
  transType: any; //  运输类型
  // fetchDrugPlan?: any;
}

const DrugPrice: FC<IProps> = (props) => {
  const { dispatch, goodsInfo, totalPrice = 0, transType = '', drugPlanAndOrder = {}, repeatDetail } = props;
  const { isValueInsure = false } = drugPlanAndOrder;
  const [isSelected, setIsSelected] = useState<boolean>(isValueInsure);

  useEffect(() => {
    setIsSelected(isValueInsure);
  }, [isValueInsure]);
  useEffect(() => {
    if (Number(transType) === 4) {
      console.log(8888888);
      repeatDetail && repeatDetail(4);
    } else {
      console.log(999999999);
      repeatDetail && repeatDetail(1);
    }
  }, [isSelected]);

  const updateValue = () => {
    console.log('ppppp报价', transType);

    dispatch({
      type: 'drugPlanAndOrder/updateState',
      payload: {
        isValueInsure: !isSelected,
      },
    });
    // 模拟请求延迟
    setTimeout(() => {
      setIsSelected(!isSelected);
    }, 1000);
  };

  // 定义是否选择保价
  const hasSelected = () => {
    if (isSelected) {
      alert(
        <div className={styles.valueUnsure}>
          <img src={tixingIcon} alt="" />
          <span>保价取消提醒</span>
        </div>,
        <span style={{ textAlign: 'center', fontSize: '16px' }}>
          特殊药品位高价值药品，若取消保价，将无安全赔付保障，请谨慎操作
        </span>,
        [
          { text: '取消' },
          {
            text: '确定',
            onPress: () => {
              updateValue();
            },
          },
        ],
      );
    } else {
      updateValue();
    }
  };

  // 跳转去保价协议
  const hytValuation = () => {
    window.location.href = 'https://cdnhyt.cd120.com/static/agreement/the_insured.html';
  };

  return (
    <div className={styles.goodsInfo}>
      {goodsInfo.map(
        (goodsItem: {
          id?: number;
          title?: string;
          price?: number;
          show?: boolean;
          isRefundShow?: boolean;
          children?: any[];
        }) => {
          const { children = [] } = goodsItem;
          return (
            <div>
              {/* 遍历父级 */}
              {goodsItem.show && (
                <div className={styles.goodsItem} key={goodsItem?.id}>
                  <div className={styles.title}>
                    {goodsItem.title}
                    {goodsItem.title === '商品金额' && (
                      <span style={{ fontSize: '12px', color: '#3AD3C1' }}>(医保报销后)</span>
                    )}
                    {/* 这个要判断显示，配送费那边不显示，订单详情要根据字段判断是否显示 */}
                    {goodsItem.isRefundShow && <span className={styles.refundLabel}>已退款</span>}
                  </div>
                  <div className={goodsItem.id === 1 && goodsItem?.price < 0 ? styles.emptyPrice : styles.price}>
                    <span>{goodsItem.id === 1 && goodsItem?.price < 0 ? '' : '￥'}</span>
                    {goodsItem.id === 1
                      ? goodsItem?.price >= 0
                        ? goodsItem.price
                        : '请先添加收货地址'
                      : goodsItem.price}
                  </div>
                </div>
              )}
              {/* 遍历子级 */}
              {goodsItem.show && children.length > 0 && (
                <>
                  {children.map((subItem = {}) => {
                    const { id = '', subTitle = '', subPrice = 0, isSubShow = false } = subItem;

                    return (
                      <>
                        {isSubShow && (
                          <div className={styles.goodsItemChildren} key={id}>
                            <div className={styles.subTitle}>
                              <img src={isSelected ? selected : noSelected} alt="" onClick={hasSelected} />
                              {subTitle}
                              <span className={styles.tips} onClick={() => hytValuation()}>
                                《保价说明》
                              </span>
                            </div>
                            <div className={styles.subPrice}>
                              <span>{subPrice < 0 ? '' : '￥'}</span>
                              {subPrice}
                            </div>
                          </div>
                        )}
                      </>
                    );
                  })}
                </>
              )}
            </div>
          );
        },
      )}
      <div className={styles.totalPrice}>
        <span>总计：</span>
        <span>￥</span>
        <span>{totalPrice}</span>
      </div>
    </div>
  );
};

export default connect(({ drugPlanAndOrder }: { drugPlanAndOrder: IDrugPlanAndOrderModelState }) => ({
  drugPlanAndOrder,
}))(DrugPrice);
