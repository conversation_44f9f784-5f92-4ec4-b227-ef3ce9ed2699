.goodsInfo {
  margin: 24px 0;
  background-color: #fff;
  border-radius: 8px;
}
.goodsInfo .goodsItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32px 22px;
  color: #03081a;
}
.goodsInfo .goodsItem .title {
  display: flex;
  align-items: center;
  font-size: 32px;
}
.goodsInfo .goodsItem .title .refundLabel {
  margin-left: 16px;
  padding: 4px 8px;
  color: #fff;
  font-size: 20px;
  background-color: #b0b3bf;
  border-radius: 18px;
}
.goodsInfo .goodsItem .price {
  font-weight: bold;
  font-size: 40px;
}
.goodsInfo .goodsItem .price span {
  font-size: 28px;
}
.goodsInfo .goodsItem .emptyPrice {
  color: #989eb4;
  font-size: 32px;
}
.goodsInfo .goodsItemChildren {
  display: flex;
  justify-content: space-between;
  padding: 0 22px;
  color: #03081a;
}
.goodsInfo .goodsItemChildren .subTitle {
  font-size: 28px;
  display: flex;
  align-items: center;
}
.goodsInfo .goodsItemChildren .subTitle img {
  width: 30px;
  height: 30px;
  margin-right: 5px;
}
.goodsInfo .goodsItemChildren .subTitle .tips {
  color: #3AD3C1;
  text-decoration: underline;
}
.goodsInfo .goodsItemChildren .subPrice {
  font-weight: bold;
  font-size: 30px;
}
.goodsInfo .goodsItemChildren .subPrice span {
  font-size: 28px;
}
.goodsInfo .totalPrice {
  padding: 32px 22px;
  text-align: right;
}
.goodsInfo .totalPrice :nth-child(1) {
  color: #03081a;
  font-size: 32px;
}
.goodsInfo .totalPrice :nth-child(2) {
  color: #fc4553;
  font-weight: bold;
  font-size: 28px;
}
.goodsInfo .totalPrice :nth-child(3) {
  color: #fc4553;
  font-weight: bold;
  font-size: 40px;
}
.valueUnsure {
  display: flex;
  align-items: center;
  justify-content: center;
}
.valueUnsure img {
  width: 28px;
  height: 28px;
  margin-right: 6px;
}
.valueUnsure .span {
  font-size: 20px;
  color: #03081A;
  font-weight: bold;
}
.message {
  height: 300px;
  overflow-x: hidden;
}
