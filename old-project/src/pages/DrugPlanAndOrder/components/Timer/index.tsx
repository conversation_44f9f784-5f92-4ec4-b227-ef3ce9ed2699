import React, { useEffect, useState } from 'react';
import styles from './index.less';

interface IProps {
  /** 计时时间 */
  invalidTime: number;
  /** 自定义类名，控制样式 */
  className?: string;
  /** 刷新事件 */
  onEnd?: () => void;
}

const Timer = (props: IProps) => {
  const { invalidTime = 0, onEnd, className } = props;
  const [toPayTime, setToPayTime] = useState<number>(invalidTime);

  useEffect(() => {
    let payTime: any = 0;
    if (toPayTime !== 0) {
      payTime = setInterval(() => {
        setToPayTime((toPayTime) => {
          if (toPayTime === 1000) {
            clearInterval(payTime);
            onEnd && onEnd();
          }
          return toPayTime - 1000;
        });
      }, 1000);
    }
    return () => {
      clearInterval(payTime);
    };
  }, []);

  const day = toPayTime > 0 ? Math.floor(toPayTime / 1000 / 3600 / 24) : 0;
  const hour = toPayTime > 0 ? Math.floor((toPayTime / 1000 / 3600) % 24) : 0;
  const minute = toPayTime > 0 ? Math.floor((toPayTime / 1000 / 60) % 60) : 0;
  const second = toPayTime > 0 ? Math.floor((toPayTime / 1000) % 60) : 0;

  return (
    <div className={`${styles.item} ${className}`}>
      {day !== 0 ? (
        <div className={styles.Timer}>
          剩余：{day}天{hour}小时{minute}分钟
        </div>
      ) : (
        <div className={styles.Timer}>
          剩余：{hour}小时{minute}分钟{second}秒
        </div>
      )}
    </div>
  );
};

export default Timer;
