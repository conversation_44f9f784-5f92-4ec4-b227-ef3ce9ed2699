import React, { FC } from 'react';
import { HxIcon } from '@/components';
import { history } from 'umi';
import { copyText } from '@/utils/tool';
import Timer from '../Timer';
import styles from './index.less';

interface IProps {
  /** 状态栏信息 */
  statusInfo?: any;
  /** 详情接口 */
  fetchDrugPlan?: () => void;
  orderStatusNotice?: string;
}

const OrderTop: FC<IProps> = (props) => {
  const {
    orderStatus = 0,
    payId = '',
    remainingTime = '',
    fetchDrugPlan,
    prescUrl = '',
    orderStatusNotice = '',
    orderStatusDesc = '',
    status = 1,
  } = props?.statusInfo;

  const ORDER_STATUS = {
    0: {
      statusName: '待支付',
      desc: remainingTime && (
        <Timer invalidTime={Number(remainingTime)} className={styles.timer} onEnd={fetchDrugPlan} />
      ),
      icon: 'mall-wait',
    },
    201: {
      statusName: '待配货',
      desc: '药房人员正在紧急打包中...',
      icon: 'mall-wait',
    },
    202: {
      statusName: '待发货',
      desc: '药房人员正在紧急发货',
      icon: 'mall-wait',
    },
    301: {
      statusName: '待收货',
      desc: `运单编号: ${payId}`,
      icon: 'mall-wait',
    },
    203: {
      statusName: '待取货',
      desc: '请通过店铺地址前往取货哦~',
      icon: 'mall-wait',
    },
    302: {
      statusName: '已完成',
      desc: '感谢使用华医通APP~',
      icon: 'image-finish',
    },
    101: {
      statusName: '已取消',
      desc: '您已取消订单',
      icon: 'mall-error',
    },
    401: {
      statusName: '已退款',
      desc: '',
      icon: 'image-finish',
    },
    204: {
      statusName: '打包中',
      desc: '药房人员正在紧急打包......',
      icon: 'image-finish',
    },
  };
  const { desc = '', icon = '' } = ORDER_STATUS[orderStatus] || {};

  return (
    <div className={styles.topInfo}>
      <div className={styles.orderStatusDesc}>
        <div className={styles.orderStatus}>
          <HxIcon iconName={icon} className={styles.icon} />
          <div>{orderStatusDesc}</div>
        </div>
        <div className={styles.desc}>
          {/* 因为之前都已经写好了，后端又要改 */}
          {orderStatus === 0 || orderStatus === 301 ? desc : orderStatusNotice}
          {orderStatus === 301 && (
            <div
              className={styles.copy}
              onClick={() => {
                copyText(payId);
              }}
            >
              复制
            </div>
          )}
        </div>
      </div>
      {/* {(orderStatus === 202 ||
        orderStatus === 204 ||
        orderStatus === 301 ||
        orderStatus === 203 ||
        orderStatus === 302) && (
        <div onClick={() => history.push(`/drugplanandorder/prescription?prescUrl=${prescUrl}&status=${status}`)}>
          查看处方
        </div>
      )
      } */}
    </div>
  );
};

export default OrderTop;
