import React, { FC, useState, useEffect } from 'react';
import { Dispatch, connect, Loading } from 'umi';

import { Modal, Toast } from 'antd-mobile';
import styles from './index.less';

const sendicon = require('@/assets/sendicon.png');
const radio = require('@/assets/radio.png');
const selradio = require('@/assets/selradio.png');
const enter = require('@/assets/enter.png');

interface IProps {
  /** 选中tab的唯一标识 */
  alreadySelect?: number;

  /** 显示定点配送的预留信息 */
  displayInfo?: any;
  dispatch: Dispatch;
  sendParms: Object;
  sendTimeParms: any; // 定点配送的时间
  storageType: any;
  params?: any;
  location?: {
    search: any;
  };
}

const OrderInfo: FC<IProps> = (props) => {
  // const { dispatch, displayInfo, sendParms } = props;
  const { dispatch, displayInfo, sendParms = {}, storageType, sendTimeParms, alreadySelect }: any = props;
  console.log(9999999, props);
  const [visible, setVisible] = useState<boolean>(false);
  const [sendTime, setSendTime] = useState<string>('');
  const [tipsModal, setTipsModal] = useState<boolean>(false);
  const [dateIndex, setDateIndex] = useState<number>(0); //  当前选中的日期
  const [timeIndex, setTimeIndex] = useState<number>(0); //  当前选中的时间点
  const [begin, setBegin] = useState<string>(''); //  开始时间
  const [end, setEnd] = useState<string>(''); //  结束时间

  // 物流配送信息
  // const [shipTypeDtoList, setShipTypeDtoList] = useState<boolean>([]);
  // 定时定点数据
  const [shipAppointDtoList, setShipAppointDtoList] = useState<any>([]);
  const [newSegmentDate, setNewSegmentDate] = useState<any>([]); //  当前选中的时间点
  const [contentVisible, setContentVisible] = useState<boolean>(false); //  是否显示定时定点配送
  const { bizSubType, bizSysSeq, drugStoreId } = sendParms;
  const [address, setAddress] = useState<string>('');
  const [delivery, setDelivery] = useState<string>(''); //  送货时间
  const [deliveryDate, setDeliveryDate] = useState<string>(''); //  送货日期

  const selectedTime = () => {
    setVisible(!visible);
    displayInfo(!visible);
  };
  // 处理数组
  const handleDate = (data, index) => {
    data.shipAppointDtoList &&
      data.shipAppointDtoList.map((item, index2) => {
        if (data.shipAppointDtoList[index].preDate === data.shipAppointDtoList[index2].preDate) {
          setNewSegmentDate(data.shipAppointDtoList[index2].segmentDate);
        }
      });
  };
  // /** 获取定点配送的时间 */
  const getSegmentDate = () => {
    dispatch({
      type: 'drugPlanAndOrder/timeQuery',
      payload: {
        shopId: drugStoreId,
        storageType,
      },

      callback: (data: any = {}) => {
        if (data) {
          const { shipAppointDtoList = [], appointArea } = data;
          if (shipAppointDtoList?.length > 0) {
            // setBegin(shipAppointDtoList[0]?.preDate);
            // setEnd(shipAppointDtoList[0]?.segmentDate[0]?.note);
            handleDate(data, 0);
            setShipAppointDtoList(shipAppointDtoList);
            setAddress(appointArea);
          }
        }
      },
    });
  };

  /** 获取物流方式信息数据 */
  const getShipTypeDtoList = () => {
    dispatch({
      type: 'drugPlanAndOrder/logisticsInfo',
      payload: {
        bizSubType,
        bizSysSeq,
      },
      callback: (data: any) => {
        const { shipTypeDtoList = [] } = data;
        shipTypeDtoList &&
          shipTypeDtoList.map((item: any): void => {
            if (item.shippingType === 3) {
              setContentVisible(true);
            }
          });
      },
    });
  };

  // 提交时间
  const goConfirm = () => {
    if (begin === '' && shipAppointDtoList?.length > 0) {
      const defaultBegin = shipAppointDtoList[0]?.preDate;
      const defaultEnd = shipAppointDtoList[0]?.segmentDate[0]?.note;
      setBegin(defaultBegin);
      setEnd(defaultEnd);
      const time = `${defaultBegin}  ${defaultEnd}`;
      setSendTime(time);
      const params = {
        defaultBegin,
        defaultEnd,
        address,
      };
      // 回调定点配送参数
      sendTimeParms(params);
    } else {
      const params = {
        begin,
        end,
        address,
      };
      const date = deliveryDate || shipAppointDtoList[0].preDate;
      const selTime = `${date} ${delivery}`;
      // 回调定点配送参数
      sendTimeParms(params);
      setSendTime(selTime);
    }

    setTipsModal(false);
  };
  // 选择日期
  const selDate = (index, value) => {
    setDateIndex(index);
    setDeliveryDate(value);
  };
  // 选择时间
  const selTime = (item, index, note) => {
    setTimeIndex(index);
    setBegin(item.begin);
    setEnd(item.end);
    setDelivery(note);
  };
  // 显示时间模态框
  const showModal = () => {
    setTipsModal(true);
    displayInfo(true);
    setBegin('');
    setEnd('');
  };

  // 获取物流方式信息表和定点数据
  useEffect(() => {
    getShipTypeDtoList();
    getSegmentDate();
  }, []);
  return (
    <>
      {alreadySelect === 1 && contentVisible ? (
        <div className={styles.sendInfo}>
          <div className={styles.sendContent}>
            <div className={styles.sendAddress}>
              <img src={sendicon} alt="" />
              {/* <span className={styles.name}>定点定时配送</span> */}
              <p>{address}</p>
            </div>
            <div className={styles.checkRadio}>
              <img src={visible ? selradio : radio} alt="" onClick={selectedTime} />
            </div>
          </div>
          {visible ? (
            <div className={styles.selTime}>
              {sendTime || '请选择配送时间'}
              <img src={enter} alt="" onClick={showModal} />
            </div>
          ) : (
            ''
          )}
          {/* 定点配送  这次需求不上线 */}
          {/* <Modal
            popup
            className="selTimemodal"
            visible={tipsModal}
            transparent
            maskClosable={false}
            animationType="slide-up"
            footer={[
              {
                text: '确定',
                onPress: () => {
                  goConfirm();
                },
              },
            ]}
          >
            <div className="modalContent">
              <div className="selectTime">
                <div> 选择时间</div>
                <div>
                  <img src="tipsIcon" alt="" />
                </div>
              </div>
              <div className="distributionInfo sendAddress">
                <div className="title">配送点</div>
                <div>{address}</div>
              </div>
              <div className="distributionInfo sendTime">
                <div className="title">送货时间</div>
                <div className="timeList">
                  <div className="date">
                    {shipAppointDtoList &&
                      shipAppointDtoList?.length &&
                      shipAppointDtoList.map((item, index) => {
                        return (
                          <div
                            className={dateIndex === index ? 'activeDate' : ''}
                            key={Number(index)}
                            onClick={() => selDate(index, item.preDate)}
                          >
                            {item.preDate || ''}
                          </div>
                        );
                      })}
                  </div>
                  <div className="time">
                    {newSegmentDate &&
                      newSegmentDate.length &&
                      newSegmentDate.map((item, index) => {
                        return (
                          <div
                            className={timeIndex === index ? 'activeTime' : ''}
                            key={Number(index)}
                            onClick={() => selTime(item, index, item.note)}
                          >
                            {item.note || ''}
                          </div>
                        );
                      })}
                  </div>
                </div>
              </div>
            </div>
          </Modal> */}
        </div>
      ) : (
        ''
      )}
    </>
  );
};

export default connect(({ loading }: { loading: Loading }) => ({
  // loading: loading.effects['drugPlanAndOrder/fetchOrderDetail'],
}))(OrderInfo);
