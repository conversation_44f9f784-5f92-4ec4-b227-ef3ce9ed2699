.container {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f6fa;
}
.container .content {
  width: 100%;
  padding: 48px 24px 0 24px;
  padding-bottom: 200px;
  background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/order-bg.png') no-repeat;
  background-size: 100% 292px;
}
.container .content .agree {
  display: flex;
  align-items: center;
  margin-bottom: 200px;
}
.container .content .agree .icon {
  width: 24px;
  height: 24px;
  margin-right: 6px;
}
.container .content .agree div {
  color: #989eb4;
  font-size: 24px;
}
.container .content .agree div :nth-child(2) {
  color: #3ad3c1;
}
.container .bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #f5f6fa;
}
.container .bottom .tips {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 24px;
  color: #989eb4;
  font-size: 20px;
  text-align: center;
  background-color: #f5f6fa;
}
.container .bottom .tips .tipsIcon {
  width: 54px;
  height: 54px;
  margin-right: 4px;
}
.container .bottom .tips .textContent {
  color: #989eb4;
  text-align: left;
}
.container .bottom .tips .textContent p {
  margin: 0;
}
.container .bottom .btnBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 20px 24px;
  background-color: #fff;
  box-shadow: 0 0 8px 0 #ebedf5;
}
.container .bottom .btnBox .payPrice {
  color: #fc4553;
  font-weight: 600;
}
.container .bottom .btnBox .payPrice :nth-child(1) {
  font-size: 26px;
}
.container .bottom .btnBox .payPrice :nth-child(2) {
  font-size: 36px;
}
.container .bottom .btnBox .payBtn {
  width: 192px;
  height: 72px;
  color: #fff;
  font-weight: 600;
  font-size: 28px;
  line-height: 72px;
  text-align: center;
  background-color: #3ad3c1;
  border-radius: 40px;
}
.yiBao {
  display: flex;
  align-items: center;
  justify-content: center;
}
.yiBao img {
  width: 28px;
  height: 28px;
  margin-right: 6px;
}
.yiBao .span {
  font-size: 28px;
  color: #03081a;
  font-weight: bold;
}
.yiBaoContent {
  text-align: center;
  font-size: 30px;
  color: #03081a;
}
.valueInsure {
  display: flex;
  align-items: center;
  justify-content: center;
}
.valueInsure img {
  width: 28px;
  height: 28px;
  margin-right: 6px;
}
.valueInsure .span {
  font-size: 20px;
  color: #03081a;
  font-weight: bold;
}
