import React, { FC, useState, useEffect } from 'react';
import { HxIcon } from '@/components';
import { Modal, Toast } from 'antd-mobile';
import tixingIcon from '@/assets/icon_modal.png';
import AppScheme from '@/utils/AppScheme';
import { getOpenId, getToken } from '@/utils/parameter';
import { isHytPerson, isWechat } from '@/utils/platform';
import { Dispatch, connect, history, IDrugPlanAndOrderModelState } from 'umi';
import { configWechatGetLocation, getDistance, getCurEvnHref, openLocation } from '../tools';
import noSelected from '@/assets/HospitalizationService/icon_未选中.png';
import { SelectAddress, StoreNameItem, DrugItem, OnTimeSend, MoreDrug, DrugPrice } from '../components';
import styles from './index.less';
import { HxLocalStorage } from '@/utils/storage';

const safe = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/drug-plan-and-order/mall-safe.png';

interface IProps {
  drugPlanAndOrder: IDrugPlanAndOrderModelState;
  address: any; //  定点配送地址
  mine: any;
  dispatch: Dispatch;
  location?: {
    query?: any;
  };
}

const Distribution: FC<IProps> = (props) => {
  const {
    dispatch,
    location: { query = {} },
    drugPlanAndOrder,
  }: any = props;

  const { orderCheckoutData = {}, isValueInsure = 0 } = drugPlanAndOrder;
  const { prices = {}, products = [], shop = {}, shipTypeDtoList = [] } = orderCheckoutData;
  const { actualPrice = 0, freightPrice = 0, goodsPrice = 0, valueInsuredFee = 0, couponPrice = '' } = prices;
  console.log('freightPrice', prices, freightPrice);
  const { name = '' } = shop;
  const valuationShow = shipTypeDtoList ? shipTypeDtoList.some((item) => item.shippingType === 4) : '';
  const {
    data = '{}',
    shopId = '',
    products: productsList = '',
    bizSysSeq = '',
    bizSubType = '',
    schemeId = '',
    drugStoreId = '',
    status = 0,
  } = query;
  const sendParms = {
    bizSysSeq,
    bizSubType,
    drugStoreId,
  };
  const goodsInfoObj = { shopId, productsList, bizSysSeq, schemeId };
  // 存储商品信息
  productsList && shopId && localStorage.setItem('goodsInfoStorage', JSON.stringify(goodsInfoObj));

  //  useState
  const [alreadySelect, setAlreadySelect] = useState<number>(1);
  const [transType, setTransType] = useState<number>(0); // 运输类型
  const [isAgree, setIsAgree] = useState<boolean>(true);
  const [telePhone, setTelePhone] = useState<string>();
  const [addressObj, setAddressObj] = useState<any>({});
  const [wechatDistance, setWechatDistance] = useState<any>(0);
  const [reservedInfo, setReservedInfo] = useState<boolean>(false); //   是否显示预留信息
  const [storageType, setStorageType] = useState<string>(''); //  定点配送参数
  const [TimeParms, setTimeParms] = useState<any>(); // 定点配送时间，地址
  const [nameParams, setNameParams] = useState<string>(); // 预定姓名传参
  const [mobileParams, setMobileParams] = useState<string>(); // 预定电话参数
  const [shipType, setShipType] = useState<number>(0); // 配送方式
  const [frePrice, setFrePrice] = useState<number>(0);
  // 获取商品信息
  const getGoodsInfo = JSON.parse(localStorage.getItem('goodsInfoStorage') || '{}');
  // const frePrice = HxLocalStorage.get('freightPrice') || freightPrice; // 运费

  const {
    shopId: localStorageShopId = '',
    productsList: localStorageProductsList = '',
    bizSysSeq: localStoragebizSysSeq,
    schemeId: localStorageschemeId,
  } = getGoodsInfo;

  const selectAddressObj = (data && JSON.parse(data)) || {};
  // 接口需要的参数
  const payload = {
    address: reservedInfo ? TimeParms?.address : addressObj?.detailAddress || selectAddressObj?.detailAddress,
    cityCode: selectAddressObj?.cityCode || addressObj?.cityCode || '',
    consignee: reservedInfo ? nameParams : selectAddressObj?.contactUserName || addressObj?.contactUserName || '',
    districtCode: selectAddressObj?.areaCode || addressObj?.areaCode,
    mobile: reservedInfo
      ? mobileParams
      : alreadySelect === 1
      ? selectAddressObj?.phone || addressObj?.phone
      : telePhone,
    provinceCode: selectAddressObj?.provinceCode || addressObj?.provinceCode || '',
    shippingType: transType,
    shopId: shopId || localStorageShopId,
    bizSysSeq: bizSysSeq || localStoragebizSysSeq,
    bizSubType,
    bizOrderCode: schemeId || localStorageschemeId,
    products:
      (productsList && JSON.parse(productsList)) || (localStorageProductsList && JSON.parse(localStorageProductsList)),
    valueInsured: Number(isValueInsure),
  };

  // 医保账户余额 弹窗
  useEffect(() => {
    if (couponPrice && Number(couponPrice) === 0) {
      Modal.alert(
        <div className={styles.yiBao}>
          <img src={tixingIcon} alt="" />
          <span>医保报销超额提醒</span>
        </div>,
        <div className={styles.yiBaoContent}>
          <span style={{ color: '#FC4553' }}>您的单行支付药品本年度内可报销余额为0</span>
          ，需要全额自费购买本次药品，请知悉
        </div>,
        [{ text: '我知道了' }],
      );
    }
  }, [couponPrice]);

  // 配送距离超出成都范围提醒弹窗
  const overDistance = () => {
    Modal.alert(
      <div className={styles.yiBao}>
        <img src={tixingIcon} alt="" />
        <span>配送范围提示</span>
      </div>,
      <div className={styles.yiBaoContent}>当前仅支持配送到成都市区域内，请重新选择地址。</div>,
      [{ text: '我知道了' }],
    );
  };
  // 重复调用的地方

  const repeatDetail = (shiptype) => {
    const defaultAddressObj = HxLocalStorage.get('defaultAddressObj') || [];
    console.log('重复调用', shiptype);
    const { detailAddress = '', areaCode = '', cityCode = '', provinceCode = '' } = defaultAddressObj;
    dispatch({
      type: 'drugPlanAndOrder/OrderCheckout',
      payload: {
        ...payload,
        shippingType: shiptype,
        address: detailAddress,
        cityCode,
        provinceCode,
        districtCode: areaCode,
      },
      callback: (res: any) => {
        console.log('any', res);
        const { prices = [] } = res;
        const { freightPrice = '' } = prices;
        setFrePrice(freightPrice);
        setTransType(shiptype);

        HxLocalStorage.set('freightPrice', freightPrice);
      },
    });
  };
  // 查询购药方案
  const fetchDrugPlan = () => {
    console.log('获取数据');
    // 获取地址列表
    dispatch({
      type: 'address/findListAddress',
      callback: (address: any) => {
        const defaultAddressObj = address.find((item: any) => item.defaultAddress === '1') || {};
        const {
          detailAddress = '',
          cityCode = '',
          contactUserName = '',
          areaCode = '',
          phone = '',
          provinceCode = '',
          cityName = '',
        }: any = defaultAddressObj;
        setAddressObj(defaultAddressObj); // 设置默认地址
        HxLocalStorage.set('defaultAddressObj', defaultAddressObj);
        const payloadOne = {
          address: detailAddress,
          cityCode,
          consignee: contactUserName,
          districtCode: areaCode,
          mobile: alreadySelect === 1 ? phone : telePhone,
          provinceCode,
          shippingType: transType, // 0是获取所有的配送方式
          shopId: shopId || localStorageShopId,
          bizSysSeq: bizSysSeq || localStoragebizSysSeq,
          bizSubType,
          bizOrderCode: schemeId || localStorageschemeId,
          products:
            (productsList && JSON.parse(productsList)) ||
            (localStorageProductsList && JSON.parse(localStorageProductsList)),
          valueInsured: Number(isValueInsure),
        };
        dispatch({
          type: 'drugPlanAndOrder/OrderCheckout',
          payload: payloadOne,
          callback: (data: any) => {
            const { storageType, shipTypeDtoList = [], prices = [] } = data;
            const { freightPrice = '' } = prices;
            HxLocalStorage.set('freightPrice', freightPrice);
            const shiptype = shipTypeDtoList ? shipTypeDtoList.some((item) => item.shippingType === 4) : '';
            if (shiptype) {
              repeatDetail(4);
              setTransType(4);
            } else {
              repeatDetail(1);
              setTransType(1);
            }
            setStorageType(storageType);
          },
        });
        // 判断是否超出成都范围
        // const isOver = cityName.includes('成都');
        // 如果在成都范围就显示 收货地址
        // if (isOver) {

        // }
        // console.log('payloadOne=======',payloadOne);
        // 如果不在成都范围就不显示 收货地址
      },
    });
  };

  useEffect(() => {
    setShipType(4);
  }, [data]);

  const fetchDetail = () => {
    dispatch({
      type: 'drugPlanAndOrder/OrderCheckout',
      payload: {
        ...payload,
        shippingType: shipType,
      },
      callback: (res) => {
        console.log('是否为4resfetchDetail', res, shipType);
        const { shipTypeDtoList = [], prices = [] } = res;
        const { freightPrice = '' } = prices;
        HxLocalStorage.set('freightPrice', freightPrice);
        const isFour = shipTypeDtoList ? shipTypeDtoList.some((item) => item.shippingType === 4) : '';
        if (isFour && shipType !== 4) {
          repeatDetail(4);
          setTransType(4);
        } else {
          repeatDetail(1);
          setTransType(1);
        }
      },
    });
  };

  useEffect(() => {
    if (Object.keys(data).length === 2) {
      fetchDrugPlan();
    } else {
      const { cityName = '' } = JSON.parse(data);
      const isOver = cityName.includes('成都');
      if (isOver) {
        setAddressObj(JSON.parse(data));
        HxLocalStorage.set('defaultAddressObj', JSON.parse(data));

        fetchDetail();
      } else if (cityName) {
        overDistance();
      }
    }
  }, [data]);
  // useEffect(() => {
  //   // data是地址数据
  //   if (Object.keys(data).length !== 2) {
  //     setAddressObj(JSON.parse(data));
  //   }
  // }, [data]);

  // 调用微信sdk，获取微信配置信息
  const fetchConfigData = () => {
    dispatch({
      type: 'global/singWechatJSSDK',
      payload: {
        url: getCurEvnHref(),
      },
      callback: (data: any) => {
        const configData = {
          ...data,
          debug: false,
          jsApiList: ['getLocation'],
        };
        configWechatGetLocation(configData, shop)
          .then(function (res) {
            console.log('当前多少米res', res);
            setWechatDistance(res);
          })
          .catch(function (err) {
            console.log('promiseerr=====', err);
          });
      },
    });
  };

  const goodsInfo = [
    {
      title: '商品金额',
      price: goodsPrice,
      show: true,
      isRefundShow: false,
      id: 0,
    },
    {
      title: '运费',
      price: frePrice,
      show: alreadySelect === 1,
      isRefundShow: false,
      id: 1,
      children: [
        {
          subTitle: '物流保价费',
          subPrice: valueInsuredFee,
          isSubShow: !valuationShow,
        },
      ],
    },
  ];
  const goNavigation = () => {
    const lat = window?.lat;
    const lng = window?.lng;
    const address = window?.address;
    const { city = '' } = address;
    const { detailAddress = '' } = addressObj;
    // region 起点和终点的城市名或者县名
    // destination 终点名称或经纬度
    window.location.href = `http://api.map.baidu.com/direction?origin=${lat},${lng}|name:我的位置&destination=${detailAddress}&mode=driving&region=${city}&output=html&src=webapp.baidu.openAPIdemo`;
    // window.location.href = `http://api.map.baidu.com/marker?location=${lat},${lng}&&title=我的位置&content=${city}&&output=html&src=webapp.baidu.openAPIdemo`;
  };
  /** 打开百度地图 */
  const getMap = () => {
    if (isWechat()) {
      dispatch({
        type: 'global/singWechatJSSDK',
        payload: {
          url: getCurEvnHref(),
        },
        callback: (data: any) => {
          const configData = {
            ...data,
            debug: false,
            jsApiList: ['getLocation', 'openLocation'],
          };
          openLocation(configData, shop);
        },
      });
    } else {
      goNavigation();
    }
  };
  /** 计算距离当前多少米 */
  const getPosition = () => {
    const lat = window?.lat;
    const lng = window?.lng;
    const { latitude: shopLatitude = 0, longitude: shopLongitude = 0 } = shop;
    const distance = getDistance(Number(lat), Number(lng), Number(shopLatitude), Number(shopLongitude)) || '';
    console.log('经纬度,当前距离', distance, lat, lng, shopLatitude, shopLongitude);
    if (distance) {
      setWechatDistance(distance);
    }
  };

  const fetchPhonePosition = () => {
    if (isWechat()) {
      fetchConfigData();
    } else {
      // 网页获取定位
      getPosition();
    }
  };

  // 切换tab
  const selectMethod = (sub: number) => {
    setAlreadySelect(sub);
    setTransType(sub);
    if (sub === 2) {
      Modal.alert('定位获取', <div>华医通将获取您的当前定位</div>, [
        { text: '取消' },
        {
          text: <div style={{ color: '#3AD3C1' }}>允许</div>,
          onPress: () => {
            Object.keys(shop).length !== 0 && fetchPhonePosition();
          },
        },
      ]);
    }
  };

  // 获取预留号码
  const inputOnChange = (val: string, type: string) => {
    if (type === 'mobile') {
      setMobileParams(val);
    } else if (type === 'consignee') {
      setNameParams(val);
    } else {
      dispatch({
        type: 'drugPlanAndOrder/updateState',
        payload: {
          modelPhone: val,
        },
      });
      setTelePhone(val);
    }
  };

  // 创建订单
  const createOrder = () => {
    // 目前这个版本不上定点配送，先注释
    // const { begin = '', end = '' } = TimeParms; // 定点配送参数
    // let sendParams;
    // if (reservedInfo) {
    //   sendParams = {
    //     ...payload,
    //     begin,
    //     end,
    //   };
    // }
    // const { begin = '', end = '', address = '' } = TimeParms || {};
    console.log('transType在哪里', transType, isValueInsure);
    dispatch({
      type: 'drugPlanAndOrder/createOrder',
      payload: {
        ...payload,
        valueInsured: Number(isValueInsure),
      },

      callback: (creatData: any) => {
        const { code = '1', data = {}, msg = '' } = creatData || {};
        const { bizSysSeq = '', dealSeq = '', merchantSeq = '' } = data || {};
        if (code === '1') {
          if (isHytPerson()) {
            AppScheme.toPay({ dealSeq, bizSysSeq, merchantSeq });
          } else {
            window.location.href = `${API_BASE}/cloud/paygateway/getCashier?bizSysSeq=${bizSysSeq}&dealSeq=${dealSeq}&merchantSeq=${merchantSeq}&accessToken=${getToken()}&openid=${getOpenId()}`;
          }
          // 有status代表特药订单
          if (status) {
            const params = {
              schemeId,
              status,
              drugStoreId,
            };
            dispatch({
              type: 'drugPlanAndOrder/updataOrderStatus',
              payload: params,
            });
          }
        } else {
          Toast.fail(msg, 1.5);
        }
      },
    });
  };

  // 跳转到收银台
  const toPayBtn = () => {
    if (alreadySelect === 1) {
      // 地址检查
      const { cityName = '' } = JSON.parse(data);
      const isOver = cityName.includes('成都');
      if (!isOver) {
        overDistance();
        return;
      }
    }
    if (alreadySelect === 1 && !valuationShow && !isValueInsure) {
      Modal.alert(
        <div className={styles.valueInsure}>
          <img src={tixingIcon} alt="" />
          <span>保价提醒</span>
        </div>,
        <span style={{ textAlign: 'center', fontSize: '16px' }}>
          特殊药品位高价值药品，建议您选择物流保价服务，为药品提供安全赔付保障
        </span>,
        [
          {
            text: '取消',
            onPress: () => createOrder(),
          },
          {
            text: '保价',
            onPress: () => {
              dispatch({
                type: 'drugPlanAndOrder/updateState',
                payload: {
                  ...payload,
                  isValueInsure: true,
                },
                callback: () => {
                  createOrder();
                },
              });
            }, // 跳转到支付页面
          },
        ],
      );
    } else {
      if (!status) {
        Modal.alert(
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            <HxIcon iconName="tips" style={{ width: '24px', height: '24px' }} />
            <div style={{ marginLeft: '4px' }}>确认支付</div>
          </div>,
          '本订单为外购药品，需要自行付费，以药房实际报销情况为准。',
          [
            {
              text: '再想一想',
            },
            {
              text: '确认',
              onPress: () => createOrder(), // 跳转到支付页面
            },
          ],
        );
      }
      createOrder();
    }
  };

  /**
   * 支付按钮
   * 物流配送---判断是否选择地址以及是否勾选购药须知
   * 线下自取--判断是否填写预留电话以及是否勾选购药须知
   */
  // const toPayBtn = () => {
  //   switch (alreadySelect) {
  //     case 1:
  //       if (!addressObj) {
  //         Toast.info('请选择配送地址', 5);
  //       } else if (!isAgree) {
  //         Toast.info('请先阅读并同意购药须知', 5);
  //       } else {
  //         Modal.alert(
  //           <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
  //             <HxIcon iconName="tips" style={{ width: '24px', height: '24px' }} />
  //             <div style={{ marginLeft: '4px' }}>确认支付</div>
  //           </div>,
  //           '本订单为外购药品，需要自行付费，是否可报销以实际报销情况为准。',
  //           [
  //             {
  //               text: '再想一想',
  //             },
  //             {
  //               text: '确认',
  //               onPress: () => toCheckstand(), // 跳转到支付页面
  //             },
  //           ],
  //         );
  //       }
  //       break;
  //     case 2:
  //       if (!telePhone) {
  //         Toast.info('请填写预留电话', 5);
  //       } else if (!/^1[3456789]\d{9}$/.test(telePhone)) {
  //         Toast.info('手机号填写有误，请重新填写', 1.5);
  //       } else if (!isAgree) {
  //         Toast.info('请先阅读并同意购药须知', 5);
  //       } else {
  //         dispatch({
  //           type: 'drugPlanAndOrder/createOrder',
  //           payload: {
  //             ...payload,
  //             valueInsured: Number(isValueInsure),
  //           },
  //           callback: (creatData: any) => {
  //             const { code = '1', data = {}, msg = '' } = creatData || {};
  //             const { bizSysSeq = '', dealSeq = '', merchantSeq = '' } = data || {};
  //             if (code === '1') {
  //               if (isHytPerson()) {
  //                 AppScheme.toPay({ dealSeq, bizSysSeq, merchantSeq });
  //               } else {
  //                 window.location.href = `${API_BASE}/cloud/paygateway/getCashier?bizSysSeq=${bizSysSeq}&dealSeq=${dealSeq}&merchantSeq=${merchantSeq}&accessToken=${getToken()}&openid=${getOpenId()}`;
  //               }
  //             } else {
  //               Toast.fail(msg, 1.5);
  //             }
  //           },
  //         });
  //       }
  //       break;
  //     default:
  //       break;
  //   }
  // };

  /** 选择地址页面 */
  const toSelectAddress = () => {
    console.log('默认地址99999status', status);

    if (Number(status) !== 5) {
      history.replace('/common/businessaddress?to=MALL');
    } else {
      history.replace('/drugplanandorder/address');
    }
  };

  /** 跳转去阅读华医通购买协议 */
  const hytAgreeReading = () => {
    window.location.href = 'https://hxgyapiv2.cd120.info/cloud/hosplatarticle/page/viewPage?articleId=234&shared=1';
  };
  // 预留信息的显示
  const displayInfo = (status) => {
    setReservedInfo(status);
  };
  useEffect(() => {
    dispatch({
      type: 'mine/queryUserInfo',
      callback: (userInfo: any) => {
        setTelePhone(userInfo?.contactMobile);
      },
    });
  }, []);

  // 获取定点配送信息的参数
  const sendTimeParms = (sendTimeParms) => {
    setTimeParms(sendTimeParms);
  };
  /** 勾选运费保价重新调用接口 */
  const onSelect = () => {
    dispatch({
      type: 'drugPlanAndOrder/OrderCheckout',
      payload: {
        ...payload,
        valueInsured: Number(isValueInsure),
      },
    });
  };

  useEffect(() => {
    onSelect();
  }, [isValueInsure]);

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <SelectAddress
          selectMethod={selectMethod}
          alreadySelect={alreadySelect}
          inputOnChange={(val: string, type: string) => inputOnChange(val, type)}
          toSelectAddress={toSelectAddress}
          addressInfo={addressObj}
          getMap={getMap}
          offlineAddress={shop}
          offPhone={telePhone}
          wechatDistance={wechatDistance}
          reservedInfo={reservedInfo}
        />
        {/* 定时定点配送 */}
        {/* <OnTimeSend
          displayInfo={displayInfo}
          sendParms={sendParms}
          storageType={storageType}
          sendTimeParms={sendTimeParms}
          alreadySelect={alreadySelect || 1}
        /> */}
        <StoreNameItem storeName={name} />
        {products.length === 1 ? (
          <DrugItem drugInfo={products[0]} />
        ) : (
          <MoreDrug drugList={products} totalPrice={goodsPrice} />
        )}
        {/* 商品信息部分 */}
        <DrugPrice
          goodsInfo={goodsInfo}
          transType={transType}
          totalPrice={alreadySelect === 1 ? actualPrice : goodsPrice}
          repeatDetail={repeatDetail}
        />
        <div className={styles.agree}>
          <HxIcon
            iconName={!isAgree ? 'checkbox-normal-gray-copy' : 'patientcard-success'}
            className={styles.icon}
            onClick={() => setIsAgree(!isAgree)}
          />
          <div>
            <span onClick={() => setIsAgree(!isAgree)}>您已同意并确认</span>
            <span onClick={() => hytAgreeReading()}>《华医通购买协议》</span>
          </div>
        </div>
      </div>
      <div className={styles.bottom}>
        <div className={styles.tips}>
          <img src={safe} alt="" className={styles.tipsIcon} />
          <div className={styles.textContent}>
            <p>药品等特殊商品，一经购买，概不退货。</p>
            <p>发票由线下药房提供，以实际报销情况为准。</p>
          </div>
        </div>
        <div className={styles.btnBox}>
          <div className={styles.payPrice}>
            <span>￥</span>

            <span>
              {alreadySelect === 1 ? (
                // eslint-disable-next-line radix
                parseInt(actualPrice) === actualPrice ? (
                  actualPrice
                ) : (
                  // eslint-disable-next-line react/jsx-no-comment-textnodes
                  <>
                    {parseInt(actualPrice, 10)}
                    <span>.{actualPrice.toString().replace(/\d+\.(\d*)/, '$1')}</span>
                  </>
                )
              ) : // eslint-disable-next-line radix
              parseInt(goodsPrice) === goodsPrice ? (
                goodsPrice
              ) : (
                // eslint-disable-next-line react/jsx-no-comment-textnodes
                <>
                  {parseInt(goodsPrice, 10)}
                  <span>.{goodsPrice.toString().replace(/\d+\.(\d*)/, '$1')}</span>
                </>
              )}
            </span>
          </div>
          <div className={styles.payBtn} onClick={() => toPayBtn()}>
            去支付
          </div>
        </div>
      </div>
    </div>
  );
};

export default connect(
  ({
    drugPlanAndOrder,
    address,
    mine,
  }: {
    drugPlanAndOrder: IDrugPlanAndOrderModelState;
    address: any;
    mine: any;
  }) => ({
    drugPlanAndOrder,
    address,
    mine,
  }),
)(Distribution);
