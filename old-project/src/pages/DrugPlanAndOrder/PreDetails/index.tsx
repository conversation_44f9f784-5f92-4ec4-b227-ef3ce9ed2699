import React, { FC } from 'react';
import { connect, IDrugPlanAndOrderModelState, Dispatch } from 'umi';
import styles from './index.less';
import qs from 'query-string';

const tipsIcon = require('@/assets/speMedicine/tipsIcon.png');

interface IProps {
  drugPlanAndOrder: IDrugPlanAndOrderModelState;
  dispatch: Dispatch;
  location?: {
    query?: {
      prescUrl?: string;
      status?: number;
    };
  };
}

const PrescriptionDetail: FC<IProps> = (props) => {
  const { location = {} } = props;
  const {
    query: { prescUrl = '' },
  }: any = location;
  const queryParams = qs.parse(window.location.href.split('?')[1]) || {};
  const url = prescUrl || queryParams?.prescUrl;
  console.log('999999', prescUrl, url);
  return (
    <div className={styles.container}>
      <div className={styles.reviewTips}>
        <div>
          <img src={tipsIcon} alt="" />
          处方审核通过后方可生效，才具备效力，处方失效 、停方、审核不通过均不可使用
        </div>
      </div>
      <div className={styles.wrapBox}>
        <iframe
          title=" "
          src={url}
          frameBorder="0"
          width="100%"
          height="100%"
          scrolling="no"
          style={{ minHeight: '100%' }}
        />
      </div>
    </div>
  );
};

export default connect(({ drugPlanAndOrder }: { drugPlanAndOrder: IDrugPlanAndOrderModelState }) => ({
  drugPlanAndOrder,
}))(PrescriptionDetail);
