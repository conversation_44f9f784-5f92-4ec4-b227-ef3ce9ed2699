const STATUS = [
  { value: 1, name: '待审核', color: '#FBBC44', icon: '' },
  { value: 0, name: '待审核', color: '#FBBC44', icon: '' }, // 后台说增加0 的状态，与待审核展示一致
  { value: 2, name: '审核通过', color: '#32B9AA', icon: 'check-circle' },
  { value: 3, name: '审核不通过', color: '#FC4553', icon: 'cross-circle-o' },
  { value: 4, name: '锁定', color: '#FC4553', icon: 'cross-circle-o' },
  { value: 5, name: '已停方', color: '#FC4553', icon: 'cross-circle-o' },
  { value: 6, name: '已失效', color: '#989EB4', icon: 'cross-circle-o' },
];

const REASON_LIST = [
  { value: 1, name: '病情需要' },
  { value: 2, name: '用药方案变更' },
  { value: 3, name: '药品缺货' },
  { value: 4, name: '药品无法配送' },
  { value: 5, name: '门特预交金不足' },
];
const TIPS = [
  '1．当前订单的所有处方均会停止。',
  '2．后台将无法继续进行处方审核或药品发货。',
  '3．你只能重新为患者开具新的处方，患者需要重新缴费和记账。',
  '4．发药后已寄出的药品不能退药。',
  '5．请与患者沟通后再完成停方操作。',
  '6．如药房已发药，且追回退药失败，将上报不良事件。',
];
const REVIEWER = [
  { value: 'hospital_review', name: '药剂科' },
  { value: 'insurance_review', name: '医保办' },
  { value: 'pharmacy_review', name: '药房' },
];
export { STATUS, REASON_LIST, TIPS, REVIEWER };