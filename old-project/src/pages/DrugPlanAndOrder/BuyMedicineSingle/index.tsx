import type { FC } from 'react';
import React, { useState, useEffect } from 'react';
// import { Toast } from 'antd-mobile';
import { connect, history } from 'umi';
import type { Dispatch, ConnectProps, Loading } from 'umi';
import { HxEmpty, HxIndicator } from '@/components';
import qs from 'query-string';
import { HxParameter } from '@/utils/parameter';
import { Modal } from 'antd-mobile';
import styles from './index.less';
import './index.css';
import { STATUS, REVIEWER } from './data';
import { Icon } from 'antd-mobile';

// import { DrugItem } from '../components';

interface IProps extends ConnectProps {
  DrugDoctorAdvice: any;
  dispatch: Dispatch;
  loading?: boolean;
}
const explain = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/speMedicine/explain.jpg';
const drug = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/speMedicine/drug.png';
const Back = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/speMedicine/Back.png';
const store = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/speMedicine/store.png';
const tipsIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/speMedicine/tipsIcon.png';
const close = require('@/assets/speMedicine/close.png');

const DoctorAdvice: FC<IProps> = ({ dispatch, loading }) => {
  // 列表数据
  const [doctorsAdviceList, setDoctorsAdviceList] = useState<any>([1, 2]);
  // const [buttonText, setButtonText] = useState<string>(); // 订单状态
  const [reviewStatus, setReviewStatus] = useState<number>(); // 审方状态
  const [status, setStatus] = useState<number>(); // 订单状态
  const [tipsModal, setTipsModal] = useState<boolean>(false); // 流程提示框
  const [notReasonModal, setNotReasonModal] = useState<boolean>(false); //  不合理原因提示框

  const { appCode = '', channelCode = '', token = '' } = HxParameter;
  const [repealModal, setRepealModal] = useState<boolean>(false);
  const [pageData, setPageData] = useState<any>({});
  const queryParams = qs.parse(window.location.href?.split('?')[1]) || {};
  const { drugStoreId = '', schemeId = '' } = queryParams;
  const {
    reviewDesc = '',
    reviewResult = '',
    doctorName = '',
    deptName = '',
    patientName = '',
    diagnoseDesc = '',
    itemDate = '',
    drugInfoList = [],
    reviewPhaseStatusDTOS = [],
    expirationTime = '',
    prescUrl = '',
  } = pageData;
  const personMoney = drugInfoList && drugInfoList[0]?.personMoney;
  const totalPrice = drugInfoList && drugInfoList[0]?.totalPrice;

  // 获取列表
  const getList = () => {
    console.log('queryParams', queryParams);
    const params = {
      drugStoreId,
      schemeId,
    };
    dispatch({
      type: 'drugPlanAndOrder/queryMedicinesList',
      payload: params,
      callback: (res: any) => {
        console.log('res', res);
        const { code, data = {} } = res;
        if (code === '1') {
          const { drugInfoList = [], status = '', reviewStatus = '' } = data || '';
          setDoctorsAdviceList(drugInfoList);
          setPageData(res?.data);
          setReviewStatus(reviewStatus);
          setStatus(status);
        }
      },
    });
  };
  // 传递到配送方式页面的数据
  const toDistributeData =
    doctorsAdviceList &&
    doctorsAdviceList[0].productList &&
    doctorsAdviceList[0].productList.map((item: any) => {
      return { productId: item?.productId, amount: Number(item?.totalQty) };
    });
  // 更新订单状态
  const updateStatus = (status) => {
    const params = {
      schemeId,
      status,
      drugStoreId,
    };
    console.log('params', params);
    dispatch({
      type: 'drugPlanAndOrder/updataOrderStatus',
      payload: params,
      callback: () => {
        // 上一页
        history.goBack();
      },
    });
  };
  // 操作订单状态
  const buttonClick = () => {
    console.log('333llll', reviewStatus, status);
    console.log('pageData', pageData, toDistributeData);

    if (reviewStatus === 1) {
      // 显示撤销弹框
      setRepealModal(true);
    } else if (reviewStatus === 2 && status !== 5) {
      // 填写订单页面
      const bizSysSeq = doctorsAdviceList[0]?.bizSysSeq;
      const bizSubType = doctorsAdviceList[0]?.bizSubType;
      window.location.href = `${window.location.origin}/person/drugplanandorder/distribution?products=${JSON.stringify(
        toDistributeData,
      )}&shopId=${drugStoreId}&bizSubType=${bizSubType}&bizSysSeq=${bizSysSeq}&schemeId=${schemeId}&drugStoreId=${drugStoreId}&status=5&organCode=HID0101`;
    } else if (reviewStatus === 5 || reviewStatus === 3 || reviewStatus === 6) {
      // 咨询记录 client参数待确认
      window.location.href = `${API_ZXMZ}/online/history?client=dzapp&type=yxzximage&organCode=HID0101&appCode=${appCode}&channelCode=${channelCode}&token=${token}`;
    } else if (Number(reviewStatus) === 2 && status === 5) {
      const { orderId } = pageData;
      // 跳转订单详情
      window.location.href = `${window.location.origin}/person/drugplanandorder/detail?orderId=${orderId}&schemeId=${schemeId}&drugStoreId=${drugStoreId}&tyOrderStatus=5`;
    }
  };

  // 撤销订单
  const confirm = () => {
    // 更新订单状态
    updateStatus(0);
    // 上一页
    // history.goBack();
  };
  // 显示介绍流程
  const showModal = () => {
    setTipsModal(true);
  };
  const closemodal = () => {
    setTipsModal(false);
  };
  useEffect(() => {
    getList();
    // confirm();
  }, []);
  // 查看处方笺
  const checkPrection = () => {
    history.push({
      pathname: '/drugplanandorder/predetails',
      query: {
        prescUrl,
      },
    });
  };
  // 查看不合理原因
  const checkReason = () => {
    setNotReasonModal(true);
  };
  // 关闭弹框
  const closeModal = () => {
    setNotReasonModal(false);
  };
  return (
    <>
      {loading ? (
        <HxIndicator />
      ) : pageData ? (
        <div className={styles.mainContent}>
          <div className={styles.reviewContent}>
            <div className={styles.reviewStatus}>
              {/* <img src={reviewStatus === 1 ? reloading : reviewStatus === 2 ? resucess : wrongtips} alt="" /> */}
              {reviewStatus === 1
                ? '待审核'
                : reviewStatus === 2
                ? '审核通过'
                : reviewStatus === 3
                ? '审核不通过'
                : reviewStatus === 4
                ? '锁定'
                : reviewStatus === 5
                ? '已停方'
                : reviewStatus === 6
                ? '已失效'
                : ''}
              {reviewStatus === 3 ? (
                <span className={styles.checkReason} onClick={checkReason}>
                  查看原因
                </span>
              ) : (
                ''
              )}
            </div>
            <div className={styles.progressBar}>
              {reviewStatus === 6 && !reviewPhaseStatusDTOS.length && <p>药剂科审核已失效</p>}

              {reviewPhaseStatusDTOS.map((item, index) => {
                return (
                  <div className={styles.reviewProgress}>
                    <div className={styles.item}>
                      {![0, 1].includes(item.reviewStatus) ? (
                        <Icon
                          type={STATUS.find((val: any) => val.value === item.reviewStatus)?.icon || ''}
                          size={item}
                        />
                      ) : (
                        <span className={styles.icon} />
                      )}

                      {index !== reviewPhaseStatusDTOS.length - 1 ? <p className={styles.line} /> : ''}
                    </div>
                    <div className={styles.reviewText}>
                      <span>{REVIEWER.find((val: any) => val.value === item.reviewName)?.name}</span>

                      <span>{STATUS.find((val: any) => val.value === item.reviewStatus)?.name}</span>
                    </div>
                  </div>
                );
              })}
            </div>

            <div className={styles.reviewDesc}>{reviewStatus !== 3 ? reviewDesc : ''}</div>
            <div className={styles.patientContent}>
              <div className={styles.patientInfo}>
                <div className={styles.sign} onClick={checkPrection}>
                  处方笺
                </div>

                <div>
                  就诊医生：<span>{doctorName}</span>
                </div>
                <div>
                  就诊科室：<span>{deptName}</span>
                </div>
                <div>
                  就诊人：<span>{patientName}</span>
                </div>
                <div>
                  诊断信息：<span>{diagnoseDesc}</span>
                </div>
                <div>
                  时 间：
                  <span>
                    {itemDate} &nbsp; （{expirationTime}后失效）
                  </span>
                </div>
              </div>
            </div>
          </div>

          <div className={styles.introduced} onClick={showModal}>
            <div>
              <img src={drug} alt="" />
            </div>
            <div className={styles.text}> 处方药购买流程介绍</div>
            <div className={styles.Back}>
              <img src={Back} alt="" />
            </div>
          </div>
          <div className={styles.drugItem}>
            <div className={styles.title}>购药需求信息</div>
            <div className={styles.drugStore}>
              <div className={styles.storeName}>
                <img src={store} alt="" />

                {doctorsAdviceList && doctorsAdviceList[0]?.drugStoreName}
              </div>

              {doctorsAdviceList &&
                doctorsAdviceList[0]?.productList &&
                doctorsAdviceList[0]?.productList.map((item, index) => {
                  return (
                    <div className={styles.item} key={Number(index)}>
                      <div className={styles.left}>
                        <img src={item?.itemImageUrl} alt="" />
                      </div>
                      <div className={styles.right}>
                        <div className={styles.name}>{item?.itemDesc || ''}</div>
                        <div className={styles.space}>
                          <span>
                            {/* 规格： */}
                            {item?.standards || ''}
                          </span>
                          <span>{item.totalQty ? ` x${item.totalQty}` : ''}</span>
                        </div>
                        {reviewStatus !== 1 ? (
                          <div className={styles.price}>
                            <span>¥</span>
                            <span> {item.drugPrice ? item.drugPrice.toFixed(2) : ''}</span>
                          </div>
                        ) : (
                          ''
                        )}
                      </div>
                    </div>
                  );
                })}
            </div>
          </div>

          {reviewStatus !== 1 && personMoney ? (
            <div className={styles.productsPrice}>
              {console.log('totalPrice')}
              <div>
                {reviewStatus === 2 ? (
                  <div>
                    <div> 商品总金额</div>
                    <div> 报销后商品总金额</div>
                  </div>
                ) : (
                  '商品总金额'
                )}
              </div>
              <div className={styles.sumPrice}>
                {reviewStatus === 2 ? (
                  <div>
                    <div>
                      <span>¥ </span>
                      {totalPrice}
                    </div>
                    <div>
                      <span>¥ </span>
                      {personMoney}
                    </div>
                  </div>
                ) : (
                  totalPrice
                )}
              </div>
            </div>
          ) : (
            ''
          )}
          <div className={styles.payTips}>该价格为药房药品售价，实际支付价格以医保报销为准</div>

          {status !== 7 && reviewStatus !== 3 && (
            <div className={styles.bottom}>
              <div className={styles.totalPrice}>
                {reviewStatus !== 1 && personMoney ? (
                  <>
                    <span>¥</span>
                    {personMoney}
                  </>
                ) : (
                  ''
                )}
              </div>
              <div>
                <button
                  className={reviewStatus === 2 && status !== 5 ? styles.greenBtn : styles.next}
                  type="button"
                  onClick={buttonClick}
                >
                  {reviewStatus === 1 && (status === 1 || status === 0)
                    ? '重新选择'
                    : status !== 5 && reviewStatus === 2
                    ? '去购买'
                    : status !== 0 && (reviewStatus === 5 || reviewStatus === 6 || reviewStatus === 3)
                    ? '联系医生'
                    : status === 5 && reviewStatus === 2
                    ? '查看订单'
                    : ''}
                </button>
              </div>
            </div>
          )}
        </div>
      ) : (
        <div style={{ height: '500px', overflow: 'hidden' }}>
          <HxEmpty canRefresh={false} />
        </div>
      )}
      <Modal
        className="tipsmodal"
        visible={repealModal}
        transparent
        maskClosable={false}
        animationType="slide-up"
        // title="购药撤销"
        footer={[
          {
            text: '确认',
            onPress: () => {
              confirm();
            },
          },
          {
            text: '再想一想',
            onPress: () => {
              setRepealModal(false);
            },
          },
        ]}
      >
        <div className={styles.content}>
          <div className="tipsTitle">
            <img src={tipsIcon} alt="" />
            购药需求确认
          </div>
          请确认是撤销该药房的购药需求
        </div>
      </Modal>
      <Modal className="explainmodal" visible={tipsModal} transparent maskClosable popup animationType="slide-up">
        <div className="explainContent">
          <img src={explain} alt="" style={{ width: '100%' }} />
          <div onClick={closemodal} className="close" />
        </div>
      </Modal>
      <Modal
        className="notReasonModal"
        visible={notReasonModal}
        transparent
        onClose={() => setNotReasonModal(false)}
        maskClosable
        popup
        animationType="slide-up"
      >
        <div className="notReason">
          <div>
            不合理原因
            <img src={close} alt="" style={{ width: '100%' }} onClick={closeModal} />
          </div>
          <div className={styles.ReasonContent}>{reviewResult}</div>
        </div>
      </Modal>
    </>
  );
};

export default connect(({ DrugDoctorAdvice, loading }: { DrugDoctorAdvice: any; loading: Loading }) => ({
  DrugDoctorAdvice,
  loading: loading.effects['DrugDoctorAdvice/doctorsAdviceList'],
}))(DoctorAdvice);
