.mainContent {
  width: 100%;
  background: #f5f6fa;
  padding-bottom: 166px;
  height: 100%;

  .reviewContent {
    // height: 296px;
    background: linear-gradient(120deg, #6da6ff 0%, #3688ff 100%);
    position: relative;

    .reviewStatus {
      font-size: 36px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #ffffff;
      line-height: 50px;
      padding: 46px 0px 34px 40px;

      img {
        margin-right: 16px;
        vertical-align: text-bottom;
      }

      .checkReason {
        float: right;
        margin-right: 40px;
        width: 176px;
        height: 56px;
        border-radius: 40px;
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        text-align: center;
        border: 1px solid #ffffff;
      }
    }

    .reviewDec {
      padding: 0px 24px 24px 24px;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
    }

    .sign {
      width: 96px;
      height: 50px;
      line-height: 50px;
      text-align: center;
      background: #568df2;
      border-radius: 0px 8px 0px 8px;
      font-size: 24px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #ffffff;
      position: absolute;
      right: 0;
      top: 0;
    }
  }

  .progressBar {
    display: flex;
    margin-bottom: 32px;
    margin-left: 40px;
    color: #fff;

    .reviewProgress {
      display: flex;
      flex-direction: column;
      color: #fff;

      .item {
        display: flex;
        height: 32px;

        .icon {
          width: 32px;
          height: 32px;
          border-radius: 50%;
          border: 1px solid #fff;
        }
      }

      :global(.am-icon) {
        color: #fff;
        width: 32px;
        height: 32px;
      }

      .reviewText {
        span {
          display: inline-block;
          padding-top: 12px;

          &:first-child {
            padding-bottom: 12px;
          }
        }
      }
    }

    .line {
      width: 212px;
      height: 2px;
      background: #ffffff;
      position: relative;
      top: 16px;
    }
  }

  .reviewDesc {
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
    padding-left: 24px;
    padding-bottom: 32px;
  }

  .patientContent {
    width: 100%;
    // height: 326px;
    background: #ffffff;
    border-radius: 16px 16px 0px 0px;
    padding: 24px 24px;

    .patientInfo {
      display: flex;
      padding: 24px 24px;
      flex-direction: column;
      background: #f5f6fa;
      font-size: 24px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #989eb4;
      position: relative;

      span {
        color: #03081a;
      }
    }
  }

  .introduced {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    height: 88px;
    line-height: 88px;
    padding-left: 26px;
    background: #ffffff;
    font-size: 28px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #03081a;
    margin-top: 24px;

    img {
      margin-right: 10px;
      //   width: 33px;
      // height: 20px;
      vertical-align: text-bottom;
    }

    .text {
      flex: 2;
    }

    .Back {
      img {
        width: 48px;
        height: 48px;
      }
    }
  }

  .drugItem {
    background: #fff;
    margin-top: 24px;
    padding-left: 24px;

    .title {
      font-size: 32px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #03081a;
      height: 112px;
      line-height: 112px;
      border-bottom: 2px solid #ebedf5;
    }

    .drugStore {
      .storeName {
        padding: 24px 0px;
        font-size: 24px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #03081a;

        img {
          vertical-align: middle;
          padding-right: 12px;
        }
      }

      .item {
        width: 100%;
        display: flex;
        padding: 30px 24px;

        .left {
          width: 180px;
          height: 180px;
          background: #f5f6fa;
          border-radius: 8px;
          margin-right: 24px;

          img {
            width: 100%;
            /* height: auto; */
            object-fit: contain;
          }
        }

        .right {
          .name {
            font-size: 32px;
            font-family: PingFangSC-Semibold, PingFang SC;
            font-weight: 600;
            color: #03081a;
          }

          .space {
            font-size: 24px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #989eb4;

            span {
              &:last-child {
                position: relative;
                right: -102px;
              }
            }
          }

          .price {
            font-size: 28px;
            font-family: DINAlternate-Bold, DINAlternate;
            font-weight: bold;
            color: #03081a;

            span {
              font-size: 28px;
            }
          }
        }
      }
    }
  }

  .productsPrice {
    height: 224px;
    // line-height: 224px;
    background: #ffffff;
    margin-top: 24px;
    padding: 0px 24px;
    display: flex;
    justify-content: space-between;
    font-size: 32px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #03081a;

    div {
      line-height: 112px;
    }

    .sumPrice {
      font-size: 40px;
      font-family: DINAlternate-Bold, DINAlternate;
      font-weight: bold;
      color: #03081a;

      span {
        font-size: 28px;
      }
    }
  }

  .payTips {
    margin-top: 32px;
    font-size: 24px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #989eb4;
    text-align: center;
  }

  .bottom {
    width: 100%;
    height: 140px;
    background: #fff;
    position: fixed;
    bottom: 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    button {
      width: 192px;
      height: 72px;
      float: right;
      margin-right: 24px;
      margin-top: 16px;
      background: #ffffff;
      box-shadow: 0px 0px 8px 0px #ebedf5;
      border-radius: 40px;
      border: 2px solid #989eb4;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #03081a;
      text-shadow: 0px 0px 8px #ebedf5;
    }

    .greenBtn {
      background: #3ad3c1;
      box-shadow: 0px 0px 4px 0px #ebedf5;
      color: #fff;
      border: none;
    }
  }

  :global(.am-modal-transparent) {
    width: 100% !important;
  }

  .totalPrice {
    font-size: 36px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #fc4553;
    // line-height: 25px;
    text-shadow: 0px 0px 4px #ebedf5;
    padding-left: 24px;

    span {
      font-size: 28px;
    }
  }
}

:global(.explainmodal .am-modal-content) {
  border-radius: 20px 20px 0px 0px !important;
}

.notReasonModal {
  font-size: 28px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #03081a;
}

.ReasonContent {
  height: 490px;
  overflow: auto;
  margin-top: 22px;
}
