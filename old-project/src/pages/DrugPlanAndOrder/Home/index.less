.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow-y: hidden;
  // height: 100vh;
  background-color: #f5f6fa;
  // & {
  ::-webkit-scrollbar {
    width: 0 !important;
  }
  // }
  .head {
    position: fixed;
    top: 0;
    display: flex;
    justify-content: center;
    width: 100%;
    background-color: #fff;
    + div {
      height: 100%;
      padding-top: 88px;
    }
  }

  .tabBox {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 35%;
    height: 88px;
    background-color: #fff;

    .tab {
      // width: 144px;
      height: 50px;
      margin-bottom: 8px;
      color: #03081a;
      font-size: 36px;
      line-height: 50px;
    }

    .activeTab {
      font-weight: bold;
    }

    .line {
      width: 80px;
      height: 8px;
      border-radius: 4px;
    }

    .activeLine {
      background: linear-gradient(134deg, #6cebe2 0%, #3ad3c1 100%);
      box-shadow: 0 4px 8px 0 rgba(58, 211, 193, 0.4);
    }
  }
  :global {
    .am-tabs-default-bar-top .am-tabs-default-bar-tab::after {
      background-color: transparent !important;
    }
    div.am-list-body::before {
      background-color: transparent;
    }
    div.am-list-body::after {
      background-color: transparent;
    }
  }
}
::-webkit-scrollbar {
  display: none;
  width: 0;
}
// ::-webkit-scrollbar {
//   display: none;
// }
