import React, { FC, useEffect, useState } from 'react';
import { Dispatch, AnyAction } from 'redux';
import { connect } from 'dva';
import { Loading, IDrugPlanAndOrderModelState } from 'umi';
import styles from './index.less';
import { PlanList, OrderList } from '../components';

interface IProps {
  dispatch: Dispatch<AnyAction>;
  drugPlanAndOrder?: IDrugPlanAndOrderModelState;
  loading: boolean;
  location: {
    query: {
      tabPage?: string;
    };
  };
}

const tabs = [
  { title: '购药方案', sub: 0 },
  { title: '购药订单', sub: 1 },
];

const Home: FC<IProps> = ({
  location: {
    query: { tabPage = 0 },
  },
}) => {
  const [newFlag, setNewFlag] = useState(tabPage);

  const changeTab = (tab: number) => {
    setNewFlag(tab);
    sessionStorage.setItem('tabs', tab);
  };

  useEffect(() => {
    const tab = sessionStorage.getItem('tabs');
    if (tab) {
      setNewFlag(Number(tab));
    } else {
      setNewFlag(Number(tabPage));
    }
  }, [tabPage]);

  return (
    <div className={styles.container}>
      <div className={styles.head}>
        {tabs.map((tab) => {
          return (
            <div className={styles.tabBox} onClick={() => changeTab(tab.sub)} key={tab.sub}>
              <div className={`${styles.tab} ${tab.sub === newFlag ? styles.activeTab : ''}`}>{tab.title}</div>
              <div className={`${styles.line} ${tab.sub === newFlag ? styles.activeLine : ''}`} />
            </div>
          );
        })}
      </div>
      <div>
        {newFlag === 0 && <PlanList />}
        {newFlag === 1 && <OrderList />}
      </div>
    </div>
  );
};

export default connect(
  ({ loading, drugPlanAndOrder }: { loading: Loading; drugPlanAndOrder: IDrugPlanAndOrderModelState }) => ({
    loading: loading.models.helpCenter,
    ...drugPlanAndOrder,
  }),
)(Home);
