/*
 * @Author: <PERSON><PERSON>
 * @Date: 2021-06-15 11:24:48
 * @LastEditTime: 2022-03-30 17:30:22
 * @LastEditors: Please set LastEditors
 * @Description:
 * @FilePath: /hyt-person/src/pages/Department/Components/HealthScience/ListItem.tsx
 */

import React from 'react';
import { useDispatch, connect, useLocation } from 'umi';
import { HxIcon } from '@/components';
import qs from 'qs';
import styles from './ListItem.less';

interface IProps {
  item: any;
  cb?: Function;
}

const defaultAvater = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/default-doctor-logo.png';
const player = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-viceo.png';

const EHCard = (props: IProps) => {
  // cb 点击事件回调
  const { item, cb } = props;
  const {
    query: { appCode, channelCode, organCode, doctorCode, doctorId },
  }: any = useLocation();
  const dispatch = useDispatch();

  const queryData = (type: string, payload: any, cb: Function) =>
    dispatch({ type, payload, cb: (res: any = {}) => cb?.(res) });

  const dataRecord = (item: any) => {
    // eslint-disable-next-line no-new
    new Promise((resolve, reject) => {
      // 只有外链调用接口
      if (item.outChain?.includes('cd120.info')) {
        return reject();
      }
      const params = {
        appCode,
        channelCode,
        organCode,
        dataType: 1,
        operator: '',
        resourceId: item.resourceId,
      };
      queryData('department/dataRecord', params, (res: any) => {
        return resolve(res);
      });
    });
  };

  const jumpTo = async (item: any) => {
    if (!item.outChain) return;
    // jhOrganCode=%@&doctorCode=%@&doctorId=%@
    const params = doctorId
      ? {
          jhOrganCode: organCode,
          doctorCode,
          doctorId,
        }
      : {};

    await dataRecord(item);
    window.location.href = `${item.outChain}${item.outChain.includes('?') ? '&' : '?'}${qs.stringify(params)}`;
  };

  const videoBg = (v: any) => ({
    background: `url(${v.coverUrl || player}) no-repeat center`,
    backgroundSize: 'cover',
  });

  const renderRead = (v: any) => (
    <div className={styles.reading}>
      <div className={styles.doctorInfo}>
        <img src={v.authorPortrait || defaultAvater} alt="" className={styles.avater} />
        <div className={styles.info}>
          <div className={styles.organName}>{v.organName}</div>
          <div>{v.authorName}</div>
        </div>
      </div>
      {v.mediaType === 1 ? (
        <div className={styles.giveLike}>
          <div>阅读 {v.sumRead}</div>
          <div>赞 {v.sumLike}</div>
        </div>
      ) : null}
    </div>
  );

  const renderPic = (v: any) => (
    <div className={styles.contentItem}>
      {/* 文章的 */}
      <div className={styles.contentInfo}>
        <div className={styles.infoLeft}>
          <div className={styles.title}>{v.title}</div>
          <div className={styles.tags}>
            {v.tagNames?.map((tag: string) => (
              <span key={tag}>{tag}</span>
            ))}
          </div>
        </div>
        <img src={v?.coverUrl} alt="" className={styles.poster} />
      </div>
      {renderRead(v)}
    </div>
  );
  const renderVideo = (v: any) => (
    <div className={styles.contentItem}>
      {/* 视频的 */}
      <div className={styles.videoContent}>
        <div className={styles.videoPoster} style={videoBg(v)}>
          {/* <video src={v.coverUrl} /> */}
        </div>

        <img src={player} alt="" className={styles.player} />
        <div className={styles.videoDesc}>
          {v.tagNames?.map((tag: string) => (
            <span key={tag}>{tag}</span>
          ))}
        </div>
      </div>
      <div className={styles.videoTitle}>{v.title}</div>
      {renderRead(v)}
    </div>
  );

  const renderLive = (v: any) => (
    <div className={styles.contentItem}>
      {/* 直播的 */}
      <div className={styles.liveContent}>
        <div className={styles.videoPoster} style={videoBg(v)}>
          {/* <video src={v.coverUrl} /> */}
        </div>

        {/* <img src={player} alt="" className={styles.player} /> */}
        <div className={styles.liveDesc}>
          {v.liveBroadcastStatus === 2 ? <HxIcon className={styles.liveicon} iconName="zhibozhong" /> : null}
          {v.liveBroadcastStatusName}
        </div>
      </div>
      <div className={styles.videoTitle}>{v.title}</div>
      {renderRead(v)}
    </div>
  );
  const { whetherAlipay }: any = window;
  return (
    <div className={styles.contentList} onClick={() => jumpTo(item)}>
      {item.mediaType === 1 ? renderPic(item) : null}
      {item.mediaType === 2 && !whetherAlipay ? renderVideo(item) : null}
      {item.mediaType === 3 ? renderLive(item) : null}
    </div>
  );
};
export default EHCard;
