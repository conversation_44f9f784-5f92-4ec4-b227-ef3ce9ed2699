/*
 * @Author: <PERSON><PERSON>
 * @Date: 2021-06-09 10:57:23
 * @LastEditTime: 2022-03-29 15:29:36
 * @LastEditors: Please set LastEditors
 * @Description:
 * @FilePath: /hyt-person/src/pages/Department/Components/HealthScience/index.tsx
 */
import React, { CSSProperties } from 'react';
import styles from './index.less';
import ListItem from './ListItem';

const arrowDown = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/arrow_right.png';

interface HealthServiceProps {
  style?: CSSProperties;
  list: any;
  showMore?: boolean;
  toAll?: () => void;
}

const HealthServise: React.FC<HealthServiceProps> = (props) => {
  const { list, showMore = false, toAll, style } = props;
  return (
    <div className={styles.healthService} style={style}>
      {list?.map((v: any, i: number) => (
        <ListItem item={v} key={i.toString()} />
      ))}
      {showMore && (
        <div className={styles.bottom}>
          <div className={styles.more} onClick={() => toAll && toAll()}>
            <span>查看更多</span>
            <img src={arrowDown} alt="" />
          </div>
        </div>
      )}
    </div>
  );
};

export default HealthServise;
