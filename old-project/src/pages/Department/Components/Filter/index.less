.filterBox {
  margin: 0 0 3.2vw 0;
  .filter {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100px;
    margin-top: -1px;
    background: #fff;

    div {
      width: 100%;
      color: #333;
      font-size: 26px;
      text-align: center;

      .active {
        color: @brand-primary;
      }

      .arrow {
        margin-left: 6px;
        font-size: 20px;
      }
    }

    div:last-child {
      &:active {
        color: @brand-primary;
      }
    }

    .active {
      color: @brand-primary;
    }
  }

  .mask {
    position: fixed;
    top: 50px;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 200;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    background-color: rgba(0, 0, 0, 0.45);

    .rest {
      flex: 1;
    }

    .titleItem {
      background: #fff;
      div {
        height: 37px;
        margin: 50px 40px;
        color: #666;
        font-size: 26px;

        &:active {
          color: @brand-primary;
        }
      }
    }
  }

  .maskYY {
    top: 200px;
  }
}

// :global(.am-popover) {
//   top: 208px !important;
//   left: 0 !important;
//   z-index: 2000;
//   width: 100%;
// }

// :global(.am-popover-inner) {
//   border-top: 1px #f9f9f9 solid;
//   border-radius: 0;
//   box-shadow: none;
// }

// :global(.am-popover-arrow) {
//   display: none;
// }
