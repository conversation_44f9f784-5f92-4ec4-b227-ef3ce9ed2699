import React, { PureComponent } from 'react';
// import { Popover } from 'antd-mobile';
import { HxIcon } from '@/components';
import styles from './index.less';

/**
 * 筛选列表选中的类型
 */
enum SelectedType {
  None = 0,
  Title, // 职称
  TimeRange, // 时间段
}
interface IProps {
  /**
   * 是否显示职称筛选条件
   */
  showTitle?: boolean;
  /**
   * 是否显示有号筛选条件
   */
  showHaveNo?: boolean;
  /**
   * 是否显示时间段筛选条件
   */
  showTimeRange?: boolean;
  /**
   * 是否显示周排班
   */
  showWeekSchedule?: boolean;
  /**
   * 职称列表
   */
  titleList: TitleItem[];
  /**
   * 时间段列表
   */
  timeRangeList: any[];
  /**
   * 周排班事件
   */
  onWeekSchedule?: (value: number) => void;
  /**
   * 是否有号事件
   */
  onHaveNo?: (valeu: boolean) => void;
  /**
   * 时间段改变事件
   */
  onTimeRange?: (value: number) => void;
  /**
   * 职称改变事件
   */
  onDoctorTitle?: (value: string) => void;
  /**
   * 挂号类型 1 预约 2 今日
   */
  sign: number;
}
interface TitleItem {
  /**
   * 医生职称编码
   */
  code: string;
  /**
   * 医生职称名称
   */
  name: string;
}
interface IState {
  /**
   * 是否弹窗显示
   */
  titleVisible: boolean;
  /**
   * 已选择医生职称编码
   */
  selectedTitleCode: string;
  /**
   * 已选择医生职称名称
   */
  selectedTitleName: string;
  /**
   * 选中类型 职称 title 时间段 timeRange
   */
  selectedType: SelectedType;
  /**
   * 已选择时间编码
   */
  selectedTimeRangeCode: number;
  /**
   * 已选择时间名称
   */
  selectedTimeRangeName: string;
  /**
   * 看有号无号
   */
  haveNo: boolean;
}

class Filter extends PureComponent<IProps, IState> {
  static defaultProps = {
    showTitle: true,
    showHaveNo: false,
    showTimeRange: false,
    showWeekSchedule: true,
    onWeekSchedule: () => {},
    onHaveNo: () => {},
    onTimeRange: () => {},
    onDoctorTitle: () => {},
  };

  constructor(props: IProps) {
    super(props);
    this.state = {
      titleVisible: false,
      selectedTitleCode: '',
      selectedTitleName: '全部职称',
      selectedTimeRangeCode: 2,
      selectedTimeRangeName: '全天',
      selectedType: SelectedType.None,
      haveNo: true,
    };
  }

  componentDidMount() {}

  onWeekSchedule = (status: number) => {
    const { onWeekSchedule } = this.props;
    onWeekSchedule && onWeekSchedule(status);
  };

  onChangeState = () => {
    this.setState({
      haveNo: false,
    });
  };

  onHaveNo = () => {
    const { haveNo } = this.state;
    const { onHaveNo } = this.props;
    this.setState(
      {
        haveNo: !haveNo,
      },
      () => {
        const { haveNo } = this.state;
        onHaveNo && onHaveNo(haveNo);
      },
    );
  };

  onDoctorTitle = (item: TitleItem) => {
    const { code, name } = item;
    this.setState({ selectedTitleCode: code, selectedTitleName: name, titleVisible: false });
    const { onDoctorTitle } = this.props;
    onDoctorTitle && onDoctorTitle(name);
  };

  onTimeRange = (item: any) => {
    const { text, value } = item;
    this.setState({ selectedTimeRangeCode: value, selectedTimeRangeName: text, titleVisible: false });
    const { onTimeRange } = this.props;
    onTimeRange && onTimeRange(value);
  };

  handleVisibleChange = (titleVisible: boolean, selectType: SelectedType) => {
    const { selectedType } = this.state;
    if (selectedType !== selectType) {
      this.setState({
        titleVisible: true,
        selectedType: selectType,
      });
      return;
    }
    this.setState({
      titleVisible: !titleVisible,
    });
  };

  render() {
    const {
      titleVisible,
      selectedTimeRangeName,
      selectedTimeRangeCode,
      selectedTitleName,
      selectedTitleCode,
      selectedType,
      haveNo,
    } = this.state;
    const { titleList, showTitle, showTimeRange, showWeekSchedule, sign, showHaveNo, timeRangeList } = this.props;
    console.log('selectedTimeRangeCode', selectedTimeRangeCode);

    return (
      <div className={styles.filterBox}>
        <div className={styles.filter}>
          {showTitle && (
            <div>
              <span
                className={`${selectedTitleCode !== '' ? styles.active : ''}`}
                onClick={() => this.handleVisibleChange(titleVisible, SelectedType.Title)}
              >
                {selectedTitleName}
              </span>
              <HxIcon
                className={styles.arrow}
                iconName={`${selectedTitleCode === '' ? 'filter-arrow' : 'filter-arrow-selected'}`}
              />
            </div>
          )}
          {showHaveNo && (
            <div onClick={() => this.onHaveNo()} className={`${haveNo ? styles.active : ''}`}>
              只看有号
            </div>
          )}
          {showTimeRange && (
            <div>
              <span
                className={`${selectedTimeRangeCode !== 2 ? styles.active : ''}`}
                onClick={() => this.handleVisibleChange(titleVisible, SelectedType.TimeRange)}
              >
                {selectedTimeRangeName}
              </span>
              <HxIcon
                className={styles.arrow}
                iconName={`${selectedTimeRangeCode === 2 ? 'filter-arrow' : 'filter-arrow-selected'}`}
              />
            </div>
          )}
        </div>
        {titleVisible && (
          <div className={sign === 2 ? styles.mask : `${styles.mask} ${styles.maskYY}`}>
            <div className={styles.titleItem}>
              {selectedType === SelectedType.Title &&
                titleList.map((item: TitleItem) => (
                  <div
                    onClick={() => this.onDoctorTitle(item)}
                    key={`${Number(Math.random().toString().substr(3, 12) + Date.now()).toString(36)}`}
                  >
                    {item.name}
                  </div>
                ))}

              {selectedType === SelectedType.TimeRange &&
                timeRangeList.map((item: any) => (
                  <div onClick={() => this.onTimeRange(item)} key={item.value}>
                    {item.text}
                  </div>
                ))}
            </div>
            <div className={styles.rest} onClick={() => this.setState({ titleVisible: false })} />
          </div>
        )}
      </div>
    );
  }
}

export default Filter;
