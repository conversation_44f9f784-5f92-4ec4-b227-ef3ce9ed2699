/*
 * @Author: <PERSON><PERSON>
 * @Date: 2021-06-04 13:54:15
 * @LastEditTime: 2021-07-07 15:27:01
 * @LastEditors: Vane
 * @Description: 
 * @FilePath: \hyt-person\src\pages\Department\Components\Table\index.less
 */
table,
th,
td {
  border: 1px solid #f3f3f3;
}
tr th,
tr td {
  border-bottom: none;
  border-left: none;
  &:last-child {
    border-right: none;
  }
}
.table {
  width: 100%;
  border-top: none;
  thead {
    tr {
      display: flex;
      justify-content: space-around;
      th {
        background: #f5f6fa;
        div {
          color: #03081a;
          font-weight: 400;
          font-size: 24px;
          line-height: 34px;
          text-align: center;
        }
      }
      th:first-child {
        padding: 24px 5px 12px 7px;
        div {
          color: #b0b3bf;
          line-height: 34px;
        }
      }
      th:not(:first-child) {
        width: 11.6vw;
        padding: 24px 15px;
      }
      th:last-child {
        padding-right: 14px;
      }
    }
  }
  tbody {
    tr {
      display: flex;
      justify-content: space-around;
      td {
        font-size: 24px;
        line-height: 34px;
        text-align: center;
        background: #fff;
      }
      td:first-child {
        padding: 10px 5px 12px 7px;
        div {
          color: #b0b3bf;
          line-height: 34px;
        }
      }
      td:not(:first-child) {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 11.7vw;
        padding: 24px 12px;
      }
      td:last-child {
        padding-right: 14px;
      }
    }
  }
  .duty {
    color: #b0b3bf;
  }

  .checked {
    width: 30px;
    height: 30px;
    border-radius: 50%;
  }
  .paibanShadow {
    box-shadow: 0 2px 6px 0 rgba(38, 207, 187, 0.4);
  }
  .yiguopaiban1Shadow {
    box-shadow: 0 2px 6px 0 rgba(235, 237, 245, 1);
  }
}
