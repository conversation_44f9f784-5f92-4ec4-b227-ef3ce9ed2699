/*
 * @Author: <PERSON><PERSON>
 * @Date: 2021-06-04 13:54:06
 * @LastEditTime: 2021-07-27 15:04:39
 * @LastEditors: Please set LastEditors
 * @Description:
 * @FilePath: \hyt-person\src\pages\Department\Components\Table\index.tsx
 */
import React, { FC } from 'react';
// import dayjs from 'dayjs';
import { HxIcon } from '@/components';
import classnames from 'classnames';
import styles from './index.less';

interface TableProps {
  list?: any;
  /** 可点击跳转 */
  tdClick: Function;
}

// const getAllWeekToday = ({ date = new Date(), format = 'YYYY-MM-DD' }) => {
//   const oneDayTime = 1000 * 60 * 60 * 24;
//   const today = date || new Date();
//   const todayDay = today.getDay() || 7; // 若那一天是周末时，则强制赋值为7
//   const startDate = new Date(today.getTime() - oneDayTime * (todayDay - 1));
//   const formatDate = (date: dayjs.ConfigType | undefined) => dayjs(date).format(format);

//   const titleList = [{ date: formatDate(startDate), title: '周一' }];

//   const title = ['一', '二', '三', '四', '五', '六', '日'];

//   for (let i: number = 1; i < 7; i += 1) {
//     titleList.push({
//       date: formatDate(startDate.getTime() + oneDayTime * i),
//       title: `周${title[i]}`,
//     });
//   }
//   return titleList;
// };

const Table: FC<TableProps> = ({ list = [], tdClick }) => {
  // const titleList = getAllWeekToday({ format: 'M.D' });
  const checkClass = (v: any) =>
    classnames(styles.checked, v.enabled ? styles.paibanShadow : styles.yiguopaiban1Shadow);
  return (
    <table className={styles.table}>
      <thead>
        <tr>
          <th className={styles.duty}>
            <div>排</div>
            <div>班</div>
          </th>
          {list.map((v: { weekDay: string; specialDate: string }) => (
            <th key={v.weekDay}>
              <div>{v.weekDay}</div>
              <div>{v.specialDate}</div>
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        <tr>
          <td className={styles.duty}>
            <div>上</div>
            <div>午</div>
          </td>
          {list.map((v: { morning: any; enabled: any; simpleDate: string }) => (
            <td key={v.simpleDate + v.morning} onClick={() => v.enabled && v.morning && tdClick(v)}>
              {v.morning ? (
                <HxIcon iconName={v.enabled ? 'paiban1' : 'yiguopaiban1'} className={checkClass(v)} />
              ) : null}
            </td>
          ))}
        </tr>
        <tr>
          <td className={styles.duty}>
            <div>下</div>
            <div>午</div>
          </td>
          {list.map((v: { afternoon: any; enabled: any; simpleDate: string }) => (
            <td key={v.simpleDate + v.afternoon} onClick={() => v.enabled && v.afternoon && tdClick(v)}>
              {v.afternoon ? (
                <HxIcon iconName={v.enabled ? 'paiban1' : 'yiguopaiban1'} className={checkClass(v)} />
              ) : null}
            </td>
          ))}
        </tr>
      </tbody>
    </table>
  );
};

export default Table;
