/*
 * @Author: <PERSON><PERSON>
 * @Date: 2021-06-04 17:47:27
 * @LastEditTime: 2021-06-09 10:32:42
 * @LastEditors: Vane
 * @Description:
 * @FilePath: \hyt-person\src\pages\Department\Components\Tabs\index.tsx
 */

import React, { FC, useState, SetStateAction } from 'react';
import classNames from 'classnames';
import styles from './index.less';

interface TabsProps {
  tabs?: any;
  children?: any;
  setCurrentTabId?: any;
}

const Tabs: FC<TabsProps> = (props) => {
  const [currentTab, setCurrentTab] = useState(0);

  // 切换
  const tabClickHandle = (i: SetStateAction<number>) => {
    props.setCurrentTabId(i);
    setCurrentTab(i);
  };

  const { children = [], tabs = [] } = props;

  const randomArr = (n: number = 5) => Array.from({ length: n }, (v: any, k: number) => k);

  const _children = randomArr(children.length);

  return (
    <div className={styles.tabWrapper}>
      <ul className={classNames(styles.clearfix, styles.tabs)}>
        {tabs?.map((v: any, i: number) => (
          <li key={v.sub} className={currentTab === i ? styles.tabActive : ''} onClick={() => tabClickHandle(i)}>
            {v.title}
          </li>
        ))}
      </ul>
      {children?.map((v: any, i: number) => (
        <div key={_children[i]} className={currentTab === i ? styles.active : styles.inactive}>
          {v}
        </div>
      ))}
    </div>
  );
};

export default Tabs;
