import React, { FC, useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Dispatch, connect, ConnectProps, IGlobalModelState, history, useLocation, Loading } from 'umi';
import { Carousel, Toast } from 'antd-mobile';
import moment from 'moment';
import { HxDoctorAvatar, HxIcon, HxIndicator } from '@/components';
import { getOrganCode, getToken } from '@/utils/parameter';
import queryString from 'query-string';
import _ from 'lodash';
import FloatEntrance from '@/components/FloatEntrance';
import { sensorsRequest } from '@/utils/sensors';
import { SingWechatJSSDKDataType } from '@/typings/global';
import { configWechatShareStyle, getCurEvnHref } from '@/utils/tool';
import { doctorAvatarDefault } from '../constance';
import Table from '../Components/Table';
import Tab from '../Components/Tabs';
import { IDeptModelState } from '../model';
import HealthScience from '../Components/HealthScience';
import styles from './index.less';
import { configWechatGetLocation, isWeixinBrowser, getAddressInfo } from './_utils';

const tabs = [
  {
    sub: 1,
    title: '预约挂号',
  },
  {
    sub: 2,
    title: '排班约诊',
  },
];

/**
 * 顶部三个选项接口
 */
interface ICardStepItem {
  menuId: string;
  menuName: string;
  linkType: number;
  imgUrl: string;
  linkUrl: string;
}

interface IProps extends ConnectProps {
  dispatch: Dispatch;
  department: IDeptModelState;
  loading?: boolean;
}

const DepartmentDetailPage: FC<IProps> = ({ dispatch, department, loading }) => {
  // 医生查看更多是否查看华西医联体的
  const [doctorListViewMore, setDoctorListViewMore] = useState(1);
  const { query }: any = useLocation();

  const { id }: any = useParams();

  const icon_testimonials =
    'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/testimonials_entrance.png';

  const { appCode, channelCode, organCode, deptName, deptCode, refDeptName, deptId } = query;

  let { deptCodes } = query;

  if (typeof deptCodes === 'string') {
    deptCodes = deptCodes.split(',');
  }
  let {
    deptConfig,
    orderData = { items: [], tips: '' },
    inquiryData = { items: [], tips: '' },
    doctorList = [],
    healthList,
    doctorListHXYLT = [],
    deptCodeList_ = [],
  } = department;

  // 科室首页随机医生 过滤四条
  doctorList = doctorList.filter((v: any) => v.homeDoctor == 1).filter((v, i) => i < 4);

  const [currentTabId, setCurrentTabId] = useState(0);
  const [imgheight, setImgheight] = useState<any>(90);

  const queryData = (type: string, payload: any, cb?: Function) =>
    dispatch({
      type,
      payload: { ...payload, query: payload.query ? { appCode, channelCode, organCode, ...payload.query } : null },
      cb: (res: any = {}) => cb?.(res),
    });

  // 轮播图
  const getDeptConfig = () => {
    const params = {
      appVersionCode: APP_VERSION_CODE,
      dataVersionCode: 'data-version3',
      deviceType: 2,
      pageCode: `${organCode}-${deptName}`,
    };
    queryData('department/getDepartmentConfig', params);
  };

  // 预约挂号数据
  const getOrderData = () => {
    // queryData('department/getDepartmentOrderdata', {scheduleDeptCode: deptCode, deptCodeList: deptCodes});
    queryData('department/getDepartmentOrderdata', {
      scheduleDeptCode: deptCode,
      deptCodeList: deptCodes,
      docCodeList: deptCodeList_,
    });
  };

  // 急速问诊数据
  const getInquiryData = () => {
    queryData('department/getDepartmentInquiryData', { deptCode, deptCodeList: deptCodes, docCodeList: deptCodeList_ });
  };

  // 医生列表（默认4条）
  const getDoctors = () => {
    const params = {
      deptCode,
      organCode,
      // deptCodeList: deptCodes,
      deptId,
      queryType: 1,
    };
    const params_ = {
      deptCode,
      organCode,
      // deptCodeList: deptCodes,
      deptId,
      queryType: 0,
    };

    queryData('department/getDepartmentDoctors', params, (data) => {
      if (!data.length) {
        getOrderData();
        getInquiryData();
      }
    });
    // 医生列表华西医联体（默认4条）
    queryData('department/getDepartmentDoctorsHX', params_);
  };

  const enterCode = 'PAT_DEPT_HOME';
  // 健康科普列表（默认5条）
  const getHealthList = () => {
    const params = {
      pageNum: 1,
      pageSize: 5,
      query: {
        attrId: id,
        enterCode,
        operator: 1,
      },
    };
    queryData('department/getDepartmentHealthList', params);
  };
  useEffect(() => {
    if (deptCodeList_.length) {
      getOrderData();
      getInquiryData();
    }
  }, [deptCodeList_, deptCode, deptName]);

  /** 设置分享给朋友的样式 */
  const defineShareAppStyle = () => {
    const link = getCurEvnHref();
    dispatch({
      type: 'global/singWechatJSSDK',
      payload: {
        url: getCurEvnHref(),
      },
      callback: (data: SingWechatJSSDKDataType) => {
        const title = `${deptName}-四川大学华西医院`;
        const desc = `${deptName}`;
        const configData = {
          ...data,
          debug: false,
          jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'],
        };

        const shareData = {
          title,
          desc,
          imgUrl: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/share.png',
          link,
          success: () => {
            // console.log('lcc-分享成功');
          },
        };
        console.log('shareData', shareData);
        configWechatShareStyle(configData, shareData);
      },
    });
  };
  const init = () => {
    getDeptConfig();
    getDoctors();
    getHealthList();
    defineShareAppStyle();
  };

  useEffect(() => {
    console.log('init====');
    init();
    setTimeout(() => {
      document.title = deptName;
      window.dispatchEvent(new Event('resize'));
    }, 0);
  }, [deptCode, deptName]);

  const jumpTo = (pathname: string, query: any = {}) => {
    history.push({ pathname, query });
  };

  // 点击更多
  const goMore = (pathname = '/department/healthscience') => {
    jumpTo(pathname, {
      appCode,
      channelCode,
      organCode,
      deptCode,
      deptId,
      enterCode,
      attrId: id,
      deptCodes,
      doctorListViewMore,
    });
  };

  // 点击card选项
  const onClickCardStep = (item: any) => {
    if (item.menuCode === 'medicine_test') {
      // 临床试验
      const query = {
        channelCode,
        organCode: getOrganCode(),
        token: getToken(),
        refDeptCodeArray: deptCodes ? deptCodes.join(',') : '',
        title: deptName,
      };
      const href = `${COMMON_DOMAIN}/gcp/?${queryString.stringify(query)}`;
      window.location.href = href;
    } else if (item.menuCode === 'deptJkkp') {
      // 健康科普
      goMore();
    } else if (item.menuCode === 'article') {
      if (!item.linkUrl) return;
      // 健康风采
      window.location.href = item.linkUrl;
    }
  };

  const jumpDoctor = (item: any = {}) => {
    const { doctorId, organId } = item;
    jumpTo('/doctor/index', {
      appCode,
      channelCode,
      organCode,
      doctorId,
      organId,
      middleType: 0,
      servCode: 'jhzy',
    });
  };

  // 点击预约挂号table td跳转
  const tdClick = (pathname: string) => {
    if (pathname.includes('http')) {
      // 极速问诊
      const query = {
        channelCode,
        organCode: getOrganCode(),
        token: getToken(),
        servCode: 'gdpb',
        deptCode,
        deptName,
        deptCodes: deptCodes.join(','),
      };
      const href = `${pathname}?${queryString.stringify(query)}`;
      window.location.href = href;
    } else {
      // 预约挂号
      const query = {
        hospitalCode: getOrganCode(),
        hospitalAreaCode: 'ALL',
        scheduleDeptCode: deptCode,
        scheduleDateStart: moment().weekday(1).format('YYYY-MM-DD'),
        scheduleDateEnd: moment().weekday(7).format('YYYY-MM-DD'),
        deptCodeList: JSON.stringify(deptCodes),
        docCodeList: JSON.stringify(deptCodeList_),
      };
      jumpTo(pathname, { ...query, from: 'depthome' });
    }
  };

  const fetchConfigDataJump = () => {
    dispatch({
      type: 'global/singWechatJSSDK',
      payload: {
        url: getCurEvnHref(),
      },
      callback: async (data: any) => {
        const configData = {
          ...data,
          debug: false,
          jsApiList: ['getLocation'],
        };
        const { latitude, longitude } = await configWechatGetLocation(configData);
        if (latitude && longitude) {
          window.location.href = `https://hxgyweb.cd120.info/physical-exam-h5/home?source=HXGYAPP&longitude=${longitude}&latitude=${latitude}&organCode=${getOrganCode()}&appCode=${appCode}&channelCode=${channelCode}&token=${getToken()}`;
        }
      },
    });
  };
  const onClickBusinessModule = async (item) => {
    const deptCodesQS = queryString.stringify({ deptCodes });
    if (item.linkUrl) {
      if (item.menuCode === 'first_jktj') {
        window.location.href = `https://hxgyweb.cd120.info/physical-exam-h5/home?source=HXGYAPP&organCode=${getOrganCode()}&appCode=${appCode}&channelCode=${channelCode}&token=${getToken()}`;
        // const geolocation = new window.BMap.Geolocation();
        // geolocation.getCurrentPosition(
        //   function (r) {
        //     console.log('geolocation', geolocation.getStatus());
        //     console.log('---BMAP_STATUS_SUCCESS', r.accuracy, geolocation.BMAP_STATUS_SUCCESS);
        //     if (geolocation.getStatus() === 0) {
        //       window.location.href = `https://hxgyweb.cd120.info/physical-exam-h5/home?source=HXGYAPP&longitude=${
        //         r?.point?.lat
        //       }&latitude=${
        //         r?.point?.lng
        //       }&organCode=${getOrganCode()}&appCode=${appCode}&channelCode=${channelCode}&token=${getToken()}`;
        //     }
        //   },
        //   { enableHighAccuracy: true },
        // );
        return;
      }
      if (item.menuCode == 'family_doctor') {
        history.push(item.linkUrl);
        return;
      }
      window.location.href = `${item.linkUrl}&${deptCodesQS}`;
    } else {
      // history.push('/common/businesscardlist?from=HEALTHY_LIFE')
      return Toast.info('配置url地址有误');
    }
  };

  const renderCarousel = () => {
    const banners = deptConfig?.bannerConfig?.bannerInfoList || [];
    if (!banners.length) return null;
    return (
      <Carousel
        dotStyle={{ width: '4px', height: '4px', background: '#ffffff4d' }}
        dotActiveStyle={{ width: '14px', borderRadius: '2px', height: '4px', background: '#fff' }}
        autoplay
        autoplayInterval={deptConfig.bannerConfig?.interval || 6000}
        infinite
      >
        {banners.map((v: any, index: number) => (
          <a
            style={{ display: 'inline-block', width: '100%' }}
            key={index.toString()}
            className={styles.banner}
            onClick={() => {
              if (!v.linkUrl) return;
              window.location.href = v.linkUrl;
            }}
          >
            <img
              alt=""
              style={{ minHeight: imgheight }}
              src={v?.imgUrl}
              onLoad={() => {
                window.dispatchEvent(new Event('resize'));
                setImgheight(imgheight);
              }}
            />
          </a>
        ))}
      </Carousel>
    );
  };
  const renderTopCard = () => {
    const topcards = deptConfig?.topCardsConfig?.menuInfoList || [];
    const clickNum = deptConfig?.clickNum || 0;
    const dayClick = deptConfig?.dayClickNum || 0;

    // if (!topcards.length) return null;

    return (
      <div>
        <div className={styles.subscribe}>
          <span>
            累计访问:&nbsp; {clickNum > 9999 ? Number(clickNum / 10000).toFixed(1) : clickNum}
            {clickNum > 9999 ? '万' : ''}
          </span>
          <span style={{ marginLeft: '20px' }}>
            今日访问:&nbsp; {dayClick > 9999 ? Number(dayClick / 10000).toFixed(1) : dayClick}
            {dayClick > 9999 ? '万' : ''}
          </span>
        </div>
        {topcards.length ? (
          <div className={styles.headerCard}>
            {topcards.map((item: ICardStepItem) => {
              return (
                <div key={item.menuId} className={styles.cardStepItem} onClick={() => onClickCardStep(item)}>
                  <img src={item.imgUrl} className={styles.cardpic} alt="" />
                  <span>{item.menuName}</span>
                </div>
              );
            })}
          </div>
        ) : null}
      </div>
    );
  };

  const renderBusinessModule = () => {
    let businessList = deptConfig?.businessConfig?.menuInfoList || [];
    if (!businessList.length) return null;
    const { whetherAlipay }: any = window;
    if (whetherAlipay) {
      // 支付宝环境去除健康体检
      businessList = businessList.filter((item) => item.menuName !== '健康体检');
    }

    const busList = _.chunk(businessList, 4);

    return (
      <div className={styles.businessCard}>
        <Carousel
          dotStyle={{
            width: '4px',
            height: '4px',
            color: '#00a4ff',
            background: 'rgba(50,185,170,.3)',
            borderRadius: '2px',
          }}
          dotActiveStyle={{ width: '12px', borderRadius: '2px', height: '4px', background: '#3AD3C1' }}
        >
          {busList.map((items: any) => {
            return (
              <div className={styles.business} key={items[0].menuId}>
                {items.map((item: ICardStepItem) => {
                  return (
                    <>
                      <div className={styles.item} key={item.menuId} onClick={() => onClickBusinessModule(item)}>
                        <div className={styles.icon}>
                          <img src={item.imgUrl} alt="" />
                        </div>
                        <div className={styles.name}>
                          <span>{item.menuName}</span>
                        </div>
                      </div>
                    </>
                  );
                })}
              </div>
            );
          })}
        </Carousel>
      </div>
    );
  };

  const whetherToRegister = deptConfig?.whetherToRegister;
  const { whetherAlipay }: any = window;

  return (
    <div className={styles.container}>
      {loading ? (
        <HxIndicator />
      ) : (
        <>
          {/* 顶部card */}
          {!whetherAlipay && (
            <FloatEntrance
              icon={icon_testimonials}
              link={`${YH_HX_BASE}/hxgzh/index.html#/praise?deptName=${deptName}&deptId=${deptId}`}
            />
          )}
          <div className={styles.header}>{renderTopCard()}</div>
          <div className={styles.content}>
            {renderCarousel()}
            {whetherToRegister && (
              <div className={styles.order}>
                <Tab tabs={tabs} setCurrentTabId={setCurrentTabId}>
                  <div className={styles.tabContent}>
                    <Table
                      list={orderData.items}
                      tdClick={() => {
                        if (currentTabId === 1) {
                          sensorsRequest('PBYZ_DEPARTMENT_CLICK', {});
                        }
                        tdClick('/hxappointment/weeklyschedule');
                      }}
                    />
                    <div className={styles.tableTips}>{orderData.tips}</div>
                  </div>
                  <div className={styles.tabContent}>
                    <Table
                      list={inquiryData.items}
                      tdClick={() => {
                        if (currentTabId === 1) {
                          sensorsRequest('PBYZ_DEPARTMENT_CLICK', {});
                        }
                        tdClick(`${API_ZXMZ}/onlinefast/home`);
                      }}
                    />
                    <div className={styles.tableTips}>{inquiryData.tips}</div>
                  </div>
                </Tab>
              </div>
            )}

            {/* 业务模块 */}
            <div>{renderBusinessModule()}</div>
            {/* 医生信息 */}
            <div className={styles.doctor}>
              {/* <div className={styles.title}>
                <div className={styles.left}>科室医生</div>

              </div> */}
              <div className={styles['doctor-list']}>
                <span
                  className={doctorListViewMore === 1 ? styles.active : styles.no_active}
                  onClick={() => {
                    setDoctorListViewMore(1);
                  }}
                >
                  华西医生
                </span>
                {deptConfig.HXYLTFlag && doctorListHXYLT.length > 0 ? (
                  <span
                    className={doctorListViewMore === 2 ? styles.active : styles.no_active}
                    onClick={() => {
                      setDoctorListViewMore(2);
                    }}
                    style={{
                      marginLeft: '6.4vw',
                    }}
                  >
                    医联体医生
                  </span>
                ) : null}
              </div>

              {doctorListViewMore === 1
                ? doctorList instanceof Array && (
                    <div className={styles.doctorList}>
                      {doctorList.map((v: any) => (
                        <div key={v.doctorId} className={styles.doctorItem} onClick={() => jumpDoctor(v)}>
                          <div className={styles.item}>
                            <HxDoctorAvatar src={v.portrait} alt="" className={styles.avatar} />
                            <div className={styles.right}>
                              <span className={styles.name}>{v.doctorName}</span>
                              <div className={styles.desc}>
                                <div className={styles.title}>{v.hospitalTitleName || v.standardTitleName}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )
                : doctorListHXYLT instanceof Array && (
                    <div className={styles.doctorList}>
                      {doctorListHXYLT.map((v: any) => (
                        <div key={v.doctorId} className={styles.doctorItem} onClick={() => jumpDoctor(v)}>
                          <div className={styles.item}>
                            <HxDoctorAvatar src={v.portrait} alt="" className={styles.avatar} />
                            <div className={styles.right}>
                              <span className={styles.name}>{v.doctorName}</span>
                              <div className={styles.desc}>
                                <div className={styles.title}>{v.hospitalTitleName || v.standardTitleName}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

              <div className={styles.right_} onClick={() => goMore('/department/doctorlist')}>
                <div className={styles.hint}>查看更多</div>
                <HxIcon iconName="arrow-right" className={styles.more} />
              </div>
            </div>

            {/* 健康科普 */}
            {!whetherAlipay && (
              <div className={styles.health}>
                <div className={styles.title}>
                  <div className={styles.left}>健康科普</div>
                  <div className={styles.right} onClick={() => goMore('/department/healthscience')}>
                    <div className={styles.hint}>查看更多</div>
                    <HxIcon iconName="arrow-right" className={styles.more} />
                  </div>
                </div>
                <div className={styles.healthList} style={{ height: !healthList.length ? '16px' : 'auto' }}>
                  <HealthScience list={healthList} />
                </div>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default connect(
  ({ global, department, loading }: { global: IGlobalModelState; department: IDeptModelState; loading: Loading }) => ({
    global,
    department,
    loading: loading.effects['department/getDepartmentConfig'],
  }),
)(DepartmentDetailPage);
