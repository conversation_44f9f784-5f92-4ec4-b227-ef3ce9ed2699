.subscribe {
  height: 1.06rem;
  margin: 0 0 0.75rem 0;
  color: #def7f4;
  font-weight: 600;
  font-size: 28px;
  line-height: 28px;
  text-align: center;
}

.container {
  height: 100%;
  background-color: #f5f6fa;

  .header {
    min-height: 128px;
    margin-bottom: 24px;
    padding: 48px 24px 0 24px;
    background: linear-gradient(#32b9aa, #f5f6fa);

    .headerCard {
      display: flex;
      justify-content: space-between;
      box-sizing: border-box;
      padding: 32px 60px; //?
      background-color: #fff;
      border-radius: 12px;
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.13);

      .cardStepItem {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;

        .cardpic {
          width: 120px;
          height: 120px;
        }

        span {
          margin-top: 12px;
          color: #333;
          font-size: 24px;
        }

        .cardBadge {
          position: absolute;
          top: -15px;
          right: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 40px;
          min-height: 40px;
          color: #fff;
          font-size: 26px;
          background: #ce0000;
          border-radius: 50%;
        }
      }
    }
  }

  .content {
    padding: 0 24px;
    padding-bottom: 24px;
    background-color: #f5f6fa;

    .banner {
      height: 140px;
      border-radius: 12px;

      img {
        width: 100%;
        object-fit: fill;
        border-radius: 12px;
        touch-action: pan-y;
      }
    }

    .order {
      margin: 24px 0;
      padding: 24px;
      background: #fff;
      border-radius: 12px;

      .tabContent {
        background: #fff;

        .tableTips {
          margin-top: 24px;
          padding: 0 30px;
          color: #989eb4;
          font-size: 24px;
          text-align: center;
        }
      }
    }

    .doctor,
    .health {
      margin: 24px 0;
      padding: 32px 24px 4px 24px;
      background: #fff;
      border-radius: 12px;

      .title {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;

        // margin-bottom: 32px;
        .left {
          color: #03081a;
          font-weight: 500;
          font-size: 36px;
          line-height: 48px;
        }

        .right {
          display: flex;
          flex-direction: row;
          align-items: center;
          color: #989eb4;
          font-size: 26px;
          text-align: right;

          .more {
            margin-left: 3px;
            font-size: 22px;
          }
        }
      }

      .right_ {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        color: #989eb4;
        font-size: 26px;
        text-align: right;
        margin-bottom: 16px;
        margin-top: 20px;
        //margin-left: 327px;

        .more {
          margin-left: 3px;
          font-size: 22px;
        }
      }

      .doctorList {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        margin-top: 32px;

        .doctorItem {
          // width: 292px;
          width: 42vw;
          height: 112px;
          margin: 0 22px 22px 0;
          padding: 16px 0 16px 24px;
          background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-item-bg.png');
          background-size: cover;
          border-radius: 4px;

          &:nth-of-type(2n) {
            margin-right: 0;
          }

          .item {
            display: flex;
            overflow: hidden;

            .avatar {
              width: 80px;
              height: 80px;
              margin-right: 28px;
              border-radius: 50%;
            }

            .right {
              .name {
                color: #03081a;
                font-weight: 500;
                font-size: 28px;
                font-family: PingFangSC-Medium, PingFang SC;
                line-height: 40px;
                white-space: nowrap;
              }

              .desc {
                display: flex;
                margin-top: 8px;

                .title {
                  height: 26px;
                  padding: 4px 16px;
                  color: #fff;
                  font-weight: 400;
                  font-size: 16px;
                  line-height: 22px;
                  background: #3ad3c1;
                  border-radius: 15px;
                }
              }
            }
          }
        }
      }
    }

    .health {
      padding: 32px 0 0 0;

      .title {
        padding: 0 24px;

        .left {
          color: #03081a;
          font-weight: 600;
          line-height: 50px;
        }
      }
    }

    .businessCard {
      // margin-bottom: 24px;
      // padding: 32px;
      // box-sizing: border-box;
      width: 100%;
      height: 214px;
      background-color: #fff;
      border-radius: 6px;

      .business {
        display: flex;
        //justify-content: space-between;
        width: 100%;
        padding: 32px;

        .item {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: space-between;
          width: 100px;
          height: 126px;
          margin-left: 10.4vw;

          .icon {
            width: 88px;
            height: 88px;
            background-size: cover;

            img {
              width: 80px;
              height: 80px;
              object-fit: fill;
            }
          }

          .name {
            margin-top: 12px;
            font-size: 24px;
          }
        }

        .item:nth-child(4n + 1) {
          margin-left: 0px;
        }
      }
    }

    :global {
      .am-carousel {
        height: 100% !important;

        .slider-frame {
          // overflow: unset !important;
          // overflow-x: clip !important;
          border-radius: 6px;

          .slider-slide {
            padding-right: 0;
            padding-left: 0;
          }
        }
      }

      .slick-dots li button {
        width: 10px !important;
        height: 10px !important;
        background: #00a34f !important; // 改变dots的颜色
        border-radius: 100% !important;
      }
    }
  }
}

.doctor-list {
  color: #989eb4;
  font-weight: 400;
  font-size: 1rem;
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 0.5rem;
  margin-bottom: 62px;
}

.no_active {
  font-weight: 400;
  font-size: 4.2667vw;
  font-family: PingFangSC-Regular, PingFang SC;
  color: #03081a;
}

.active {
  position: relative;
  font-size: 36px;
  font-family: PingFangSC-Medium, PingFang SC;
  line-height: 48px;
  font-weight: 600;
  color: #03081a;

  &::after {
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    bottom: -22px;
    transform: translate(-50%);
    background: #3ad3c1;
    content: '';
    width: 80px;
    height: 8px;
    margin: 0 auto;
    background-image: linear-gradient(to right, #6cebe2, #3ad3c1);
    border-radius: 4px;
    box-shadow: 0 4px 8px 0 rgba(58, 211, 193, 0.4);
  }
}
