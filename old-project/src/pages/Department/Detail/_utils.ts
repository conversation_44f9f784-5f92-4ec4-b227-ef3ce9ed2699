import { SingWechatJSSDKDataType } from '@/typings/global';

interface ConfigDataType extends SingWechatJSSDKDataType {
  /** 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。 */
  debug: boolean;

  /** 必填，需要使用的JS接口列表 (以逗号分割) */
  jsApiList: string[];
}
const { wx } = window;
// 获取用户当前位置经纬度信息
const configWechatGetLocation = (configData: ConfigDataType) => {
  const { debug, appId, timestamp, nonceStr, signature, jsApiList = [] } = configData;
  return new Promise(function (resolve, reject) {
    wx.config({
      debug,
      appId,
      timestamp,
      nonceStr,
      signature,
      jsApiList,
    });
    wx.ready(() => {
      // 获取用户当前地理位置经纬度信息
      wx.getLocation({
        type: 'wgs84', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
        success: (res: any) => {
          const { latitude = 0, longitude = 0 } = res;
          resolve({
            latitude,
            longitude,
          });
        },
        cancel: (err: any) => {
          console.log('用户拒绝授权获取地理位置', err);
          reject(new Error('用户拒绝授权获取地理位置'));
        },
      });
    });
    wx.error((err: any) => {
      console.log('configWechatGetLocation-err', err);
    });
  });
};
const isWeixinBrowser = () => {
  return /micromessenger/.test(navigator.userAgent.toLowerCase());
};
const getAddressInfo = () => {
  if (navigator.geolocation) {
    const position = navigator.geolocation.watchPosition();
    return {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude,
    };
  }
};
export { configWechatGetLocation, isWeixinBrowser, getAddressInfo };
