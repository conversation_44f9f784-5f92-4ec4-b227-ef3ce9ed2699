/*
 * @Author: Vane
 * @Date: 2021-06-01 15:41:50
 * @LastEditTime: 2024-12-19 16:55:37
 * @LastEditors: yyl
 * @Description:
 * @FilePath: /hyt-person/src/pages/Department/model.ts
 */
import { Reducer } from 'redux';
import { Effect } from 'umi';
import { createAction } from '@/utils/common';
import * as API from './service';
import { IDepartmentListItem } from './data.d';

export interface IMessageModelState {}

export interface IDeptModelState {
  departmentList: IDepartmentListItem[];
  deptConfig: any;
  orderData: any;
  inquiryData: any;
  doctorList: Array<any>;
  healthList: Array<any>;
  doctorListHXYLT: Array<any>;
  deptCodeList_: Array<any>;
}

export interface IDepartmentModel {
  namespace: 'department';
  state: IDeptModelState;
  effects: {
    queryDepartmentList: Effect;
    getDepartmentConfig: Effect;
    getDepartmentOrderdata: Effect;
    getDepartmentInquiryData: Effect;
    getDepartmentDoctors: Effect;
    getDepartmentHealthList: Effect;
    getDepartmentDoctorsHX: Effect;
    dataRecord: Effect;
    getP1TeamList: Effect;
  };
  reducers: {
    updateState: Reducer<IDeptModelState>;
  };
}

const DemoModel: IDepartmentModel = {
  namespace: 'department',

  state: {
    departmentList: [],
    deptConfig: {},
    orderData: {},
    inquiryData: {},
    doctorList: [],
    healthList: [],
    doctorListHXYLT: [],
    piTeamData: {},
  },

  effects: {
    *queryDepartmentList({ payload, cb }, { put }) {
      const data = yield API.queryDepartmentList({ ...payload }) || [];
      yield put(createAction('updateState')({ departmentList: data }));
      cb?.(data);
    },
    *getDepartmentConfig({ payload, cb }, { put }) {
      const data = yield API.getDepartmentConfig({ ...payload }) || {};
      const { bannerModuleList = [{}], menuModuleList, clickNum, dayClickNum } = data?.resourceInfo || {};
      const topCardsConfig =
        menuModuleList instanceof Array ? menuModuleList.filter((item) => item.moduleCode === 'tjcd') : [];
      const businessConfig =
        menuModuleList instanceof Array ? menuModuleList.filter((item) => item.moduleCode === 'businessModule') : [];
      const HXYLTFlag = menuModuleList.filter((item) => item.moduleCode === 'medicalUnionDoctor');
      // 是否有挂号排班
      const whetherToRegister = menuModuleList.some((item) => item.moduleCode === 'doctorSchedual');
      const params = {
        bannerConfig: bannerModuleList?.[0] || {},
        businessConfig: businessConfig?.[0] || {},
        topCardsConfig: topCardsConfig?.[0] || {},
        clickNum: clickNum || 0,
        dayClickNum: dayClickNum || 0,
        HXYLTFlag, // 是否是挂号排班
        whetherToRegister,
      };

      yield put(createAction('updateState')({ deptConfig: params }));
      cb?.(params);
    },
    *getDepartmentOrderdata({ payload, cb }, { put }) {
      const data = yield API.getDepartmentOrderdata({ ...payload }) || {};
      yield put(createAction('updateState')({ orderData: data }));
      cb?.(data);
    },

    *getDepartmentInquiryData({ payload, cb }, { put }) {
      const data = yield API.getDepartmentInquiryData({ ...payload }) || {};
      yield put(createAction('updateState')({ inquiryData: data }));
      cb?.(data);
    },
    *getDepartmentDoctorsHX({ payload, cb }, { put }) {
      const data = yield API.getDepartmentDoctorsHX({ ...payload }) || [];
      yield put(createAction('updateState')({ doctorListHXYLT: data }));
      cb?.(data);
    },
    *getDepartmentDoctors({ payload, cb }, { put }) {
      const data = yield API.getDepartmentDoctors({ ...payload }) || [];
      const deptCodeList_ = data.map((v) => v.doctorCode);
      yield put(createAction('updateState')({ doctorList: data, deptCodeList_ }));
      cb?.(data);
    },
    *getDepartmentHealthList({ payload, cb }, { put }) {
      // 健康列表
      const data = yield API.getDepartmentHealthList({ ...payload }) || {};
      yield put(createAction('updateState')({ healthList: data.content }));
      cb?.(data.content);
    },
    *dataRecord({ payload, cb }) {
      const data = yield API.dataRecord({ ...payload }) || {};
      cb?.(data.content);
    },
    *getP1TeamList({ payload, cb }, { put }) {
      const data = yield API.queryPI({ ...payload });
      yield put(createAction('updateState')({ piTeamData: data }));
      cb && cb(data);
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};

export default DemoModel;
