/*
 * @Author: Vane
 * @Date: 2021-06-08 13:22:38
 * @LastEditTime: 2021-07-23 19:41:03
 * @LastEditors: Please set LastEditors
 * @Description:
 * @FilePath: \hyt-person\src\pages\Department\HealthList\index.tsx
 */
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { connect, Loading, Dispatch } from 'umi';
import { ListView } from 'antd-mobile';
import { HxIndicator, StyleComponents } from '@/components';
import styles from './index.less';
import ListItem from '../Components/HealthScience/ListItem';

const { EmptyView } = StyleComponents;

interface QueryResultProps {
  takeDrug: any;
  dispatch: Dispatch;
  location: {
    query: {
      appCode: string;
      channelCode: string;
      organCode: string;
      // 后台字段：定义当前列表查询的页面类型码
      enterCode: string;
      // attrId 定义不同页面进来的查询id
      attrId: string;
      classifyId: string;
    };
  };
  loading: boolean | undefined;
}

const dataSource = new ListView.DataSource({
  rowHasChanged: (row1: any, row2: any) => row1 !== row2,
});

const QueryResult: React.FC<QueryResultProps> = (props) => {
  // props
  const {
    dispatch,
    location: { query },
    loading,
  } = props;

  const { id }: any = useParams();

  const { appCode, channelCode, organCode, enterCode, classifyId, attrId } = query;

  const [list, setList] = useState([]);

  const [pageNum, setPageNum] = useState<number>(1);

  const [noMore, setNoMore] = useState<boolean>(false);

  // 是否刷新中
  const [refreshing, setRefreshing] = useState<boolean>(false);

  const queryData = (type: string, payload: any, cb: Function) =>
    dispatch({ type, payload, cb: (res: any = {}) => cb?.(res) });

  // // state
  // const [height, setHeight] = useState(document.documentElement.clientHeight);

  // // ref
  // const ptr = useRef<any>(null);

  // 初始条数下拉刷新条数
  const pageSize = 10;
  // useEffect(() => {
  //   setHeight((pre) => pre - ptr.current.offsetTop);
  // }, []);

  const onLoadMore = () => {
    setPageNum((page) => page + 1);
  };

  // 健康科普列表（默认5条）
  const getList = ({ pageNum = 1, pageSize = 10 }) => {
    setRefreshing(true);
    const params = {
      pageNum,
      pageSize,
      query: {
        appCode,
        channelCode,
        organCode,
        attrId: id || attrId,
        enterCode,
        classifyId,
      },
    };
    queryData('department/getDepartmentHealthList', params, (res: any) => {
      const _len = res?.length;
      if (!_len || _len < pageSize) {
        setNoMore(true);
      }
      setRefreshing(false);
      setList(list.concat(res || []));
    });
  };
  const dataRecord = (item: any) => {
    if (!window.location.origin.includes('cd120.info') || !window.location.origin.includes('localhost')) return;
    const params = {
      appCode,
      channelCode,
      organCode,
      dataType: 1,
      operator: '',
      resourceId: item.resourceId,
    };
    queryData('department/dataRecord', params, (res: any) => {
      console.log('department/dataRecord----', res);
    });
  };

  const renderRow = (item: any) => (
    <div className={styles.itemWrapper} onClick={() => dataRecord(item)}>
      <ListItem item={item} />
    </div>
  );

  // useEffect(() => {
  //   window.scroll(0, 0);
  // }, []);

  useEffect(() => {
    !noMore && getList({ pageNum, pageSize });
  }, [pageNum]);

  return (
    <div className={styles.container}>
      {loading && !refreshing ? (
        <HxIndicator />
      ) : list?.length ? (
        <>
          <ListView
            dataSource={dataSource.cloneWithRows(list || [])}
            renderRow={renderRow}
            useBodyScroll
            onEndReachedThreshold={30}
            initialListSize={10}
            onEndReached={onLoadMore}
          />
        </>
      ) : (
        <EmptyView isNew text="暂无内容" />
      )}
    </div>
  );
};

export default connect(({ loading }: { loading: Loading }) => ({
  loading: loading.effects['department/getDepartmentHealthList'],
}))(QueryResult);
