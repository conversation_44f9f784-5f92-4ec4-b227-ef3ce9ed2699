.container {
  min-height: 100vh;
  padding: 24px;
  // padding-bottom: 100px;
  background-color: #f5f6f7;
  .top {
    display: flex;
    padding: 40px;
    background-color: #32b9aa;
    border-bottom: 1px solid #e5e5e5;

    img {
      width: 90px;
      height: 90px;
      margin-right: 40px;
      border-radius: 90px;
    }

    .info {
      .name {
        color: #fff;
        font-weight: 500;
        font-size: 34px;
      }

      .cardNo {
        color: rgba(255, 255, 255, 0.8);
        font-size: 28px;
      }
    }
  }

  .list {
    border-radius: 16px;
  }
  .noMore {
    height: 77px;
    overflow: hidden;
    color: #ddd;
    font-weight: 400;
    font-size: 28px;
    font-family: PingFang SC;
    line-height: 33px;
    text-align: center;
    p {
      margin: 0;
      margin-top: 21px;
    }
  }
  .itemWrapper {
    padding: 0 24px;
    background-color: #fff;
    &:first-child {
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
    }
    &:last-child {
      border-bottom-right-radius: 16px;
      border-bottom-left-radius: 16px;
    }
  }
  :global {
    .am-list-body::before,
    .am-list-body::after {
      height: 0 !important;
    }
  }
}
