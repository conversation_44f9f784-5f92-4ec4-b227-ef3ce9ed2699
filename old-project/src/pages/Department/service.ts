/*
 * @Author: <PERSON><PERSON>
 * @Date: 2021-06-01 15:41:50
 * @LastEditTime: 2022-02-14 17:49:32
 * @LastEditors: Please set LastEditors
 * @Description:
 * @FilePath: \hyt-person\src\pages\Department\service.ts
 */
import request from '@/utils/request';

const node = '/cloud';

export const queryDepartmentList = async (data: object): Promise<any> =>
  request(`${PLATFORM_TRANSFORM}/department/fetchLevelDept4ContentPlat`, {
    method: 'POST',
    data,
  });

export const getDepartmentConfig = async (data: object): Promise<any> =>
  request(`${API_CHRONIC}/doctorcenter/home/<USER>/query`, {
    method: 'POST',
    data,
  });

export const getDepartmentOrderdata = async (data: object): Promise<any> =>
  request(`${API_TEST}/hosplatcustomer/call/appointment/foreign/deptWeekScheduleV1`, {
    method: 'POST',
    data,
  });

export const getDepartmentInquiryData = async (data: object): Promise<any> =>
  request(`${PLATFORM_TRANSFORM}/schedule/list/dept`, {
    method: 'POST',
    data,
  });

export const getDepartmentDoctors = async (data: object): Promise<any> =>
  request(`${PLATFORM_TRANSFORM}/doctor/getDoctor4LevelDeptHomePage`, {
    method: 'POST',
    data,
  });
export const getDepartmentDoctorsHX = async (data: object): Promise<any> =>
  request(`${PLATFORM_TRANSFORM}/doctor/getMedicalUnionDoctorDeptHomePage`, {
    method: 'POST',
    data,
  });
export const getDepartmentHealthList = async (data: object): Promise<any> =>
  request(`${node}/mediaCloud/open/fetchMediaList`, {
    method: 'POST',
    data,
  });

export const dataRecord = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/mediaCloud/reportUserExtend`, {
    method: 'POST',
    data,
  });
export const queryPI = async (data: any): Promise<any> => {
  return request(`${node}/doctorcenter/pi/pat/queryPI`, {
    method: 'POST',
    data,
  });
};
