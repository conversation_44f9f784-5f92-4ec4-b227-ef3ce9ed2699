/*
 * @Author: <PERSON><PERSON>
 * @Date: 2021-06-05 15:54:43
 * @LastEditTime: 2022-03-22 11:20:20
 * @LastEditors: Please set LastEditors
 * @Description:
 * @FilePath: /hyt-person/src/pages/Department/DoctorList/index.tsx
 */
import React, { useEffect, useState } from 'react';
import { connect, Loading, Dispatch, history } from 'umi';
import { HxDoctorAvatar, HxIndicator, StyleComponents } from '@/components';
import { getToken } from '@/utils/parameter';
import { Button, Space, Toast, ListView } from 'antd-mobile';
import { SearchBar } from 'antd-mobile-v5';
import { SearchBarRef } from 'antd-mobile/es/components/search-bar';
import { SearchOutline, SetOutline } from 'antd-mobile-icons';
import { IDeptModelState } from '../model';
import styles from './index.less';
import { doctorAvatarDefault } from '../constance';
import Filter from '../Components/Filter';
import SpeDoclist from './speDoclist';

const { EmptyView } = StyleComponents;
interface QueryResultProps {
  department: IDeptModelState;
  dispatch: Dispatch;
  location: {
    query: {
      appCode: string;
      channelCode: string;
      organCode: string;
      deptCode: string;
      deptCodes: Array<string>;
      type: string;
      data: string;
      param: string;
      keywords: string;
      cardNo: string;
      docFillRecordId: string;
      doctorListViewMore: string;
    };
  };
  loading: boolean | undefined;
}

const dataSource = new ListView.DataSource({
  rowHasChanged: (row1: any, row2: any) => row1 !== row2,
});

const QueryResult: React.FC<QueryResultProps> = (props) => {
  // props
  const {
    dispatch,
    department,
    location: { query },
    loading,
  } = props;
  const { appCode, channelCode, organCode, deptCode, type, deptCodes, deptId, doctorListViewMore } = query;
  const { doctorList, doctorListHXYLT } = department;
  const [pageNum, setPageNum] = useState(1);
  const [noMore, setNoMore] = useState(false);
  // 设置查询参数
  const [_name, setName] = useState('');
  const [_title, setTitle] = useState('全部职称');
  // 特药专区
  const [medicine, setMedicine] = useState(false);
  const queryData = (type: string, payload: any, cb: Function) =>
    dispatch({ type, payload, cb: (res: any = {}) => cb?.(res) });
  const [showLIst, setShowList] = useState([]);

  // 初始条数下拉刷新条数
  const pageSize = 10;
  const [titleList, setTitleList] = useState([]);
  const getTitles = (arr) => {
    const res = [
      {
        hospitalTitleName: '全部职称',
        name: '全部职称',
      },
    ];
    arr.forEach((e) => {
      const index = res.findIndex((i) => {
        return i.name == e.hospitalTitleName;
      });
      if (index === -1 && e.hospitalTitleName) {
        res.push({
          hospitalTitleName: e.hospitalTitleName,
          name: e.hospitalTitleName,
        });
      }
    });
    return res;
  };
  // 健康科普列表（默认5条）
  const getList = ({ pageNum = 1, pageSize = 10 }) => {
    const params = {
      deptCode,
      organCode,
      deptCodes,
      deptId,
      queryType: 1,
    };
    if (doctorListViewMore == '2') {
      queryData('department/getDepartmentDoctorsHX', params, (res: any) => {
        console.log(res);
        const _len = res?.length;
        if (!_len || _len < pageSize) {
          setNoMore(true);
        }
        setShowList([]);
        setShowList(res);
        setTitleList(getTitles(res));
        // setList(list.concat(res || []));
      });
    } else {
      queryData('department/getDepartmentDoctors', params, (res: any) => {
        console.log(res);
        const _len = res?.length;
        if (!_len || _len < pageSize) {
          setNoMore(true);
        }
        setShowList([]);
        setShowList(res);
        setTitleList(getTitles(res));

        // setList(list.concat(res || []));
      });
    }
  };

  const filterShowList = () => {
    const list = doctorListViewMore == '1' ? [...doctorList] : [...doctorListHXYLT];
    const list1 =
      _title === '全部职称'
        ? list
        : list.filter((e) => {
            return e.hospitalTitleName === _title;
          });
    const list2 =
      _name === ''
        ? list1
        : list1.filter((e) => {
            return e.doctorName.includes(_name);
          });
    setShowList([...list2]);
  };
  useEffect(() => {
    filterShowList();
  }, [_title]);
  useEffect(() => {
    filterShowList();
  }, [_name]);
  const onDoctorTitle = (value: string) => {
    setTitle(value);
  };
  const onSearch = (value: string) => {
    setName(value);
  };
  const jumpTo = (pathname: string, query: any = {}) => {
    history.push({ pathname, query });
  };

  const jumpDoctor = (item: any = {}) => {
    const { doctorId, organId } = item;
    jumpTo('/doctor/index', {
      appCode,
      channelCode,
      organCode,
      doctorId,
      organId,
      middleType: 0,
      servCode: 'jhzy',
    });
  };

  const onLoadMore = () => {
    setPageNum((page) => page + 1);
  };

  const renderNoMore = () => (
    <div className={styles.noMore}>
      <p>—— 没有更多啦 ——</p>
    </div>
  );

  const renderRow = (item: any = {}, index: any) => {
    return (
      <div
        key={item?.doctorId ? item.doctorId : item.doctorName}
        className={styles.messageListItem}
        onClick={() => jumpDoctor(item)}
      >
        <HxDoctorAvatar src={item.portrait} alt="" className={styles.listAvatar} />
        <div className={styles.messageListItemRight}>
          <div className={styles.header}>
            <div className={styles.rightName}>{item.doctorName}</div>
            <div className={styles.title}>{item.hospitalTitleName} </div>
          </div>
          <div className={styles.rightDown}>
            <div className={styles.downMessage}>
              <img
                src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-main-goodat.png"
                alt=""
              />
              {item.profession || '暂无'}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // 特药数据
  useEffect(() => {
    // alert(doctorListViewMore);
    if (type) {
      setMedicine(true);
    } else {
      !noMore && getList({ pageNum, pageSize });
    }
  }, [pageNum, deptCode, doctorListViewMore]);
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <>
      {medicine ? (
        <div className={styles.mainContent}>
          {/* 新需求====》外购药的医生列表 */}
          <SpeDoclist params={query} />
        </div>
      ) : (
        <div className={styles.container}>
          <div className={styles.searchBar}>
            <SearchBar placeholder="输入医生、科室、擅长搜索" onSearch={onSearch} />
          </div>
          <Filter
            sign={1}
            titleList={titleList}
            showTitle
            showHaveNo={false}
            showTimeRange={false}
            showWeekSchedule={false}
            timeRangeList={[]}
            onDoctorTitle={onDoctorTitle}
          />
          {loading ? (
            <HxIndicator />
          ) : showLIst?.length ? (
            <>
              <ListView
                dataSource={dataSource.cloneWithRows(showLIst || [])}
                renderRow={renderRow}
                onEndReached={onLoadMore}
                useBodyScroll
                initialListSize={200}
              />
              {renderNoMore()}
            </>
          ) : (
            <EmptyView />
          )}
        </div>
      )}
    </>
  );
};

export default connect(({ department }: { department: IDeptModelState; loading: Loading }) => ({
  department,

  // loading: loading.effects['department/getDepartmentDoctors'],
}))(QueryResult);
