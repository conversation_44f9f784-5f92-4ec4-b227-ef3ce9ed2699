import React, { FC, useEffect, useState } from 'react';
import { connect, history, Dispatch, Loading } from 'umi';
import { HxEmpty, HxIndicator, HxIcon } from '@/components';
// import { getOrganCode } from '@/utils/parameter'; //  机构编码
import { ListView } from 'antd-mobile';
import styles from './index.less';

const avater = require('@/assets/speMedicine/default.jpg');
const goodAt = require('@/assets/speMedicine/begoodat.png');
const search = require('@/assets/speMedicine/Ico-Search.png');

interface IProps {
  SpecificMedicinePrefecture: any;
  dispatch: Dispatch;
  loading?: boolean;
  params: {
    cardNo: string;
    docFillRecordId: string;
    keywords: string;
  };
}

const dataSource = new ListView.DataSource({
  rowHasChanged: (row1: any, row2: any) => row1 !== row2,
});
const SpeDoclist: FC<IProps> = (props) => {
  const { dispatch, params } = props;
  const pageSize = 10;
  const { cardNo = '', docFillRecordId } = params;
  const [isLoading, setIsLoading] = useState<boolean>(true);
  // const [hasMore, setHasMore] = useState<boolean>(true);
  const [pageNum, setPageNum] = useState<number>(1);
  const [listData, setListData] = useState<any>([]);
  const [keywords, setKeywords] = useState('');
  const [noMore, setNoMore] = useState<boolean>(false);

  /** 获取列表数据 */
  const fetchData = (usePageNum = 1, pageSize = 10) => {
    dispatch({
      type: 'SpecificMedicinePrefecture/doctorList',
      payload: {
        pageSize,
        cardNo, // 上个页面返回
        // cardNo: '000000000070901',
        currentPage: usePageNum,
        docFillRecordId, // 上个页面返回
        organCode: 'HID0101SD', // 代表华西
        searchStr: keywords || '',
      },

      callback: (res: any = {}) => {
        const {
          code,
          data: { records },
        } = res;
        console.log('res', res);
        const dataLength = records?.length;
        if (!dataLength || dataLength < pageSize) {
          setNoMore(true);
        }
        if (code === '1') {
          // setListData([...listData, ...records]);
          setListData(listData.concat(records || []));
        }
        setIsLoading(false);
      },
    });
  };

  const onInputChange = () => {
    history.push('/search/seardata');
  };
  // 清空输入框
  const clear = () => {
    setKeywords(' ');
    fetchData();
  };
  const jumpTo = (pathname: string, query: any = {}) => {
    history.push({ pathname, query });
  };

  // 跳转医生详情
  const jumpDoctor = (item: any = {}) => {
    const { doctorId, organId } = item;
    // 认定记录iddoctorId,
    jumpTo('/doctor/index', {
      organCode: 'HID0101SD', // 代表华西
      doctorId,
      organId,
      middleType: 1,
      servCode: 'jhzy',
      docFillRecordId,
    });
  };
  const onLoadMore = () => {
    setPageNum((page) => page + 1);
  };
  // 获取搜索数据
  useEffect(() => {
    setKeywords(props?.params?.keywords || '');
  }, []);
  // 请求数据
  useEffect(() => {
    !noMore && fetchData(pageNum, pageSize);
  }, [pageNum]);
  const renderRow = (item: any = {}, index: any): React.ReactElement => {
    return (
      <div className={styles.docList} key={index?.toString()} onClick={() => jumpDoctor(item)}>
        <div className={styles.doctorItem}>
          <div className={styles.item}>
            <img src={avater || item.headPortraits} alt="" className={styles.poster} />
            <div className={styles.doctorInfo}>
              <div className={styles.basicInfo}>
                <div className={styles.doctorName}>{item.doctorName}</div>
                <div className={styles.titleNmae}>{item.titelName}</div>
                {item.organLevel && <div className={styles.rank}>{item.organLevel}</div>}
              </div>
              <div className={styles.workInfo}>
                <span className={styles.organName}>{item.organName}</span>
                <span>{item.deptName}</span>
              </div>
              <div className={styles.introduce}>
                <img src={goodAt} alt="" className={styles.goodAt} />
                <div className={styles.introContent}>{item.profession}</div>
              </div>
              <div className={styles.commont}>
                好评率<span> {item.satisfaction}% </span> / 问诊量
                <span>&nbsp;{item.servTimes}</span>
              </div>
              {/* <div className={styles.price}>￥{item.price}</div> */}
              {/* {'servCode' === 'zxyz' && <div className={styles.price}>￥price</div>} */}
            </div>
          </div>
        </div>
      </div>
    );
  };
  const renderNoMore = () => (
    <div className={styles.noMore}>
      <p>—— 没有更多啦 ——</p>
    </div>
  );

  return (
    <div className={styles.speContent}>
      <div className={styles.inputView}>
        <img src={search} alt="" onClick={onInputChange} />
        <input
          placeholder="输入医生、科室搜索"
          value={keywords}
          type="search"
          onFocus={() => onInputChange()}
          // autoFocus
          readOnly
        />
        {keywords.length > 0 && <HxIcon className={styles.clear} onClick={() => clear()} iconName="clear" />}
      </div>
      {isLoading ? (
        <HxIndicator />
      ) : listData.length > 0 ? (
        <div className={styles.listview}>
          <ListView
            dataSource={dataSource.cloneWithRows(listData || [])}
            renderRow={renderRow}
            onEndReached={onLoadMore}
            // useBodyScroll
            onEndReachedThreshold={30}
            initialListSize={10}
          />
          {noMore ? renderNoMore() : null}
        </div>
      ) : (
        // <HxListView
        //   dataSource={listData}
        //   renderRow={renderRow}
        //   // initialListSize={pageSize}
        //   initialListSize={listData.length || 10}
        //   pageSize={pageSize}
        //   onEndReached={() => onEndReached()}
        //   onEndReachedThreshold={20}
        //   isRenderFooter
        //   hasMore={hasMore}
        // />
        <div style={{ height: '500px', overflow: 'hidden' }}>
          <HxEmpty canRefresh={false} />
        </div>
      )}
    </div>
  );
};
export default connect(({ SpecificMedicinePrefecture }: { SpecificMedicinePrefecture: any; loading: Loading }) => ({
  SpecificMedicinePrefecture,
  // loading: loading.effects['SpecificMedicinePrefecture/doctorList'],
}))(SpeDoclist);
