@import '~@/styles/mixin.less';

.container {
  // padding-bottom: 100px;
  min-height: 100vh;
  background-color: #f5f6f7;

  .top {
    display: flex;
    padding: 40px;
    background-color: #32b9aa;
    border-bottom: 1px solid #e5e5e5;

    img {
      width: 90px;
      height: 90px;
      margin-right: 40px;
      border-radius: 90px;
    }

    .info {
      .name {
        color: #fff;
        font-weight: 500;
        font-size: 34px;
      }

      .cardNo {
        color: rgba(255, 255, 255, 0.8);
        font-size: 28px;
      }
    }
  }

  .main {
    padding: 24px;

    .resultItem {
      margin-bottom: 36px;
      padding: 40px 50px;
      text-align: center;
      background-color: #fff;
      border-left-width: 14px;
      border-left-style: solid;

      .resultText {
        font-weight: 500;
        font-size: 38px;
      }

      .resultTip {
        padding-top: 4px;
        font-size: 32px;
      }
    }
  }

  .messageListItem {
    position: relative;
    display: flex;
    justify-content: flex-start;
    box-sizing: border-box;
    margin: 0 24px 24px 24px;
    padding: 24px;
    background: #fff;
    border-radius: 12px;

    .listAvatar {
      flex-shrink: 0;
      width: 128px;
      height: 128px;
      margin-right: 24px;
      border-radius: 50%;
    }

    .listBadge {
      position: absolute;
      top: 15px;
      left: 90px;
      display: flex;
      align-items: center;
      justify-content: center;
      min-width: 40px;
      min-height: 40px;
      color: #fff;
      font-size: 22px;
      background: #ce0000;
      border-radius: 50%;
    }

    .messageListItemRight {
      display: flex;
      flex-direction: column;
      width: -webkit-fill-available;

      .header {
        display: flex;
        align-items: center;

        .rightName {
          color: #03081a;
          font-weight: 600;
          font-size: 36px;
          font-family: PingFangSC-Semibold, PingFang SC;
          line-height: 52px;
        }

        .title {
          margin-left: 24px;
          color: #03081a;
          font-weight: 400;
          font-size: 28px;
          font-family: PingFangSC-Regular, PingFang SC;
          line-height: 40px;
        }
      }

      .rightDown {
        margin-top: 12px;

        .downMessage {
          position: relative;
          display: -webkit-box;
          overflow: hidden;
          color: #989eb4;
          font-weight: 400;
          font-size: 24px;
          font-family: PingFangSC-Regular, PingFang SC;
          line-height: 32px;
          // .textOverflowMulti(2);
          text-overflow: ellipsis;

          /*! autoprefixer: off */
          -webkit-box-orient: vertical;

          /* autoprefixer: on */
          -webkit-line-clamp: 2;

          img {
            width: 52px;
            height: 24px;
            margin-top: -6px;
            margin-right: 6px;
          }
        }
      }
    }
  }

  .listItem {
    margin-bottom: 0.24px;
  }

  :global(.am-list-view-scrollview) {
    height: 100% !important;
  }

  .noMore {
    text-align: center;
  }
}

.mainContent {
  width: 100%;
  height: 100%;
  // overflow: hidden;
}

.speContent {
  position: relative;
  width: 100%;
  height: 100%;
  // overflow-y: scroll;
  background: #f5f6fa;

  .inputView {
    position: relative;
    position: sticky;
    top: 0;
    z-index: 14;
    width: 100%;
    height: 136px;
    margin-bottom: 24px;
    padding: 32px 32px;
    background: #fff;

    img {
      position: absolute;
      top: 50px;
      left: 54px;
      width: 36px;
      height: 36px;
    }

    input {
      width: 100%;
      height: 72px;
      padding-left: 80px;
      color: #989eb4;
      font-weight: 400;
      font-size: 32px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 72px;
      background: #f5f6fa;
      border: none;
      border-radius: 36px;
    }

    input::-webkit-input-placeholder {
      color: #989eb4;
      font-weight: 400;
    }

    .clear {
      position: absolute;
      top: 54px;
      right: 62px;
      width: 28px;
      height: 28px;
    }
  }

  .docList {
    clear: both;
    // height: calc(100% - 136px);
    width: 100%;
    padding: 24px;
    padding-bottom: 0;
    background: #f5f6fa;

    .doctorItem {
      // margin-bottom: 24px;
      height: 346px;
      padding: 24px 24px;
      background-color: #fff;
      border-radius: 12px;

      .item {
        display: flex;
        // border-bottom: 2px solid rgba(235, 237, 245, 0.3);
      }

      .poster {
        width: 128px;
        height: 128px;
        margin-right: 24px;
        border-radius: 50%;
      }

      .doctorInfo {
        .basicInfo {
          display: flex;
          align-items: center;

          .doctorName {
            margin-right: 16px;
            color: #03081a;
            font-weight: 600;
            font-size: 36px;
          }

          .titleNmae {
            margin-right: 24px;
            color: #03081a;
            font-size: 28px;
          }

          .rank {
            height: 32px;
            padding: 0 12px;
            color: #fff;
            font-size: 18px;
            line-height: 32px;
            text-align: center;
            background-color: #568df2;
            border-radius: 8px;
          }
        }

        .workInfo {
          margin: 12px 0;
          color: #03081a;
          font-size: 28px;

          .organName {
            margin-right: 16px;
          }
        }

        .introduce {
          position: relative;
          display: -webkit-box;
          width: 502px;
          overflow: hidden;
          font-size: 24px;
          text-indent: 55px;
          text-overflow: ellipsis;

          /*! autoprefixer: off */
          -webkit-box-orient: vertical;

          /* autoprefixer: on */
          -webkit-line-clamp: 2;

          .goodAt {
            position: absolute;
            top: 5.5px;
            left: 0;
            width: 50px;
            height: 24px;
            // float: left;
            // margin-top: 0.06px;
            // margin-right: 0.07px;
          }

          .introContent {
            color: #989eb4;
            font-weight: 400;
            font-size: 24px;
            font-family: PingFangSC-Regular, PingFang SC;
          }
        }

        .commont {
          // margin: 18px 0 0px 0;
          margin-top: 18px;
          color: #989eb4;
          font-size: 24px;

          span {
            color: #3ad3c1;
            font-weight: bold;
            font-size: 32px;
          }
        }

        .price {
          padding-bottom: 24px;
          color: #fbbc44;
          font-weight: bold;
          font-size: 32px;
        }

        .priceBox {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding-bottom: 24px;
          color: #03081a;
          font-size: 28px;

          .line {
            width: 2px;
            height: 28px;
            margin: 0 16px;
            background-color: #ebedf5;
          }

          span {
            color: #fbbc44;
            font-weight: bold;
            font-size: 28px;
          }
        }
      }
    }
  }

  // ���б��������߶�

  :global(.am-list-view-scrollview) {
    height: calc(100vh - 108px);
    min-height: 100vh;
    margin-top: -24px;
    // height: 700px;
    overflow-x: visible !important;
    background: #f5f6fa;
  }

  .noMore {
    height: 77px;
    overflow: hidden;
    color: #ddd;
    font-weight: 400;
    font-size: 28px;
    font-family: PingFang SC;
    line-height: 33px;
    text-align: center;
    background: #f5f6fa;

    p {
      margin: 0;
      margin-top: 21px;
    }
  }
}

// .listview {
//   height: 500px;
// }
.searchBar {
  padding: 3.2vw 3.2vw 0 3.2vw;
  background: #fff;
}
