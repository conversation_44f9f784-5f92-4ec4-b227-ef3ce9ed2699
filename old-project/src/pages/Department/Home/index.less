.container {
  text-align: center;
  .top {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    // height: 100px;
    padding: 32px;
    background: #fff;

    .inputView {
      position: relative;
      width: 100%;

      input {
        box-sizing: border-box;
        width: 100%;
        // height: 36px;
        padding: 12px 80px;
        color: #333;
        font-size: 32px;
        line-height: 48px;
        background: #f5f6fa;
        border: none;
        border-radius: 36px;
        caret-color: @brand-primary;
      }

      input::-webkit-input-placeholder,
      input::-moz-placeholder,
      input::-ms-input-placeholder {
        color: #989eb4;
        font-size: 32px;
      }

      .search {
        position: absolute;
        top: 18px;
        left: 24px;
        width: 36px;
        height: 36px;
      }
    }
  }
  :global {
    .am-list-body::before,
    .am-list-body::after,
    .am-list-line::after {
      display: none !important;
    }
    .am-list-item {
      padding-left: 24px !important;
      border-bottom: 1px solid #f5f7fd !important;
      .am-list-line {
        padding-right: 24px !important;
      }
      .am-list-thumb {
        margin-right: 24px !important;
      }
    }
  }
}
