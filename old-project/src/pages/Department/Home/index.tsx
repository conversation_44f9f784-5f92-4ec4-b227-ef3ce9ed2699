/*
 * @Author: <PERSON>e
 * @Date: 2021-06-01 16:13:42
 * @LastEditTime: 2022-03-29 18:00:09
 * @LastEditors: yyl
 * @Description:
 * @FilePath: /hyt-person/src/pages/Department/Home/index.tsx
 */
import React, { FC, useEffect, useState } from 'react';
import { List } from 'antd-mobile';
import { HxIcon, HxIndicator } from '@/components';
import { connect, history, Dispatch, Loading, ConnectProps, useLocation } from 'umi';
import { IDeptModelState } from '../model';
import { IDepartmentListItem } from '../data.d';
import styles from './index.less';

const { Item } = List;

interface IProps extends ConnectProps {
  department: IDeptModelState;
  dispatch: Dispatch;
  loading?: boolean;
}
interface IEvent {
  target: {
    value: string;
  };
  preventDefault: () => void;
}

const DepartmentListPage: FC<IProps> = ({ department: { departmentList }, loading, dispatch }) => {
  const tempList = departmentList ? [...departmentList] : [];

  const [keywords, setKeywords] = useState('');

  const [list, setList] = useState<Array<IDepartmentListItem>>([]);

  const { query }: any = useLocation();

  const { appCode, channelCode, organCode } = query;

  const filterKeys = (val: any) => tempList.filter((v: any) => v.deptName.includes(val));

  const onInputChange = (e: any) => {
    const { value } = e.target;
    setKeywords(value.trim());
    setList(filterKeys(value.trim()));
  };

  const onSubmit = (e: any) => {
    e.preventDefault();
    setList(filterKeys(keywords));
  };

  const toDetail = (row: any) => {
    history.push({
      pathname: `/department/detail/${row.deptId}`,
      query: {
        appCode,
        channelCode,
        organCode,
        deptId: row.deptId,
        deptName: row.deptName,
        deptCode: row.deptCode,
        refDeptName: row.refDeptName,
        deptCodes: row.refDeptCode ? row.refDeptCode.join(',') : '',
      },
    });
  };

  useEffect(() => {
    dispatch({
      type: 'department/queryDepartmentList',
      payload: {
        organCode,
        queryType: 1,
      },
    });
  }, [organCode]);

  useEffect(() => {
    console.log('departmentList', departmentList);
    setList(departmentList);
  }, [departmentList]);

  return (
    <div className={styles.container}>
      <div className={styles.top}>
        <div className={styles.inputView}>
          <form action="" onSubmit={(e) => onSubmit(e)}>
            <HxIcon className={styles.search} iconName="appointment-search" />
            <input
              placeholder="输入科室名称搜索"
              value={keywords}
              type="search"
              onChange={(e: IEvent) => onInputChange(e)}
              // autoFocus
            />
          </form>
        </div>
      </div>
      <div className={styles.list}>
        {loading ? (
          <HxIndicator />
        ) : (
          <List>
            {list &&
              list.map((v: any) => (
                <Item
                  key={v.deptId}
                  style={{ lineHeight: '50px' }}
                  thumb={v.icon}
                  onClick={() => toDetail(v)}
                  arrow="horizontal"
                >
                  {v.deptName}
                </Item>
              ))}
          </List>
        )}
      </div>
    </div>
  );
};

export default connect(({ department, loading }: { department: IDeptModelState; loading: Loading }) => ({
  department,
  loading: loading.effects['department/queryDepartmentList'],
}))(DepartmentListPage);
