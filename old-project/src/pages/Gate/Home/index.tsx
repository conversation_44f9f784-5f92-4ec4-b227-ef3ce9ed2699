import React, { FC, useEffect } from 'react';
import { Toast } from 'antd-mobile';
import { connect, IGateModelState, Dispatch } from 'umi';
import { HxIcon, HxEmpty } from '@/components';
import styles from './index.less';
import GateWay from '../components/GateWay';

interface IProps {
  dispatch: Dispatch;
  gate: IGateModelState;
  location: {
    query: {
      data: string;
    };
  };
}

const GateHome: FC<IProps> = (props) => {
  const {
    dispatch,
    gate,
    location: { query = {} },
  } = props;
  const {}: any = query;
  const { gatedataObj = {} } = gate;
  useEffect(() => {}, []);

  return (
    <>
      <div className={styles.container}>
        <GateWay />
      </div>
    </>
  );
};

export default connect(({ gate }: { gate: IGateModelState }) => ({
  gate,
}))(GateHome);
