import React, { FC, useEffect } from 'react';
import { Toast } from 'antd-mobile';
import { connect, IGateModelState, Dispatch, history } from 'umi';
import { HxIcon, HxEmpty } from '@/components';
import styles from './index.less';
import zyimg from '@/assets/gateWay/one.png';
import phimg from '@/assets/gateWay/two.png';
import lsimg from '@/assets/gateWay/three.png';
import cximg from '@/assets/gateWay/four.png';

const gatewayObj = [
  {
    title: '住院患者登记',
    img: zyimg,
    fontColor: '#568DF2',
    url: `/gate/gatehz`,
  },
  {
    title: '陪护家属登记',
    img: phimg,
    fontColor: '#3AD3C1',
    url: '/gate/gateph',
  },
  {
    title: '临时通行登记',
    img: lsimg,
    fontColor: '#FBBC44',
    url: '/gate/gatels',
  },
  {
    title: '登记结果查询',
    img: cximg,
    fontColor: '#44B2FB',
    url: '/gate/gatesearchreslut',
  },
];
const GateWay = () => {
  useEffect(() => {}, []);
  const handelGO = (url: string) => {
    // 跳转到登记页面
    history.push(`${url}`);
  };
  return (
    <>
      {gatewayObj.map((item) => (
        <div
          onClick={() => handelGO(item.url)}
          style={{ backgroundImage: `url(${item.img})` }}
          className={styles.container}
        >
          <div className={styles.cardTitle}>{item.title}</div>
          <div style={{ color: item.fontColor }} className={styles.cardBtn}>
            {item.title === '登记结果查询' ? '前往查询' : '立即填写'}
          </div>
        </div>
      ))}
    </>
  );
};

export default connect(({ gate }: { gate: IGateModelState }) => ({
  gate,
}))(GateWay);
