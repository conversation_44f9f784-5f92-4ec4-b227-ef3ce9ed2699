import React, { FC, useEffect } from 'react';
import { Toast } from 'antd-mobile';
import { connect, IGateModelState, Dispatch, history } from 'umi';
import { HxIcon, HxEmpty } from '@/components';
import styles from './index.less';

const BottomBtn = (props) => {
  const { sub, isSub } = props;
  useEffect(() => {}, []);
  const handelSub = () => {};
  return (
    <>
      <div className={styles.container}>
        <div
          onClick={() => (isSub ? sub() : Toast.info('请完善信息', 1))}
          style={{ backgroundColor: isSub ? '#3AD3C1' : '#989EB4', color: isSub ? '#ffffff' : '#EBEDF5' }}
          className={styles.btn}
        >
          进入流行病学史调查
        </div>
      </div>
    </>
  );
};

export default connect(({ gate }: { gate: IGateModelState }) => ({
  gate,
}))(BottomBtn);
