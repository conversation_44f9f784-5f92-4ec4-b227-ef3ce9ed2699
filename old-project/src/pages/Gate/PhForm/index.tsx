import React, { FC, useEffect, useRef, useState } from 'react';
import { Form, Input, Picker, Toast } from 'antd-mobile-v5';
import { connect, IGateModelState, Dispatch, history } from 'umi';
import { HxIcon, HxEmpty } from '@/components';
import styles from './index.less';
import BottomBtn from '../components/BottomBtn';
import _ from 'lodash';
import { HxLocalStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';

const PhForm = () => {
  const [form] = Form.useForm();
  const [isSub, setIsSub] = useState<boolean>(false); // 提交按钮是否能提交
  const [value, setValue] = useState<any>(null);

  const basicColumns: any = [];

  useEffect(() => {
    HxLocalStorage.get(StorageEnum.BASE_DATA_RELATION).map((item) => {
      basicColumns.push({
        label: item.dicName,
        value: item.dicCode,
      });
    });
  }, []);

  const handelSub = () => {
    // 表单提交
    form.validateFields().then((res) => {
      console.log(res, 'res');
      const {
        kinsfolkIdCard = '',
        kinsfolkName = '',
        kinsfolkPhone = '',
        kinsfolkRelation = [],
        organPmiNo = '',
        patientName = '',
      } = res;
      history.push(
        `/epidemic/home?gate=${true}&organPmiNo=${organPmiNo}&kinsfolkName=${kinsfolkName}&patientType=${1}&kinsfolkIdCard=${kinsfolkIdCard}&kinsfolkPhone=${kinsfolkPhone}&kinsfolkRelation=${
          kinsfolkRelation[0]
        }&patientName=${patientName}`,
      );
    });
  };
  const handelFieldsChange = () => {
    // 检验表单完整性
    let obj = form.getFieldsValue();
    let arr = [];
    for (let i in obj) {
      if (!obj[i]) {
        arr.push(obj[i]);
      }
    }
    if (arr.length > 0) {
      setIsSub(false);
    } else {
      setIsSub(true);
    }
  };

  return (
    <>
      <div className={styles.container}>
        <Form
          form={form}
          hasFeedback={false}
          requiredMarkStyle="text-optional"
          onFieldsChange={() => handelFieldsChange()}
          layout="horizontal"
        >
          <Form.Item rules={[{ required: true, message: '患者姓名不能为空!' }]} name="patientName" label="患者姓名">
            <Input style={{ '--text-align': 'right' }} placeholder="请输入" />
          </Form.Item>
          <Form.Item rules={[{ required: true, message: '登记号不能为空!' }]} name="organPmiNo" label="登记号">
            <Input style={{ '--text-align': 'right' }} placeholder="请输入" />
          </Form.Item>

          <Form.Header>
            <span className={styles.zwspan}></span>
          </Form.Header>

          <Form.Item rules={[{ required: true, message: '家属姓名不能为空!' }]} name="kinsfolkName" label="家属姓名">
            <Input style={{ '--text-align': 'right' }} placeholder="请输入" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: '与患者关系不能为空!' }]}
            name="kinsfolkRelation"
            label="与患者关系"
            onClick={async () => {
              HxLocalStorage.get(StorageEnum.BASE_DATA_RELATION).map((item) => {
                basicColumns.push({
                  label: item.dicName,
                  value: item.dicCode,
                });
              });
              const value = await Picker.prompt({
                columns: [basicColumns],
                onConfirm: (val, extend) => {
                  console.log(val, extend, 'val,extend');
                  const { items } = extend;
                  form.setFieldsValue({ kinsfolkRelation: items[0]?.label });
                },
              });
            }}
          >
            <Input readOnly style={{ '--text-align': 'right' }} placeholder="请选择" />
          </Form.Item>
          <Form.Item rules={[{ required: true, message: '联系电话不能为空!' }]} name="kinsfolkPhone" label="联系电话">
            <Input style={{ '--text-align': 'right' }} placeholder="请输入" />
          </Form.Item>
          <Form.Item rules={[{ required: true, message: '身份证号不能为空!' }]} name="kinsfolkIdCard" label="身份证号">
            <Input style={{ '--text-align': 'right' }} placeholder="请输入" />
          </Form.Item>
        </Form>
        <BottomBtn isSub={isSub} sub={handelSub} />
      </div>
    </>
  );
};

export default connect(({ gate }: { gate: IGateModelState }) => ({
  gate,
}))(PhForm);
