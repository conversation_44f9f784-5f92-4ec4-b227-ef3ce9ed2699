import React, { FC, useEffect, useRef, useState } from 'react';
import { Form, Input, Picker, Toast } from 'antd-mobile-v5';
import { connect, IGateModelState, Dispatch, history } from 'umi';
import { HxIcon, HxEmpty } from '@/components';
import styles from './index.less';
import BottomBtn from '../components/BottomBtn';
import _ from 'lodash';
import checkSuccess from '@/assets/gateWay/checkSuccess.png';
import checkFail from '@/assets/gateWay/checkFail.png';

interface IProps {
  dispatch: Dispatch;
  gate: IGateModelState;
  location: {
    query: {
      data: string;
    };
  };
}

const GateCheck: FC<IProps> = (props) => {
  const {
    dispatch,
    gate,
    location: { query = {} },
  } = props;
  const { name = '', idcard = '', result = '', organPmiNo = '', bookingId = '' }: any = query;
  const handleBack = () => {
    if (organPmiNo) {
      // 线下通道
      history.push('/gate/home');
    } else if (bookingId) {
      // 线上通道
      history.push(`/gate/gateresult?bookingId=${bookingId}`);
    } else {
      // 线下临时通道
      history.push('/gate/home');
    }
  };
  useEffect(() => {}, []);

  return (
    <>
      <div className={styles.container}>
        <div className={styles.titlebox}>
          <div className={styles.titlename}>验证人员：</div>
          <div className={styles.titleinfo}>
            <span>{name}</span>
            <span>{idcard}</span>
          </div>
        </div>
        <div className={styles.checkresult}>
          <div className={styles.titlename}>验证结果：</div>
          <div className={styles.checkresultbox}>
            <img src={result.toString() === '1' ? checkSuccess : checkFail} alt="" className={styles.checkresultimg} />
            <div
              style={{ color: result.toString() === '1' ? '#3ad3c1' : '#FC4553' }}
              className={styles.checkresulttips}
            >
              {result.toString() === '1' ? '提交成功' : '提交失败'}
            </div>
            <div onClick={() => handleBack()} className={styles.checkresultback}>
              返回
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default connect(({ gate }: { gate: IGateModelState }) => ({
  gate,
}))(GateCheck);
