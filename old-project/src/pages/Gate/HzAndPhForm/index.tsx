import React, { FC, useEffect, useRef, useState } from 'react';
import { Form, Input, Picker, Toast } from 'antd-mobile-v5';
import { connect, IGateModelState, Dispatch, history } from 'umi';
import { HxIcon, HxEmpty } from '@/components';
import styles from './index.less';
import BottomBtn from '../components/BottomBtn';
import _ from 'lodash';
import { HxLocalStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import { IdCardDiscern } from '@/utils/common';

interface IProps {
  dispatch: Dispatch;
  gate: IGateModelState;
  location: {
    query: {
      data: string;
    };
  };
}
let sex: any = '';
let age: any = '';
const HzAndPhForm: FC<IProps> = (props) => {
  const {
    dispatch,
    gate,
    location: { query = {} },
  } = props;
  const { bookingId = '', organPmiNo = '', hzidcard = '', hzname = '', isSMHZ = false }: any = query;

  const [form] = Form.useForm();
  const [isSub, setIsSub] = useState<boolean>(false); // 提交按钮是否能提交
  const [visible, setVisible] = useState(false);
  const [valuess, setValuess] = useState<any>(null);

  var basicColumns: any = [];

  const handelSub = () => {
    // 表单提交
    form.validateFields().then((res) => {
      console.log(res, 'res');
      const { kinsfolkIdCard = '', kinsfolkName = '', kinsfolkPhone = '', kinsfolkRelation = [] } = res;
      if (isSMHZ) {
        // 线下扫码患者通道过来
        history.push(
          `/epidemic/home?gate=${true}&organPmiNo=${organPmiNo}&kinsfolkName=${kinsfolkName}&patientType=${1}&kinsfolkIdCard=${kinsfolkIdCard}&kinsfolkPhone=${kinsfolkPhone}&kinsfolkRelation=${
            kinsfolkRelation[0]
          }`,
        );
      } else {
        // 线上患者通道过来
        history.push(
          `/epidemic/home?gate=${true}&bookingId=${bookingId}&kinsfolkName=${kinsfolkName}&patientType=${1}&kinsfolkIdCard=${kinsfolkIdCard}&kinsfolkPhone=${kinsfolkPhone}&kinsfolkRelation=${
            kinsfolkRelation[0]
          }`,
        );
      }
    });
  };
  useEffect(() => {}, []);

  const handelFieldsChange = () => {
    // 检验表单完整性
    let obj = form.getFieldsValue();
    console.log(obj, 'obj');
    let arr = [];
    for (let i in obj) {
      if (!obj[i]) {
        arr.push(obj[i]);
      }
    }
    if (arr.length > 0) {
      setIsSub(false);
    } else {
      setIsSub(true);
    }
  };

  return (
    <>
      <div className={styles.container}>
        <div className={styles.cardbox}>
          <div className={styles.cardinfo}>
            <span>{hzname}</span>
            <span>{IdCardDiscern(hzidcard, 3)}岁</span>
            <span>{IdCardDiscern(hzidcard, 2)}</span>
          </div>
          <div className={styles.idcard}>身份证号：{hzidcard}</div>
        </div>
        <Form
          form={form}
          hasFeedback={false}
          requiredMarkStyle="text-optional"
          onFieldsChange={() => handelFieldsChange()}
          layout="horizontal"
        >
          <Form.Item rules={[{ required: true, message: '家属姓名不能为空!' }]} name="kinsfolkName" label="家属姓名">
            <Input style={{ '--text-align': 'right' }} placeholder="请输入" />
          </Form.Item>
          <Form.Item
            rules={[{ required: true, message: '与患者关系不能为空!' }]}
            name="kinsfolkRelation"
            label="与患者关系"
            onClick={async () => {
              HxLocalStorage.get(StorageEnum.BASE_DATA_RELATION).map((item) => {
                basicColumns.push({
                  label: item.dicName,
                  value: item.dicCode,
                });
              });
              const value = await Picker.prompt({
                columns: [basicColumns],
                onConfirm: (val, extend) => {
                  console.log(val, extend, 'val,extend');
                  const { items } = extend;
                  form.setFieldsValue({ kinsfolkRelation: items[0]?.label });
                },
              });
            }}
          >
            <Input readOnly style={{ '--text-align': 'right' }} placeholder="请选择" />
          </Form.Item>
          <Form.Item rules={[{ required: true, message: '联系电话不能为空!' }]} name="kinsfolkPhone" label="联系电话">
            <Input style={{ '--text-align': 'right' }} placeholder="请输入" />
          </Form.Item>
          <Form.Item rules={[{ required: true, message: '身份证号不能为空!' }]} name="kinsfolkIdCard" label="身份证号">
            <Input style={{ '--text-align': 'right' }} placeholder="请输入" />
          </Form.Item>
        </Form>
        <BottomBtn isSub={isSub} sub={handelSub} />
      </div>
    </>
  );
};

export default connect(({ gate }: { gate: IGateModelState }) => ({
  gate,
}))(HzAndPhForm);
