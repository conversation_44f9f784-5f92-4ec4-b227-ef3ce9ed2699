import React, { FC, useEffect, useRef, useState } from 'react';
import { Form, Input, Picker, Toast } from 'antd-mobile-v5';
import { connect, IGateModelState, Dispatch, history } from 'umi';
import { HxIcon, HxEmpty } from '@/components';
import styles from './index.less';
import BottomBtn from '../components/BottomBtn';
import _ from 'lodash';
import hzTitle from '@/assets/gateWay/hzTitle.png';
import phTitle from '@/assets/gateWay/phTitle.png';
import rightIcon from '@/assets/gateWay/rightIcon.png';

interface IProps {
  dispatch: Dispatch;
  gate: IGateModelState;
  location: {
    query: {
      data: string;
    };
  };
}

const GateResult: FC<IProps> = (props) => {
  const {
    dispatch,
    gate,
    location: { query = {} },
  } = props;
  const { bookingId = '' }: any = query;
  const {
    resultList: {
      kinsfolkIDCard = '',
      kinsfolkName = '',
      kinsfolkResultCode = 0,
      organPmiNo = '111',
      patientIDCard = '',
      patientName = '',
      patientResultCode = 0,
    },
    hzInfo: { idCard = '', patientAge = '', patientName: hzpatientName = '', patientSex = '' },
  } = gate;

  useEffect(() => {
    getList();
  }, []);

  const getList = () => {
    dispatch({
      type: 'gate/getQuestionResultList',
      payload: {
        bookingId,
      },
      callback() {
        dispatch({
          type: 'gate/queryQuestionPatientInfo',
          payload: {
            bookingId,
            hospitalCode: 'HID0101',
          },
        });
      },
    });
  };

  const handelGo = (val) => {
    // 跳转流行病史填写
    if (val === 1) {
      // 患者通道
      if (patientResultCode === 1) {
        history.push(
          `/gate/gatecheck?name=${patientName}&idcard=${patientIDCard}&result=${patientResultCode}&bookingId=${bookingId}`,
        );
      } else {
        history.push(
          `/epidemic/home?gate=${true}&bookingId=${bookingId}&patientName=&kinsfolkName=${hzpatientName}&patientType=${0}&kinsfolkIdCard=${idCard}&kinsfolkPhone=`,
        );
      }
    } else if (val === 2) {
      // 陪护通道
      if (kinsfolkResultCode === 1) {
        history.push(
          `/gate/gatecheck?name=${kinsfolkName}&idcard=${kinsfolkIDCard}&result=${kinsfolkResultCode}&bookingId=${bookingId}`,
        );
      } else {
        // history.push(`/epidemic/home?gate=${true}&bookingId=${bookingId}&patientName=&kinsfolkName=${hzpatientName}&patientType=${1}&kinsfolkIdCard=${idCard}&kinsfolkPhone=`);
        history.push(
          `/gate/gatehzandph?bookingId=${bookingId}&patientName=&hzname=${hzpatientName}&patientType=${0}&hzidcard=${idCard}`,
        );
      }
    }
  };

  return (
    <>
      <div className={styles.container}>
        <div onClick={() => handelGo(1)} className={styles.cardbox}>
          <img src={hzTitle} alt="" className={styles.titleimg} />
          <div className={styles.cardinfo}>
            <div className={styles.cardinfoLeft}>
              <div className={styles.cardinfoTitle}>住院患者流行病学史调查</div>
              <div className={styles.gowhere}>
                <span>点击进入</span>
                <img src={rightIcon} alt="" />
              </div>
            </div>
            <div className={`${styles.cardinfoRight} ${patientResultCode === 1 ? styles.yzsuccess : styles.yzno}`}>
              {patientResultCode === 1 ? `提交成功` : '未验证'}
            </div>
          </div>
        </div>
        <div onClick={() => handelGo(2)} className={styles.cardbox}>
          <img src={phTitle} alt="" className={styles.titleimg} />
          <div className={styles.cardinfo}>
            <div className={styles.cardinfoLeft}>
              <div className={styles.cardinfoTitle}>陪护家属流行病学史调查</div>
              <div className={styles.gowhere}>
                <span>点击进入</span>
                <img src={rightIcon} alt="" />
              </div>
            </div>
            <div className={`${styles.cardinfoRight} ${kinsfolkResultCode === 1 ? styles.yzsuccess : styles.yzno}`}>
              {kinsfolkResultCode === 1 ? '提交成功' : '未验证'}
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default connect(({ gate }: { gate: IGateModelState }) => ({
  gate,
}))(GateResult);
