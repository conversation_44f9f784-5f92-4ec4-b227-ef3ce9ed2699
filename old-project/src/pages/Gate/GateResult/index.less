.container {
  width: 100%;
  background-color: #f5f6fa;
  padding: 32px 24px 0 24px;
  box-sizing: border-box;
  .cardbox {
    width: 100%;
    margin-bottom: 24px;
    .titleimg {
      width: 100%;
      height: 52px;
    }
    .cardinfo {
      width: 100%;
      padding: 30px 40px 30px 32px;
      box-sizing: border-box;
      background-color: #ffffff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 0px 0px 16px 16px;
      .cardinfoLeft {
        .cardinfoTitle {
          font-size: 36px;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #03081a;
        }
        .gowhere {
          display: flex;
          align-items: center;
          margin-top: 16px;
          span {
            font-size: 32px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #989eb4;
            margin-right: 8px;
          }
          img {
            width: 24px;
            height: 24px;
          }
        }
      }
      .cardinfoRight {
        width: 140px;
        height: 140px;
        border-radius: 50%;
        line-height: 140px;
        text-align: center;
        font-size: 28px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
      }
      .yzno {
        background-color: #f5f6fa;
        color: #989eb4;
      }
      .yzsuccess {
        background-color: #def6f3;
        color: #25c8b5;
      }
      .yzfail {
        background-color: #ffe7e9;
        color: #eb3f4c;
      }
    }
  }
}
