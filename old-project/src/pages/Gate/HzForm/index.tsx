import React, { FC, useEffect, useRef, useState } from 'react';
import { Form, Input } from 'antd-mobile-v5';
import { connect, IGateModelState, Dispatch, history } from 'umi';
import { HxIcon, HxEmpty } from '@/components';
import styles from './index.less';
import BottomBtn from '../components/BottomBtn';
import _ from 'lodash';

const HzForm = () => {
  useEffect(() => {}, []);
  const [form] = Form.useForm();
  const [isSub, setIsSub] = useState<boolean>(false); // 提交按钮是否能提交
  const handelSub = () => {
    // 表单提交
    form.validateFields().then((res) => {
      console.log(res, 'res');
      const { kinsfolkIdCard = '', kinsfolkName = '', organPmiNo = '' } = res;
      history.push(
        `/epidemic/home?isSMHZ=${true}&gate=${true}&bookingId=&patientName=&kinsfolkName=${kinsfolkName}&patientType=${0}&kinsfolkIdCard=${kinsfolkIdCard}&organPmiNo=${organPmiNo}&hzname=${kinsfolkName}&hzidcard=${kinsfolkIdCard}`,
      );
    });
  };
  const handelFieldsChange = () => {
    // 检验表单完整性
    let obj = form.getFieldsValue();
    let arr = [];
    for (let i in obj) {
      if (!obj[i]) {
        arr.push(obj[i]);
      }
    }
    if (arr.length > 0) {
      setIsSub(false);
    } else {
      setIsSub(true);
    }
  };
  return (
    <>
      <div className={styles.container}>
        <Form
          form={form}
          hasFeedback={false}
          requiredMarkStyle="text-optional"
          onFieldsChange={() => handelFieldsChange()}
          layout="horizontal"
        >
          <Form.Item rules={[{ required: true, message: '患者姓名不能为空!' }]} name="kinsfolkName" label="患者姓名">
            <Input style={{ '--text-align': 'right' }} placeholder="请输入" />
          </Form.Item>
          <Form.Item rules={[{ required: true, message: '身份证号不能为空!' }]} name="kinsfolkIdCard" label="身份证号">
            <Input style={{ '--text-align': 'right' }} placeholder="请输入" />
          </Form.Item>
          <Form.Item rules={[{ required: true, message: '登记号不能为空!' }]} name="organPmiNo" label="登记号">
            <Input style={{ '--text-align': 'right' }} placeholder="请输入" />
          </Form.Item>
        </Form>
        <BottomBtn isSub={isSub} sub={handelSub} />
      </div>
    </>
  );
};

export default connect(({ gate }: { gate: IGateModelState }) => ({
  gate,
}))(HzForm);
