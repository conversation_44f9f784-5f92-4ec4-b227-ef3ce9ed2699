import React, { FC, useEffect, useState } from 'react';
import { Toast } from 'antd-mobile';
import { connect, IGateModelState, Dispatch } from 'umi';
import styles from './index.less';
import clear from '@/assets/gateWay/clear.png';
import checkFailnew from '@/assets/gateWay/checkFailnew.png';
import checkSuccessnew from '@/assets/gateWay/checkSuccessnew.png';

interface IProps {
  dispatch: Dispatch;
  gate: IGateModelState;
  location: {
    query: {
      data: string;
    };
  };
}

const gatesearchreslut: FC<IProps> = (props) => {
  const {
    dispatch,
    gate,
    location: { query = {} },
  } = props;
  const {}: any = query;
  const { resultInfo = {} } = gate;
  const {
    idCard = '',
    papmiNo = '',
    patientName = '',
    epidemicQuestionDateTime = '',
    relationIdCard = '',
    relationName = '',
  } = resultInfo;

  const [inputVal, setInputVal] = useState('');
  const [haveClear, setHaveClear] = useState(false);
  const [flag, setFlag] = useState(false);
  const [isPass, setIspass] = useState(false);

  useEffect(() => {}, []);

  /* 查询 */
  const handelSearch = () => {
    setFlag(true);
    if (inputVal) {
      console.log('查询', inputVal);
      dispatch({
        type: 'gate/queryQuestionRecord',
        payload: {
          papmiNo: inputVal,
        },
        callback(res) {
          res ? setIspass(true) : setIspass(false);
        },
      });
    }
  };
  return (
    <>
      <div className={styles.container}>
        <div className={styles.title}>
          <div className={styles.titltename}>患者登记号</div>
          <div className={styles.titlesearch}>
            <div className={styles.searchbox}>
              <input
                placeholder="请输入"
                type="search"
                value={inputVal}
                onChange={(e) => {
                  setInputVal(e.target.value);
                }}
                onFocus={() => setHaveClear(true)}
                onBlur={() =>
                  setTimeout(() => {
                    setHaveClear(false);
                  }, 100)
                }
              />
              <div className={styles.searchBtn}>
                <img
                  src={clear}
                  alt=""
                  style={{ display: haveClear ? 'initial' : 'none' }}
                  onClick={() => setInputVal('')}
                />
                <span onClick={() => handelSearch()}>查询</span>
              </div>
            </div>
          </div>
        </div>

        <div style={{ display: flag ? '' : 'none' }} className={styles.result}>
          <div className={styles.resulttitle}>查询结果</div>
          <div style={{ display: isPass ? '' : 'none' }} className={styles.resultinfo}>
            <div className={styles.infoitem}>
              <span>患者姓名：</span>
              <span>{patientName}</span>
            </div>
            <div className={styles.infoitem}>
              <span>陪护姓名：</span>
              <span>{relationName}</span>
            </div>
            <div className={styles.infoitem}>
              <span>陪护身份证号：</span>
              <span>{relationIdCard}</span>
            </div>
          </div>
          <div className={styles.checkstatus}>
            <img src={isPass ? checkSuccessnew : checkFailnew} alt="" />
            <div style={{ color: isPass ? '#3AD3C1' : '#FE8F3C' }}>{isPass ? '登记成功' : '无登记记录'}</div>
          </div>
        </div>
      </div>
    </>
  );
};

export default connect(({ gate }: { gate: IGateModelState }) => ({
  gate,
}))(gatesearchreslut);
