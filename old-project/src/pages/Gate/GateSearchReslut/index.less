.container {
  padding: 24px;
  box-sizing: border-box;
  background-color: #f5f6fa;
  .title {
    width: 100%;
    height: 232px;
    border-radius: 16px;
    background-color: #ffffff;
    .titltename {
      width: 100%;
      height: 80px;
      border-radius: 16px 16px 0px 0px;
      background-color: #63affd;
      padding: 16px 24px;
      box-sizing: border-box;
      font-size: 30px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #ffffff;
    }
    .titlesearch {
      padding: 32px 24px;
      box-sizing: border-box;
      .searchbox {
        background-color: #f5f6fa;
        border-radius: 16px;
        width: 100%;
        height: 88px;
        padding: 20px 40px 20px 24px;
        box-sizing: border-box;
        display: flex;
        justify-content: space-between;
        align-items: center;
        input {
          margin: 0;
          padding: 0;
          border: none;
          background: #f5f6fa;
          height: 48px;
          width: 75%;
          font-size: 32px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
        }
        .searchBtn {
          display: flex;
          align-items: center;
          justify-content: space-between;
          img {
            width: 32px;
            height: 32px;
            margin-right: 40px;
          }
          span {
            font-size: 32px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #63affd;
          }
        }
      }
    }
  }

  .result {
    width: 100%;
    background-color: #ffffff;
    border-radius: 16px;
    padding: 32px 24px 80px 24px;
    box-sizing: border-box;
    margin-top: 24px;
    .resulttitle {
      font-size: 30px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #03081a;
    }
    .resultinfo {
      width: 100%;
      height: 236px;
      border-radius: 16px;
      background-color: #f5f6fa;
      padding: 32px;
      box-sizing: border-box;
      margin-top: 32px;
      .infoitem {
        margin-bottom: 10px;
        span:nth-of-type(1) {
          font-size: 32px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #989eb4;
        }
        span:nth-of-type(2) {
          font-size: 32px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #03081a;
        }
      }
    }
  }

  .checkstatus {
    margin-top: 80px;
    display: flex;
    align-items: center;
    flex-direction: column;
    img {
      width: 200px;
      height: 200px;
    }
    div {
      font-size: 36px;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      margin-top: 24px;
    }
  }
}
