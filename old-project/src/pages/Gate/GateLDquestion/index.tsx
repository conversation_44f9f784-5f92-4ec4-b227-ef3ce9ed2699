import React, { FC, useEffect, useState } from 'react';
import { Dispatch, ConnectProps, useLocation, history, connect } from 'umi';
import { HxIcon } from '@/components';
import { Toast, Modal } from 'antd-mobile';
import { TextArea, Dialog } from 'antd-mobile-v5';
import AppScheme from '@/utils/AppScheme';
import { ModuleEnum } from '@/utils/enum';
import { ExclamationCircleFilled } from '@ant-design/icons';
import queryString from 'query-string';
import { getChannelCode, getOpenId, getToken } from '@/utils/parameter';
import styles from './index.less';

interface IProps extends ConnectProps {
  dispatch: Dispatch;
}
var quesNum: any = 0;
const Detail: FC<IProps> = ({ dispatch }) => {
  // submitCode 后台推送需要 医嘱：oeoitem   处方：drug
  const {
    query: {
      organPmiNo = '',
      bookingId = '',
      patientName = '',
      kinsfolkName = '',
      patientType = '',
      kinsfolkIdCard = '',
      kinsfolkPhone = '',
      hzname = '',
      hzidcard = '',
      isSMHZ = false,
    } = {},
  }: any = useLocation();

  const [quesList, setQuesList] = useState<Array<any>>([]);
  const [ansList, setAnsList] = useState<object>({});

  const fetchData = () => {
    dispatch({
      type: 'gate/getcisepidemicquestion',
      payload: {
        InputMsg: 'CIS',
      },
      callback: (data: any = {}) => {
        quesNum = 0;
        console.log(data, 'datadatadata');
        setQuesList(data.data);
        data.data.map((item) => {
          quesNum += item.titleSubData.length;
        });
      },
    });
  };

  useEffect(() => {
    fetchData();
  }, []);

  const onChange = (value: any, QItemId: string) => {
    console.log(ansList, 'ansList,', value, 'value,', QItemId, 'QItemId');
    const newAnsList = { ...ansList };
    newAnsList[QItemId] = { QItemId, QItemDesc: value };
    setAnsList(newAnsList);
  };

  const goWhere = (organPmiNo, bookingId) => {
    // 跳转到结果页
    // history.go(-3);
    console.log(history, 'history');
    history.push(
      `/gate/gatecheck?&name=${kinsfolkName || hzname}&idcard=${
        kinsfolkIdCard || hzidcard
      }&result=${1}&organPmiNo=${organPmiNo}&bookingId=${bookingId}`,
    );
  };

  const goSubmit = (arr) => {
    console.log(arr, 'arr');
    dispatch({
      type: 'gate/submitQuestion',
      payload: {
        organPmiNo,
        bookingId,
        patientName,
        kinsfolkName,
        patientType,
        kinsfolkIdCard,
        kinsfolkPhone,
        questionData: arr,
        organCode: 'HID0101',
      },
      callback: (data: any = {}) => {
        const { code = '0', msg = '' } = data;
        if (code === '1') {
          if (isSMHZ) {
            // 线下扫码患者通道过来
            Dialog.confirm({
              content: '请问是否有家属陪护？',
              onConfirm: () => {
                history.push(
                  `/gate/gatehzandph?hzname=${hzname}&hzidcard=${hzidcard}&organPmiNo=${organPmiNo}&isSMHZ=${isSMHZ}`,
                );
              },
              onCancel: () => {
                Toast.info(msg, 1, () => {
                  goWhere(organPmiNo, bookingId);
                });
              },
            });
          } else {
            // 线上通道过来
            Toast.info(msg, 1);
            goWhere(organPmiNo, bookingId);
          }
        } else {
          Toast.fail(msg, 1);
        }
      },
    });
  };

  const onSubmit = () => {
    let arr: any = [];
    for (let i in ansList) {
      arr.push(ansList[i]);
    }
    console.log(arr, quesNum, 'arr.length<quesNum');
    if (arr.length < quesNum - 1) {
      Toast.info('请填写完整', 1);
      return;
    } else {
      goSubmit(arr);
    }
  };

  const RadioItem = ({ checked = true, text = '', onClick = () => {} }) => {
    return (
      <div className={styles.radio} onClick={onClick}>
        <HxIcon className={styles.image} iconName={checked ? 'checkbox-selected' : 'checkbox-normal'} />
        <span className={styles.text}>{text}</span>
      </div>
    );
  };

  return (
    <div className={styles.container}>
      {quesList.map((item: any, index: number) => {
        const { TitleDR = '', TitleName = '', titleSubData = [] } = item;
        let ansData = [];
        let str1 = '';
        titleSubData.map((i: any = {}) => {
          i.questCityObj.map((objitem) => {
            str1 += objitem.CityName + (objitem.CityRemark || '') + '、';
          });
          ansData = i.qItemDesc.map((ii) => {
            return {
              label: ii?.OptionDesc,
              value: ii?.OptionNo,
            };
          });
        });
        return (
          <div className={styles.item} key={index.toString()}>
            <div className={styles.titleBG}>{TitleName}</div>
            {titleSubData.map((kk: any, ii: any) => {
              // const
              if (TitleName === '备注') {
                return (
                  <TextArea
                    className={styles.textarea}
                    placeholder="请输入备注"
                    rows={6}
                    onChange={(val) => {
                      const newAnsList = { ...ansList };
                      newAnsList[kk.QItemId] = { QItemId: kk.QItemId, QItemDesc: val };
                      setAnsList(newAnsList);
                    }}
                  />
                );
              } else {
                return (
                  <div className={styles.title}>
                    {/* 第{ii + 1}题： */}
                    {kk.QuestName.replace('[Citys]', str1)}
                    {kk.qItemDesc.map((k, i) => {
                      return (
                        <RadioItem
                          key={i.toString()}
                          checked={ansList[kk.QItemId]?.QItemDesc === k.OptionDesc}
                          text={k.OptionDesc}
                          onClick={() => onChange(k.OptionDesc, kk.QItemId)}
                        />
                      );
                    })}
                  </div>
                );
              }
            })}
          </div>
        );
      })}
      {quesList?.length === 0 ? (
        <div style={{ textAlign: 'center' }}>暂无问卷</div>
      ) : (
        <div className={styles.btnBox}>
          <div className={styles.btn} onClick={onSubmit}>
            提交
          </div>
        </div>
      )}
    </div>
  );
};

export default connect()(Detail);
