import request from '@/utils/request';

const API = APP_ENV === 'dev' ? API_HX : APP_ENV === 'staging' ? API_BASE : API_HXYY;
/**
 * 请求第三方token
 * @param params
 */
export const getauthtoken = async (params: object): Promise<object> =>
  request(`${API_BASE}/cloud/usercenter/account/default/token/test`, {
    method: 'GET',
    params,
  });
/**
 *  流调结果列表
 * @param data
 */
export const getQuestionResultList = async (data: object): Promise<object> =>
  request(`${API}/cloud/hosplatcustomer/call/medicalaffairs/getQuestionResultList`, {
    method: 'POST',
    data,
  });
/**
 *  流调问题列表
 * @param data
 */
export const getcisepidemicquestion = async (data: object): Promise<object> =>
  request(`${API}/cloud/hosplatcustomer/electronicvisit/getcisepidemicquestion`, {
    method: 'POST',
    data,
  });
/**
 *  提交流调问题
 * @param data
 */
export const submitQuestion = async (data: object): Promise<object> =>
  request(`${API}/cloud/hosplatcustomer/call/medicalaffairs/submitQuestion`, {
    method: 'POST',
    data: {
      showOriginData: true,
      ...data,
    },
  });
/**
 *  患者信息
 * @param data
 */
export const queryQuestionPatientInfo = async (data: object): Promise<object> =>
  request(`${API}/cloud/hosplatcustomer/call/medicalaffairs/queryQuestionPatientInfo`, {
    method: 'POST',
    data,
  });

/**
 * 查询医保服务授权
 * @param data
 */
export const updatepatientinfo = async (data: object): Promise<object> =>
  request(`${API_DOCTOR}/netInquiry/insurance/updatepatientinfo`, {
    method: 'POST',
    data: {
      skipError: true,
      showOriginData: true,
      ...data,
    },
  });
/**
 * 查询闸机填报结果
 * @param data
 */
export const queryQuestionRecord = async (data: object): Promise<object> =>
  request(`${API_BASE}/cloud/hosplatcustomer/call/medicalaffairs/queryQuestionRecord`, {
    method: 'POST',
    data,
  });
