import React, { FC, useEffect, useRef, useState } from 'react';
import { Form, Input, Picker, Toast } from 'antd-mobile-v5';
import { connect, IGateModelState, Dispatch, history } from 'umi';
import { HxIcon, HxEmpty } from '@/components';
import styles from './index.less';
import BottomBtn from '../components/BottomBtn';
import _ from 'lodash';

const PhForm = () => {
  useEffect(() => {}, []);

  const [form] = Form.useForm();
  const [isSub, setIsSub] = useState<boolean>(false); // 提交按钮是否能提交
  const [value, setValue] = useState<any>(null);

  const basicColumns = [
    [
      { label: '周一', value: 'Mon' },
      { label: '周二', value: 'thues' },
    ],
  ];

  const handelSub = () => {
    // 表单提交
    form.validateFields().then((res) => {
      console.log(res, 'res');
      const { kinsfolkIdCard = '', kinsfolkName = '', kinsfolkPhone = '' } = res;
      history.push(
        `/epidemic/home?gate=${true}&kinsfolkName=${kinsfolkName}&patientType=${2}&kinsfolkIdCard=${kinsfolkIdCard}&kinsfolkPhone=${kinsfolkPhone}`,
      );
    });
  };
  const handelFieldsChange = () => {
    // 检验表单完整性
    let obj = form.getFieldsValue();
    let arr = [];
    for (let i in obj) {
      if (!obj[i]) {
        arr.push(obj[i]);
      }
    }
    if (arr.length > 0) {
      setIsSub(false);
    } else {
      setIsSub(true);
    }
  };

  return (
    <>
      <div className={styles.container}>
        <Form
          form={form}
          hasFeedback={false}
          requiredMarkStyle="text-optional"
          onFieldsChange={() => handelFieldsChange()}
          layout="horizontal"
        >
          <Form.Item
            rules={[{ required: true, message: '探视人姓名不能为空!' }]}
            name="kinsfolkName"
            label="探视人姓名"
          >
            <Input style={{ '--text-align': 'right' }} placeholder="请输入" />
          </Form.Item>
          <Form.Item rules={[{ required: true, message: '身份证号不能为空!' }]} name="kinsfolkIdCard" label="身份证号">
            <Input style={{ '--text-align': 'right' }} placeholder="请输入" />
          </Form.Item>
          <Form.Item rules={[{ required: true, message: '联系电话不能为空!' }]} name="kinsfolkPhone" label="联系电话">
            <Input style={{ '--text-align': 'right' }} placeholder="请输入" />
          </Form.Item>
        </Form>
        <BottomBtn isSub={isSub} sub={handelSub} />
      </div>
    </>
  );
};

export default connect(({ gate }: { gate: IGateModelState }) => ({
  gate,
}))(PhForm);
