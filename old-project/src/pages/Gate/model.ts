import { Reducer } from 'redux';
import { Effect, Subscription } from 'umi';
import { createAction } from '@/utils/common';
import {
  getauthtoken,
  getQuestionResultList,
  getcisepidemicquestion,
  submitQuestion,
  queryQuestionPatientInfo,
  queryQuestionRecord,
} from './service';
import { IGateDataObjItemType } from './data.d';
import { HxLocalStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';

export interface IGateModelState {
  gatedataObj: IGateDataObjItemType;
  authToken: string;
  resultList: {
    kinsfolkIDCard: string;
    kinsfolkName: string;
    kinsfolkResultCode: number;
    organPmiNo: string;
    patientIDCard: string;
    patientName: string;
    patientResultCode: number;
  };
  hzInfo: {
    idCard: string;
    patientAge: string;
    patientName: string;
    patientSex: string;
  };
  resultInfo: any;
}

export interface IGateModel {
  namespace: 'gate';
  state: IGateModelState;
  effects: {
    getauthtoken: Effect;
    getQuestionResultList: Effect;
    getcisepidemicquestion: Effect;
    submitQuestion: Effect;
    queryQuestionPatientInfo: Effect;
    queryQuestionRecord: Effect;
  };
  reducers: {
    updateState: Reducer<IGateModelState>;
  };
  subscriptions: {
    setup: Subscription;
  };
}

const GateModel: IGateModel = {
  namespace: 'gate',
  state: {
    gatedataObj: {},
    authToken: '',
    resultList: {},
    hzInfo: {},
    resultInfo: {},
  },
  effects: {
    *getauthtoken({ payload, callback }, { put }) {
      const res = yield getauthtoken({ ...payload }) || {};
      HxLocalStorage.set(StorageEnum.TOKEN, res);
      yield put(
        createAction('updateState')({
          authToken: res,
        }),
      );
      callback && callback(res);
    },
    *getQuestionResultList({ payload, callback }, { put }) {
      const res = yield getQuestionResultList({ ...payload }) || {};
      yield put(
        createAction('updateState')({
          resultList: res,
        }),
      );
      callback && callback(res);
    },
    *getcisepidemicquestion({ payload, callback }, { put }) {
      const res = yield getcisepidemicquestion({ ...payload }) || {};
      callback && callback(res);
    },
    *queryQuestionRecord({ payload, callback }, { put }) {
      const res = yield queryQuestionRecord({ ...payload }) || {};
      yield put(
        createAction('updateState')({
          resultInfo: res,
        }),
      );
      callback && callback(res);
    },
    *queryQuestionPatientInfo({ payload, callback }, { put }) {
      const res = yield queryQuestionPatientInfo({ ...payload }) || {};
      yield put(
        createAction('updateState')({
          hzInfo: res,
        }),
      );
      callback && callback(res);
    },
    *submitQuestion({ payload, callback }, { put }) {
      const res = yield submitQuestion({ ...payload }) || {};
      callback && callback(res);
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname }) => {
        if (pathname.toLowerCase().includes('gate')) {
          dispatch({
            type: 'getauthtoken',
            payload: {},
          });
        }
      });
    },
  },
};
export default GateModel;
