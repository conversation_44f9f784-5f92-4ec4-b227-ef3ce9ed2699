import request from '@/utils/request';

/**
 * 授权获取code
 * @param data
 */
export const authorize = async (data: object): Promise<object> =>
  request('/cloud/usercenter/auth/authorize', {
    method: 'POST',
    data,
  });

/**
 * 判断用户授权是否超期
 * @param data
 */
export const isoutauthtime = async (data: object): Promise<object> =>
  request('/cloud/hosplatcustomer/usercenter/auth/isoutauthtime', {
    method: 'POST',
    data: { ...data, showOriginData: true, skipError: true },
  });
