import { Reducer } from 'redux';
import { Effect } from 'umi';
import * as authApi from './service';

export interface IAuthorizationModelState {}

export interface IAuthorizationModel {
  namespace: 'authorization';
  state: IAuthorizationModelState;
  effects: {
    authorize: Effect;
    isoutauthtime: Effect;
  };
  reducers: {
    updateState: Reducer<IAuthorizationModelState>;
  };
}

const AuthorizationModel: IAuthorizationModel = {
  namespace: 'authorization',

  state: {},

  effects: {
    *authorize({ payload, callback }, { call }) {
      const res = yield call(authApi.authorize, payload);
      res && callback && callback(res);
    },
    *isoutauthtime({ payload, callback }, { call }) {
      const res = yield call(authApi.isoutauthtime, payload);
      callback && callback(res);
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};

export default AuthorizationModel;
