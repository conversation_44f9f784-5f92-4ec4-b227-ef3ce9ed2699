@import '~@/styles/mixin.less';

.container {
  display: flex;
  flex-direction: column;
  padding: 30px;

  .info {
    display: flex;
    flex-direction: row;
    align-items: center;

    img {
      width: 108px;
      height: 108px;
      margin-right: 30px;
      border-radius: 50%;
    }

    div.right {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      height: 108px;
      color: #9b9797;
      font-size: 26px;

      span {
        margin: 0 6px;
        color: #000;
        font-weight: bold;
      }
    }
  }

  .content {
    margin: 30px 0;
    padding: 30px 30px 0 0;
    color: #000;
    font-weight: 500;
    font-size: 28px;
    background-color: #f5f6f7;
    border-radius: 20px;

    ul {
      margin-bottom: 0;

      li {
        margin-bottom: 30px;
      }
    }
  }

  .agreementArea {
    margin-top: 10px;
    color: #666;
    font-size: 24px;

    span {
      color: #3ba4ff;
    }
  }

  .btnBox {
    margin-top: 100px;

    div:first-child {
      width: 100%;
      height: 80px;
      color: #fff;
      font-weight: bold;
      font-size: 28px;
      line-height: 80px;
      text-align: center;
      background-color: #007fff;
      border-radius: 40px;
    }

    div:last-child {
      margin-top: 30px;
      color: #007fff;
      font-weight: bold;
      font-size: 24px;
      text-align: center;
    }
  }
}
