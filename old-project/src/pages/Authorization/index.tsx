import React, { FC, useEffect } from 'react';
import { history, Dispatch, connect, IGlobalModelState, IAuthorizationModelState, useLocation } from 'umi';
import { Toast } from 'antd-mobile';
import { decode } from 'js-base64';
import AppScheme from '@/utils/AppScheme';
import { isHyt<PERSON>erson, isHytDoctor } from '@/utils/platform';
import styles from './index.less';

const logo = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/hytlogo.png';

interface IPageProps {
  dispatch: Dispatch;
  global: IGlobalModelState;
  authorization: IAuthorizationModelState;
  loading?: boolean;
}

const Authorization: FC<IPageProps> = ({ dispatch }) => {
  const { search = '' }: any = useLocation();

  // ?token={}&data=businessCode+++{}---clientId+++{}---hospName+++{}---redirectURL+++(base64加密的{})
  const dataStr = decodeURIComponent(search)?.split('data=')[1] || ''; // 浏览器将+转成%20 =>将参数decode一下
  const objArr = dataStr.split('---') || [];
  const obj: any = {};
  for (const i of objArr) {
    const k = i.split('+++');
    if (k[0] !== 'redirectURL') {
      // 义幻的重定向地址值未进行url编码，无需解码
      // eslint-disable-next-line prefer-destructuring
      obj[k[0]] = decodeURIComponent(k[1]);
    } else {
      // 义幻的重定向地址进行了base64加密，需解密
      // eslint-disable-next-line prefer-destructuring
      obj[k[0]] = decode(k[1]);
    }
  }

  const { businessCode = '', clientId = '', hospName = '平台', redirectURL = '' } = obj;

  // 去义幻的界面咯
  const goToYH = (code: string = '') => {
    const hasQ = redirectURL.indexOf('?') !== -1;
    const url = `${redirectURL}${hasQ ? '&' : '?'}code=${code}`;
    const hh = {
      url,
    };
    Toast.info('跳转中～');
    console.log(hh, '跳转');
    window.location.href = url;
  };

  // 点击授权
  const auth = () => {
    dispatch({
      type: 'authorization/authorize',
      payload: {
        businessCode,
        clientId,
        redirectURL,
        isAgree: 1,
      },
      callback: (res: any = {}) => {
        const { code = '' } = res;
        goToYH(code);
      },
    });
  };

  /*
  // 有没有超期
  const isOutAuthTime = () => {
    dispatch({
      type: 'authorization/isoutauthtime',
      payload: {
        businessCode,
        clientId,
        redirectURL,
      },
      callback: (res: any = {}) => {
        const { code = '0', errCode = '', msg = '数据有问题哦～', data = {} } = res;

        if (code === '0') {
          // 查询超期报错
          if (errCode === '1130040') {
            // 未授权过`
            // auth();
          } else {
            Toast.info(msg, 2);
          }
        } else if (code === '1') {
          const { code: code1 = '', isOutTime = true } = data;

          if (!isOutTime) {
            // 没有超期
            goToYH(code1);
          } else {
            // 超期了呢
            // auth();
          }
        }
      },
    });
  };
  */

  const onHandleClick = (isAgreen: boolean = false) => {
    if (!isAgreen) {
      Toast.info('您已拒绝授权', 1);
      const timer = setTimeout(() => {
        clearTimeout(timer);

        if (isHytPerson() || isHytDoctor()) {
          AppScheme.closeWeb();
        } else {
          history.goBack();
        }
      }, 1000);
    } else {
      auth();
    }
  };

  // 需求变更，一进来就获取是否授权期
  useEffect(() => {
    // isOutAuthTime(); // 需求变更，原生判断是否授权期内
  }, []);

  const goToAgreement = () => {
    window.location.href = 'https://hytapi.cd120.com/hxgyAppV2/system/appNotice';
  };

  return (
    <div className={styles.container}>
      <div className={styles.info}>
        <img src={logo} alt="" className={styles.bgImage} />
        <div className={styles.right}>
          <div>
            该服务由<span>{hospName}</span>提供，
          </div>
          <div>需要获取以下权限：</div>
        </div>
      </div>
      <div className={styles.content}>
        <ul>
          <li>获取您的公开信息（昵称、头像、性别等）</li>
          <li>使用您的身份信息办理业务（姓名、身份证、手机号）</li>
        </ul>
      </div>
      <div className={styles.agreementArea}>
        确认授权即视为本人知情并同意<span onClick={() => goToAgreement()}>《用户授权协议》</span>
      </div>
      <div className={styles.btnBox}>
        <div onClick={() => onHandleClick(true)}>确认授权</div>
        <div onClick={() => onHandleClick(false)}>暂不授权</div>
      </div>
    </div>
  );
};

export default connect(
  ({ global, authorization }: { global: IGlobalModelState; authorization: IAuthorizationModelState }) => ({
    global,
    authorization,
  }),
)(Authorization);
