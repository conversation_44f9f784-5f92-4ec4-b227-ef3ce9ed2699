.inputItem {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100px;
  padding: 0px !important;
  font-size: 28px;
  background: #fff;
  &.noBorder {
    &::after {
      display: none;
    }
  }
  input {
    padding-left: 0;
    background-color: #fff;
  }
  input::-webkit-input-placeholder {
    color: #bbbeca !important;
  }

  div:nth-child(1) {
    color: #51586d;
    white-space: pre-wrap;
    word-break: break-all;
    width: 160px;
  }
  div:nth-child(2) {
    flex: 1;
    display: flex;
    align-items: center;

    input {
      display: block;
      width: 100%;
      color: #03081a;
      border: none;
      text-align: right;
      &.disabled {
        color: #bbbeca !important;
        opacity: 1;
      }
    }
  }
}

.inputItem::after {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  color: #eee;
  border-bottom: 1px solid #eee;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  content: '';
}

::-webkit-input-placeholder {
  color: #ccc;
}
