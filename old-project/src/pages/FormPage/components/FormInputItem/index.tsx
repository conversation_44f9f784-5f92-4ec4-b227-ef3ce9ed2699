import React from 'react';
import styles from './index.less';
import classnames from 'classnames';

interface IProps {
  /**
   * rc-form表单对象
   */
  // form: any;
  /**
   * 字段名称
   */
  label: string;
  /**
   * 最大输入字符
   */
  maxLength?: number;
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 占位符
   */
  placeholder?: string;
  /**
   * prop值
   */
  // propKey: string;
  /**
   * 默认值
   */
  value?: string;
  /**
   * 输入键盘类型
   */
  type: string;
  /**
   * 点击选中时执行的回调
   */
  onOkCustom?: (value: any) => void;

  noBorder?: boolean;

  suffix?: string;
}

const FormInputItem = (props: IProps) => {
  const { label, maxLength, disabled, placeholder, value, type, onOkCustom, noBorder = false, suffix = '' } = props;

  return (
    <div className={classnames(styles.inputItem, noBorder ? styles.noBorder : '')}>
      <div>{label}</div>
      <div>
        <input
          maxLength={maxLength}
          disabled={disabled}
          placeholder={placeholder}
          onChange={(value: any) => onOkCustom && onOkCustom(value?.target?.value)}
          type={type}
          value={value}
          className={disabled ? styles.disabled : ''}
        />
        {suffix}
      </div>
    </div>
  );
};

export default FormInputItem;
