.pickerItem {
  position: relative;
  display: flex;
  align-items: center;
  height: 104px;
  padding: 0px !important;
  font-size: 28px;
  background: #fff;
  width: 100%;

  &.noBorder {
    &::after {
      display: none;
    }
  }
  > div:nth-child(1) {
    color: #51586d;
    width: 160px;
    flex-shrink: 0;
    white-space: pre-wrap;
    word-break: break-all;
  }
  > div:nth-child(2) {
    flex: 1;
  }

  .child {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;

    .default {
      color: #bbbeca;
    }

    .selected {
      color: #03081a;
    }
    span {
      font-size: 28px;
      line-height: 0px;
    }

    svg {
      width: 36px;
      // height: 36px;
      margin-left: 8px;
    }
  }
}

.pickerItem::after {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  color: #eee;
  border-bottom: 1px solid #eee;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  content: '';
}

:global {
  ._cascader {
    .adm-list-body {
      font-size: 32px;
      color: #03081a;
    }
    .adm-cascader-header {
      height: 96px !important;
      background: #f9fafc;
      border-bottom: 1px solid #ebedf5;
    }
  }
}
