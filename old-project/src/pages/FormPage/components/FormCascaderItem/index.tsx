import React, { useState } from 'react';
import { HxIcon } from '@/components';
import classnames from 'classnames';
import { Cascader } from 'antd-mobile-v5';
import styles from './index.less';

interface IProps {
  /**
   * 字段说明
   */
  label: string;
  /**
   * 数据源
   */
  data: any[];
  /**
   * prop值
   */
  propKey?: string;
  /**
   * 默认值
   */
  value?: string[];
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 提示语
   */
  extra?: string;
  /**
   * 点击选中时执行的回调
   */
  onOkCustom: (value: string[], itemList: any) => void;
  /**
   * 弹框标题
   */
  title?: string;

  noBorder?: boolean;
  /* 分割符号 */
  split?: string;
}

const Child = (props: any) => {
  const { extra = '请选择', value = [], data, disabled, onClick, split } = props;
  const getLabel = () => {
    if (!value || !value.length) {
      return '';
    }
    const cols = value.length;
    if (cols === 1) {
      return data.find((i) => i.value === value[0])?.label;
    }
    if (cols === 2) {
      /* 获取一急索引 */
      try {
        const index = data.findIndex((i) => i.value === value[0]);
        const p1 = data.find((i) => i.value === value[0])?.label;
        const p2 = data[index]?.children.find((i) => i.value === value[1])?.label;
        return `${p1}${split}${p2}`;
      } catch (error) {
        console.log('error:', error);
      }
    }
    if (cols === 3) {
      /* 获取一急索引 */
      try {
        const index1 = data.findIndex((i) => i.value === value[0]);
        const index2 = data[index1]?.children.findIndex((i) => i.value === value[1]);
        const p1 = data.find((i) => i.value === value[0])?.label;
        const p2 = data[index1]?.children.find((i) => i.value === value[1])?.label;
        const p3 = data[index1]?.children[index2]?.children.find((i) => i.value === value[2])?.label;
        return `${p1}${split}${p2}${split}${p3}`;
      } catch (error) {
        console.log('error:', error);
      }
    }
  };
  return (
    <div className={styles.child}>
      <span
        className={`${!value.length || (!value[0] && value[0] !== 0) || disabled ? styles.default : styles.selected}`}
        onClick={() => {
          onClick && onClick();
        }}
      >
        {getLabel() || extra}
      </span>
      {!disabled && <HxIcon iconName="arrow-right" />}
    </div>
  );
};

const FormCascaderItem = (props: IProps) => {
  const {
    label,
    data = [],
    extra,
    disabled,
    propKey,
    value = [],
    onOkCustom,
    title,
    noBorder = false,
    split = '/',
  } = props;
  const [visible, setVisible] = useState(false);
  return (
    <div className={classnames(styles.pickerItem, noBorder ? styles.noBorder : '')}>
      <div className={styles.label}>{label}</div>
      <Cascader
        className="_cascader"
        options={data}
        visible={visible}
        title={title}
        onClose={() => {
          setVisible(false);
        }}
        value={value}
        onConfirm={(val: string[], extend) => {
          if (disabled) return;
          onOkCustom && onOkCustom(val, extend.items);
        }}
        onSelect={(val, extend) => {
          console.log('onSelect', val, extend.items);
        }}
      />
      <Child
        value={value}
        extra={extra}
        data={data}
        disabled={disabled}
        onClick={() => {
          if (disabled) {
            return;
          }
          setVisible(true);
        }}
        split={split}
      />
    </div>
  );
};

export default FormCascaderItem;
