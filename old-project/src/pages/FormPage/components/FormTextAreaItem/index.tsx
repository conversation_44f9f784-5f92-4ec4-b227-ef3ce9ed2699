import React from 'react';
import styles from './index.less';
import classnames from 'classnames';
import { TextArea } from 'antd-mobile-v5';

interface IProps {
  /**
   * rc-form表单对象
   */
  // form: any;
  /**
   * 字段名称
   */
  label: string;
  /**
   * 最大输入字符
   */
  maxLength?: number;
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 占位符
   */
  placeholder?: string;
  /**
   * prop值
   */
  // propKey: string;
  /**
   * 默认值
   */
  value?: string;
  /**
   * 点击选中时执行的回调
   */
  onOkCustom?: (value: any) => void;

  noBorder?: boolean;
}

const FormInputItem = (props: IProps) => {
  const { label, maxLength, disabled, placeholder, value, type, onOkCustom, noBorder = false } = props;

  return (
    <div className={classnames(styles.inputItem, noBorder ? styles.noBorder : '')}>
      <div className={styles.label}>{label}</div>
      <div>
        <TextArea
          placeholder={placeholder}
          maxLength={maxLength}
          disabled={disabled}
          showCount
          value={value}
          rows={4}
          onChange={(val) => {
            onOkCustom && onOkCustom(val);
          }}
          className={disabled ? styles.disabled : ''}
        />
      </div>
    </div>
  );
};

export default FormInputItem;
