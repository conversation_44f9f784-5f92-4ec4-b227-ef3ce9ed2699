.inputItem {
  position: relative;
  padding: 0px;
  font-size: 28px;
  background: #fff;
  padding-bottom: 32px;
  .label {
    height: 96px;
    color: #51586d;
    line-height: 96px;
  }
  &.noBorder {
    &::after {
      display: none;
    }
  }
  :global {
    .adm-text-area {
      background: #f5f6fa;
      border-radius: 16px;
      padding: 24px 24px 16px;
      .adm-text-area-element {
        font-size: 28px;
        color: #03081a;
        &::placeholder {
          color: #bbbeca;
        }
      }
      .adm-text-area-count {
        font-size: 24px;
      }
    }
  }
}
.inputItem::after {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  color: #eee;
  border-bottom: 1px solid #eee;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  content: '';
}
