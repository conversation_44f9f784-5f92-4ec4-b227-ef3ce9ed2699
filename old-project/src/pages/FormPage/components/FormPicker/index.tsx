import React, { useEffect, useState } from 'react';
import { Popup, Button, PickerView, CascadePickerView } from 'antd-mobile-v5';
import { Dispatch } from 'umi';
import styles from './index.less';

const IconClose = 'https://cdnhyt.cd120.com/person/assets/convenienceclinic/circularClose.png';

interface IProps {
  dispatch: Dispatch;
  /**
   * 数据源
   */
  data: any[];

  title: string;

  disabled: boolean;

  value?: any; // 默认值

  /**
   * 点击选中时执行的回调
   */
  onOkCustom: (value: []) => void;
}
const FormPicker: React.FC<IProps> = (props) => {
  const { data = [], title = '请选择', children, onOkCustom, disabled, value = [] } = props;
  const [visible, setVisible] = useState<boolean>(false);
  const [pickerValue, setPickerValue] = useState<any>(value || []);

  const handleOk = () => {
    onOkCustom && onOkCustom(pickerValue);
    setVisible(false);
  };

  const pickerChange = (val) => {
    setPickerValue(val);
  };

  return (
    <div className={styles.formPicker}>
      <div
        onClick={() => {
          if (disabled) return;
          setPickerValue(value);
          setVisible(true);
        }}
      >
        {children}
      </div>
      <div>
        <Popup
          visible={visible}
          bodyStyle={{
            borderTopLeftRadius: '12px',
            borderTopRightRadius: '12px',
            minHeight: '280px',
            overflow: 'hidden',
          }}
          className="pickerWrap"
        >
          <div className={styles.formPicker}>
            <div className={styles.header}>
              <span className={styles.title}>{title}</span>
              <img
                src={IconClose}
                alt=""
                className={styles.close}
                onClick={() => {
                  setVisible(false);
                }}
              />
            </div>
            <div className={styles.picker}>
              <CascadePickerView
                options={data}
                value={pickerValue}
                style={{ '--item-height': '3.4rem' }}
                onChange={pickerChange}
              />
            </div>
            <div className={styles.footer}>
              <Button color="primary" shape="rounded" className={styles.submitBtn} onClick={handleOk}>
                确定
              </Button>
            </div>
          </div>
        </Popup>
      </div>
    </div>
  );
};

export default FormPicker;
