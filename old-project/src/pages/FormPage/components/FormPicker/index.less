.formPicker {
  width: 100%;
  display: flex;
  flex-direction: column;

  .submitBtn {
    height: 96px;
    width: 100%;
  }
  .picker {
    flex: 1;

    :global([aria-label='active']) {
      :global(.adm-picker-view-column-item-label) {
        color: #03081a !important;
        font-weight: 500;
      }
    }
    :global(.adm-picker-view-column-item-label) {
      font-size: 36px;
      font-weight: 400;
      color: #989eb4;
    }
    :global(.adm-picker-view-column) {
      z-index: 10001;
    }
    :global(.adm-picker-view-mask-middle) {
      border: none;
      background-color: #f5f6fa;
    }
  }
  .footer {
    height: 160px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .header {
    height: 100px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    padding: 0 8px;
    .close {
      width: 40px;
      height: 40px;
    }
    .title {
      font-size: 36px;
      font-weight: 500;
      color: #03081a;
    }
  }
}

:global(.pickerWrap) {
  .formPicker {
    padding: 0 24px;
  }
}
