import React from 'react';
import { HxIcon } from '@/components';
import classnames from 'classnames';
import styles from './index.less';
import FormPicker from '../FormPicker';

interface IProps {
  /**
   * 字段说明
   */
  label: string;
  /**
   * 数据源
   */
  data: any[];
  /**
   * 列数
   */
  cols: number;
  /**
   * prop值
   */
  propKey?: string;
  /**
   * 默认值
   */
  value?: string;
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 提示语
   */
  extra?: string;
  /**
   * 点击选中时执行的回调
   */
  onOkCustom: (value: []) => void;
  /**
   * 弹框标题
   */
  title?: string;

  noBorder?: boolean;
}

const Child = (props: any) => {
  const { extra = '请选择', value = [], data, disabled, cols = 1 } = props;
  const getLabel = () => {
    if (!value || !value.length) {
      return '';
    }
    if (cols === 1) {
      return data.find((i) => i.value === value[0])?.label;
    }
    if (cols === 2) {
      /* 获取一急索引 */
      try {
        const index = data.findIndex((i) => i.value === value[0]);
        const p1 = data.find((i) => i.value === value[0])?.label;
        const p2 = data[index]?.children.find((i) => i.value === value[1])?.label;
        return `${p1} ${p2}`;
      } catch (error) {
        console.log('error:', error);
      }
    }
    if (cols === 3) {
      /* 获取一急索引 */
      try {
        const index1 = data.findIndex((i) => i.value === value[0]);
        const index2 = data[index1]?.children.findIndex((i) => i.value === value[1]);
        const p1 = data.find((i) => i.value === value[0])?.label;
        const p2 = data[index1]?.children.find((i) => i.value === value[1])?.label;
        const p3 = data[index1]?.children[index2]?.children.find((i) => i.value === value[2])?.label;
        return `${p1} ${p2} ${p3}`;
      } catch (error) {
        console.log('error:', error);
      }
    }
  };
  return (
    <div className={styles.child}>
      <span
        className={`${!value.length || (!value[0] && value[0] !== 0) || disabled ? styles.default : styles.selected}`}
      >
        {getLabel() || extra}
      </span>
      {!disabled && <HxIcon iconName="arrow-right" />}
    </div>
  );
};

const FormPickerItem = (props: IProps) => {
  const { label, data = [], extra, disabled, cols, propKey, value, onOkCustom, title, noBorder = false } = props;
  return (
    <div className={classnames(styles.pickerItem, noBorder ? styles.noBorder : '')}>
      <div className={styles.label}>{label}</div>
      <FormPicker
        data={data}
        value={value}
        title={title}
        disabled={disabled}
        onOkCustom={(value: []) => {
          if (disabled) return;
          onOkCustom && onOkCustom(value);
        }}
      >
        <Child value={value} extra={extra} data={data} disabled={disabled} cols={cols} />
      </FormPicker>
    </div>
  );
};

export default FormPickerItem;
