.formUploadItem {
  padding-bottom: 32px;
  .label {
    height: 96px;
    color: #51586d;
    line-height: 96px;
  }
  .content {
    .hint {
      padding: 12px;
      background: rgba(254, 143, 60, 0.08);
      border-radius: 16px;
      font-size: 24px;
      text-align: left;
      color: #fe8f3c;
      line-height: 34px;
      margin-bottom: 32px;
    }
    .uploadWrap {
      :global {
        .adm-image-uploader-cell {
          overflow: hidden;
          background-color: #fff;
          border-radius: 16px;
        }
        .adm-image-uploader-upload-button-wrap .adm-image-uploader-upload-button {
          border: 2px solid #ebedf5 !important;
          border-radius: 8px;
        }
        .adm-image-uploader-upload-button-icon {
          --adm-color-weak: #ebedf5;
        }
        .adm-image-uploader-cell-delete {
          background-color: unset;
          right: 0;
          top: -16px;
          width: unset;
          height: unset;
        }
      }
    }
  }
  &.noBorder {
    &::after {
      display: none;
    }
  }
}
.pickerItem::after {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  color: #eee;
  border-bottom: 1px solid #eee;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  content: '';
}
