.FileUploader {
  width: 100%;
  .fileList {
    display: flex;
    flex-wrap: wrap;
    .fileItem {
      height: 176px;
      flex-shrink: 0;
      border-radius: 16px;
      // overflow: hidden;
      padding-bottom: 32px;
      margin-right: 26px;
      &:nth-of-type(4n) {
        margin-right: 0;
      }
      .uploaderWrap {
        position: relative;
        width: 144px;
      }
      .uploaderCell {
        background-color: #fff;
        border-radius: 16px;
        border: 2px solid #ebedf5;
        width: 144px;
        height: 144px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        .loadingMask {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          color: #fff;
          background-color: rgba(50, 50, 51, 0.88);
          border-radius: 16px;
          z-index: 9;
          .loadingWrap {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            box-sizing: border-box;
            padding-top: 8px;
            .loadingMessage {
              font-size: 24px;
              margin-top: 14px;
            }
          }
        }
        .uploadDelete {
          position: absolute;
          right: -20px;
          top: -20px;
          z-index: 10;
        }
        .uploadImg {
          object-fit: cover;
          display: block;
          width: 100%;
          height: 100%;
          border-radius: 16px;
        }
        .videoPlayIcon {
          width: 40px;
          height: 40px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
        .videoWrap {
          width: 100%;
          height: 100%;
          position: relative;
        }
        .uploadVideo {
          width: 100%;
          height: 100%;
          border-radius: 16px;
          object-fit: cover;
          &::-webkit-media-controls-timeline {
            display: none;
          }
          &::-webkit-media-controls-mute-button {
            display: none;
          }
          &::-webkit-media-controls-volume-slider {
            display: none;
          }
        }
      }
      input {
        cursor: pointer;
        position: absolute;
        opacity: 0;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        border-radius: 4px;
        overflow: visible;
      }
    }
  }
}
:global {
  .adm-image-viewer-image-wrapper {
    img {
      max-height: 50%;
    }
  }
}
.videoClose {
  position: absolute;
  width: 48px;
  right: 40px;
  top: 70px;
  z-index: 99999;
}
.maskVideo {
  width: 100%;
  // height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  z-index: 9999;
  top: 50%;
  transform: translateY(-50%);
  // height: 100%;

  video {
    // width: 100%;
    // height: 500px;
    object-fit: contain;
  }
}
