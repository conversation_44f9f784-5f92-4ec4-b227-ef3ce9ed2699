import React, { ReactHTMLElement, useEffect, useRef, useState } from 'react';
import { AddOutline, ForbidFill } from 'antd-mobile-icons';
import { Grid, ImageViewer, Mask, SpinLoading, Toast } from 'antd-mobile-v5';
import _ from 'lodash';
import { ImageUploadItem } from 'antd-mobile-v5/es/components/image-uploader';
import styles from './FileUploader.less';
const videoPlayIcon = 'https://cdnhyt.cd120.com/person/assets/common/video_play_icon.png';
const close =
  'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/medical-insurance-pay-distribution/icon-close.png';

interface IFileObj {
  url: string;
}
interface IProps {
  accept?: string;
  value?: IFileObj[];
  maxCount?: number;
  disabled?: boolean; // 禁止上传
  onChange?: (list: IFileObj[]) => void;
  upload: <T>(file: File) => Promise<T>;
}
const FileUploader: React.FC<IProps> = (props) => {
  const { accept = 'file', value = [], maxCount = 5, onChange, upload, disabled = false } = props;
  const [fileList, setFileList] = useState<IFileObj[]>(value);
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const imgRegex = /(\.jpg|\.jpeg|\.png|\.gif|\.bmp|\.webp)$/i;
  const videoExtensions = ['mp4', 'webm', 'mov', 'mkv', 'avi']; // 视频格式
  const videoRegex = new RegExp(`\\.(${videoExtensions.join('|').toLowerCase()})$`, 'i');
  const [visible, setVisible] = useState<boolean>(false);
  const [currentUrl, setCurrentUrl] = useState<string>('');
  const [videoRef, setVideoRef] = useState<any>(null);
  const [inputValue, setInputValue] = useState(null);
  /**
   * 上传
   * @return {*}
   */

  const uploadFile = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const eventTarget = e.target;
    const { files: rawFiles } = e.target;
    if (!rawFiles) return;
    const files = [].slice.call(rawFiles) as File[];
    if (files.length === 0) {
      return;
    }
    let deepCloneFileList: IFileObj[] = _.cloneDeep(fileList);
    await Promise.all(
      files.map(async (file, index) => {
        // eslint-disable-next-line no-useless-catch
        try {
          const result: IFileObj = await upload(file);
          if (result && result?.url) {
            setFileList([...fileList, result]);
            deepCloneFileList = [...deepCloneFileList, result];
          }
        } catch (error) {
          eventTarget.value = '';
          throw error;
        }
      }),
    ).catch((error) => console.error(error));

    // setFileList(list);
    onChange && onChange(deepCloneFileList);
  };

  /**
   * 校验文件是否是图片和视频
   * @param {*} url
   * @return {*}
   */

  const verifyFile = (url: string, type: 'img' | 'video') => {
    if (!url) return false;
    if (type === 'img') {
      return imgRegex.test(url);
    }
    if (type === 'video') {
      return videoRegex.test(url);
    }
    return false;
  };

  /**
   * 删除文件
   * @return {*}
   */
  const deleteFile: (index: number) => void = (index: number) => {
    const delFileList: IFileObj[] = _.cloneDeep(fileList);
    delFileList.splice(index, 1);
    setFileList(delFileList);
    onChange && onChange(delFileList);
  };

  const renderLoading = (status) => {
    return (
      status === 'pending' && (
        <div className={styles.loadingMask}>
          <span className={styles.loadingWrap}>
            <SpinLoading color="white" />
            <span className={styles.loadingMessage}>上传中...</span>
          </span>
        </div>
      )
    );
  };
  /**
   * 点击播放视频
   * @return {*}
   */
  const playVideo = (item) => {
    try {
      setCurrentUrl(item);
      setVisible(true);
    } catch (error) {
      console.log('error:', error);
      throw error;
    }
  };

  useEffect(() => {
    if (videoRef) {
      const height = document.documentElement.clientHeight;
      const width = document.documentElement.clientWidth;
      videoRef.width = width;
      videoRef.height = height;
    }
  }, [videoRef]);

  return (
    <div className={styles.FileUploader}>
      <div className={styles.fileList}>
        {value.map((item, index) => {
          return (
            <div className={styles.fileItem} key={item.url ?? index}>
              <div className={styles.uploaderCell}>
                {/* 删除 */}
                {!disabled && (
                  <div className={styles.uploadDelete}>
                    <ForbidFill color="#FC4553" fontSize={20} onClick={() => deleteFile(index)} />
                  </div>
                )}
                {/* 图片展示 */}
                {verifyFile(item.url, 'img') && (
                  <img
                    src={item.url}
                    alt=""
                    className={styles.uploadImg}
                    onClick={() => {
                      const imgList = fileList.filter((item) => verifyFile(item.url, 'img'));
                      ImageViewer.Multi.show({ images: imgList.map((item) => item.url) });
                    }}
                  />
                )}

                {/* 视频播放 */}
                {verifyFile(item.url, 'video') && (
                  <div onClick={(e) => playVideo(item.url)} className={styles.videoWrap}>
                    <video src={item.url} className={styles.uploadVideo} />
                    <img src={videoPlayIcon} alt="" className={styles.videoPlayIcon} />
                  </div>
                )}
              </div>
            </div>
          );
        })}
        {fileList.length < maxCount && !disabled && (
          <div className={styles.fileItem}>
            <div className={styles.uploaderWrap}>
              <div className={styles.uploaderCell}>
                <AddOutline fontSize={30} color="#EBEDF5" />
              </div>
              <input
                accept={accept}
                type="file"
                onChange={uploadFile}
                onError={() => {
                  Toast.show('上传失败');
                }}
              />
            </div>
          </div>
        )}
        <Mask visible={visible} onMaskClick={() => setVisible(false)} destroyOnClose>
          <img src={close} className={styles.videoClose} alt="" onClick={() => setVisible(false)} />
          <div className={styles.maskVideo}>
            <video
              ref={(ref) => {
                setVideoRef(ref);
              }}
              src={currentUrl}
              controls
              autoPlay
              x5-video-player-type="h5"
              webkit-playsinline
              x-webkit-airplay="true"
              x5-video-orientation="portraint"
              playsInline
              x5-video-player-fullscreen="portraint"
              x5-video-ignore-metadata="true"
              width="100%"
            />
          </div>
        </Mask>
      </div>
    </div>
  );
};

export default FileUploader;
