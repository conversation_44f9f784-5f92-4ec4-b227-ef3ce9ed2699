import React, { useEffect, useState } from 'react';
import classNames from 'classnames';
import { ImageUploader, Toast } from 'antd-mobile-v5';
import OssUpload from '@/utils/ossUpload';
import { ForbidFill } from 'antd-mobile-icons';
import styles from './index.less';
import FileUploader from './FileUploader';

interface IProps {
  label?: string;
  noBorder?: boolean;
  hint?: string;
  maxCount?: number;
  value: string[];
  disabled?: boolean;
  maxSize?: number;
  /**
   * 点击选中时执行的回调
   */
  onOkCustom?: (value: any) => void;
}
const FormUploadItem: React.FC<IProps> = (props) => {
  const upload = new OssUpload();
  const {
    label,
    noBorder = false,
    hint = '',
    maxCount = 5,
    value = [],
    onOkCustom,
    disabled = false,
    maxSize = 20 * 1024 * 1024,
  } = props;

  /**
   * 获取视频时长
   * @param {*} files
   * @return {*}
   */

  const getVideoTime = (files: File): Promise<number> => {
    return new Promise((resolve, reject) => {
      // 获取视频时长
      let duration = 0;
      const url = URL.createObjectURL(files);
      const audioElement = new Audio(url);
      audioElement.addEventListener('loadedmetadata', function () {
        duration = audioElement.duration; // 时长以秒作为单位
        resolve(duration);
      });
      audioElement.addEventListener('error', function (e) {
        reject();
      });
    });
  };

  /* 上传 */
  const handleUpload: any = async (file: File) => {
    let duration: number = 0;
    /* 兼容上传图片无duration */
    try {
      duration = await getVideoTime(file);
    } catch (error) {
      duration = 0;
    }
    console.log('视频时长：', duration);
    if (duration && duration > 5) {
      Toast.show('视频时长不得大于5秒');
      throw new Error('视频时长不得大于5秒');
    }
    if (file.size > maxSize) {
      Toast.show('文件大小不得大于20M');
      throw new Error('文件大小不得大于20M');
    }
    let res: any = '';
    try {
      Toast.show({ icon: 'loading', duration: 0 });
      res = await upload.uploadFileBySTS({ file });
    } catch (error) {
      Toast.show('上传失败');
      console.log('error:', error);
    } finally {
      Toast.clear();
    }
    return { url: res };
  };

  const mockUpload = (value) => {
    onOkCustom && onOkCustom(value);
  };

  return (
    <div className={classNames(styles.formUploadItem, noBorder ? styles.noBorder : '')}>
      <div className={styles.label}>{label}</div>
      <div className={styles.content}>
        {hint && <div className={styles.hint}>{hint}</div>}
        <div className={styles.uploadWrap}>
          <FileUploader
            value={value}
            onChange={mockUpload}
            upload={handleUpload}
            accept="video/*,image/*"
            maxCount={maxCount}
            disabled={disabled}
          />
        </div>
      </div>
    </div>
  );
};

export default FormUploadItem;
