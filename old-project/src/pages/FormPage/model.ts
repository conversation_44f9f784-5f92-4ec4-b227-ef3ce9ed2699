import { Reducer } from 'redux';
import { Effect } from 'umi';
import { createAction } from '@/utils/common';
import { queryWoundInfo } from './service';

export interface FormPageModelState {
  woundInfo: any;
}

export interface IFormPageModel {
  namespace: string;
  state: FormPageModelState;
  effects: {
    queryWoundInfo: Effect;
  };
  reducers: {
    updateState: Reducer<any>;
  };
}

const FormPageModel: IFormPageModel = {
  namespace: 'formpage',

  state: {
    woundInfo: {},
  },

  effects: {
    *queryWoundInfo({ payload, callback }, { put }) {
      const res = yield queryWoundInfo(payload) || {};
      yield put(
        createAction('updateState')({
          woundInfo: res,
        }),
      );
      callback && callback(res);
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
export default FormPageModel;
