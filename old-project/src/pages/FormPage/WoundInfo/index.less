.woundInfo {
  height: 100vh;
  display: flex;
  flex-direction: column;

  .content {
    flex: 1;
    overflow-y: auto;
    padding: 24px 24px 0;
    width: 100%;
    .list {
      background-color: #fff;
      border-radius: 24px;
      width: 100%;
      padding: 0 24px 24px;
      margin-bottom: 32px;
      .uploadRow {
        .label {
          height: 96px;
          color: #51586d;
          line-height: 96px;
        }
        .value {
          .hint {
            padding: 12px;
            background: rgba(254, 143, 60, 0.08);
            border-radius: 16px;
            font-size: 24px;
            text-align: left;
            color: #fe8f3c;
            line-height: 34px;
          }
          .uploadWrap {
            display: flex;
            flex-wrap: wrap;
            img {
              width: 144px;
              height: 144px;
              border-radius: 12px;
              flex-shrink: 0;
              margin-top: 32px;
              &:not(:nth-child(4n)) {
                margin-right: 26px;
              }
            }
          }
        }
      }
      .topRow {
        :global {
          .adm-text-area {
            background: #f5f6fa;
            border-radius: 16px;
            padding: 24px 24px 16px;
            .adm-text-area-element {
              font-size: 28px;
              color: #03081a;
              &::placeholder {
                color: #bbbeca;
              }
            }
            .adm-text-area-count {
              font-size: 24px;
            }
          }
        }
        .label {
          height: 96px;
          color: #51586d;
          line-height: 96px;
        }
      }
      .row {
        position: relative;
        display: flex;
        align-items: center;
        height: 104px;
        padding: 0px !important;
        font-size: 28px;
        background: #fff;
        width: 100%;
        &.wrapRow {
          height: auto !important;
          min-height: 4.16rem;
          padding: 0.64rem 0 !important;
          .value {
            white-space: unset;
            text-align: right;
            overflow: unset;
            text-overflow: unset;
          }
        }
        &::after {
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 1px;
          color: #eee;
          border-bottom: 1px solid #eee;
          -webkit-transform: scaleY(0.5);
          transform: scaleY(0.5);
          -webkit-transform-origin: 0 100%;
          transform-origin: 0 100%;
          content: '';
        }
        .label {
          color: #51586d;
          width: 160px;
          flex-shrink: 0;
          white-space: pre-wrap;
          word-break: break-all;
        }
        .value {
          color: #03081a;
          flex: 1;
          text-align: right;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }
    .form {
      background-color: #fff;
      border-radius: 24px;
      width: 100%;
      padding: 0 24px;
      margin-bottom: 32px;
      .DatePicker {
        box-sizing: border-box;
        width: 100%;
        height: 100px;
        background: #fff;

        .children {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          box-sizing: border-box;
          height: 100%;
          border-bottom: 0.5px solid #eee;
          position: relative;
          &.noBorder {
            &::after {
              display: none;
            }
          }
          &::after {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            color: #eee;
            border-bottom: 1px solid #eee;
            -webkit-transform: scaleY(0.5);
            transform: scaleY(0.5);
            -webkit-transform-origin: 0 100%;
            transform-origin: 0 100%;
            content: '';
            z-index: 1;
          }
          .label {
            color: #51586d;
            width: 160px;
            white-space: pre-wrap;
            word-break: break-all;
            font-size: 28px;
            flex-shrink: 0;
          }

          .pickerContent {
            display: flex;
            flex-direction: row;
            align-items: center;
            width: 100%;
            justify-content: flex-end;
            .disable {
              color: #bbbeca;
            }

            .extra {
              color: #bbbeca;
              font-size: 28px;
            }
            .right {
              width: 30px;
              height: 52px;
              margin-left: 12px;
            }
          }
        }
      }
    }
  }
  .footer {
    width: 100%;
    height: 128px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    background-color: #fff;
    padding: 0 24px;
    .btn {
      width: 100%;
      height: 96px;
    }
  }
}
