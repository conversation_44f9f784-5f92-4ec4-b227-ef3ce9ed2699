import React, { useEffect, useState } from 'react';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { HxIcon } from '@/components';
import { Button, DatePicker, TextArea, Toast } from 'antd-mobile-v5';
import AppScheme from '@/utils/AppScheme';
import { isHytDoctor, isHytPerson } from '@/utils/platform';
import { connect, Dispatch, Loading, history, FormPageModelState } from 'umi';
import FormPickerItem from '../components/FormPickerItem';
import FormInputItem from '../components/FormInputItem';
import FormCascaderItem from '../components/FormCascaderItem';
import FormTextAreaItem from '../components/FormTextAreaItem';
import FormUploadItem from '../components/FormUploadItem';
import { submitWoundInfo } from '../service';
import { requireForm } from '../dataDictionary';
import styles from './index.less';

interface IProps {
  loading: boolean;
  dispatch: Dispatch;
  formpage: FormPageModelState;
  location: {
    query: {
      admissionId?: string;
      packageId?: string;
    };
  };
}
interface IFileObj {
  url: string;
}
interface IFormData {
  bodyPartCode?: string[];
  bodyPartDesc?: string; // 身体部位
  startDateTime?: string;
  visitLocation?: string;
  painLevel: string[]; //  疼痛程度
  feverSituation: string[]; // 发热程度
  remarks?: string; // 备注
  attachmentList?: IFileObj[]; // 附件
  smell: string[]; // smell
  temperature?: string; // 体温
}
const WoundInfo: React.FC<IProps> = (props) => {
  const {
    loading = false,
    dispatch,
    formpage: { woundInfo = {} },
    location: {
      query: { admissionId = '', packageId = '' },
    },
  } = props;
  const [formData, setFormData] = useState<IFormData>({
    painLevel: [],
    feverSituation: [],
    smell: [],
    attachmentList: [],
  });
  const [timeVisible, setTimeVisible] = useState(false);
  const [isDetail, setIsDetail] = useState<boolean>(false); // 是否是详情通过 patWoundInfoVO 判断
  const now = new Date();
  const { patWoundInfoVO = {} } = woundInfo;

  const fetchData = () => {
    dispatch({
      type: 'formpage/queryWoundInfo',
      payload: {
        packageId,
        admissionId,
      },
      callback: (res: any = {}) => {
        setIsDetail(!!res?.patWoundInfoVO);
      },
    });
  };

  const CustomChildren = (props: any) => {
    return (
      <div className={styles.DatePicker}>
        <div className={classNames(styles.children, props?.noBorder ? styles.noBorder : '')}>
          <div className={styles.label}>{props.label}</div>
          <div
            className={styles.pickerContent}
            onClick={() => {
              if (props?.disabled) return;
              props.handleClick();
            }}
          >
            <div className={styles.extra} style={{ color: !props.value || props?.disabled ? '#B0B3BF' : '#03081A' }}>
              {props.value ? props.value : props.extra}
            </div>
            {!props.disabled && <HxIcon iconName="arrow-right" className={styles.right} />}
          </div>
        </div>
      </div>
    );
  };

  /**
   * 疼痛程度
   * @return {*}
   */
  const getPainLevelList = () => {
    const { painLevelList = [] } = woundInfo || {};
    return painLevelList.map((item) => ({ label: item?.painLevelName, value: item?.painLevelCode }));
  };

  /**
   * 气味
   * @return {*}
   */
  const getSmellList = () => {
    const { smellList = [] } = woundInfo || {};
    return smellList.map((item) => ({ label: item?.smellName, value: item?.smellCode }));
  };

  /**
   * 发热程度
   * @return {*}
   */
  const getFeverSituationList = () => {
    const { feverSituationList = [] } = woundInfo || {};
    return feverSituationList.map((item) => ({ label: item?.feverSituationName, value: item?.feverSituationCode }));
  };

  /**
   * 身体部位
   * @return {*}
   */

  const cascaderData = () => {
    const { bodyPartList = [] } = woundInfo || {};
    const data = bodyPartList.map((item) => {
      return item?.partList && item?.partList.length
        ? {
            label: item?.bodyName,
            value: item?.bodyCode,
            children: item?.partList.map((val) => {
              if (val?.positionList && val?.positionList.length) {
                return {
                  label: val?.partName,
                  value: val?.partCode,
                  children: (val?.positionList || []).map((v) => ({
                    value: v.positionCode,
                    label: v?.positionName,
                  })),
                };
              }
              return { label: val?.partName, value: val?.partCode };
            }),
          }
        : { label: item?.bodyName, value: item?.bodyCode };
    });

    return data;
  };

  /**
   * 提交表单
   * @return {*}
   */
  const submitForm = async () => {
    try {
      Toast.show({
        icon: 'loading',
        duration: 0,
      });
      const payload: any = {
        admissionId,
        packageId,
        bodyPartDesc: formData?.bodyPartDesc,
        startDateTime: formData.startDateTime,
        visitLocation: formData.visitLocation,
        painLevel: getPainLevelList().find((item) => item.value === formData.painLevel[0])?.label,
        smell: getSmellList().find((item) => item.value === formData.smell[0])?.label,
        feverSituation: getFeverSituationList().find((item) => item.value === formData.feverSituation[0])?.label,
        remarks: formData?.remarks,
        attachmentList: (formData?.attachmentList || []).map((item: any) => item.url),
      };
      JSON.stringify(formData.feverSituation) === JSON.stringify(['ALL']) &&
        (payload.temperature = formData.temperature);

      delete payload.bodyPartCode;
      /* 判断非空 */
      // eslint-disable-next-line guard-for-in
      for (const key in payload) {
        if (!payload[key] && key in requireForm) {
          if (requireForm[key]) {
            Toast.show(`${requireForm[key]}不能为空`);
          }
          return;
        }
        if (key === 'attachmentList' && !payload[key].length) {
          Toast.show(`${requireForm[key]}不能为空`);
          return;
        }
      }
      const res: any = await submitWoundInfo({ ...payload });
      if (res?.code === '1') {
        Toast.show({
          content: '提交成功',
          duration: 1000,
          afterClose: () => {
            /* 关闭 */
            if (isHytPerson() || isHytDoctor()) {
              AppScheme.closeWeb();
            } else {
              history.goBack();
            }
          },
        });
      } else {
        Toast.clear();
      }
    } catch (error) {
      console.log('error:', error);
      Toast.clear();
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (loading) {
      Toast.show({
        icon: 'loading',
        duration: 0,
      });
    } else {
      Toast.clear();
    }
  }, [loading]);
  return (
    <div className={styles.woundInfo}>
      <div className={styles.content}>
        {!loading &&
          (isDetail ? (
            <div className={styles.list}>
              <div className={styles.row}>
                <div className={styles.label}>部位</div>
                <div className={styles.value}>{patWoundInfoVO?.bodyPartDesc}</div>
              </div>
              <div className={styles.row}>
                <div className={styles.label}>形成时间</div>
                <div className={styles.value}>{patWoundInfoVO?.startDateTime}</div>
              </div>
              <div className={classNames(styles.row, styles.wrapRow)}>
                <div className={styles.label}>治疗地址</div>
                <div className={styles.value}>{patWoundInfoVO?.visitLocation}</div>
              </div>
              <div className={styles.row}>
                <div className={styles.label}>疼痛程度</div>
                <div className={styles.value}>{patWoundInfoVO?.painLevel}</div>
              </div>
              <div className={styles.row}>
                <div className={styles.label}>气味</div>
                <div className={styles.value}>{patWoundInfoVO?.smell}</div>
              </div>
              <div className={styles.row}>
                <div className={styles.label}>发热情况</div>
                <div className={styles.value}>{patWoundInfoVO?.feverSituation}</div>
              </div>
              {patWoundInfoVO?.temperature && (
                <div className={styles.row}>
                  <div className={styles.label}>体温</div>
                  <div className={styles.value}>{patWoundInfoVO?.temperature}</div>
                </div>
              )}
              <div className={styles.topRow}>
                <div className={styles.label}>备注</div>
                <TextArea disabled value={patWoundInfoVO?.remarks} autoSize />
              </div>
              {patWoundInfoVO?.attachmentList && (
                <FormUploadItem
                  value={patWoundInfoVO?.attachmentList.map((item) => ({ url: item }))}
                  disabled={isDetail}
                  label="伤口照片"
                  hint="请在伤口正上方20~30厘米处拍照，不要开闪光灯，分别在揭开敷料后、换药后拍照。（支持图片和视频，最多5个）"
                />
              )}
            </div>
          ) : (
            <div className={styles.form}>
              <FormCascaderItem
                data={cascaderData()}
                value={formData?.bodyPartCode}
                disabled={isDetail}
                split="/"
                label="部位"
                title="部位"
                extra="请选择"
                onOkCustom={(value: string[], itemList: any) => {
                  setFormData({
                    ...formData,
                    bodyPartCode: value,
                    bodyPartDesc: itemList.map((item) => item.label).join('/'),
                  });
                }}
              />
              <DatePicker
                visible={timeVisible}
                title="形成时间"
                min={new Date(1900, 1, 1, 0, 0, 0)}
                onClose={() => {
                  setTimeVisible(false);
                }}
                onConfirm={(val) => {
                  const date = dayjs(val).format('YYYY-MM-DD');
                  setFormData({ ...formData, startDateTime: date });
                }}
                defaultValue={now}
                max={now}
              >
                {(value) =>
                  CustomChildren({
                    label: '形成时间',
                    extra: '请选择',
                    value: formData?.startDateTime,
                    disabled: isDetail,
                    handleClick: () => {
                      if (isDetail) {
                        return;
                      }
                      setTimeVisible(true);
                    },
                  })
                }
              </DatePicker>
              <FormInputItem
                value={formData?.visitLocation}
                label="治疗地址"
                disabled={isDetail}
                placeholder="请输入治疗地址"
                type="text"
                onOkCustom={(value: any) => {
                  setFormData({ ...formData, visitLocation: value });
                }}
              />
              <FormPickerItem
                data={getPainLevelList()}
                value={formData.painLevel}
                disabled={isDetail}
                cols={1}
                label="疼痛程度"
                title="请选择疼痛程度"
                extra="请选择"
                onOkCustom={(value: any) => {
                  setFormData({ ...formData, painLevel: value });
                }}
              />
              <FormPickerItem
                data={getSmellList()}
                value={formData?.smell}
                disabled={isDetail}
                cols={1}
                label="气味"
                title="请选择气味"
                extra="请选择"
                onOkCustom={(value: any) => {
                  setFormData({ ...formData, smell: value });
                }}
              />
              <FormPickerItem
                data={getFeverSituationList()}
                value={formData?.feverSituation}
                disabled={isDetail}
                cols={1}
                label="发热情况"
                title="请选择发热情况"
                extra="请选择"
                onOkCustom={(value: any) => {
                  setFormData({ ...formData, feverSituation: value });
                }}
              />
              {JSON.stringify(formData.feverSituation) === JSON.stringify(['ALL']) && (
                <FormInputItem
                  value={formData?.temperature}
                  disabled={isDetail}
                  label="体温"
                  placeholder="请输入体温"
                  type="text"
                  suffix="度"
                  maxLength={10}
                  onOkCustom={(value: any) => {
                    setFormData({ ...formData, temperature: value });
                  }}
                />
              )}
              <FormTextAreaItem
                value={formData?.remarks}
                disabled={isDetail}
                label="备注"
                maxLength={500}
                placeholder="请输入内容"
                onOkCustom={(value: any) => {
                  setFormData({ ...formData, remarks: value });
                }}
              />
              <FormUploadItem
                value={formData?.attachmentList}
                disabled={isDetail}
                label="伤口照片"
                hint="请在伤口正上方20~30厘米处拍照，不要开闪光灯，分别在揭开敷料后、换药后拍照。（支持图片和视频，最多5个，视频最多5秒，超过无法上传。）"
                onOkCustom={(value: any) => {
                  setFormData({ ...formData, attachmentList: value });
                }}
              />
            </div>
          ))}
      </div>
      {!isDetail && (
        <div className={styles.footer}>
          <Button color="primary" className={styles.btn} shape="rounded" onClick={submitForm} disabled={isDetail}>
            提交
          </Button>
        </div>
      )}
    </div>
  );
};
export default connect(({ formpage, loading }: { formpage: FormPageModelState; loading: Loading }) => ({
  formpage,
  loading: loading.effects['formpage/queryWoundInfo'],
}))(WoundInfo);
