import request from '@/utils/request';

/**
 * 查询伤口管理表单信息
 * @param data
 */
export const queryWoundInfo = async (data: object): Promise<object> =>
  request(`${API_DOCTOR}/netInquiry/chronicdisease/queryWoundInfo`, {
    method: 'POST',
    data,
  });
/**
 * 提交伤口管理表单信息
 * @param data
 */
export const submitWoundInfo = async (data: object): Promise<object> =>
  request(`${API_DOCTOR}/netInquiry/chronicdisease/submitWoundInfo`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });
