import React, { useEffect, useState } from 'react';
import OssUpload from '@/utils/ossUpload';
import { connect, Dispatch, Loading, history } from 'umi';
import { Button, Toast } from 'antd-mobile';
import { StorageEnum } from '@/utils/enum';
import { HxLocalStorage } from '@/utils/storage';
import _ from 'lodash';
import { getOrganCode } from '@/utils/parameter';
import styles from './index.less';
import { CaseMailingState } from '../data.d';
import { confirmProxyApply } from '../service';

const line = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/case-mailing/case_line.png';
const star = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/case-mailing/case_star.png';
const deleteIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/case-mailing/del.png';

interface IProps {
  dispatch: Dispatch;
  loading: Boolean | undefined;
  location: {
    query: {
      cardId: string;
    };
  };
}
const certificateInfo = [
  {
    title: '患者信息',
    info: [
      {
        label: '患者本人有效证件（若需用户口证明关系，此处上传患者户口页，患者与办理人关系处上传办理人户口页）',
        type: 'patient_front',
        url: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/front_of_ID_card.png',
        imagesUrl: '',
        id: 1,
      },
    ],
  },
  {
    title: '代理人信息',
    info: [
      {
        label: '代理人本人有效证件',
        type: 'proxy_front',
        url: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/front_of_ID_card.png',
        imagesUrl: '',
        id: 2,
      },
      {
        label: '代理人手持证件照照片',
        type: 'proxy_font_in_hand',
        url: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/case-mailing/hand_id_card.png',
        imagesUrl: '',
        id: 3,
      },
    ],
  },
  {
    title: '代理人与患者关系证明',
    info: [
      {
        label: '可上传证件如：户口本、结婚证、出生证明。',
        type: 'relationship',
        url: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/case-mailing/relation.png',
        imagesUrl: '',
        id: 4,
      },
    ],
  },
  {
    title: '其它文书上传',
    info: [
      {
        label: '1.成年患者需上传本人签署的代办委托书。</br>2.患者死亡的上传死亡证明书。</br>3.未成年患者不填此项。',
        type: 'authorization',
        url: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/case-mailing/entrust_book.png',
        imagesUrl: '',
        id: 5,
      },
    ],
  },
];

const exampleImgs = [
  {
    image: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/ID_card_standard.png',
    text: '标准',
  },
  {
    image: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/ID_card_missing.png',
    text: '边框缺失',
  },
  {
    image: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/ID_card_blurry.png',
    text: '照片模糊',
  },
  {
    image: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/ID_card-reflective.png',
    text: '拍摄反光',
  },
];

const CaseMailingAgentData: React.FC<IProps> = (props) => {
  const {
    location: {
      query: { cardId = '' },
    },
    dispatch,
  } = props;
  const [uploadInfo, setUploadInfo] = useState<any>(certificateInfo);
  const [notice, setNotice] = useState<string>('');

  const handleUploadImg = (e: React.ChangeEvent<HTMLInputElement>, indexArr: number[]) => {
    e.stopPropagation();
    const files = e.target.files || [];
    if (files?.length > 0) {
      Toast.loading('上传中', 0);
      const upload = new OssUpload();
      upload
        .uploadImg({ file: files[0] })
        .then((res: any) => {
          setUploadInfo((pre) => {
            return pre.map((item, inx) => {
              if (inx === indexArr[0]) {
                const info = item?.info?.map((i, index) => {
                  if (index === indexArr[1]) {
                    return {
                      ...i,
                      imagesUrl: res,
                    };
                  }
                  return i;
                });
                return {
                  ...item,
                  info,
                };
              }
              return item;
            });
          });

          Toast.hide();
          Toast.info('上传成功～', 1);
        })
        .catch((err) => {
          Toast.fail('上传照片出错，请重试');
          console.log('err', err);
        });
    }
  };

  /* 删除图片 */
  const deleteImg = (indexArr: number[]) => {
    setUploadInfo((pre) => {
      return pre.map((item, inx) => {
        if (inx === indexArr[0]) {
          const info = item?.info?.map((i, index) => {
            if (index === indexArr[1]) {
              return {
                ...i,
                imagesUrl: '',
              };
            }
            return i;
          });
          return {
            ...item,
            info,
          };
        }
        return item;
      });
    });
  };

  /**
   * @name: 检测是否已经上传了
   * @param {*}
   * @return {*}
   */
  const checkUpImg = () => {
    return !uploadInfo.some((i) => i?.info.some((item) => item.id !== 5 && !item.imagesUrl));
  };

  /**
   * @description: 上传信息接口
   * @return {*}
   */
  const fetchUpload = async () => {
    /* 组装上传的图片数据 {imagesUrl:'',type:''} */
    const uploadImgInfo = uploadInfo.map((i) => {
      return i?.info.map((item) => {
        return item;
      });
    });
    let idImages: any = [];
    try {
      idImages = _.flattenDepth(uploadImgInfo, 1).reduce((pre: any, cur: any) => {
        if (cur?.imagesUrl) {
          pre.push({ imagesUrl: cur.imagesUrl, type: cur?.type });
        }
        return pre;
      }, []);
    } catch (error) {
      console.log('error:', error);
    }
    await confirmProxyApply({
      idImages,
      organCode: getOrganCode(),
      cardId,
    })
      .then((res) => {
        if (res?.code === '1') {
          Toast.info('上传成功');
          /* 上传成功返回首页 */
          // const userInfo = HxLocalStorage.get(StorageEnum.USER_INFO);
          // const { openid = '', token = '', accountNo = '', name = '' } = userInfo;
          // window.location.href = `${YH_HX_HOME}&openid=${openid}&token=${token}&registerMobile=${accountNo}&name=${name}`;
          /* 上传成功返回就诊卡列表 */
          history.push({
            pathname: '/common/businesscardlist',
            query: {
              from: 'BAYJ',
            },
          });
        }
      })
      .catch((e) => {
        console.log(e);
        Toast.info('上传失败');
      });
  };

  /* 确认上传数据 */
  const confirmUpload = () => {
    if (!checkUpImg()) {
      Toast.info('请完善信息');
      return;
    }
    fetchUpload();
  };

  /* 获取提示文案 */
  const fetchDecData = () => {
    dispatch({
      type: 'common/queryTextList',
      payload: {
        textCodes: ['CASE_MAIL_PROXY_UPLOAD_IMAGE'],
      },
      callback: (res: any) => {
        try {
          if (res && res.length) {
            setNotice(res[0]?.textContent);
          }
        } catch (error) {
          console.log('error:', error);
        }
      },
    });
  };

  useEffect(() => {
    fetchDecData();
  }, []);

  return (
    <div className={styles.agentData}>
      {uploadInfo.map((i, inx) => {
        return (
          <div className={styles.agentDataWrap} key={inx.toString()}>
            <div className={styles.title}>
              <img src={line} alt="" className={styles.line} />
              {i.title}
            </div>
            <div className={styles.cardWrap}>
              {i?.info.map((item, index) => {
                return (
                  <div className={styles.cardItem} key={item.id}>
                    <div className={styles.label}>
                      {item.id !== 5 && (
                        <div className={styles.img_wrap}>
                          <img src={star} alt="" />
                        </div>
                      )}
                      <div dangerouslySetInnerHTML={{ __html: item.label }} />
                      {/* {item.label} */}
                    </div>
                    <div className={styles.uploadImg}>
                      <div className={styles.imgWrap}>
                        {/* 删除 */}
                        {item.imagesUrl && (
                          <img
                            src={deleteIcon}
                            alt=""
                            className={styles.deleteIcon}
                            onClick={() => {
                              deleteImg([inx, index]);
                            }}
                          />
                        )}
                        {/* 展示图片 */}
                        <img src={item.imagesUrl ? item.imagesUrl : item.url} alt="" className={styles.img} />
                        {/* 上传 */}
                        <input
                          className={styles.imagePicker}
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            handleUploadImg(e, [inx, index]);
                          }}
                        />
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        );
      })}

      {/* 拍摄要求 */}
      <div className={styles.claim}>
        <div className={styles.claimTitle}>
          <span className={styles.horizontalLine} />
          <span className={styles.claimTitleText}>资料拍摄要求</span>
          <span className={styles.horizontalLine} />
        </div>
        <div dangerouslySetInnerHTML={{ __html: notice }} className={styles.claimText} />
        <div className={styles.exampleImgs}>
          {exampleImgs.map((item) => (
            <div key={item.text}>
              <img src={item.image} alt="" />
              <div className={styles.exampleImgsText}>{item.text}</div>
            </div>
          ))}
        </div>
        <div className={styles.button}>
          <Button
            type="primary"
            style={{ color: '#ffffff', borderRadius: '52px', backgroundColor: checkUpImg() ? '#3AD3C1' : '#B0B3BF' }}
            onClick={confirmUpload}
          >
            确认上传
          </Button>
        </div>
      </div>
    </div>
  );
};

export default connect(({ caseMailing }: { caseMailing: CaseMailingState }) => ({
  caseMailing,
}))(CaseMailingAgentData);
