.agentData {
  width: 100%;
  height: 100%;
  background-color: #f5f6fa;
  padding-bottom: 150px;

  .agentDataWrap {
    padding-top: 48px;

    .title {
      display: flex;
      align-items: center;
      font-size: 36px;
      font-weight: 600;
      color: #03081a;
      padding-left: 24px;

      .line {
        margin-right: 16px;
        width: 8px;
      }
    }

    .cardItem {
      margin-top: 24px;
      width: 100%;
      min-height: 354px;
      padding: 24px 34px 32px;
      background-color: #fff;

      .uploadImg {
        width: 332px;
        margin-top: 24px;
        position: relative;

        .imgWrap {
          width: 332px;
          position: relative;

          .deleteIcon {
            position: absolute;
            width: 40px;
            height: 40px;
            right: -20px;
            top: -20px;
            object-fit: cover;
          }
        }

        .imagePicker {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          opacity: 0;
        }

        img {
          width: 100%;
          height: 230px;
          object-fit: cover;
        }
      }

      .label {
        width: 100%;
        display: flex;
        align-items: center;
        font-size: 32px;
        line-height: 46px;
        font-weight: 400;
        color: #989eb4;

        .img_wrap {
          height: 46px;
          display: flex;
          align-items: center;
          align-self: flex-start;
        }
        img {
          margin-right: 8px;
          width: 16px;
          height: 16px;
          object-fit: cover;
        }
      }
    }
  }

  .claim {
    margin-top: 48px;
    padding: 20px;
    padding-top: 32px;
    background-color: #fff;

    .claimTitle {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: 24px;

      .horizontalLine {
        width: 32px;
        height: 1px;
        background: #ccc;
      }

      .claimTitleText {
        margin: 0 30px;
        color: #03081a;
        font-weight: 500;
        font-size: 36px;
      }
    }

    .claimText {
      color: #989eb4;
      font-size: 28px;

      .noticeText {
        color: #32b9aa;
      }
    }

    .exampleImgs {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 40px;

      img {
        width: 150px;
        height: 108px;
      }

      .exampleImgsText {
        color: #333;
        font-size: 24px;
        text-align: center;
      }
    }

    .button {
      // padding-top: 200px;
      background-color: #fff;
      position: fixed;
      width: 100%;
      left: 0;
      bottom: 0;
      padding: 16px 24px;
      box-shadow: 0px -4px 8px 0px rgba(152, 158, 180, 0.1);
      padding-bottom: calc(16px + constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
      padding-bottom: calc(16px + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
    }
  }
}
