import React, { useEffect, useState } from 'react';
import { Checkbox } from 'antd';
import { Button, Toast } from 'antd-mobile';
import { CheckboxChangeEvent } from 'antd/lib//checkbox/Checkbox';
import { connect, Dispatch, Loading, history } from 'umi';
import { HxIndicator } from '@/components';
import styles from './index.less';

interface MedicalRecordNoticeProps {
  dispatch: Dispatch;
  loading: Boolean | undefined;
}

const MedicalRecordNotice: React.FC<MedicalRecordNoticeProps> = (props) => {
  // props
  const { dispatch, loading } = props;

  // state
  const [time, setTime] = useState(4);
  const [checked, setChecked] = useState(false);
  const [notice, setNotice] = useState('');

  const doCountdown = () => {
    setTimeout(() => {
      setTime((pre) => pre - 1);
    }, 1000);
  };

  const fetchData = () => {
    dispatch({
      type: 'common/queryText',
      payload: {
        textCode: 'CASE_MAIL_NOTICE',
      },
      callback: (text: string) => {
        setNotice(text);
      },
    });
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    notice && time > 0 && doCountdown();
  }, [time, notice]);

  const onChangeCheck = (e: CheckboxChangeEvent) => {
    setChecked(e.target.checked);
  };

  const onSure = () => {
    if (checked) {
      history.push('/common/businesscardlist?from=BAYJ');
    } else {
      Toast.info('请勾选已阅读并同意《病案邮寄》须知');
    }
  };

  return (
    <div className={styles.container}>
      {loading && <HxIndicator />}
      <div dangerouslySetInnerHTML={{ __html: notice }} />
      <div className={styles.check}>
        <Checkbox onChange={onChangeCheck} checked={checked}>
          已阅读并同意《线上预约病案复印服务》须知
        </Checkbox>
      </div>
      <Button type="primary" disabled={time > 0} onClick={onSure} style={{ color: '#ffffff' }}>
        {time > 0 ? `${time}秒` : '确定'}
      </Button>
    </div>
  );
};

export default connect(({ loading }: { loading: Loading }) => ({ loading: loading.effects['common/queryText'] }))(
  MedicalRecordNotice,
);
