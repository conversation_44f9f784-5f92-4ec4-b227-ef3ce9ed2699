.container {
  min-height: 100vh;
  padding: 20px;
  color: #333;
  background: #fff;

  .check {
    padding: 20px 0 40px 0;
    :global {
      .ant-checkbox-wrapper:hover .ant-checkbox-inner,
      .ant-checkbox:hover .ant-checkbox-inner,
      .ant-checkbox-input:focus + .ant-checkbox-inner {
        border-color: #32b9aa;
      }
      .ant-checkbox-checked .ant-checkbox-inner {
        background-color: #32b9aa;
      }
      .ant-checkbox-checked::after {
        border: 1px solid #32b9aa;
      }
    }
  }

  .button {
    width: 100%;
    height: 45px;
    background: #32b9aa;
    border-radius: 8px;
  }
}
