/** 住院单条记录数据类型 */
interface ResidentAdmitItemType {
  /** 记录ID */
  recordId: string;

  /** 机构名称 */
  organName: string;

  /** 病案号 */
  caseNo: string;

  /** 医生姓名 */
  docName: string;

  /** 科室名称 */
  deptName: string;

  /** 住院时间 */
  admitDate: string;
}

/** 住院记录数据类型 */
interface ResidentAdmitDataType {
  /** 患者年龄 */
  age?: string;

  /** 患者姓名 */
  patName?: string;

  /** 患者登记号 */
  patRegNo?: string;

  /** 患者性别 */
  patSex?: string;

  /** 住院记录列表 */
  residentAdmitList?: ResidentAdmitItemType[];

  /** 家属代办申请资料审核的ID  */
  applyRecordId?: string;
}

/** 复印目的数据类型 */
interface PurposeItemType {
  /** 目的code */
  code: string;

  /** 目的文案 */
  text: string;

  /** 目的的总页数 */
  totalPages: number;

  /** 目的描述 */
  projectDesc: string;
  purposeType?: any;
}

/** 邮寄种类列表 */
interface WayListItemType {
  /** 地址详情 */
  addrDetails: string;

  /** 地址总览 */
  address: string;

  /** 经纬度地址 */
  latitude: string;
  longitude: string;

  /** 邮寄种类 1: 线下 2: 线上 */
  wayCode: number;

  /** 邮寄种类描述 */
  wayDesc: string;

  /** 邮寄种类名称 */
  wayName: string;
}

/** 病案复印申请详情数据类型 */
interface CasePrintDetailType {
  /** 就诊卡号 */
  cardNo?: string;

  /** 病案号 */
  caseNo?: string;

  /** 复印单价 */
  copiesUnitPrice?: number;

  /** 最大复印数量 */
  maxCopies?: number;

  /** 患者名称 */
  patName?: string;

  /** 复印目的集合 */
  purposeList?: PurposeItemType[];

  /** 住院记录ID */
  recordId?: string;

  /** 复印总数 */
  totalCopies?: number;
  packageDesc?: string;
  freeDesc?: string;

  /** 邮寄种类列表 */
  wayLists?: WayListItemType[];
}

export interface CaseMailingState {
  /** 住院记录数据 */
  residentAdmitData: ResidentAdmitDataType;

  /** 住院记录申请详情 */
  casePrintDetail: CasePrintDetailType;
}
