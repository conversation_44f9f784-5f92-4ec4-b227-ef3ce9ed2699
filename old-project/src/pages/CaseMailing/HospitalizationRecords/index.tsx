import React, { useEffect, useState } from 'react';
import { connect, history, Dispatch, Loading } from 'umi';
import { StyleComponents, HxIndicator } from '@/components';
import { FaceCallBack } from '@/utils/useFace';
import styles from './index.less';
import { CaseMailingState } from '../data.d';

const { EmptyView } = StyleComponents;
const user_default_avater = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/user_default_avater.png';

const fieldList: {
  label: any;
  key: any;
}[] = [
  {
    label: '就诊医院：',
    key: 'organName',
  },
  {
    label: '主治医生：',
    key: 'docName',
  },
  {
    label: <span>科&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;室：</span>,
    key: 'deptName',
  },
  {
    label: '住院时间：',
    key: 'admitDate',
  },
];

interface HospitalizationRecordsProps {
  dispatch: Dispatch;
  location: {
    query: {
      /** 就诊卡ID  */
      cardId: string;
      /** 证件号 */
      credNo: string;
      /** 患者姓名 */
      name: string;
      /** 是否跳过人脸识别 */
      isJumpFace?: string;
      /** 家属代办资料审核的id  */
      applyRecordId?: string;
    };
  };
  caseMailing: CaseMailingState;
  loading: boolean | undefined;
}

const HospitalizationRecords: React.FC<HospitalizationRecordsProps> = (props) => {
  // props
  const {
    dispatch,
    loading,
    location: {
      query: { cardId, credNo, name, isJumpFace, applyRecordId: proxyApplyRecordId = '' },
    },
    caseMailing: {
      residentAdmitData: { age, patName, patRegNo, patSex, residentAdmitList = [], applyRecordId = '' } = {},
    } = {},
  } = props;

  // state
  const [hadpPassFaceVertify, setHadpPassFaceVertify] = useState(false);

  const fetchData = () => {
    dispatch({
      type: 'caseMailing/residentAdmitList',
      payload: {
        cardId,
        applyRecordId: proxyApplyRecordId,
      },
    });
  };

  /** 校验人脸识别结果 */
  const vertifyFacePass = () => {
    FaceCallBack({
      credNo,
      bussinessCode: 'medical-record-send',
      patientName: name,
      onSuccess: () => {
        // 人脸识别成功
        setHadpPassFaceVertify(true);
      },
      onFail: () => {
        // 人脸识别失败，返回就诊卡
        history.push({
          pathname: '/common/businesscardlist',
          query: {
            from: 'BAYJ',
          },
        });
      },
    });
  };

  useEffect(() => {
    if (isJumpFace) {
      // 如果跳过人脸识别，直接显示入院记录
      setHadpPassFaceVertify(true);
    } else {
      // 没跳过人脸识别则，获取人脸识别结果
      vertifyFacePass();
    }
  }, []);

  useEffect(() => {
    hadpPassFaceVertify && fetchData();
  }, [hadpPassFaceVertify]);

  /**
   * 进入病案复印申请页面
   * @param recordId 住院记录ID
   */
  const onSelect = (recordId: string) => {
    history.push({
      pathname: '/casemailing/application',
      query: {
        recordId,
        applyRecordId,
      },
    });
  };

  return (
    <div>
      {hadpPassFaceVertify && (
        <div className={styles.container}>
          {loading && <HxIndicator />}
          <div className={styles.basicInfo}>
            <div className={styles.userInfo}>
              <img src={user_default_avater} alt="" />
              <div>
                <div className={styles.patientName}>{patName}</div>
                <div className={styles.ageAndGender}>
                  {age && <span>{age}</span>}
                  <span>{patSex}</span>
                </div>
              </div>
            </div>
            {patRegNo && (
              <div>
                <span>登记号：</span>
                <span>{patRegNo}</span>
              </div>
            )}
          </div>
          {!!residentAdmitList.length && (
            <div className={styles.records}>
              {residentAdmitList.map((item) => (
                <div
                  key={item.recordId}
                  className={styles.record}
                  onClick={() => {
                    onSelect(item.recordId);
                  }}
                >
                  <div className={styles.recordTitle}>
                    <img
                      src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/case-mailing/medical_record_icon.png"
                      alt=""
                    />
                    <span className={styles.number}>病案号：{item.caseNo}</span>
                  </div>
                  <div className={styles.recordContent}>
                    {fieldList.map((fieldItem) => (
                      <div key={fieldItem.key} className={styles.fieldItem}>
                        <span className={styles.label}>{fieldItem.label}</span>
                        <span className={styles.value}>{item[fieldItem.key]}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
          {residentAdmitList.length === 0 && !loading && <EmptyView isNew text="暂无住院记录" />}
        </div>
      )}
    </div>
  );
};

export default connect(({ caseMailing, loading }: { caseMailing: CaseMailingState; loading: Loading }) => ({
  caseMailing,
  loading: loading.effects['caseMailing/residentAdmitList'],
}))(HospitalizationRecords);
