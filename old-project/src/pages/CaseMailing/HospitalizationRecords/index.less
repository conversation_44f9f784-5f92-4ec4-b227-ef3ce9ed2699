.container {
  background-color: #f2f2f2;
  .basicInfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40px 24px;
    color: #fff;
    background: #32b9aa;

    .userInfo {
      display: flex;
      align-items: center;
      justify-content: space-between;

      img {
        width: 88px;
        height: 88px;
        margin-right: 20px;
      }

      .patientName {
        font-weight: 600;
        font-size: 36px;
      }

      .ageAndGender {
        display: flex;
        justify-content: space-between;
      }
    }
  }

  .records {
    padding: 24px;

    .record {
      margin-bottom: 24px;
      padding: 24px;
      background-color: #fff;
      border-radius: 16px;

      .recordTitle {
        display: flex;
        flex-direction: row;
        align-items: center;
        img {
          width: 48px;
          height: 48px;
          margin-right: 20px;
        }

        .number {
          color: #333;
          font-weight: 600;
          font-size: 36px;
        }
      }

      .recordContent {
        .fieldItem {
          padding-top: 10px;
          .label {
            color: #333;
          }
          .value {
            color: #666;
          }
        }
      }
    }
  }
}
