import { Request, Response } from 'express';
import Mock from 'mockjs';
import { commonSuccessResponse } from '../../../mock/util';

// const errCodeObj = {
//   C1000: '该就诊卡对应的身份证信息错误!',
//   C1001: '年龄<18，无法使用病例邮寄业务!',
//   C1002: '该就诊卡对应的患者已死亡，无法使用病例邮寄业务!',
//   C1003: '改患者需要上传证件照信息!',
//   C1004: '需要上传5S患者本人视频录像!',
// };

const residentAdmitData = Mock.mock({
  'list|8-20': [
    {
      admitDate: '@date',
      caseNo: 'BA23634746',
      deptName: '神经外科',
      docName: '@cname',
      organName: '四川大学华西第四医院',
      recordId: '@id',
    },
  ],
});

// 病案复印详情
const casePrintDetails = {
  cardNo: 'JZK4214158833221',
  caseNo: 'BA44235590123',
  copiesUnitPrice: 1.0,
  patName: '李常超',
  freeDesc: '这是freeDesc',
  packageDesc: '这是packageDesc',
  purposeList: [
    {
      text: '司法诉讼（全套病历）',
      totalPages: 5,
      code: '1',
      projectDesc: '所有主客观病历',
      purposeType: 0,
    },
    {
      text: '特殊门诊',
      totalPages: 6,
      code: '2',
      projectDesc: '出院记录、大病历、检查检验、血糖血压、手术及麻醉、体温',
      purposeType: 0,
    },
    {
      text: '医保报销',
      totalPages: 7,
      code: '3',
      projectDesc: '出院记录、大病历、检查检验、血糖血压、手术及麻醉、体温',
      purposeType: 0,
    },
    {
      text: '看病复诊',
      totalPages: 8,
      code: '4',
      projectDesc: '出院记录、大病历、检查检验、血糖血压、手术及麻醉、体温',
      purposeType: 0,
    },
  ],
};

export default {
  'POST /cloud/hosplatcustomer/caseExpress/requireProcess': (req: Request, res: Response) => {
    // const errCode = 'C1003';
    setTimeout(() => {
      res.status(200).send({
        // code: '0',
        // errCode,
        // msg: errCodeObj[errCode],
        ...commonSuccessResponse,
      });
    }, 5000);
  },
  /** 查询住院记录 */
  'POST /cloud/hosplatcustomer/caseExpress/residentAdmitList': (req: Request, res: Response) => {
    setTimeout(() => {
      res.status(200).send({
        ...commonSuccessResponse,
        data: {
          age: '30',
          patName: '李常超',
          patRegNo: 'DJ3248054334',
          patSex: '男',
          residentAdmitList: residentAdmitData.list,
        },
      });
    }, 500);
  },
  /** 住院记录申请详情 */
  'POST /cloud/hosplatcustomer/caseExpress/casePrintDetails': (req: Request, res: Response) => {
    setTimeout(() => {
      res.status(200).send({
        ...commonSuccessResponse,
        data: casePrintDetails,
      });
    }, 500);
  },
  /** 校验用户地址快递是否可达 */
  'POST /cloud/hosplatcustomer/caseExpress/requireUserAddress': (req, res: Response) => {
    res.status(200).send({
      ...commonSuccessResponse,
    });
  },
  /** 确认病历复印申请 */
  'POST /cloud/hosplatcustomer/caseExpress/confirmApply': (req, res: Response) => {
    res.status(200).send({
      ...commonSuccessResponse,
    });
  },
  /** 申请人修改证件照 */
  'POST /cloud/hosplatcustomer/caseExpress/updateIdImages': (req, res: Response) => {
    setTimeout(() => {
      res.status(200).send({
        ...commonSuccessResponse,
      });
    }, 500);
  },
};
