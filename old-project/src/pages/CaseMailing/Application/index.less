.container {
  background: #f5f6fa;

  .commonStyle {
    margin-bottom: 24px;
    padding: 24px;
    color: #333;
    background-color: #fff;
    border-radius: 8px;
  }

  .rightIcon {
    width: 32px;
    height: 32px;
    margin-left: 8px;
  }

  .addressInfo {
    position: relative;
    margin-bottom: 24px;
    background-color: #fff;

    .addressData {
      padding: 30px 24px 24px;

      .addressBox {
        display: flex;
        align-items: center;

        & span {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 58px;
          height: 28px;
          margin-right: 8px;
          color: #fff;
          font-size: 20px;
          line-height: 28px;
          background: #3ad3c1;
          border-radius: 8px;
        }
        & div {
          color: #03081a;
          font-size: 24px;
          line-height: 34px;
        }
      }

      .userInfo {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin: 12px 0;
        font-weight: 500;
        font-size: 36px;

        & span {
          color: #03081a;
        }
      }

      .detailAddress {
        color: #03081a;
        font-size: 24px;
      }
    }
  }

  .addAddress {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 0 0 8px 8px;
    .iconandText {
      display: flex;
      align-items: center;
      .addressIcon {
        width: 40px;
        height: 40px;
        margin-right: 20px;
      }

      .text {
        color: #313133;
        font-weight: 500;
        font-size: 36px;
      }
    }
  }

  .recordInfo {
    display: flex;
    align-items: center;

    img {
      width: 80px;
      height: 80px;
      margin-right: 20px;
    }

    .number {
      color: #03081a;
      font-weight: 500;
    }
  }

  .reasons {
    padding-bottom: 24px;
    background-color: #f2f2f2;

    .title {
      padding: 24px 24px 0 24px;
      background-color: #fff;

      .titleText {
        margin-right: 20px;
        color: #333;
        font-weight: 500;
        font-size: 36px;
      }

      .titleNotice {
        color: #999;
        font-size: 24px;
      }
    }

    .reasonList {
      .reasonItemBorder {
        border-bottom: 1px solid #ebedf5;
      }

      .reasonItem {
        background-color: #fff;

        .reasonItemTitle {
          display: flex;
          padding: 24px;

          .reasonItemTitleText {
            margin-top: -4px;
          }

          .circleIcon {
            width: 40px;
            height: 40px;
            flex-shrink: 0;
          }

          .selectedIcon {
            color: #32b9aa;
            font-size: 40px;
          }

          .reasonItemText {
            margin-left: 10px;
            color: #03081a;
            font-size: 32px;
          }

          .projectDesc {
            margin-left: 10px;
            color: #999;
          }
        }

        .copyInfoLast {
          margin-bottom: 0;
        }

        .copyInfo {
          padding: 24px;
          background: #f5f6fa;
          border-radius: 8px;

          .subtotal {
            text-align: right;

            .subtotalPrice {
              color: #fc4553;
            }
          }
        }
      }
    }
  }

  .info {
    margin-bottom: 24px;
    padding-bottom: 0;
    color: #333;
    .infoItem {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 24px;

      .numberButton {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 192px;
        height: 52px;
        padding: 0 10px;
        background: #fff;
        border: 2px solid #f2f2f2;
        border: 1px solid #ebedf5;
        border-radius: 26px;

        .addButton {
          color: #32b9aa;
        }

        .numberText {
          color: #333;
          font-weight: 600;
        }
      }
    }
  }

  .bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0;
    padding-bottom: 80px;
    border-radius: 0;
    .priceUnit {
      color: #fc4553;
      font-weight: 600;
    }

    .price {
      color: #fc4553;
      font-weight: 600;
      font-size: 36px;
    }

    .button {
      width: 272px;
      height: 90px;
      line-height: 90px;
      border-radius: 90px;
    }
  }
}

.contentBox {
  position: relative;
  width: 100%;
  padding: 0 24px;
  overflow: hidden;
  background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/case-mailing/application_header_bg.png')
    no-repeat top center;

  .tabBox {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    width: 100%;
    margin: 24px 0 0;
    padding: 0;
    overflow: hidden;

    & div {
      position: relative;
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: center;
      height: 66px;
      color: #03081a;
      font-size: 28px;
      list-style-type: none;
      background: #d8f6f3;
    }
    & div:first-child {
      border-radius: 8px 8px 0 0;
    }
    & div:last-child {
      border-radius: 8px 8px 0 0;
    }
    & div.active {
      height: 90px;
      font-weight: bold;
      font-size: 32px;
      background: #fff;

      &::after {
        position: absolute;
        z-index: 9;
        display: block;
        border-style: solid;
        content: '';
      }
      &::before {
        position: absolute;
        top: 0;
        z-index: 9;
        display: block;
        width: 0;
        height: 90px;
        background: #fff;
        content: '';
      }
    }
    & div:first-child.active {
      &::after {
        bottom: 0;
        left: calc(100% - 1px);
        border-color: transparent #fff;
        border-width: 90px 0 0 45px;
      }
      &::before {
        left: calc(100% - 1px);
      }
    }
    & div:last-child.active {
      &::after {
        right: calc(100% - 1px);
        bottom: 0;
        border-color: #fff transparent;
        border-width: 0 0 90px 45px;
      }
      &::before {
        right: calc(100% - 1px);
      }
    }
  }
}

.infoWrap {
  padding: 24px;
  background: #fff;
}

.offlineBox {
  min-height: 224px;
  padding-right: 224px !important;

  .offlineMap {
    position: absolute;
    top: 24px;
    right: 24px;
    width: 176px;
    height: 176px;
    background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/case-mailing/offline_map.png') no-repeat
      center center;

    & span {
      z-index: 9;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 80%;
      height: 40px;
      margin: 40px auto;
      color: #03081a;
      font-size: 20px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 0 8px 0 rgba(58, 211, 193, 0.35);
    }
  }
}

.addressBottomIcon {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 4px;
}
