import React, { useEffect, useState } from 'react';
import classnames from 'classnames';
import { Button, Toast } from 'antd-mobile';
import { CheckCircleFilled } from '@ant-design/icons';
import { history, connect, Dispatch } from 'umi';
import Bignumber from 'bignumber.js';
import { IAddrerssItem } from '@/pages/Address/data.d';
import { getNoTokenHref } from '@/utils/tool';
import { SingWechatJSSDKDataType } from '@/typings/global';
import { isWechat } from '@/utils/platform';
import styles from './index.less';
import { CaseMailingState } from '../data.d';

const address_icon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/address_icon.png';
const new_right_icon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/new_right_icon.png';
const medical_record_icon =
  'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/case-mailing/medical_record_icon.png';
const circle_icon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/circle_icon.png';
const address_bottom_icon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/address_bottom_icon.png';

interface ReasonItemType {
  /** 唯一key值 */
  key: any;

  /** 原因标题 */
  title: string;

  /** 原因对应的页数 */
  pageNum: number;

  /** 复制份数 */
  copyNumber: number;

  /** 与后端定义code保持一致 */
  code: string;

  /** 目的描述 */
  projectDesc: string;

  /** 是否选中 */
  checked: boolean;
  /** 1套餐 2自选 */
  purposeType: number;
}

interface ApplicationProps {
  location: {
    query: {
      /** 住院记录ID */
      recordId: string;
      /** 地址信息（需要JSON.parse解析） */
      data?: string;
      /** 用户之前选择的目的信息【重定向需要带回来的参数】（需要JSON.parse解析） */
      reasons?: string;
      /** 家属代办资料上传后生成的recordId  */
      applyRecordId?: string;
    };
  };
  caseMailing: CaseMailingState;
  dispatch: Dispatch;
}

const Application: React.FC<ApplicationProps> = (props) => {
  // props
  const {
    location: { query },
    dispatch,
    caseMailing: {
      casePrintDetail: { purposeList, caseNo, patName, cardNo, copiesUnitPrice = 0, freeDesc, packageDesc, wayLists },
    },
  } = props;
  const { data, recordId, reasons = null, applyRecordId = '' } = query;
  // 地址相关信息
  const {
    contactUserName = '',
    phone = '',
    detailAddress = '',
    addressId,
    provinceName = '',
    cityName = '',
    areaName = '',
    defaultAddress = '',
  }: IAddrerssItem = data ? JSON.parse(data) : {};
  const { wx } = window;

  // state
  const [reasonList, setReasonList] = useState<ReasonItemType[]>([]);
  const [totalPrice, setTotalPrice] = useState(0);
  const [currentTab, setCurrentTab] = useState(2);
  const [offlineDistance, setOfflineDistance] = useState(0);

  const fetchData = () => {
    dispatch({
      type: 'caseMailing/casePrintDetails',
      payload: { recordId },
    });
  };

  // 经纬度转换成三角函数中度分表形式。
  const Rad = (d: any) => {
    return (d * Math.PI) / 180.0;
  };

  /** 经纬度换算距离 */
  const GetDistance = (lat1: any, lng1: any, lat2: any, lng2: any) => {
    const radLat1 = Rad(lat1);
    const radLat2 = Rad(lat2);
    const a = radLat1 - radLat2;
    const b = Rad(lng1) - Rad(lng2);
    const s =
      2 *
      Math.asin(
        // eslint-disable-next-line no-restricted-properties
        Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)),
      );
    return Number((Math.round(s * 6378.137 * 10000) / 10000).toFixed(1));
  };

  /** 获取微信定位 */
  const fetchWxLocation = (configData: any) => {
    const { debug, appId, timestamp, nonceStr, signature, jsApiList = [] } = configData;
    wx.config({
      debug,
      appId,
      timestamp,
      nonceStr,
      signature,
      jsApiList,
    });

    wx.ready(() => {
      wx.getLocation({
        type: 'wgs84', // 默认为wgs84的gps坐标，如果要返回直接给openLocation用的火星坐标，可传入'gcj02'
        success: (res: any) => {
          console.log(res);
          console.log(wayLists);
          // const latitude = '30.583433';
          // const longitude = '104.06263';
          const offlineObj = wayLists?.find((item: any) => item?.wayCode === 1);
          setOfflineDistance(GetDistance(res?.latitude, res?.longitude, offlineObj?.latitude, offlineObj?.longitude));
        },
      });
    });

    wx.error((err: any) => {
      console.log('configWechatShareStyle-err', err);
    });
  };

  /** 获取微信JSSDK */
  const fetchWxJSDK = () => {
    const link = getNoTokenHref();
    dispatch({
      type: 'global/singWechatJSSDK',
      payload: {
        url: link,
      },
      callback: (data: SingWechatJSSDKDataType) => {
        const configData = {
          ...data,
          debug: false,
          jsApiList: ['getLocation'],
        };
        fetchWxLocation(configData);
      },
    });
  };

  useEffect(() => {
    fetchData();
    isWechat() && fetchWxJSDK();
  }, []);

  useEffect(() => {
    if (reasons) {
      setReasonList(JSON.parse(reasons));
    }
  }, [reasons]);

  /** 将复印目的转为前端数据格式 */
  useEffect(() => {
    // 如果路由中有上一次选的内容则不做操作
    if (reasons) return;
    const reasonListInit = purposeList
      ? purposeList?.map((item, index) => ({
          key: index,
          title: item.text,
          pageNum: item.totalPages,
          copyNumber: 1,
          code: item.code,
          checked: false,
          projectDesc: item.projectDesc,
          purposeType: item.purposeType,
        }))
      : [];
    setReasonList(reasonListInit);
  }, [purposeList, reasons]);

  /** 计算总价 */
  useEffect(() => {
    const newTotalPrice = reasonList.reduce((accumulator, currentValue) => {
      if (currentValue.checked) {
        const currentBigNumber = new Bignumber(copiesUnitPrice)
          .multipliedBy(currentValue.pageNum * currentValue.copyNumber)
          .toNumber();

        return new Bignumber(accumulator).plus(currentBigNumber).toNumber();
      }
      return accumulator;
    }, 0);
    setTotalPrice(newTotalPrice);
  }, [reasonList]);

  /**
   * 点击原因的回调
   * @param reasonKey 点击的原因的key值
   */
  const onClickReason = (reasonKey: any) => {
    const checked = reasonList.find((item) => item.key === reasonKey)?.checked;
    setReasonList((pre) =>
      pre.map((preItem) => {
        if (preItem.key === reasonKey) {
          return {
            ...preItem,
            copyNumber: 1, // 只要点击，复印份数都为初始值
            checked: !checked,
          };
        }
        return preItem;
      }),
    );
  };

  /**
   * 改变原因的复印份数
   * @param type 改变类型：加法/减法
   * @param reasonKey 操作的原因的key
   */
  const changeCopyNumber = (type: 'add' | 'sub', reasonKey: any) => {
    // 至少复印一份
    if (type === 'sub' && reasonList.find((item) => item.key === reasonKey)?.copyNumber <= 1) return;
    setReasonList((pre) =>
      pre.map((preItem) => {
        const newCopyNumber = type === 'sub' ? preItem.copyNumber - 1 : preItem.copyNumber + 1;
        if (preItem.key === reasonKey) {
          return {
            ...preItem,
            copyNumber: newCopyNumber,
          };
        }
        return preItem;
      }),
    );
  };

  /** 添加地址 */
  const onAddAddress = () => {
    history.push({
      pathname: '/common/businessaddress',
      query: {
        ...query,
        to: 'SQBLFY',
        reasons: JSON.stringify(reasonList),
      },
    });
  };

  const onNext = () => {
    if (currentTab === 2 && !data) {
      Toast.info('请选择收件地址');
      return;
    }
    if (!reasonList.filter((item) => item.checked).length) {
      Toast.info('请选择复印目的');
      return;
    }
    // 进入病案邮寄上传证件照页面【上传完后进入病历订单页面，并将确认订单接口的入参传递到下个页面】
    // queryData表示调confirmApply接口需要的入参
    const queryData = {
      addressId,
      recordId,
      applyRecordId,
      wayCode: currentTab,
      items: reasonList
        .filter((item) => item.checked)
        .map((item) => ({
          copies: item.copyNumber,
          purposeCode: item.code,
          totalPages: item.pageNum,
        })),
    };
    history.push({
      pathname: '/common/uploadidcard',
      query: {
        to: 'BLDD',
        queryData: JSON.stringify(queryData),
      },
    });
  };

  /** 切换tab */
  const switchTab = (index: number) => {
    if (currentTab === index) {
      return;
    }
    setCurrentTab(index);
  };

  return (
    <div className={styles.container}>
      <div className={styles.contentBox}>
        {/* 邮寄模块：分为线上邮寄和线下自取 */}
        {!!wayLists?.length && (
          <div className={styles.tabBox}>
            {wayLists?.map((item, index) => (
              <div
                key={index.toString()}
                className={currentTab === item?.wayCode ? styles.active : ''}
                onClick={() => switchTab(item?.wayCode)}
              >
                {item?.wayName}
              </div>
            ))}
          </div>
        )}
        {/* 地址模块：线上邮寄已选地址 */}
        {currentTab === 2 && data && (
          <div className={classnames(styles.addressInfo)} onClick={onAddAddress}>
            <div className={styles.addressData}>
              <div className={styles.addressBox}>
                {defaultAddress === '1' && <span>默认</span>}
                <div>
                  {provinceName}
                  {cityName}
                  {areaName}
                </div>
              </div>
              <div className={styles.userInfo}>
                <span>{detailAddress}</span>
                <div>
                  <img className={styles.rightIcon} src={new_right_icon} alt="" />
                </div>
              </div>
              <div className={styles.detailAddress}>
                {contactUserName}
                {phone.split('').fill('*', 3, 7)}
              </div>
            </div>
            <img className={styles.addressBottomIcon} src={address_bottom_icon} alt="" />
          </div>
        )}
        {/* 地址模块：线上邮寄未选地址 */}
        {currentTab === 2 && !data && (
          <div className={styles.addressInfo}>
            <div className={classnames(styles.commonStyle, styles.addAddress)} onClick={onAddAddress}>
              <div className={styles.iconandText}>
                <img className={styles.addressIcon} src={address_icon} alt="" />
                <span className={styles.text}>添加收货地址</span>
              </div>
              <img className={styles.rightIcon} src={new_right_icon} alt="" />
            </div>
            <img className={styles.addressBottomIcon} src={address_bottom_icon} alt="" />
          </div>
        )}
        {/* 地址模块：线下自取地址 */}
        {currentTab === 1 && (
          <div className={classnames(styles.addressInfo)}>
            <div className={classnames(styles.addressData, offlineDistance && styles.offlineBox)}>
              <div className={styles.addressBox}>
                <div>{wayLists?.find((item) => item.wayCode === 1)?.address}</div>
              </div>
              <div className={styles.userInfo}>
                <span>{wayLists?.find((item) => item.wayCode === 1)?.addrDetails}</span>
              </div>
              {offlineDistance ? (
                <div className={styles.offlineMap}>
                  <span>距您{offlineDistance}km</span>
                </div>
              ) : (
                ''
              )}
            </div>
          </div>
        )}
        {/* 病案号 */}
        <div className={classnames(styles.commonStyle, styles.recordInfo)}>
          <img src={medical_record_icon} alt="" />
          <div className={styles.number}>病案号：{caseNo}</div>
        </div>
        {/* 就诊卡信息 */}
        <div className={classnames(styles.commonStyle, styles.info)}>
          <div className={styles.infoItem}>
            <span>就诊卡姓名</span>
            <span>{patName}</span>
          </div>
          <div className={styles.infoItem}>
            <span>就诊卡号</span>
            <span>{cardNo}</span>
          </div>
        </div>
        {/* 选择复印目的 */}
        <div className={styles.reasons}>
          <div className={styles.title}>
            <span className={styles.titleText}> 按目的选择复印套餐</span>
            <span className={styles.titleNotice}>支持多选</span>
            <div>{packageDesc}</div>
          </div>
          <div className={styles.reasonList}>
            {reasonList.map((item, index) => {
              return (
                item?.purposeType === 1 && (
                  <div key={item.key} className={styles.reasonItem}>
                    <div
                      className={classnames(
                        styles.reasonItemTitle,
                        index !== reasonList.length - 1 && styles.reasonItemBorder,
                      )}
                      onClick={() => {
                        onClickReason(item.key);
                      }}
                    >
                      {item.checked ? (
                        <CheckCircleFilled className={styles.selectedIcon} />
                      ) : (
                        <img className={styles.circleIcon} src={circle_icon} alt="" />
                      )}
                      <div className={styles.reasonItemTitleText}>
                        <div className={styles.reasonItemText}>{item.title}</div>
                        <div className={styles.projectDesc}>{item.projectDesc}</div>
                      </div>
                    </div>
                    {item.checked && (
                      <div className={styles.infoWrap}>
                        <div
                          className={classnames(
                            styles.info,
                            styles.copyInfo,
                            index === reasonList.length - 1 && styles.copyInfoLast,
                          )}
                        >
                          <div className={styles.infoItem}>
                            <span>复印份数</span>
                            <div className={styles.numberButton}>
                              <span
                                style={{ color: item.copyNumber > 1 ? '#32B9AA' : '#999999' }}
                                onClick={() => {
                                  changeCopyNumber('sub', item.key);
                                }}
                              >
                                -
                              </span>
                              <span className={styles.numberText}>{item.copyNumber}</span>
                              <span
                                className={styles.addButton}
                                onClick={() => {
                                  changeCopyNumber('add', item.key);
                                }}
                              >
                                +
                              </span>
                            </div>
                          </div>
                          <div className={styles.infoItem}>
                            <span>页码总数</span>
                            <span>{item.pageNum} 页</span>
                          </div>
                          <div className={styles.infoItem}>
                            <span>单页金额</span>
                            <span>￥{copiesUnitPrice}</span>
                          </div>
                          <div className={styles.subtotal}>
                            <span>小计：</span>
                            <span className={styles.subtotalPrice}>
                              ￥{new Bignumber(copiesUnitPrice).multipliedBy(item.pageNum * item.copyNumber).toNumber()}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )
              );
            })}
            <div
              style={{ backgroundColor: '#fff', padding: '3.2vw', fontSize: '4.8vw', fontWeight: 500, color: '#333' }}
            >
              {freeDesc}
            </div>
            {reasonList.map((item, index) => {
              return (
                item?.purposeType === 2 && (
                  <div key={item.key} className={styles.reasonItem}>
                    <div
                      className={classnames(
                        styles.reasonItemTitle,
                        index !== reasonList.length - 1 && styles.reasonItemBorder,
                      )}
                      onClick={() => {
                        onClickReason(item.key);
                      }}
                    >
                      {item.checked ? (
                        <CheckCircleFilled className={styles.selectedIcon} />
                      ) : (
                        <img className={styles.circleIcon} src={circle_icon} alt="" />
                      )}
                      <div className={styles.reasonItemTitleText}>
                        <div className={styles.reasonItemText}>{item.title}</div>
                        <div className={styles.projectDesc}>{item.projectDesc}</div>
                      </div>
                    </div>
                    {item.checked && (
                      <div className={styles.infoWrap}>
                        <div
                          className={classnames(
                            styles.info,
                            styles.copyInfo,
                            index === reasonList.length - 1 && styles.copyInfoLast,
                          )}
                        >
                          <div className={styles.infoItem}>
                            <span>复印份数</span>
                            <div className={styles.numberButton}>
                              <span
                                style={{ color: item.copyNumber > 1 ? '#32B9AA' : '#999999' }}
                                onClick={() => {
                                  changeCopyNumber('sub', item.key);
                                }}
                              >
                                -
                              </span>
                              <span className={styles.numberText}>{item.copyNumber}</span>
                              <span
                                className={styles.addButton}
                                onClick={() => {
                                  changeCopyNumber('add', item.key);
                                }}
                              >
                                +
                              </span>
                            </div>
                          </div>
                          <div className={styles.infoItem}>
                            <span>页码总数</span>
                            <span>{item.pageNum} 页</span>
                          </div>
                          <div className={styles.infoItem}>
                            <span>单页金额</span>
                            <span>￥{copiesUnitPrice}</span>
                          </div>
                          <div className={styles.subtotal}>
                            <span>小计：</span>
                            <span className={styles.subtotalPrice}>
                              ￥{new Bignumber(copiesUnitPrice).multipliedBy(item.pageNum * item.copyNumber).toNumber()}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )
              );
            })}
          </div>
        </div>
      </div>
      {/* 结算按钮 */}
      {!!reasonList.filter((reasonItem) => reasonItem.checked).length && (
        <div className={classnames(styles.commonStyle, styles.bottom)}>
          <div>
            <span>合计：</span>
            <span className={styles.priceUnit}>￥</span>
            <span className={styles.price}>{totalPrice}</span>
          </div>
          <Button className={styles.button} type="primary" size="small" style={{ color: '#ffffff' }} onClick={onNext}>
            下一步
          </Button>
        </div>
      )}
    </div>
  );
};

export default connect(({ caseMailing }: { caseMailing: CaseMailingState }) => ({ caseMailing }))(Application);
