import request from '@/utils/request';

/** 检查就诊卡状态 */
export const requireProcess = async (data: {
  /** 就诊卡is */
  cardId: string;
}) =>
  request('/cloud/hosplatcustomer/caseExpress/requireProcess', {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
      skipError: true,
    },
    prefix: API_HXYY_NEW,
  });

/** 查询住院记录 */
export const residentAdmitList = async (data: { cardId: string }) =>
  request('/cloud/hosplatcustomer/caseExpress/residentAdmitList', {
    method: 'POST',
    data,
    prefix: API_HXYY_NEW,
  });

/** 代办上传信息 */
export const confirmProxyApply = async (data: any) =>
  request('/cloud/hosplatcustomer/caseExpress/confirmProxyApply', {
    method: 'POST',
    data: { ...data, showOriginData: true },
    prefix: API_HXYY_NEW,
  });

/** 住院记录申请详情 */
export const casePrintDetails = async (data: {
  /** 记录ID */
  recordId: string;
}) =>
  request('/cloud/hosplatcustomer/caseExpress/casePrintDetails', {
    method: 'POST',
    data,
    prefix: API_HXYY_NEW,
  });

/** 校验用户地址快递是否可达 */
export const requireUserAddress = async (data: {
  /** 地址ID */
  addressId: string;
}) =>
  request('/cloud/hosplatcustomer/caseExpress/requireUserAddress', {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
    prefix: API_HXYY_NEW,
  });

/** 确认病历复印申请入参数据类型 */
export interface ConfirmApplyQueryType {
  /** 病案邮寄类型 */
  wayCode?: number;
  /** 地址ID */
  addressId: string;
  /** 住院记录ID */
  recordId: string;
  /** 复印明细 */
  items: {
    /** 复印份数 */
    copies: number;
    /** 复印code */
    purposeCode: string;
    /** 总页数 */
    totalPages: number;
  }[];
  /** 身份证正反面照 */
  idImages: {
    /** 证件照url */
    imagesUrl: string;
    /** 类型 */
    type: 'front' | 'back';
  }[];
}

/** 确认病历复印申请 */
export const confirmApply = async (data: ConfirmApplyQueryType) =>
  request('/cloud/hosplatcustomer/caseExpress/confirmApply', {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
    prefix: API_HXYY_NEW,
  });

interface UpdateIdImagesQueryType {
  /** 申请ID */
  recordId: string;
  /** 身份证正反面照 */
  idImages: {
    /** 证件照url */
    imagesUrl: string;
    /** 类型 */
    type: 'front' | 'back';
  }[];
}

/** 申请人修改证件照 */
export const updateIdImages = async (data: UpdateIdImagesQueryType) =>
  request('/cloud/hosplatcustomer/caseExpress/updateIdImages', {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
    prefix: API_HXYY_NEW,
  });

/**
 * 家属代办确认病历复印申请
 * @param {UpdateIdImagesQueryType} data
 * @return {*}
 */
export const saveProxyApplyRecord = async (data: UpdateIdImagesQueryType) =>
  request('/cloud/hosplatcustomer/caseExpress/saveProxyApplyRecord', {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
    prefix: API_HXYY_NEW,
  });
