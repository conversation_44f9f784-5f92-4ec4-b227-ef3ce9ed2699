import { Reducer, Effect } from 'umi';
import { CaseMailingState } from './data.d';
import {
  requireProcess,
  residentAdmitList,
  casePrintDetails,
  requireUserAddress,
  confirmApply,
  updateIdImages,
  saveProxyApplyRecord,
} from './service';

interface ModelType {
  namespace: string;

  state: CaseMailingState;

  effects: {
    /** 检查就诊卡状态 */
    requireProcess: Effect;
    /** 查询住院记录 */
    residentAdmitList: Effect;
    /** 病案复印申请页详情 */
    casePrintDetails: Effect;
    /** 校验用户地址快递是否可达 */
    requireUserAddress: Effect;
    /** 确认病历复印申请 */
    confirmApply: Effect;
    /** 申请人修改证件照 */
    updateIdImages: Effect;
    /** 家属代办申请  */
    saveProxyApplyRecord: Effect;
  };

  reducers: {
    updateState: Reducer;
  };
}

const Model: ModelType = {
  namespace: 'caseMailing',

  state: {
    residentAdmitData: {},
    casePrintDetail: {},
  },

  effects: {
    *requireProcess({ payload, callback }, { call }) {
      const res = yield call(requireProcess, payload);
      res && callback && callback(res);
    },
    *residentAdmitList({ payload }, { call, put }) {
      const data = yield call(residentAdmitList, payload);
      yield put({
        type: 'updateState',
        payload: {
          residentAdmitData: data,
        },
      });
    },
    *casePrintDetails({ payload }, { call, put }) {
      const data = yield call(casePrintDetails, payload);
      yield put({
        type: 'updateState',
        payload: {
          casePrintDetail: data,
        },
      });
    },
    *requireUserAddress({ payload, callback }, { call }) {
      const res = yield call(requireUserAddress, payload);
      const { code } = res || {};
      code === '1' && callback && callback();
    },
    *confirmApply({ payload, callback }, { call }) {
      const res = yield call(confirmApply, payload);
      const { code } = res || {};
      code === '1' && callback && callback();
    },
    *saveProxyApplyRecord({ payload, callback }, { call }) {
      const res = yield call(saveProxyApplyRecord, payload);
      const { code } = res || {};
      code === '1' && callback && callback();
    },
    *updateIdImages({ payload, callback }, { call }) {
      const res = yield call(updateIdImages, payload);
      const { code } = res || {};
      code === '1' && callback && callback();
    },
  },

  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};

export default Model;
