.caseTypeContainer {
  width: 100%;
  height: 100%;
  padding: 32px 24px 0;
  background-color: #f5f6fa;

  .hint {
    font-size: 32px;
    font-weight: 500;
    color: #989eb4;
  }

  .cardWrap {
    .cardItem {
      margin-top: 32px;
      overflow: hidden;
      min-height: 266px;
      background-color: #fff;
      border-radius: 16px;

      &.selfCard {
        .cardHeader {
          background-color: #3ad3c1;
        }
      }

      &.famliyTo {
        .cardHeader {
          background-color: #568df2;
        }
      }

      .cardHeader {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 32px;
        height: 82px;
        border-radius: 16px 16px 0 0;
        color: #fff;

        h2 {
          color: #fff;
          margin: 0;
          padding: 0;
          font-size: 36px;
          font-weight: 600;
        }

        .option {
          display: flex;
          align-items: center;
          font-size: 32px;
        }
      }

      .content {
        padding: 24px 32px;
        min-height: 92px;
        font-size: 24px;
        color: #989eb4;
      }

      .auditWrap {
        width: 172px;
        height: 52px;
        border-radius: 26px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 24px;
        margin-bottom: 24px;

        &.pass {
          color: #1ac3af;
          background-color: #e4f8f6;
        }

        &.noPass {
          color: #fc4553;
          background-color: #fbe9ea;
        }

        &.inReview {
          color: #f7a70e;
          background-color: #fff4df;
        }
      }
    }
  }
}
