import React, { useEffect, useState } from 'react';
import { connect, Dispatch, Loading, history } from 'umi';
import classnames from 'classnames';
import { HxIcon } from '@/components';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { Modal } from 'antd-mobile';
import { HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import { getToken, getOrganCode, getChannelCode } from '@/utils/parameter';
import { Face } from '@/utils/useFace';
import { CaseMailingState } from '../data.d';
import styles from './index.less';
import { APPLY_STATUS } from '../dataDictionary';

interface IProps {
  dispatch: Dispatch;
  loading: Boolean | undefined;
  location: {
    query: {
      cardId?: string;
      data: string;
    };
  };
}

const CaseMailingType: React.FC<IProps> = (props) => {
  const {
    location: {
      query: { cardId = '', data },
    },
    dispatch,
  } = props;
  const _data = JSON.parse(data);
  const [pageData] = useState(_data);

  const { oneself = {}, proxy = {} } = pageData;
  /* 提示文案 */
  const [hint, setHint] = useState<any>([]);

  /* 通过就诊卡查询当前卡是否有病案 */
  const fetchRequireProcess = () => {
    dispatch({
      type: 'caseMailing/requireProcess',
      payload: {
        cardId,
      },
      callback: (res: any) => {
        console.log('res:', res);
      },
    });
  };

  /**
   * 获取提示文案
   * CASE_MAIL_HANDLE_WAY_PROXY //代办 CASE_MAIL_HANDLE_WAY_ONESELF // 本人办理
   */
  const fetchData = () => {
    dispatch({
      type: 'common/queryTextList',
      payload: {
        textCodes: ['CASE_MAIL_HANDLE_WAY_PROXY', 'CASE_MAIL_HANDLE_WAY_ONESELF'],
      },
      callback: (text: any) => {
        setHint(text ?? []);
      },
    });
  };

  /**
   * 选择本人办理
   * @return {*}
   */
  const handleOneself = async () => {
    /* 卡异常 未满18 患者死亡不允许走本人办理 */
    if (!oneself?.allow) return;

    /* 后台判断是否需要人脸识别 */
    const { veriface = true } = oneself;

    /* 本人办理直接走人脸识别 */
    const cardInfo = HxSessionStorage.get(StorageEnum.BIZ_PATIENTCARD_DATA);
    const { credNo = '', patientName = '', credType = '01' } = cardInfo || {};

    /* 不需要人脸识别 */
    if (!veriface) {
      /* 资料审核通过 */
      history.push({
        pathname: '/casemailing/hospitalizationrecords',
        query: {
          cardId,
          credNo,
          name: patientName,
          isJumpFace: true,
        },
      });
      return;
    }

    /* 人脸识别 */
    const callBackUrl = `${THE_DOMAIN}/casemailing/hospitalizationrecords?cardId=${cardId}&credNo=${credNo}&name=${patientName}&organCode=${getOrganCode()}&channelCode=${getChannelCode()}`;
    Face({
      bussinessCode: 'medical-record-send',
      credNo,
      credType,
      patientName,
      callBackUrl,
    });
  };

  /**
   * 选择代办
   * 判断是否已经提交了审核
   * applyStatus 1 上传资料待审核 弹框提示 12 资料审核通过 进入病例列表
   * @return {*}
   */
  const famliyToDo = () => {
    const cardInfo = HxSessionStorage.get(StorageEnum.BIZ_PATIENTCARD_DATA);
    const { credNo = '', patientName = '' } = cardInfo || {};

    if (!proxy?.allow && proxy?.applyStatus === 1) {
      Modal.alert(
        <div>
          <ExclamationCircleFilled style={{ color: '#fe8f3c' }} /> 特别提醒
        </div>,
        // tslint:disable-next-line: jsx-wrap-multiline
        <div style={{ textAlign: 'left', color: '#03081A' }}>
          您的资料还在审核中，请耐心等待，审核通过后即可进入办理。
        </div>,
        [
          {
            text: '我知道了',
            onPress: () => {},
          },
        ],
      );
    } else if (proxy?.applyStatus === 12) {
      /* 资料审核通过 */
      history.push({
        pathname: '/casemailing/hospitalizationrecords',
        query: {
          cardId,
          credNo,
          name: patientName,
          isJumpFace: true,
          applyRecordId: proxy?.logicId,
        },
      });
    } else {
      /* 上传资料 */
      history.push({
        pathname: '/casemailing/agentData',
        query: {
          cardId,
        },
      });
    }
  };

  /**
   * 审核文案
   * @return {*}
   */
  const statusDec = () => {
    return APPLY_STATUS.find((i) => i.applyStatus === proxy?.applyStatus)?.label;
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className={styles.caseTypeContainer}>
      <div className={styles.hint}>请选择所需的办理方式：</div>
      <div className={styles.cardWrap}>
        <div
          className={classnames(styles.selfCard, styles.cardItem)}
          onClick={() => {
            handleOneself();
          }}
        >
          <div className={styles.cardHeader}>
            <h2>本人办理</h2>
            {oneself?.allow ? (
              <div className={styles.option}>
                进入办理
                <HxIcon iconName="arrow-right-white" />
              </div>
            ) : (
              <div className={styles.option}>不可办理</div>
            )}
          </div>
          <div className={styles.content}>
            {!!hint.length && (
              <div
                dangerouslySetInnerHTML={{
                  __html: hint.find((i) => i?.textCode === 'CASE_MAIL_HANDLE_WAY_ONESELF')?.textContent,
                }}
              />
            )}
          </div>
        </div>
        <div className={classnames(styles.famliyTo, styles.cardItem)} onClick={famliyToDo}>
          <div className={styles.cardHeader}>
            <h2>家属代办</h2>
            <div className={styles.option}>
              {proxy?.applyStatus === 12 ? '进入办理' : proxy?.applyStatus === 1 ? '不可办理' : '上传资料'}
              <HxIcon iconName="arrow-right-white" />
            </div>
          </div>
          <div className={styles.content}>
            {!!hint.length && (
              <div
                dangerouslySetInnerHTML={{
                  __html: hint.find((i) => i?.textCode === 'CASE_MAIL_HANDLE_WAY_PROXY')?.textContent,
                }}
              />
            )}
          </div>
          {proxy?.applyStatus && proxy?.applyStatus !== 2 && (
            <div
              className={classnames(
                styles.auditWrap,
                proxy?.applyStatus === 1 ? styles.inReview : proxy?.applyStatus === 5 ? styles.noPass : styles.pass,
              )}
            >
              {statusDec()}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default connect(({ caseMailing }: { caseMailing: CaseMailingState }) => caseMailing)(CaseMailingType);
