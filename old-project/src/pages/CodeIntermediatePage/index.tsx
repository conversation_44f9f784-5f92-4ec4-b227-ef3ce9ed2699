import React, { FC, useEffect, useState } from 'react';
import { history, Dispatch, connect, ICodeIntermediatePageState } from 'umi';
import { Toast, Modal } from 'antd-mobile';
import { HxLocalStorage } from '@/utils/storage';
import queryString from 'query-string';
import { queryDocJumpInfo, setReadStatus } from './service';
import { getChannelCode, getOrganCode, getToken } from '@/utils/parameter';
import { HxSessionStorage } from '@/utils/storage';

interface IPageProps {
  dispatch: Dispatch;
  location: {
    search: any;
  };
}

const CodeIntermediatePage: FC<IPageProps> = ({ location }) => {
  const { search = '' } = location;
  const { docCode = 'ys0022', patientPushRecordId,from } = search && queryString.parse(search); //医疗组长工号

  const toOnline = () => {
    // 从报告详情页跳转
    window.location.href = `${API_ZXMZ}/onlineExpert/notice2?channelCode=${getOrganCode()}&organCode=HID0101&servCode=zxmz&token=${getToken()}`;
  };
  const queryPageJump = async () => {
    const payload = {
      organCode: 'HID0101',
      docCode,
    };
    const res: any = await queryDocJumpInfo(payload);
    if (!res && from === 'reportDetail') {
      toOnline();
      return;
    }
    console.log(1111, res);
    // return;
    const { jumpStatus, teamId, doctorId, organId, teamWorkId, deptName, nda, deptCode } = res;
    Toast.hide();
    sessionStorage.setItem('sourceCode', 'hid0101');

    if (jumpStatus === '1' || jumpStatus === '2') {
      //专家团队详情
      history.push({
        pathname: '/doctor/team/home',
        query: {
          teamId,
          teamWorkId,
          organCode: getOrganCode(),
        },
      });
    } else if (jumpStatus === '3') {
      //医生主页
      history.push({
        pathname: '/doctor/hxhome',
        query: {
          organCode: getOrganCode(),
          // openId,//不要
          doctorId,
          servCode: 'zxmz',
          middleType: 1,
          channelCode: getChannelCode(),
          // deptName, //不要
          organId,
        },
      });
    } else {
      //科室列表
      window.location.href = `${API_ZXMZ}/onlinefast/home?selectIndex=-1&deptCode=${deptCode}&deptName=${deptName}&channelCode=${getChannelCode()}&organCode=HID0101&servCode=zxmz&token=${getToken()}`;
    }
  };
  const fetchRecord = async () => {
    const res: any = await setReadStatus(patientPushRecordId);
    console.log('res----', res);
  };

  useEffect(() => {
    Toast.loading('加载中', 0);
    if (docCode) {
      //调接口
      queryPageJump();
    } else if (from === 'reportDetail') {
      toOnline();
    }
    if (patientPushRecordId) {
      fetchRecord();
    }
  }, [docCode, patientPushRecordId]);
  return <div></div>;
};

export default connect(({ global }: { global: ICodeIntermediatePageState }) => ({
  global,
}))(CodeIntermediatePage);
