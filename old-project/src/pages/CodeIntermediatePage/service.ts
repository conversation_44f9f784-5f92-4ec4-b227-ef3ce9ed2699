import request from '@/utils/request';

/**
 * 提交表单信息
 * @param data
 */
export const queryDocJumpInfo = async (data: object): Promise<object> =>
  request(`${API_CHRONIC}/doctorcenter/pi/queryDocJumpInfo`, {
    method: 'POST',
    // data: { ...data, showOriginData: true },
    data
  });
// 设置推送记录
export const setReadStatus = async (data: object): Promise<any> =>
  request(`${API_ONLINE}/medicalOrders/mtPushRecord/setReadStatus/${data}`, {
    method: 'POST',
    data,
  });