import { Reducer } from 'redux';
import { Effect } from 'umi';
import { createAction } from '@/utils/common';
import { getOrganCode } from '@/utils/parameter';
import { queryDocJumpInfo, setReadStatus } from './service';

export interface ICodeIntermediatePageState {
  userCenterOrderMenu: [];
  userCenterToolMenu: [];
  count: number;
}

export interface IQuestionInvestigationModel {
  namespace: 'codeIntermediatePage';
  state: ICodeIntermediatePageState;
  effects: {
    queryDocJumpInfo: Effect;
    setReadStatus: Effect
  };
  reducers: {
    updateState: Reducer<ICodeIntermediatePageState>;
  };
}

const CodeIntermediateModel: IQuestionInvestigationModel = {
  namespace: 'codeIntermediatePage',
  state: {
    userCenterOrderMenu: [],
    userCenterToolMenu: [],
    count: 0,
  },

  effects: {
    *queryDocJumpInfo({ payload, callback }, { call }) {
      const res = yield call(queryDocJumpInfo, payload);
      res && callback(res);
    },

    *setReadStatus({ payload, callback }, { call }) {
      const res = yield call(setReadStatus, payload);
      res && callback(res);
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};

export default CodeIntermediateModel;
