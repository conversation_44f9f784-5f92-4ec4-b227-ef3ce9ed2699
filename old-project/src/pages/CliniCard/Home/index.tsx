import React, { PureComponent, memo, useState, useEffect, useRef } from 'react';
import { history, connect, Loading, IhospitalizationServiceState } from 'umi';
import { Dispatch, AnyAction } from 'redux';
import { HxIcon, HxIndicator, HxEmpty } from '@/components';
import { getOrganCode, HxParameter } from '@/utils/parameter';
import styles from './index.less';
import { temp, getDistanceTime } from '../utils/hospitalizationServiceData';

interface IProps {
  loading?: boolean;
  location: {
    query: {
      data: string;
    };
    state: any;
  };
  dispatch: Dispatch<AnyAction>;
}

interface IState {
  cardId: string;
  list: Array<any>;
  noticeText: string;
}

interface IItemProps {
  data: any;
  onClick: any;
}

const Item = memo((props: IItemProps) => {
  const { data = {}, onClick } = props;
  const [hh, sethh] = useState<string>('');
  const time = useRef<any>({});

  useEffect(() => {
    const { confirmedCountdown = 0 } = data;

    if (confirmedCountdown <= 0) {
      return;
    }
    time.current.confirmedCountdown = confirmedCountdown;

    time.current.timer = setInterval(() => {
      const { confirmedCountdown } = time.current;
      const hh = getDistanceTime(confirmedCountdown);
      sethh(hh);
      time.current.confirmedCountdown = confirmedCountdown - 1000;
    }, 1000);
    return function cleanup() {
      clearInterval(time.current.timer);
    };
  }, []);

  const {
    patientName,
    patientAge,
    patientSex,
    currentStateCode,
    bookingDeptName,
    bookingDoctor,
    applyCountdown = 0,
    confirmedCountdown = 0,
  } = data;

  const renderContent = (
    <div onClick={() => onClick(data)} className={styles.item}>
      <div className={styles.itemTop}>
        <div style={{ display: 'flex', justifyContent: 'row' }}>
          <p className={styles.name}>{patientName}</p>
          {`${patientAge !== '' ? `${patientAge}岁` : ''}   ${patientSex}`}
        </div>
        <div className={styles.itemTopRight} style={{ color: temp[currentStateCode].color }}>
          {temp[currentStateCode].statusText}
          <HxIcon iconName="arrow-right" className={styles.icon} />
        </div>
      </div>
      <div className={styles.itemMiddle}>
        <div>入院科室：{bookingDeptName}</div>
        <div>开证医生：{bookingDoctor}</div>
      </div>
      {temp[currentStateCode].text !== '' && (
        <div className={styles.itemBottom}>
          <div>
            {currentStateCode === 'Booking' &&
              (applyCountdown <= 0 ? temp[currentStateCode].text : `请在${applyCountdown}天内申请入院`)}
            {currentStateCode === 'SignBed' &&
              (confirmedCountdown <= 0 ? temp[currentStateCode].text : `请在${hh}内确认入院时间`)}
          </div>
          <div className={styles.btn}>{temp[currentStateCode].btn}</div>
        </div>
      )}
    </div>
  );

  return renderContent;
});

class Home extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      cardId: '',
      list: [],
      noticeText: '',
    };
  }

  componentDidMount() {
    const {
      location: {
        query: { data = '' },
      },
    } = this.props;
    this.getNoticeData();
    if (data) {
      const { cardId = '' } = JSON.parse(data) || {};
      this.setState(
        {
          cardId,
        },
        () => {
          this.fetchData();
        },
      );
    } else {
      const {
        location: {
          state: { cardId = '' },
        },
      } = this.props;
      this.setState(
        {
          cardId,
        },
        () => {
          this.fetchData();
        },
      );
    }
  }

  componentWillUnmount() {
    this.setState = () => {
      return false;
    };
  }

  fetchData = () => {
    const { cardId = '' } = this.state;
    const { organCode } = HxParameter;

    this.props.dispatch({
      type: 'hospitalizationService/queryAdmissionInfor',
      payload: {
        cardId,
        hospitalCode: organCode,
      },
      callback: (data: any = []) => {
        this.setState({
          list: data,
        });
      },
    });
  };

  // 获取列表页头部提示数据
  getNoticeData = () => {
    const { organCode } = HxParameter;
    this.props.dispatch({
      type: 'hospitalizationService/queryListNotice',
      payload: {
        nodeCode: 'hos_ser',
        organCode,
      },
      callback: (res: any) => {
        res.length &&
          this.setState({
            noticeText: res[0].content,
          });
      },
    });
  };

  onClick = (item: any = {}) => {
    const { cardId } = this.state;
    this.props.dispatch({
      type: 'hospitalizationService/updateState',
      payload: {
        chooseInfos: {},
      },
    });
    if (getOrganCode() === 'HID0101' || getOrganCode() === 'HYT') {
      history.push({
        pathname: '/hospitalizationService/detail',
        query: {
          cardId,
          item: JSON.stringify(item),
        },
      });
    } else {
      // 非华西
      history.push({
        pathname: '/hospitalizationService/detailplatform',
        query: {
          cardId,
          item: JSON.stringify(item),
        },
      });
    }
  };

  render() {
    const { loading } = this.props;
    const { list = [], noticeText } = this.state;
    return (
      <div className={styles.container}>
        <div className={noticeText ? `${styles.notopBox} ${styles.topBox}` : styles.notopBox}>
          <div className={noticeText ? styles.notice : ''}>
            {noticeText.split('\n').map((item: string) => {
              return <p key={`${Number(Math.random().toString().substr(3, 12) + Date.now()).toString(36)}`}>{item}</p>;
            })}
          </div>
        </div>
        {loading ? (
          <div className={styles.showLoading}>
            <HxIndicator />
          </div>
        ) : list.length === 0 ? (
          <HxEmpty emptyIcon="no-admission-card" canRefresh={false} emptyMsg="暂无入院证" />
        ) : (
          <div className={styles.listView}>
            {list.map((item) => (
              <Item
                key={Math.random().toString(36).substr(3, 12)}
                data={item}
                onClick={(item: any) => this.onClick(item)}
              />
            ))}
          </div>
        )}
      </div>
    );
  }
}

export default connect(
  ({
    loading,
    hospitalizationService,
  }: {
    hospitalizationService: IhospitalizationServiceState;
    loading: Loading;
  }) => ({
    loading: loading.effects['hospitalizationService/queryAdmissionInfor'],
    hospitalizationService,
  }),
)(Home);
