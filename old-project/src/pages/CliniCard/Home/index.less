@import '~@/styles/mixin.less';

.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f5f6f7;

  p {
    margin: 0;
  }

  .notopBox {
    .notice {
      position: fixed;
      top: 0;
      right: 0;
      left: 0;
      width: 100%;
      padding: 20px 30px;
      text-align: left;
      background: #fff;

      p {
        color: @notice-text-color;
        color: #ce2d32;
        font-size: 26px;
        line-height: 40px;
      }
    }

    .notices {
      width: 100%;
      padding-block: 0;
    }
  }

  .topBox {
    height: 150px;
  }

  .showLoading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 350px;
  }

  .listView {
    flex: 1;
    width: 100%;
    margin-top: 20px;
    overflow: auto;
  }

  .item {
    margin: 0 20px 20px;
    padding: 30px;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 16px;

    .itemTop {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      color: #333;
      font-size: 28px;

      .name {
        display: inline-block;
        max-width: 200px;
        margin-right: 10px;
        overflow: hidden;
        white-space: nowrap;
        -o-text-overflow: ellipsis;
        text-overflow: ellipsis;
      }

      .itemTopRight {
        display: flex;
        flex-direction: row;
        align-items: center;
        font-size: 26px;
        line-height: 37px;

        .icon {
          height: 26px;
          margin-left: 10px;
        }
      }
    }

    .itemMiddle {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      box-sizing: border-box;
      height: 122px;
      margin-top: 20px;
      padding: 20px;
      color: #333;
      font-size: 26px;
      background: #f7f7f7;
    }

    .itemBottom {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      height: 58px;
      margin-top: 30px;
      color: @brand-primary;
      font-size: 26px;

      .btn {
        width: 152px;
        height: 58px;
        color: #fff;
        font-size: 28px;
        line-height: 58px;
        text-align: center;
        background: #32b9aa;
        border-radius: 8px;
      }
    }
  }
}
