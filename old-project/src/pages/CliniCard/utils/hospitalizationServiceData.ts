const getDistanceTime = (time: any) => {
  let hour = 0;
  let minute = 0;
  let second = 0;

  if (time >= 0) {
    hour = Math.floor((time / 1000 / 60 / 60) % 24);
    minute = Math.floor((time / 1000 / 60) % 60);
    second = Math.floor((time / 1000) % 60);
  } else {
    return '0';
  }

  return `${hour}小时${minute}分${second}秒`;
};

const temp: any = {
  Booking: {
    statusText: '等待预约',
    color: '#F47F1B',
    text: '请补充入院资料',
    btn: '去申请',
  },
  Booked: {
    statusText: '入院等待',
    color: '#32B9AA',
    text: '',
    btn: '',
  },
  Confirmed: {
    statusText: '入院',
    color: '#32B9AA',
    text: '',
    btn: '',
  },
  Admission: {
    statusText: '已入院',
    color: '#32B9AA',
    text: '',
    btn: '',
  },
  SignBed: {
    statusText: '待确认入院',
    color: '#F47F1B',
    text: '请保持通讯畅通',
    btn: '去确认',
  },
  CancelBook: {
    statusText: '取消预约',
    color: '#666666',
    text: '',
    btn: '',
  },
  FailBook: {
    statusText: '预约失败',
    color: '#666666',
    text: '',
    btn: '',
  },
};

const progressTemp: any = {
  Booking: {
    firstCircle: '#fff',
    firstLine: '#0DA796',
    secondCircle: '#0DA796',
    secondLine: '#0DA796',
    thirdCircle: '#0DA796',
    firstText: '入院申请',
    secondText: '入院等待',
    thirdText: '入院确认',
  },
  FailBook: {
    firstCircle: '#fff',
    firstLine: '#0DA796',
    secondCircle: '#0DA796',
    secondLine: '#0DA796',
    thirdCircle: '#0DA796',
    firstText: '入院申请',
    secondText: '入院等待',
    thirdText: '入院确认',
  },
  Booked: {
    firstCircle: '#fff',
    firstLine: '#fff',
    secondCircle: '#fff',
    secondLine: '#0DA796',
    thirdCircle: '#0DA796',
    firstText: '入院申请',
    secondText: '入院等待',
    thirdText: '入院确认',
  },
  CancelBook: {
    firstCircle: '#fff',
    firstLine: '#fff',
    secondCircle: '#fff',
    secondLine: '#fff',
    thirdCircle: '#fff',
    firstText: '入院申请',
    secondText: '入院等待',
    thirdText: '取消预约',
  },
  Confirmed: {
    firstCircle: '#fff',
    firstLine: '#fff',
    secondCircle: '#fff',
    secondLine: '#fff',
    thirdCircle: '#fff',
    firstText: '入院申请',
    secondText: '入院等待',
    thirdText: '入院确认',
  },
  Admission: {
    firstCircle: '#fff',
    firstLine: '#fff',
    secondCircle: '#fff',
    secondLine: '#fff',
    thirdCircle: '#fff',
    firstText: '入院申请',
    secondText: '入院等待',
    thirdText: '入院确认',
  },
  SignBed: {
    firstCircle: '#fff',
    firstLine: '#fff',
    secondCircle: '#fff',
    secondLine: '#fff',
    thirdCircle: '#fff',
    firstText: '入院申请',
    secondText: '入院等待',
    thirdText: '入院确认',
  },
};

const messageTemp: any = {
  Booking: {
    message: '为不影响您入院，请及时前往入院服务中心申请入院',
  },
  Booked: {
    message: '',
  },
  Confirmed: {
    messageConfirmed: '您已成功确认入院，请在入院当天带上《入院注意事项》中说明的相关资料到报到地点报到入院',
    messageDelay: '为保证您能顺利入院，请您保持您预留联系电话的通讯畅通，工作人员稍后将会电话联系您，沟通入院时间',
  },
  Admission: {
    message: '',
  },
  SignBed: {
    message: `1、为了您能顺利入院，请确认报到时间是否可准时报到入院，可准时报到则点击“确认时间”，请在指定时间准时前往报到地址报到入院；
    2、若不能准时报到请点击“需调换时间”，稍后将有工作人员与您联系，沟通入院时间；`,
  },
  CancelBook: {
    message: '您已取消预约，若需再次申请则需前往医院入院服务登记中心重新申请',
  },
  FailBook: {
    message: '为不影响您入院，请及时前往入院服务中心申请入院',
  },
};

export {
  temp, // 列表页（home）使用状态
  getDistanceTime, // 列表页item显示倒计时
  progressTemp, // 详情页显示进度条
  messageTemp, // 最上方注意事项
};
