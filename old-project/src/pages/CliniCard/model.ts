import { Reducer } from 'redux';
import { Effect, Subscription } from 'umi';
import { getOrganCode } from '@/utils/parameter';
import { HxLocalStorage } from '@/utils/storage';
import * as clinicardApi from './service';

export interface CliniCard {
  namespace: 'clinicard';
  state: any;
  effects: {
    queryAdmissionInfor: Effect;
    queryPatInfo: Effect;
    applyHospitalised: Effect;
    cancelHospitalised: Effect;
    confirmHospitalised: Effect;
    queryListNotice: Effect;
    findDocumentUrl: Effect;
    queryPlatformDictionary: Effect;
  };
  reducers: {
    updateState: Reducer<CliniCard>;
  };
  subscriptions: {
    setup: Subscription;
  };
}

const Model: CliniCard = {
  namespace: 'clinicard',
  state: {
    chooseInfos: {},
  },
  effects: {
    *queryAdmissionInfor({ payload, callback }, { call }) {
      const res = yield call(clinicardApi.queryAdmissionInfor, payload);
      res && callback(res);
    },
    *queryPatInfo({ payload, callback }, { call }) {
      const res = yield call(clinicardApi.queryPatInfo, payload);
      res && callback(res);
    },
    *applyHospitalised({ payload, callback }, { call }) {
      const res = yield call(clinicardApi.applyHospitalised, payload);
      res && callback(res);
    },
    *cancelHospitalised({ payload, callback }, { call }) {
      const res = yield call(clinicardApi.cancelHospitalised, payload);
      res && callback(res);
    },
    *confirmHospitalised({ payload, callback }, { call }) {
      const res = yield call(clinicardApi.confirmHospitalised, payload);
      res && callback(res);
    },
    *queryListNotice({ payload, callback }, { call }) {
      const res = yield call(clinicardApi.queryListNotice, payload);
      res && callback(res);
    },
    *findDocumentUrl({ payload, callback }, { call }) {
      const res = yield call(clinicardApi.findDocumentUrl, payload);
      res && callback(res);
    },
    *queryPlatformDictionary({ payload }, { call }) {
      const res = yield call(clinicardApi.queryPlatformDictionary, payload);
      const { code = '', data = {} } = res || {};
      if (res?.code && code === '1') {
        const { provinceRspVOList = [], relationList = [] } = data;
        HxLocalStorage.set('data_province_platform', provinceRspVOList);
        HxLocalStorage.set('data_province_relation', relationList);
      }
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return { ...state, ...payload };
    },
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen((path) => {
        const { pathname = '' } = path;
        const hxflag = getOrganCode() === 'HID0101' || getOrganCode() === 'HYT';
        if (!hxflag && pathname === '/hospitalizationservice/firstnote') {
          dispatch({
            type: 'queryPlatformDictionary',
            payload: {
              hospitalCode: getOrganCode(),
            },
          });
        }
      });
    },
  },
};

export default Model;
