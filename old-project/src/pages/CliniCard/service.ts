import request from '@/utils/request';
import { getOrganCode } from '@/utils/parameter';

let node = '/cloud';
if (APP_ENV === 'prod') {
  node = getOrganCode() === 'HID0101' || getOrganCode() === 'HYT' ? `${API_HXYY}/cloud` : `${API_BASE}/cloud`;
}

const handleOrganCode = (data: any = {}) => {
  const { hospitalCode = '' } = data;
  if (getOrganCode() === 'HYT' && hospitalCode === 'HYT') {
    return {
      ...data,
      hospitalCode: 'HID0101',
    };
  }
  return data;
};

// 获取入院证列表
export const queryAdmissionInfor = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/hospitalisedservice/queryadmissioninfor`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

// 获取入院证详情（查询患者详细资料）
export const queryPatInfo = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/hospitalisedservice/querypatinfo`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

// 申请入院
export const applyHospitalised = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/hospitalisedservice/applyhospitalised`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

// 取消入院
export const cancelHospitalised = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/hospitalisedservice/cancelhospitalised`, {
    method: 'POST',
    data,
  });

// 确认入院
export const confirmHospitalised = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/hospitalisedservice/confirmhospitalised`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

// 列表页提示信息
export const queryListNotice = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/queryNotice`, {
    method: 'POST',
    data,
  });

// 获取入院须知文章链接
export const findDocumentUrl = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/hospitalisedservice/findDocumentUrl`, {
    method: 'POST',
    data: handleOrganCode({ ...data, showOriginData: true }),
  });

// 获取非华西医院得省会地址字典
export const queryPlatformDictionary = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/hospitalisedservice/queryAddressCode`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });
