import React, { PureComponent } from 'react';

// const content = `
// <html>
// <head>
// <title>注意事项</title>

// <meta name=keywords content=keyword1,keyword2,keyword3>
// <meta name=content-type content=text/html; charset=UTF-8>

// <style type=text/css>
// #title{
//  font-weight: bold;
//  text-align: center;
// }
// p{
//  line-height:20px;
// }
// </style>
// </head>

// <body>
// 	<div id=title>
// 		<p>等候床位注意事项</p>
// 	</div>
// 	<p>&emsp;因医疗资源有限，本院将根据您病情的轻重缓急及先后顺序安排入院。开具《电子入院证》后，可能要等候一段日子您才能入院，很抱歉让您等候了。</p>
// 	<p>&emsp;1.在等候床位期间，患者的病情有可能发生变化，患者及家属务必详尽了解所患疾病的基本知识与相关风险。鉴于疾病的多样性及严重程度的差异，本告知书不能详尽列明相关内容，患者及家属应认真全面地对可能存在风险因素及应对处理措施进行掌握，也可向有关专业人士咨询。等候床位期间患者病情发生变化时，请及时就诊。</p>
// 	<p>&emsp;2.我院坚持在“公平、公正”基础上“危急重症优先”的入院收治原则，但来院就诊患者具有随机性，且急危重程度具有不可预测性，当我院医生判断有更为急危重的患者就诊时，他们将被优先安排入院。在此情况下，患者及家属不得以本次预约顺序进行抗辩。</p>
// 	<p>&emsp;3.医院严格禁止其员工向您做出代办入院、有偿办理入院的类似承诺，患者及家属应当通过规范的预约程序等候入院通知，否则，一切后果将由您自行承担。</p>
// 	<p>&emsp;4.在接到入院通知后，请务必在“通知所约定的时间内”办理入院手续，否则将被视为“爽约”，该床位将会安排其他患者入院。“爽约”行为发生后，患者的预约申请信息将会被系统自动删除，如果仍有住院需求，需要到入院服务中心窗口重新预约，并且以重新预约申请的时间作为排队的起始时间。</p>
// 	<div id=title>
//         <p>医疗保险需知</p>
//     </div>
//     <p>&emsp;办理入院手续时，需要您出示患者本人身份证（包括代理人身份证）及开具入院证时所持的就诊卡，以免个人信息错误风险发生。如果您是参加基本医疗保险的患者（以下简称“参保患者”），需携带医保卡、医疗证或记账专用表，省内异地新农合转院患者请携带转院证明，避免您出院后报销困难。</p>
//     <p>&emsp;1.如您为大成都区域医保患者，请主动出示医保卡、医疗证等相关卡证，在办理相关医保入院手续后，市医保按医院规定缴纳预计住院费用的50%；城乡居民患者缴纳预计住院费用的70%；省医保、市属离休患者缴纳预计住院费用的30%。如您为外伤入院的以上参保患者以及少儿互助基金患儿，请仔细阅读参保患者入院流程，并需全额缴纳住院费，出院时办理相关结算手续。</p>
//     <p>&emsp;2.成都市医保患者请持医保卡至入院办理处办理。（合并门、急诊费用温馨提示：按医保局要求，部分术前检查和入院前三天的CT、磁共振、彩超等与疾病相关的阳性体征检查（城乡医保不享受）可纳入住院费用报销，请符合要求的患者保留相应发票和检查报告，可于出院时办理费用合并。）</p>
//     <p>&emsp;3.四川省内各市及重庆、新疆两地医保联网患者请持医保卡至结算科16号窗口办理。四川省内新农合患者请持新型农村合作医疗证、转诊单至结算科17-22号窗口办理。由于全国其他省市尚未与我院联网结算，该类医保患者需返回医保缴纳地进行报销。</p>
//     <div id=title>
//         <p>其他温馨提示</p>
//     </div>
//     <p>&emsp;1.本院是卫生部重点综合性教学医院之一，按照教育部要求，承担着医学教育临床实践工作，有临床实习生参加临床实践，希望您理解并支持医院医学教育临床实践工作，如果您不同意在诊疗过程中接受医学实习生的服务，请您入院后向医生明确提出。</p>
//     <p>&emsp;2.在诊治过程中，请您密切配合医务人员，以保证诊疗措施的顺利实施。您应当向医务人员如实告知病情，即提供您病情有关的准确、真实的全面资料，以避免报销等困难及其他不利发生。</p>
//     <p>&emsp;3.在住院期间携带日用品请以必需为限。医院是开放的公共场所，环境较复杂，请注意保护您的自身健康及财产安全。严禁用火和吸烟、私拉乱接电线路和使用大功率电器设备，切实保障医院消防安全。</p>
//     <p>&emsp;4.您合法权益受侵害或对我们的服务不满意时，请拨医院投诉电话：</p>
//     <p>&emsp;上班时间请拨打：</br>
//      医务部-85422580   &emsp;护理部-85422044</br>
//      财务部-85422863   &emsp;保卫部-85422119</br>
//      医保办-85422301   &emsp;候床查询-85421772</br>
//      入院办理处-85422298

//     <p>&emsp;下班时间请拨打：</br>
//     医院总值班－85422023 </br>
//     医院电话查询台－85422114
// </body>
// </html>
// `;

const contentSJ = `
<html>
<head>
<title>注意事项</title>

<meta name=keywords content=keyword1,keyword2,keyword3>
<meta name=content-type content=text/html; charset=UTF-8>

<style type=text/css>
#title{
 font-weight: bold;
 text-align: center;
}
 
p{
 line-height:20px;
}
</style>
</head>

<body>
	<div id=title>
    <p>成都上锦南府医院/四川大学华西医院上锦医院</p>
		<p>入院知情告知书</p>
	</div>
	<p>&emsp;尊敬的患者朋友:您好!感谢您对本院的信任!</p>
	<p>&emsp;因医疗资源有限,本院将根据您病情的轻重缓急按照先后顺序安排入院。</p>
	<p>&emsp;本院承担着医学教育临床实践工作，有临床实习生参加临床实践，感谢您理解并支持医院医学教育临床实践工作。</p>
	<p>&emsp;请您特别注意以下事项：</p>
	<p>&emsp;1.我院是四川省卫生厅批复成立，由四川大学华西医院全面托管的三级甲等民营非营利性医院，是四川省省本级、异地联网和成都市医疗保险定点医疗机构。</p>
  <p>&emsp;2.办理入院手续时,需要您主动出示并复印患者本人身份证、社会保障卡（用于病历中医保身份存档），开具入院证时所持的就诊卡,以免登记个人信息错误。</p>
  <p>&emsp;3.医保登记办理需知</p>
  <p>&emsp;（1）医保政策和流程相关注意事项详见《住院患者温馨提示》。</p>
  <p>&emsp;（2）根据基本医疗保险相关政策，本次住院治疗可能会用到医保自费或部分自费的药品、耗材以及诊疗等项目。</p>
  <p>&emsp;4,在诊治过程中,请您密切配合医务人员,以保证诊疗措施的顺利实施。您应当向医务人员如实告知病情,即提供您病情有关的准确、真实的全面资料,以避免报销等困难及其他不利发生。</p>
  <p>&emsp;5.请您知晓医院会对手术切除的病变器官、组织或标本进行处置,包括术中冰冻活检、病理学检查、细胞学检查和医疗废物处理,以及用于科学研究等。</p>
  <p>&emsp;6,在住院期间携带日用品请以必需为限。医院是开放的公共场所,环境较复杂,请注意保护您的自身健康及财产安全。严禁用火和吸烟、私拉乱接电线路和使用大功率电器设备,切实保障医院消防安全。</p>
  <p>&emsp;7.如果您购买了商业保险，请提前向所在商保公司落实报销事宜。</p>
  <p>&emsp;8.您合法权益受侵害或对我们的服务不满意时,请拨医院投诉电话:62539114</p>
</body>
</html>
`;

function createMarkup() {
  return { __html: contentSJ };
}
class Attentionjs extends PureComponent {
  render() {
    return (
      <div
        style={{
          width: '100%',
          height: '100vh',
          fontSize: '16px',
          boxSizing: 'border-box',
          padding: '20px 10px',
          textAlign: 'justify',
          color: 'rgba(0,0,0,0.65)',
          backgroundColor: '#fff',
        }}
      >
        {/* eslint-disable-next-line react/no-danger */}
        <div dangerouslySetInnerHTML={createMarkup()} />
      </div>
    );
  }
}

export default Attentionjs;
