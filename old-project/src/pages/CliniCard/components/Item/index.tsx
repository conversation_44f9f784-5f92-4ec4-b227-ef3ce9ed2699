import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Dispatch, AnyAction } from 'redux';
import { HxIcon } from '@/components';
import styles from './index.less';

interface IProps {
  title: string;
  text: string;
  type?: string;
  color?: string;
  onClick?: any;
  addressData?: any;
  dispatch: Dispatch<AnyAction>;
}

interface IState {
  text: string;
}

class Item extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);

    this.state = {
      text: '',
    };
  }

  componentDidMount() {
    const { text = '' } = this.props;
    this.setState({
      text,
    });
  }

  getData = (title: string = '', text: string = '') => {
    let code = '';
    if (title === '婚姻状态') {
      code = 'marriage';
    } else if (title === '民族') {
      code = 'ethnic';
    } else if (title === '职业') {
      code = 'profession';
    } else if (title === '家属与患者关系') {
      code = 'inhos_relation';
    }

    this.props.dispatch({
      type: 'global/getDictionary',
      payload: {
        typeCode: code,
      },
      callback: (data: any = {}) => {
        for (let i = 0; i < data.length; i += 1) {
          const { dicName = '', dicCode = '' } = data[i];
          if (text === dicCode) {
            this.setState({
              text: dicName,
            });
          }
        }
        // for (const i in data) {
        //   const { dicName = '', dicCode = '' } = data[i];
        //   if (text === dicCode) {
        //     this.setState({
        //       text: dicName,
        //     });
        //   }
        // }
      },
    });
  };

  render() {
    const { title, text: text1, type = '', color = '#666', onClick = () => {} } = this.props;
    const { text } = this.state;

    if (title === '婚姻状态' || title === '民族' || title === '职业' || title === '家属与患者关系') {
      this.getData(title, text1);
    }

    return (
      <div className={styles.item} onClick={onClick}>
        <span className={styles.title}>{title}</span>
        {type === 'arrow' ? (
          <HxIcon iconName="arrow-right" className={styles.icon} />
        ) : (
          <span className={styles.text} style={{ color }}>
            {text || text1}
          </span>
        )}
      </div>
    );
  }
}

export default connect()(Item);
