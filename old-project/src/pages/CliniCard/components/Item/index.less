.item {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  width: 100%;
  min-height: 100px;
  padding: 32px;
  background: #fff;
  border-bottom: 1px solid #eee;

  .title {
    width: 220px;
    color: #333;
    font-size: 28px;
  }

  .icon {
    width: 15px;
    height: 26px;
  }

  .text {
    flex: 1;
    color: #999;
    font-size: 28px;
    text-align: end;
  }
}
