import React, { PureComponent } from 'react';
import { connect } from 'dva';
import styles from './index.less';

interface IProps {
  data: any;
  cardNo: string;
  onClick: any;
}

interface IState {}

class Info extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  render() {
    const { data = {}, cardNo = '', onClick = () => {} } = this.props;
    const {
      patientName = '',
      patientAge = '',
      patientSex = '',
      bookingDeptName = '',
      bookingDate = '',
      bookingDoctor = '',
    } = data;
    return (
      <div className={styles.info}>
        <div className={styles.card}>
          <div>{`患者：${patientName} ${patientAge !== '' ? `${patientAge}岁` : ''} ${patientSex}`}</div>
          <div>{`就诊卡号：${cardNo}`}</div>
          <div>{`入院证科室：${bookingDeptName}`}</div>
          <div>{`入院证时间：${bookingDate}`}</div>
          <div>{`开证医生：${bookingDoctor}`}</div>
        </div>
        <div onClick={onClick} className={styles.click}>
          {'入院注意事项，请申请前咨询阅读>>'}
        </div>
      </div>
    );
  }
}

export default connect()(Info);
