import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { HxIcon } from '@/components';
import styles from './index.less';
import { messageTemp } from '../../utils/hospitalizationServiceData';

interface IProps {
  status: string;
  admissionStatus: string;
}

interface IState {}

class Tips extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  render() {
    const { status, admissionStatus } = this.props;
    console.log(status, admissionStatus);
    return (
      messageTemp[status].message !== '' && (
        <div className={styles.tips}>
          <div className={styles.title}>
            <HxIcon iconName="notice" className={styles.image} />
            注意事项
          </div>
          <div>
            {(admissionStatus === '' || admissionStatus === undefined) && messageTemp[status].message}
            {admissionStatus === 'Confirmed' && messageTemp[status].messageConfirmed}
            {admissionStatus === 'Delay' && messageTemp[status].messageDelay}
          </div>
        </div>
      )
    );
  }
}

export default connect()(Tips);
