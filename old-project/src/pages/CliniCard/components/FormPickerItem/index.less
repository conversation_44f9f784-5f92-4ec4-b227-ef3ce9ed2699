.pickerItem {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100px;
  padding: 0 30px;
  font-size: 28px;
  background: #fff;

  .child {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .default {
      color: #ccc;
    }

    .selected {
      color: #666;
    }
    span {
      font-size: 28px;
      line-height: 0px;
    }

    svg {
      width: 30px;
      height: 52px;
      margin-left: 20px;
    }
  }
}

.pickerItem::after {
  position: absolute;
  bottom: 0;
  left: 30px;
  width: 100%;
  height: 1px;
  color: #eee;
  background-color: #eee;
  border-bottom: 1px solid #ddd;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  -webkit-transform-origin: 0 100%;
  transform-origin: 0 100%;
  content: '';
}
