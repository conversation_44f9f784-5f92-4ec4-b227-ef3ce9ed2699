import React from 'react';
import { Picker } from 'antd-mobile';
import { HxIcon } from '@/components';
import styles from './index.less';

interface IProps {
  form: any;
  /**
   * 字段说明
   */
  label: string;
  /**
   * 数据源
   */
  data: any[];
  /**
   * 列数
   */
  cols: number;
  /**
   * prop值
   */
  propKey: string;
  /**
   * 默认值
   */
  value?: any;
  /**
   * 是否禁用
   */
  disabled?: boolean;
  /**
   * 提示语
   */
  extra?: string;
  /**
   * 点击选中时执行的回调
   */
  onOkCustom: (value: []) => void;
  /**
   * 红点标志
   */
  has?: boolean;
  /**
   * 是否为必填项
   */
  required?: boolean;
}

const Child = (props: any) => {
  const { extra = '请选择' } = props;
  return (
    <div className={styles.child} onClick={props.onClick}>
      <span className={`${extra === '请选择' ? styles.default : styles.selected}`}>{extra}</span>
      <HxIcon iconName="arrow-right" />
    </div>
  );
};

const FormPickerItem = (props: IProps) => {
  const {
    label,
    data = [],
    extra,
    disabled,
    form,
    propKey,
    value,
    onOkCustom,
    cols = 1,
    has = false,
    required = true,
  } = props;
  const { getFieldProps } = form;
  return (
    <div className={styles.pickerItem}>
      <div>
        {has && <span style={{ color: 'red' }}>*</span>}
        {label}
      </div>
      <Picker
        data={data}
        cols={cols}
        extra={extra}
        disabled={disabled}
        onOk={(value: []) => onOkCustom && onOkCustom(value)}
        {...getFieldProps(propKey, {
          rules: [{ required }],
          initialValue: value,
          validateFirst: true,
        })}
      >
        <Child />
      </Picker>
    </div>
  );
};

export default FormPickerItem;
