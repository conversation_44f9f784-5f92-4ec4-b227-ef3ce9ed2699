import React, { PureComponent } from 'react';
import { connect } from 'dva';
import styles from './index.less';
import { progressTemp } from '../../utils/hospitalizationServiceData';

interface IProps {
  status: string;
}

interface IState {}

class Progress extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  render() {
    const { status = '' } = this.props;

    return (
      <div className={styles.progress}>
        <div className={styles.img}>
          <span style={{ background: progressTemp[status].firstCircle }} className={styles.circle} />
          <span style={{ background: progressTemp[status].firstLine }} className={styles.line} />
          <span style={{ background: progressTemp[status].secondCircle }} className={styles.circle} />
          <span style={{ background: progressTemp[status].secondLine }} className={styles.line} />
          <span style={{ background: progressTemp[status].thirdCircle }} className={styles.circle} />
        </div>
        <div className={styles.text}>
          <span>{progressTemp[status].firstText}</span>
          <span>{progressTemp[status].secondText}</span>
          <span>{progressTemp[status].thirdText}</span>
        </div>
      </div>
    );
  }
}

export default connect()(Progress);
