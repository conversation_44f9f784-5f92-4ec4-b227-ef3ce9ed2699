import Mock from 'mockjs';
import { commonSuccessResponse } from '../../../mock/util';

const { Random } = Mock;

// 导诊单列表
const queryAdmissionInfor = (req: any, res: any) => {
  const response = Mock.mock({
    ...commonSuccessResponse,
    'data|3-8': [
      {
        papmi: '',
        cardNo: '000100008474893',
        'showCardNo|202194616936697856-203194616936697856': 202094616936697856,
        hospitalCode: 'HID0101',
        'bookingId|202194616936697856-203194616936697856': 202094616936697856,
        'patientName|+1': ['紧那罗', '夜叉', '迦楼罗', '天众', '乾达婆', '摩呼罗伽', '龙众', '阿修罗'],
        'patientSex|1': ['男', '女'],
        'patientAge|13-30': 13,
        bookingDeptName: () => Random.ctitle(3, 5),
        bookingDate: () => Random.datetime(),
        bookingDoctor: () => Random.ctitle(3, 5),
        'currentStateCode|+1': ['Booking', 'Booked', 'Confirmed', 'Admission', 'SignBed', 'CancelBook', 'FailBook'],
        'currentState|+1': ['等待预约', '入院等待', '入院', '已入院', '待确认入院', '取消预约', '预约失败'],
        entireDate: () => Random.datetime(),
        'entireAddress|1': ['纳尼亚', '霍格沃兹', '亚特兰蒂斯'],
        applicaDate: () => Random.datetime(),
        'admissionStatus|1': ['Booking', 'Booked', 'Confirmed', 'Admission', 'SignBed', 'CancelBook', 'FailBook'],
        'isOldApply|1': ['0', '1'],
        applyCountdown: 7,
        confirmedCountdown: 100000000,
      },
    ],
  });
  // 模拟请求延迟
  setTimeout(() => {
    return res.status(200).send(response);
  }, 500);
};

// 导诊单详情
const queryPatInfo = (req: any, res: any) => {
  const response = Mock.mock({
    ...commonSuccessResponse,
    data: {
      phoneNo: 18148101373,
      'marital|1': ['已婚', '未婚', '离异'],
      'national|1': ['汉族', '兽族', '暗夜精灵族', '死灵族', '娜迦族'],
      'professional|1': ['战士', '法师', '猎人', '术士'],
      insuranceType: '医保类型',
      'workCompany|1': ['战士工会', '魔法学院', '盗贼工会', '猎人营地', '十万大山'],
      workAddress: '这里的山路十八弯，这里的水湾九连环',
      workDetailedAddress: '在山的那边,海的那边,有群~~嘿嘿',
      liveAddress: '在那遥远的地方，有位好姑娘~~~~~',
      liveDetailedAddress: '我站在!烈烈风中,恨不能~~~荡尽绵绵心痛',
      'birthplace|1': ['成都', '重庆'],
      outPatientId: '98789789789',
      'censusRegister|1': ['金牛区', '沙坪坝'],
      nativePlace: '你猜',
      kinsfolkName: 'ZZF',
      kinsfolkRelation: '父子',
      kinsfolkPhone: '7895621358789',
    },
  });
  // 模拟请求延迟
  setTimeout(() => {
    return res.status(200).send(response);
  }, 500);
};

export default {
  'POST /cloud/hosplatcustomer/call/medicalaffairs/hospitalisedservice/queryadmissioninfor': queryAdmissionInfor,
  'POST /cloud/hosplatcustomer/medicalaffairs/hospitalisedservice/querypatinfo': queryPatInfo,
};
