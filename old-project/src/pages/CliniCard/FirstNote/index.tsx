import React, { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import { Toast } from 'antd-mobile';
import { Dispatch, AnyAction } from 'redux';
// import queryString from "query-string";
import { history, Loading } from 'umi';
import { HxIndicator, HxEmpty } from '@/components';
import selected from '@/assets/HospitalizationService/icon_选中.png';
import noSelected from '@/assets/HospitalizationService/icon_未选中.png';
import noticeImg from '@/assets/HospitalizationService/组 <EMAIL>';
import notice1 from '@/assets/notice-1.png';
import notice2 from '@/assets/notice-2.png';
import { getOrganCode } from '@/utils/parameter';
import { DocumentUrlInfo } from '../data.d';
import styles from './index.less';

const codePic = require('@/assets/codePic.png');

export interface IProps {
  dispatch: Dispatch<AnyAction>;
  orderDetailData: any;
  loading?: boolean;
  location: {
    search: string;
  };
}

interface IState {
  isSelected: boolean;
  time: number;
  onceClock: number;
  documentUrls: DocumentUrlInfo;
}

class Admissionnotice extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      isSelected: false,
      time: 5,
      onceClock: 1,
      documentUrls: {},
    };
  }

  componentDidMount() {
    document.title = '入院预约须知';
    this.initTimer();
    this.findDocumentUrl();
  }

  findDocumentUrl = () => {
    const { dispatch } = this.props;
    dispatch({
      type: 'hospitalizationService/findDocumentUrl',
      payload: {
        hospitalCode: getOrganCode(),
      },
      callback: (res: any) => {
        const { code = '', data = {} } = res || {};
        if (code && code === '1') {
          this.setState({
            documentUrls: data,
          });
        }
      },
    });
  };

  hasSelected = () => {
    const { onceClock, isSelected } = this.state;
    if (onceClock === 2) {
      this.setState({
        isSelected: !isSelected,
      });
    } else {
      Toast.info('请阅读《四川大学华西医院入院预约须知》', 2);
    }
  };

  initTimer = () => {
    const timer = setInterval(() => {
      const { time } = this.state;
      const times = time - 1;
      if (times <= 0) {
        this.setState(
          {
            onceClock: 2,
          },
          () => {
            clearTimeout(timer);
          },
        );
      } else {
        this.setState({
          time: times,
        });
      }
    }, 1000);
  };

  turnIndex = () => {
    const { isSelected, onceClock, documentUrls } = this.state;
    const { hospitalName = '' } = documentUrls;
    // const { organCode = '', token = '' } = queryString.parse(this.props.location.search) || {};
    if (onceClock === 2) {
      if (isSelected) {
        const { search = '' } = this.props.location;
        const redirect = '/hospitalizationservice/home';
        const newServer = 'admission';
        // history.push(`/patientcard/home${search}${search ? '&' : '?'}redirect=${redirect}`);
        history.push({
          pathname: '/patientcard/home',
          search: `newServer=${newServer}`,
        });
      } else {
        Toast.info(`请勾选“已阅读《${hospitalName}入院预约须知》”`, 2);
      }
    } else {
      Toast.info(`请阅读《${hospitalName}入院预约须知》`, 2);
    }
  };

  render() {
    const { loading } = this.props;
    const { isSelected, onceClock, time, documentUrls } = this.state;
    const { documentUrl = '', hospitalName = '' } = documentUrls;

    return (
      <Fragment>
        {loading && (
          <div style={{ height: '100%' }}>
            <HxIndicator />
          </div>
        )}
        {!loading && (
          <div className={styles.admissionNotice}>
            <>
              {documentUrl && <iframe title=" " src={documentUrl} frameBorder="0" width="100%" style={{ flex: 1 }} />}
              {!documentUrl && <HxEmpty emptyIcon="patientcard-warn" emptyMsg="入院须知获取失败" canRefresh={false} />}
              <div style={{ height: '10px' }} />
            </>
            <div className={styles.bottomBox}>
              <div className={styles.selectIconBox}>
                <div className={styles.selectIcon}>
                  <img src={isSelected ? selected : noSelected} alt="" onClick={this.hasSelected} />
                  <div>已阅读《{hospitalName}预约入院须知》</div>
                </div>
              </div>
              <div className={`${onceClock === 2 ? styles.activeBtn : styles.btn}`} onClick={this.turnIndex}>
                {onceClock === 2 ? '确定' : `${time}秒`}
              </div>
            </div>
          </div>
        )}
      </Fragment>
    );
  }
}

export default connect(({ loading }: { loading: Loading }) => ({
  loading: loading.effects['hospitalizationService/findDocumentUrl'],
}))(Admissionnotice);
