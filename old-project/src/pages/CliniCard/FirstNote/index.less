.admissionNotice {
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  // padding: 0 30px;
  // padding-top: 40px;
  padding-bottom: 5px;
  overflow: hidden;
  color: #333;
  background-color: #fff;
  .notice {
    display: flex;
    flex: 1;
    flex-direction: column;
    width: 100%;
    margin-top: 40px;
    padding: 0 30px;
    overflow: hidden;
    font-size: 28px;
    .texts {
      flex: 1;
      margin-bottom: 20px;
      overflow: auto !important;
      p {
        margin-bottom: 20px !important;
      }
      h6 {
        height: 50px;
        margin-top: 20px;
        margin-bottom: 30px;
        color: #333;
        font-weight: bolder;
        font-size: 36px;
        font-family: PingFang SC, PingFang SC-Medium;
        line-height: 28px !important;
        text-align: center;
      }
      .titles {
        height: 42px;
        color: #333;
        font-weight: bolder;
        font-size: 30px;
        font-family: PingFang SC, PingFang SC-Medium;
        line-height: 28px;
        text-align: left;
      }
      .textContents {
        color: #666;
        font-weight: 400;
        font-size: 28px;
        font-family: PingFang SC, PingFang SC-Regular;
        line-height: 50px;
        letter-spacing: 1px;
        text-align: left;
        span {
          color: #de0a0a;
        }
        .blue {
          color: rgb(88, 135, 211);
        }
        .yellow {
          background-color: rgb(243, 243, 19);
          color: #666;
        }
      }
      .endtexts {
        height: 40px;
        margin-bottom: 10px !important;
        color: #333;
        font-weight: bolder;
        font-size: 28px;
        font-family: PingFang SC, PingFang SC-Medium;
        text-align: right;
      }
      .imgsbox {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 1228px;
        margin-top: 50px;
        margin-bottom: 40px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .promptBox {
        height: 163px;
        padding: 20px 24px;
        background: #f7f7f7;
        .promptBoxTitle {
          height: 38px;
          margin: 0 !important;
          color: #ff8a26;
          font-weight: 500;
          font-size: 28px;
          font-family: Alibaba PuHuiTi, Alibaba PuHuiTi-Medium;
          line-height: 38px !important;
          line-height: 69px;
          text-align: left;
        }
        .promptBoxContent {
          height: 73px;
          color: #333;
          font-weight: 400;
          font-size: 26px;
          font-family: Alibaba PuHuiTi, Alibaba PuHuiTi-Regular;
          text-align: left;
        }
      }
    }
  }

  .bottomBox {
    width: 100%;
    padding: 0 30px;
  }

  .selectIconBox {
    width: 100%;

    .selectIcon {
      display: flex;
      align-items: center;

      img {
        width: 30px;
        height: 30px;
        margin-right: 20px;
      }
    }
  }

  .btn,
  .activeBtn {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 690px;
    height: 90px;
    margin-top: 30px;
    margin-bottom: 30px;
    color: #fff;
    font-size: 32px;
    background: #ccc;
    border-radius: 10px;
    border-radius: 1.6px;
  }

  .activeBtn {
    background: #32b9aa;
  }
}

.imgBox {
  img {
    width: 200px;
    // margin:0 auto;
    // display:block;
  }
}
