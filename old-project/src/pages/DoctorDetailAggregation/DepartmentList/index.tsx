/*
 * @Author: <PERSON>
 * @Date: 2022-03-24 21:37:06
 * @LastEditor: <PERSON>
 * @LastEditTime: 2022-03-25 16:17:15
 * @FilePath: /hyt-person/src/pages/DoctorDetailAggregation/DepartmentList/index.tsx
 * Copyright (c) 2022 by <PERSON>, All Rights Reserved.
 * @Description:
 */
import React, { useEffect } from 'react';
import { connect, history, Dispatch } from 'umi';

import { DoctorModelState, NumberSourceItemType } from '@/typings/doctor';
import { isHytPerson } from '@/utils/platform';
import AppScheme from '@/utils/AppScheme';
import styles from './index.less';

const { toAppointmentRegistration } = AppScheme;

interface DepartmentListProps {
  doctor: DoctorModelState;
  location: {
    query: {
      doctorId: string;
      organCode: string;
      nodeCode: string;
    };
  };
  dispatch: Dispatch;
}

const DepartmentList: React.FC<DepartmentListProps> = (props) => {
  // props
  const {
    doctor: { numberSourceList, selDoctorDetails },
    location: { query },
    dispatch,
  } = props;
  const { docName, hospitalName } = selDoctorDetails;
  const { doctorId, organCode, nodeCode } = query;

  /** 获取医生号源详情 */
  const fetchSelDoctorDetails = () => {
    dispatch({
      type: 'doctor/selDoctorDetails',
      payload: {
        appointmentType: 1, // 预约挂号
        doctorId,
        hospitalCode: organCode,
      },
    });
  };

  useEffect(() => {
    fetchSelDoctorDetails();
  }, []);

  const onToAppointmentRegistration = (record: NumberSourceItemType) => {
    const { hospitalAreaCode, deptCode } = record;
    if (isHytPerson()) {
      // 跳转到原生的预约挂号
      const appointmentRegistrationParams = `todayTreat=1&doctorId=${doctorId}&hospitalAreaCode=${
        hospitalAreaCode || ''
      }&organCode=${organCode}&nodeCode=${nodeCode}&organName=${hospitalName}&deptCode=${deptCode}`;
      toAppointmentRegistration(appointmentRegistrationParams);
    } else {
      const queryObj: AnyObject = {
        doctorId,
        hospitalCode: organCode,
        hospitalAreaCode,
      };
      if (deptCode) {
        queryObj.deptCode = deptCode;
      }
      // 跳转至微信公众号的预约挂号
      history.push({
        pathname: '/appointment/doctorDetail',
        query: queryObj,
      });
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.title}>请选择您需要挂号的科室</div>
      <div className={styles.doctorInfo}>
        <span>{docName && `${docName}医生`}</span>
        <span>{hospitalName}</span>
      </div>
      <div className={styles.departmentList}>
        {numberSourceList.map((item) => (
          <div
            key={item.deptCode}
            className={styles.department}
            onClick={() => {
              onToAppointmentRegistration(item);
            }}
          >
            <span>{item.deptName}</span>
            <span>&gt;</span>
          </div>
        ))}
      </div>
    </div>
  );
};

export default connect(({ doctor }: { doctor: DoctorModelState }) => ({ doctor }))(DepartmentList);
