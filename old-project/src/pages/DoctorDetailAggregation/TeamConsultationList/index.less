.container {
  padding: 20px;

  .typeItem {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 0 8px 0 #eaedf0;

    .main {
      display: flex;

      .avatar {
        flex-shrink: 0;
        width: 100px;
        height: 100px;
        margin-right: 20px;
        border-radius: 100px;
      }

      .basicInfo {
        .name {
          color: #132e3e;
          font-weight: 500;
          font-size: 32px;
        }

        .desc {
          color: #999;
          font-size: 26px;
        }
      }
    }

    .rightIcon {
      width: 13px;
      height: 21px;
      margin-left: 60px;
    }

    .offIcon {
      flex-shrink: 0;
      width: 116px;
      height: 44px;
    }
  }
}
