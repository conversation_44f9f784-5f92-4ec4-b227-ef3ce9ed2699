import React, { useEffect, useState } from 'react';
import { connect, Dispatch, Loading, history } from 'umi';
import classnames from 'classnames';
import { DoctorModelState } from '@/typings/doctor';
import { HxIndicator } from '@/components';
import { getToken } from '@/utils/parameter';
import styles from './index.less';

const rightIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/right_icon.png';
const offIcon =
  'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor-business/tdzx/%E6%9A%82%E6%9C%AA%E5%BC%80%E5%90%AF%402x.png';

const teamConsultationTypeList = [
  {
    name: '线上专家团队诊疗',
    desc: '权威医生团队线上提供优质医疗服务，您可与医生团队进行图文交流',
    isOn: false,
    servType: 22,
    avatar:
      'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor-business/tdzx/%E7%BA%BF%E4%B8%8A%E4%B8%93%E5%AE%B6%402x.png',
  },
  {
    name: '多学科联合会诊MDT',
    desc: '针对疑难病症的多学科会诊，需满足医生的评估后，定时进行的多人视频交流',
    isOn: false,
    servType: 99999,
    avatar:
      'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor-business/tdzx/%E5%A4%9A%E5%AD%A6%E7%A7%91%E4%BC%9A%E8%AF%8A%402x.png',
  },
];

interface TeamConsultationListProps {
  dispatch: Dispatch;
  doctor: DoctorModelState;
  location: {
    query: {
      /** 医生ID 【用途：接口和跳医生团队列表】 */
      docId: string;
      /** 服务类型 【用途：接口和跳团队主页和跳医生团队列表】 */
      servType: number;
      /** 机构ID 【用途：跳团队主页和跳医生团队列表】 */
      organId: string;
      /** 机构code 【用途： 跳团队主页和跳医生团队列表】 */
      organCode: string;
      /** 医生名称 【用途：跳医生团队列表】 */
      doctorName: string;
    };
  };
  loading: boolean | undefined;
}

const TeamConsultationList: React.FC<TeamConsultationListProps> = (props) => {
  // props
  const {
    dispatch,
    location: {
      query: { docId, servType, organId, organCode, doctorName },
    },
    doctor: { serviceTeamList },
    loading,
  } = props;

  // stata
  const [data, setData] = useState(teamConsultationTypeList);

  const fetchServiceStatus = () => {
    dispatch({
      type: 'doctor/queryTeamByServTypes',
      payload: {
        docId,
        servTypes: [Number(servType)],
      },
    });
  };

  useEffect(() => {
    fetchServiceStatus();
  }, []);

  useEffect(() => {
    const newData = teamConsultationTypeList.map((item) => {
      const isOn = serviceTeamList.find((serviceTeamItem) => serviceTeamItem.servType === item.servType);
      return {
        ...item,
        isOn: Boolean(isOn),
      };
    });
    setData(newData);
  }, [serviceTeamList]);

  const toInquiry = (servType: number) => {
    // 判断是否有多个团队
    const teamList = serviceTeamList.find((item) => item.servType === servType)?.teamList;
    if (!teamList) return;
    if (teamList.length === 1) {
      // 一个团队，直接跳转到团队咨询页面
      window.location.href = `${API_ZXMZ}/online/teamdetail?servType=${servType}&teamId=${
        teamList[0].teamId
      }&organId=${organId}&organCode=${organCode}&token=${getToken()}`;
    }
    if (teamList.length > 1) {
      // 多个团队，进入团队选择页面
      history.push({
        pathname: '/team/doctorteamlist',
        query: {
          docId,
          servType,
          organId,
          organCode,
          doctorName,
        },
      });
    }
  };

  return (
    <div className={styles.container}>
      {loading && <HxIndicator />}
      {data.map((item) => (
        <div
          key={item.name}
          className={classnames(styles.typeItem)}
          style={{ alignItems: item.isOn ? 'center' : 'unset' }}
          onClick={() => {
            if (!item.isOn) return;
            toInquiry(item.servType);
          }}
        >
          <div className={styles.main}>
            <img className={styles.avatar} src={item.avatar} alt="" />
            <div className={styles.basicInfo}>
              <div className={styles.name}>{item.name}</div>
              <div className={styles.desc}>{item.desc}</div>
            </div>
          </div>
          {item.isOn ? (
            <img className={styles.rightIcon} src={rightIcon} alt="" />
          ) : (
            <img className={styles.offIcon} src={offIcon} alt="" />
          )}
        </div>
      ))}
    </div>
  );
};

export default connect(({ doctor, loading }: { doctor: DoctorModelState; loading: Loading }) => ({
  doctor,
  loading: loading.effects['doctor/queryTeamByServTypes'],
}))(TeamConsultationList);
