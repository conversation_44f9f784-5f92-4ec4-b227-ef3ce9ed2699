import React from 'react';
import { connect } from 'umi';

import { DoctorModelState, TeamItemType } from '@/typings/doctor';
import { getToken, getOpenId } from '@/utils/parameter';
import styles from './index.less';

interface TeamListProps {
  doctor: DoctorModelState;
}

const TeamList: React.FC<TeamListProps> = (props) => {
  // props
  const {
    doctor: { doctorInfo },
  } = props;
  const { teamList = [], workOrganId, doctorId, doctorType, doctorName, deptCode, deptName } = doctorInfo;

  /**
   * 跳转到在线咨询购买页面
   * @param {*} teamInfo 团队信息数据
   */
  const toBuy = (teamInfo: TeamItemType) => {
    const { price, servTime, servType } = teamInfo;
    window.location.href = `${API_ZXMZ}/online/verify?price=${price}&servTime=${servTime}&organId=${workOrganId}&servType=${servType}&doctorId=${doctorId}&doctorType=${doctorType}&doctorName=${doctorName}&organCode=HYTAPP&token=${getToken()}&openId=${getOpenId()}&deptCode=${deptCode}&deptName=${deptName}`;
  };

  return (
    <div className={styles.main}>
      {teamList.map((item) => (
        <div
          className={styles.team}
          onClick={() => {
            toBuy(item);
          }}
        >
          <img src={item.portrait} alt="" />
          <div className={styles.teamInfo}>
            <div className={styles.teamNameAndDep}>
              <span className={styles.teamName}>{item.teamName}</span>
              <span className={styles.department}>{item.department}</span>
            </div>
            <div className={styles.hospName}>{item.hospName}</div>
            <div className={styles.introduction}>{item.introduction}</div>
            <div className={styles.price}>{`¥ ${item.price}/${item.servTime}小时`}</div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default connect(({ doctor }: { doctor: DoctorModelState }) => ({ doctor }))(TeamList);
