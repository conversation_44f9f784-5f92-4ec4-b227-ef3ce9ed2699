import React, { useCallback, useEffect, useRef, useState } from 'react';
import { connect, Dispatch, history, Loading } from 'umi';
import { Toast, Modal } from 'antd-mobile';
import _ from 'lodash';

import { DoctorModelState, NumberSourceItemType } from '@/typings/doctor.d';
import { Comment, GuideToExternalBrowser, StyleComponents, HxDrawer, HxIcon } from '@/components';

import AppScheme from '@/utils/AppScheme';
import { isWechat, isHytDoctor, isHytPerson, isTfsmy } from '@/utils/platform';
import openAppUtil from '@/utils/openApp';
import { SingWechatJSSDKDataType } from '@/typings/global';
import { configWechatShareStyle, getCurEvnHref } from '@/utils/tool';
import HealthScience from '@/pages/Department/Components/HealthScience';
import { sensorsRequest } from '@/utils/sensors';
import { HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import { TopInfo, MiddlePart, GdpbModal } from './components';
import styles from './index.less';
import { addShareToApp } from '../util';

const doctor_defalut_logo = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/default-doctor-logo.png';
const doctor_notice = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-main-notice.png';
const icon_right = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/icon_right.png';
const introImg = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-main-intro.png';
const goodAt = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-main-goodat.png';
const downArrow = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/down-arrow.png';
const upArrow = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/up-arrow.png';
const { PartialComment } = Comment;
const { removeShare } = AppScheme;
const { ScrollText } = StyleComponents;

interface DoctorDetailAggregationProps {
  doctor: DoctorModelState;
  dispatch: Dispatch;
  location: {
    query: {
      doctorCode: string;
      doctorId: string;
      organCode: string;
      servCode: string;
      appCode: string;
      channelCode: string;
      docFillRecordId: string; // 认定记录id
      /** 医生主页中间显示模块类型（number类型） 0:医生服务 1:医学资讯列表 2: 专病服务包列表 */
      middleType: string;
      /** 是否是二维码扫描出来的页面（boolean类型） */
      openApp: string;
      /** 重定向需要的参数 */
      purePlayKey: 'DH';
      /** 重定向路由的必要参数组成的字符串 */
      purePlayParam: string;
      organId: string;
      pmi: string;
      /** 互联网总医院接入（医健通页面跳转过来携带的机构参数） */
      channel?: string;
      doctor_id?: string;
      actId?: string;
    };
  };
  detailLoading: boolean | undefined;
  numberSourceLoading: boolean | undefined;
}

const DoctorDetailAggregation: React.FC<DoctorDetailAggregationProps> = (props) => {
  // props
  const {
    doctor: { doctorInfo, numberSourceList, doctorCommentData },
    dispatch,
    location: { query },
    detailLoading,
    numberSourceLoading,
  } = props;
  // doctorInfo.organCode
  // doctorInfo.workOrganId;
  // 将query中的字符串undefined转为undefined类型
  Object.keys(query).forEach((key) => {
    if (query[key] === 'undefined') {
      query[key] = undefined;
    }
  });
  // purePlayParam中的参数
  const purePlayParamObj: AnyObject = {};

  // 重定向存在，必要参数从purePlayParam里面取（减少路由带的参数）
  if (query.purePlayKey === 'DHNEW') {
    const purePlayParamArr = decodeURIComponent(query.purePlayParam).split('|');
    purePlayParamArr.forEach((item) => {
      const keyAndValue = item.split('@');
      if (keyAndValue && keyAndValue.length === 2) {
        const objKey = keyAndValue[0];
        const objValue = keyAndValue[1] === 'undefined' ? undefined : keyAndValue[1];
        purePlayParamObj[objKey] = objValue;
      }
    });
  }

  if (query.purePlayKey === 'DH') {
    const purePlayParamArr = query.purePlayParam.split('|');
    purePlayParamArr.forEach((item) => {
      const keyAndValue = item.split('@');
      if (keyAndValue && keyAndValue.length === 2) {
        const objKey = keyAndValue[0];
        const objValue = keyAndValue[1] === 'undefined' ? undefined : keyAndValue[1];
        purePlayParamObj[objKey] = objValue;
      }
    });
    const { doctorCode, doctorId, organCode, servCode, middleType } = purePlayParamObj;
    window.location.href = `${THE_DOMAIN}/doctor/hxhome?organCode=HID0101&channelCode=PATIENT_WECHAT&appCode=HXGYAPP&purePlayKey=DH&purePlayParam=doctorCode@undefined|doctorId@${doctorId}|servCode@zxmz|middleType@1|openApp@true`;
  }
  const { doctorCode, organCode = 'HID0101', middleType, openApp, appCode, channelCode } =
    query.purePlayKey === 'DHNEW' ? purePlayParamObj : query;

  /* 医健通跳转参数处理 */
  let { doctorId, servCode = 'jhzy', actId = '' } = query.purePlayKey === 'DHNEW' ? purePlayParamObj : query;
  const channel = query?.channel || HxSessionStorage.get(StorageEnum.YJT_CHANNEL);
  if (channel === 'HLWZYY') {
    actId = query?.actId || HxSessionStorage.get(StorageEnum.YJT_ACTID);
    doctorId = query?.doctor_id || HxSessionStorage.get(StorageEnum.YJT_DOCTOR_ID);
  }
  actId && (servCode = 'zxyz');

  const {
    doctorType,
    workOrganId,
    followStatus,
    organName,
    docTitle,
    doctorServiceInfo,
    doctorComment,
    satisfaction,
    portrait = doctor_defalut_logo,
    doctorName,
    deptName,
    organId,
    profession,
    introduction,
    servType,
  } = doctorInfo;

  // const { query }: any = useLocation();
  const { commentList = [] } = doctorCommentData || {};

  const { notice } = doctorServiceInfo || {};

  // const { appCode, channelCode, organCode, token, deptName } = query;
  const { docFillRecordId, pmi } = query;
  const speMedOrganId = query?.organId;
  doctorInfo.workOrganId = docFillRecordId ? speMedOrganId : workOrganId;
  // state
  const [currentMiddleType, setCurrentMiddleType] = useState(middleType ? Number(middleType) : actId ? 1 : 0);
  const [guideImageVisible, setGuideImageVisible] = useState(false);
  const [modalVisible, setModalVisible] = useState(false); // 号源是否可用弹窗
  const [applayDisabled, setApplayDisabled] = useState(false); // middlepart里的申请按钮disabled
  const [visible, setVisible] = useState<boolean>(servCode === 'gdpb');
  const [modalSwitch, setModalSwitch] = useState<boolean>(false);
  const [toPayTime, setToPayTime] = useState<number>(7);
  const [isExpend, setIsexpend] = useState<boolean>(true);
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  // 健康科普列表
  const [healthList, setHealthList] = useState<Array<any>>([]);

  // ref
  const doctorDetailRef = useRef(null);

  const pathnameQuery = `doctorCode=${doctorCode}&doctorId=${doctorId}&organCode=${organCode}&servCode=${
    currentMiddleType === 0 ? 'jhzy' : servCode
  }&middleType=${currentMiddleType}`;

  /** 外部浏览器中拦截该页面的所有点击事件 */
  const interceptClickEvents = () => {
    doctorDetailRef.current?.addEventListener('click', (e: Event) => {
      if (isWechat() || isHytPerson() || isHytDoctor() || isTfsmy()) {
        return;
      }
      e.stopPropagation();
      openAppUtil.openAppModal({
        appPageUrl: `hxgyappscheme://startapp/web?url=${THE_DOMAIN}/doctor/home?${pathnameQuery}`,
      });
    });
  };

  /** 获取医生号源详情 */
  const fetchSelDoctorDetails = (data?: any) => {
    dispatch({
      type: 'doctor/getDoctorIsUse',
      payload: {
        organCode: 'HID0101',
        doctorType: data.doctorType,
        doctorId,
        organId: data.workOrganId,
        servType: data.servType,
      },
      callback: (res) => {
        if (String(res) === 'false') {
          if (servCode === 'zxmz') {
            setModalVisible(true);
            setApplayDisabled(true);
          } else {
            setModalSwitch(true);
          }
        }
      },
    });
  };

  /**
   * @name: 获取评论列表
   * @param {*}
   * @return {*}
   */
  const getCommentList = () => {
    dispatch({
      type: 'doctor/getDoctorCommentListNew',
      payload: {
        pageNum: 1,
        pageSize: 5,
        query: {
          organCode,
          appCode,
          channelCode,
          enterCode: 'DOC_HOME',
          toAppraiserId: doctorId,
        },
      },
    });
  };

  /**
   * 获取医生详情
   * @param isJHZY 是否是聚合主页
   */
  const fetchDoctorInfo = (isJHZY: boolean = false) => {
    dispatch({
      type: 'doctor/queryDoctorInfo',
      payload: {
        doctorCode: doctorCode === 'undefined' ? undefined : doctorCode,
        doctorId,
        organCode: servCode === 'yxzximg' ? 'HYTAPP' : organCode,
        pageNum: 1,
        pageSize: 5,
        servCode: isJHZY ? 'jhzy' : servCode,
      },
      callback: (res) => {
        if (res) {
          const doction = {
            deptName: res.deptName,
            doctorId: res.doctorId,
            docName: res.doctorName,
            deptCategoryCode: res.deptCode,
            organCode: servCode === 'yxzximg' ? 'HYTAPP' : organCode,
          };
          if (servCode === 'zxmz') {
            sensorsRequest('URL_MZ_DoctorInfo', doction);
          } else {
            sensorsRequest('URL_GH_DoctorDetail ', doction);
          }
        }

        isJHZY && setCurrentMiddleType(0);
        currentMiddleType === 1 && fetchSelDoctorDetails(res);
      },
    });
  };

  /** 给原生二维码传参 */
  const doAddShareToApp = () => {
    const pathData = {
      doctorCode,
      organCode,
      doctorId,
      servCode,
      middleType,
    };
    const otherData = {
      portrait,
      doctorName,
      docTitle,
      organName,
      deptName,
      servCode,
    };
    addShareToApp(pathData, otherData);
  };

  const addEventListener = () => {
    window.addEventListener('pageshow', function () {
      doAddShareToApp();
      const param = 'color=blue';
      AppScheme.changeColor(param);
    });
    window.addEventListener('pagehide', function () {
      removeShare();
      const param = 'color=white';
      AppScheme.changeColor(param);
    });
  };

  /** 设置分享给朋友的样式 */
  const defineShareAppStyle = () => {
    // 在线门诊医生主页中的快速问诊【固定排班】分享后显示在线门诊业务 （需求by龚蓉蓉+康惠子）
    const link = getCurEvnHref().replace('gdpb', 'zxmz');
    dispatch({
      type: 'global/singWechatJSSDK',
      payload: {
        url: getCurEvnHref(),
      },
      callback: (data: SingWechatJSSDKDataType) => {
        const title = `【${doctorName}】${docTitle}`;
        const desc = `${organName} ${deptName}`;

        const configData = {
          ...data,
          debug: false,
          jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'],
        };

        const shareData = {
          title,
          desc,
          imgUrl: portrait,
          link,
          success: () => {
            // console.log('lcc-分享成功');
          },
        };
        configWechatShareStyle(configData, shareData);
      },
    });
  };

  /** 倒计时 */
  const initToPayTime = () => {
    const payTime = setInterval(() => {
      if (toPayTime <= 0) {
        clearTimeout(payTime);
      } else {
        setToPayTime((toPayTime) => toPayTime - 1);
      }
    }, 1000);
  };

  /** 关闭Modal框 */

  const changeVisible = () => {
    if (toPayTime <= 0) {
      setVisible(!visible);
      if (modalSwitch) {
        setModalVisible(true);
        setApplayDisabled(true);
      }
    }
  };
  const enterCode = 'PAT_DOCTOR_HOME';
  // 健康科普列表（默认5条）
  const getHealthList = () => {
    const params = {
      pageNum: 1,
      pageSize: 5,
      query: {
        appCode,
        organCode,
        channelCode,
        attrId: doctorId,
        enterCode,
        operator: 1,
      },
    };
    dispatch({
      type: 'department/getDepartmentHealthList',
      payload: params,
      cb: (res: any) => {
        setHealthList(res || []);
      },
    });
  };

  const jumpTo = (pathname: string, query: any = {}) => {
    history.push({ pathname, query });
  };

  const goMore = (pathname = '/department/healthscience') => {
    jumpTo(pathname, { appCode, channelCode, organCode, enterCode, attrId: doctorId });
  };

  useEffect(() => {
    setTimeout(() => {
      fetchDoctorInfo();
      getHealthList();
      interceptClickEvents();
      addEventListener();
      getCommentList();
    }, 500);
    return () => {
      // 清除原生二维码传参
      removeShare();
      /* 评论数据清除 */
      dispatch({
        type: 'doctor/updateState',
        payload: {
          doctorCommentData: {
            commentList: [],
            totalCount: 0,
            satisfaction: 0,
          },
        },
      });
    };
  }, []);

  /** 微信定义分享给朋友的样式 */
  useEffect(() => {
    isWechat() && !_.isEmpty(doctorInfo) && defineShareAppStyle();
  }, [doctorInfo]);

  /** 倒计时 */
  useEffect(() => {
    !_.isEmpty(doctorInfo) && toPayTime > 0 && initToPayTime();
  }, [doctorInfo]);

  useEffect(() => {
    (detailLoading || numberSourceLoading) && Toast.loading('正在加载中...');
  }, [detailLoading, numberSourceLoading]);

  /**
   * 验证token是否失效
   * @param callback token存在后的回调
   */
  const doCheckToken = (callback: () => void) => {
    dispatch({
      type: 'global/checkToken',
      callback,
    });
  };

  useEffect(() => {
    if (_.isEmpty(doctorInfo) || openApp === 'true') {
      return;
    }
    doAddShareToApp();
  }, [doctorInfo]);

  /** 关注医生 */
  const onFavorite = () => {
    if (isWechat() || isHytPerson() || isHytDoctor() || isTfsmy()) {
      dispatch({
        type: 'doctor/docFollow',
        payload: {
          doctorId,
          doctorType,
          workOrganId,
          status: followStatus === 2 ? 1 : 2,
          workOrganName: organName,
          workOrganCode: organCode || 'HID0101',
          level: docTitle,
          showOriginData: true,
        },
        callback: () => {
          fetchDoctorInfo();
          Toast.info(followStatus === 1 ? '取消关注成功' : '关注成功', 1);
        },
      });
    }
  };

  const goBriefInfo = () => {
    const pathname = `/doctor/briefinfo?${pathnameQuery}`;
    history.push(pathname);
  };

  /** 查看公告 */
  const onShowNotice = () => {
    // Modal.alert('医生公告', <div style={{ textAlign: 'left' }}>{notice}</div>);
    setDrawerOpen(true);
  };

  const changeCurrentMiddleType = useCallback((type: number) => {
    if (type === 0) {
      // 为0表示回到聚合主页，请求医生聚合主页详情
      fetchDoctorInfo(true);
    } else {
      setCurrentMiddleType(type);
    }
  }, []);

  const goToAllComment = () => {
    if (isWechat() || isHytPerson() || isHytDoctor() || isTfsmy()) {
      const callback = () => {
        history.push({
          pathname: '/allcomment',
          query: {
            teamOrDoctorId: doctorId,
            organCode,
            type: 'DOC_HOME',
          },
        });
      };
      if (isWechat()) {
        doCheckToken(callback);
      } else {
        callback();
      }
    }
  };

  /** 获取医生人事机构的号源 */
  const fetchNumberSource = (callback: (list: NumberSourceItemType[]) => void) => {
    dispatch({
      type: 'doctor/selDoctorDetails',
      payload: {
        appointmentType: 1, // 预约挂号
        doctorId,
        hospitalCode: organCode,
      },
      callback: (list: NumberSourceItemType[]) => {
        if (list.length) {
          callback(list);
        } else {
          Toast.info('当前医生无明日之后的排班');
        }
      },
    });
  };

  /** 展开与收起擅长和简介内容 */
  const expendBtn = () => {
    setIsexpend(!isExpend);
  };

  return (
    <div className={styles.container} ref={doctorDetailRef}>
      <GuideToExternalBrowser
        visible={guideImageVisible}
        onClick={() => {
          setGuideImageVisible(false);
        }}
      />
      <div>
        <TopInfo doctorInfo={doctorInfo} onFavorite={onFavorite} goBriefInfo={goBriefInfo} />
        {notice && (
          <div className={styles.notice} id="toppoint">
            <img src={doctor_notice} alt="" className={styles.doctorNotice} />
            <div className={styles.noticeCont} onClick={onShowNotice}>
              <ScrollText text={notice} />
              <img src={icon_right} alt="" className={styles.noticeIcon} />
            </div>
          </div>
        )}
        <HxDrawer
          drawerShow={drawerOpen}
          title="医生公告"
          drawerContent={notice}
          clickBg={() => setDrawerOpen(false)}
          closeBtn={() => setDrawerOpen(false)}
        />
        {/* 医生擅长和简介 */}
        <div className={styles.intro}>
          <div className={isExpend ? styles.introduce : styles.introduceExpend}>
            <img src={goodAt} alt="" className={styles.goodAt} key={profession} />
            <div className={styles.introContent}>{profession || '-'}</div>
          </div>
          <div className={isExpend ? styles.introduce : styles.introduceExpend}>
            <img src={introImg} alt="" className={styles.goodAt} key={introduction} />
            <div className={styles.introContent}>{introduction || '-'}</div>
          </div>
          <div className={styles.arrowBox}>
            <img
              src={isExpend ? downArrow : upArrow}
              alt=""
              className={styles.introArrow}
              onClick={() => expendBtn()}
            />
          </div>
        </div>

        <div>
          <MiddlePart
            middleType={currentMiddleType}
            doctorInfo={doctorInfo}
            changeCurrentMiddleType={changeCurrentMiddleType}
            servCode={servCode}
            fetchNumberSource={fetchNumberSource}
            doCheckToken={doCheckToken}
            applayDisabled={applayDisabled}
            numberSourceList={numberSourceList}
            organCode={organCode}
            docFillRecordId={docFillRecordId}
            pmi={pmi}
            channel={channel}
          />
        </div>
        {/* 健康科普相关，内容平台的医生跳转过来需要显示的板块 */}
        {/* 健康科普 */}
        {servCode === 'jhzy' ? (
          <div className={styles.health}>
            <div className={styles.title}>
              <div className={styles.left}>健康科普</div>
              <div className={styles.right} onClick={() => goMore('/department/healthscience')}>
                <div className={styles.hint}>查看更多</div>
                <HxIcon iconName="arrow-right" className={styles.more} />
              </div>
            </div>
            <div className={styles.healthList}>
              <HealthScience list={healthList} />
            </div>
          </div>
        ) : null}
        <PartialComment
          commentList={commentList}
          character={<HxIcon iconName="Star1" />}
          goToAllComment={goToAllComment}
        />
      </div>
      {/* 固定排版不显示modal */}
      {(Number(servType) !== 21 || Number(servCode) !== 29) && currentMiddleType === 1 && (
        <Modal
          visible={modalVisible}
          transparent
          maskClosable
          onClose={() => setModalVisible(false)}
          title="温馨提示"
          footer={[
            {
              text: <div style={{ color: '#3AD3C1' }}>确定</div>,
              onPress: () => {
                setModalVisible(false);
              },
            },
          ]}
        >
          <div>
            <span>该医生号源已约满，您可申请该科室其他医生</span>
          </div>
        </Modal>
      )}
      <GdpbModal visible={visible} toPayTime={toPayTime} changeVisible={changeVisible} />
    </div>
  );
};

export default connect(({ doctor, loading }: { doctor: DoctorModelState; loading: Loading }) => ({
  doctor,
  detailLoading: loading.effects['doctor/queryDoctorInfo'],
  numberSourceLoading: loading.effects['doctor/selDoctorDetails'],
}))(DoctorDetailAggregation);
