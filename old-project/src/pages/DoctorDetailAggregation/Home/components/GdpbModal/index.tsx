import React from 'react';
import { Modal } from 'antd-mobile';

interface TopInfoProps {
  /** 是否显示modal框 */
  visible: boolean;
  /** 倒计时 */
  toPayTime: number;
  /** 关闭modal框 */
  changeVisible: () => void;
}

const GdpbModal: React.FC<TopInfoProps> = ({ visible, toPayTime, changeVisible }) => {
  return (
    <Modal
      title="温馨提示"
      visible={visible}
      animationType="slide"
      transparent
      onClose={changeVisible}
      footer={[{ text: toPayTime > 0 ? `${toPayTime}秒` : '确定', onPress: () => changeVisible() }]}
    >
      <div style={{ textAlign: 'left' }}>
        <div>
          您即将申请<span style={{ color: '#D03838', fontWeight: 'bold' }}>快速线上门诊</span>，问诊须知如下，请知晓：
        </div>
        <div>
          1.请在上午<span style={{ color: '#D03838', fontWeight: 'bold' }}>8：00~11：30</span>，下午
          <span style={{ color: '#D03838', fontWeight: 'bold' }}>14：00~17:30</span> 申请问诊。
        </div>
        <div>
          2.支付后即表示申请成功，
          <span style={{ color: '#D03838', fontWeight: 'bold' }}>医生将在30分钟内接诊，有效问诊时长为1小时</span>
        </div>
        <div>
          3.医生接诊后，请您在有效时长内完成问诊。
          <span style={{ color: '#D03838', fontWeight: 'bold' }}>超时系统将自动结束</span>，届时医生将无法为您继续看诊。
        </div>
      </div>
    </Modal>
  );
};

export default GdpbModal;
