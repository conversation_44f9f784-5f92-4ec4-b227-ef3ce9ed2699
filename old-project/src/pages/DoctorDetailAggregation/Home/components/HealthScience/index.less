.healthService {
  margin: 24px;
  padding: 32px 24px 0 24px;
  background-color: #fff;
  border-radius: 16px;
  .titleStore {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      color: #03081a;
      font-weight: 600;
      font-size: 36px;
    }
    .seeMore {
      color: #989eb4;
      font-size: 26px;
    }
  }
  .contentList {
    padding: 32px 0 34px 0;
    border-bottom: 2px solid #ebedf5;
    .contentInfo {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;
      .infoLeft {
        .title {
          display: -webkit-box;
          width: 410px;
          overflow: hidden;
          color: #03081a;
          font-weight: 600;
          font-size: 32px;
          text-overflow: ellipsis;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
        }
        .tags {
          margin-top: 20px;
          span {
            margin-right: 20px;
            padding: 0 8px;
            color: #3ad3c1;
            font-weight: 500;
            font-size: 20px;
            border: 1px solid #6cebe2;
            border-radius: 15px;
          }
        }
      }
      .poster {
        width: 204px;
        height: 140px;
        margin-top: 6px;
        border-radius: 12px;
      }
    }
    .reading {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 32px;
      .doctorInfo {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .avater {
          width: 48px;
          height: 48px;
          margin-right: 16px;
        }
        .info {
          display: flex;
          justify-content: flex-start;
          color: #989eb4;
          font-size: 24px;
          .organName {
            width: 190px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          div {
            margin-right: 24px;
          }
        }
      }
      .giveLike {
        display: flex;
        color: #989eb4;
        font-size: 20px;
        :nth-child(1) {
          margin-right: 22px;
        }
      }
    }
    .videoContent {
      position: relative;
      .videoPoster {
        width: 654px;
        height: 360px;
        border-radius: 16px;
      }
      .player {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        z-index: 100;
        width: 72px;
        height: 72px;
        margin: auto;
      }
      .videoDesc {
        position: absolute;
        top: 6px;
        left: 16px;
        display: inline-block;
        padding: 0 8px;
        color: #fff;
        font-size: 20px;
        border: 1px solid #fff;
        border-radius: 16px;
      }
    }
    .videoTitle {
      width: 654px;
      margin-top: 24px;
      overflow: hidden;
      color: #03081a;
      font-weight: 600;
      font-size: 32px;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
}
