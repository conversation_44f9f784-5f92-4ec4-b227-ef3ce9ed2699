import React from 'react';
import styles from './index.less';

const defaultAvater = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/default-doctor-logo.png';
const player = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-viceo.png';

interface HealthServiceProps {
  // /** 医生相关数据 */
  // doctorInfo: DoctorInfoType;
  // /** 关注医生 */
  // onFavorite: () => void;
  // /** 跳转至医生简介页面 */
  // goBriefInfo: () => void;
}

const HealthServise: React.FC<HealthServiceProps> = (props) => {
  console.log('HealthServise====', props);
  return (
    <div className={styles.healthService}>
      <div className={styles.titleStore}>
        <div className={styles.title}>健康科普</div>
        <div className={styles.seeMore}>查看更多 &gt;</div>
      </div>
      {/* 健康科普内容列表部分 */}
      <div className={styles.contentList}>
        {/* 文章的 */}
        <div className={styles.contentInfo}>
          <div className={styles.infoLeft}>
            <div className={styles.title}>
              还在观望近视手术？华西眼科专家说，我一堆同事都做还在观望近视手术？华西眼科专家说，我一堆同事都做过…
            </div>
            <div className={styles.tags}>
              <span>华西大讲堂</span>
              <span>原创</span>
            </div>
          </div>
          <img
            src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-main-notice.png"
            alt=""
            className={styles.poster}
          />
        </div>
        <div className={styles.reading}>
          <div className={styles.doctorInfo}>
            <img src={defaultAvater} alt="" className={styles.avater} />
            <div className={styles.info}>
              <div className={styles.organName}>四川大学华西医院四川大学华西医院</div>
              <div>李主任</div>
            </div>
          </div>
          <div className={styles.giveLike}>
            <div>阅读 10W+</div>
            <div>赞 6661</div>
          </div>
        </div>
      </div>

      <div className={styles.contentList}>
        {/* 视频的 */}
        <div className={styles.videoContent}>
          <video
            src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/first-video.mp4"
            className={styles.videoPoster}
          />
          <img src={player} alt="" className={styles.player} />
          <div className={styles.videoDesc}>华西大讲堂</div>
        </div>
        <div className={styles.videoTitle}>一个常见坏习惯，让年轻人的耳朵越来越聋了</div>
        <div className={styles.reading}>
          <div className={styles.doctorInfo}>
            <img src={defaultAvater} alt="" className={styles.avater} />
            <div className={styles.info}>
              <div className={styles.organName}>四川大学华西医院四川大学华西医院</div>
              <div>李主任</div>
            </div>
          </div>
          <div className={styles.giveLike}>
            <div>阅读 10W+</div>
            <div>赞 6661</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HealthServise;
