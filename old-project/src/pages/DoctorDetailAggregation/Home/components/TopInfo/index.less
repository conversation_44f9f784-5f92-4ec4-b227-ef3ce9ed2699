.topInfo {
  .bgCont {
    .topBg {
      height: 278px;
      margin-bottom: 110px;
      padding-top: 52px;
      background: linear-gradient(180deg, #3ad3c1 0%, #6cebe2 100%);
    }
    .doctorInfo {
      width: 702px;
      margin: auto;
      padding: 24px;
      background-repeat: no-repeat;
      .info {
        display: flex;
        justify-content: space-between;
        .avatar {
          width: 128px;
          height: 128px;
          margin-right: 30px;
          border: 1px solid #f5f6fa;
          border-radius: 50%;
        }

        .mainInfo {
          display: flex;
          flex: 1;
          flex-direction: column;
          justify-content: space-between;
          padding: 5px 0;
          color: #03081a;
          font-size: 26px;

          p {
            margin-bottom: 4px;
          }

          .name {
            margin-right: 16px;
            font-weight: 600;
            font-size: 36px;
          }

          .docTitle {
            font-size: 28px;
          }

          .hospital {
            margin-right: 16px;
            font-size: 28px;
          }
        }

        .favoriteCont {
          height: 52px;
          margin-top: 5px;
          padding: 0 26px;
          line-height: 52px;
          background: linear-gradient(90deg, #3ad3c1 0%, #68e9db 100%);
          border-radius: 26px;
          .favorite {
            display: flex;
            align-items: center;
            padding: 0;
            color: #fff;
            font-size: 26px;
            .addIcon {
              width: 19px;
              height: 19px;
              margin-right: 6px;
            }
          }
        }
        .selectFavorite {
          height: 52px;
          margin-top: 5px;
          padding: 0 26px;
          color: #b0b3bf;
          line-height: 52px;
          background-color: #ebedf5;
          border-radius: 26px;
        }
      }
      .time {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        p {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          width: 50%;
          margin-bottom: 0;
          padding: 6px 0;
          font-size: 28px;

          span {
            &:first-child {
              margin-bottom: 5px;
              color: #3ad3c1;
              font-weight: bold;
              font-size: 40px;
            }

            &:nth-child(2) {
              color: #989eb4;
            }
          }
        }
      }
    }

    .doctorbriefInfo {
      margin-top: 30px;
      color: #fff;

      .introduction {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        word-break: break-all;
      }

      .profession {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 6px;

        .professionText {
          flex: 1;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          word-break: break-all;
        }

        .goBriefInfo {
          width: 24px;
          height: 24px;
          margin-left: 20px;
        }
      }
    }
  }
}
