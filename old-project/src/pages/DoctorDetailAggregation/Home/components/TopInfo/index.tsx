import React from 'react';

import { DoctorInfoType } from '@/typings/doctor.d';
import styles from './index.less';

const doctor_defalut_logo = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/default-doctor-logo.png';
const add_logo = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/addicon.png';
// const arrow_circle = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/arrow_circle%402x.png';
const doctorMainBg = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-main-bg.png';

interface TopInfoProps {
  /** 医生相关数据 */
  doctorInfo: DoctorInfoType;
  /** 关注医生 */
  onFavorite: () => void;
  /** 跳转至医生简介页面 */
  goBriefInfo: () => void;
}

const TopInfo: React.FC<TopInfoProps> = ({ doctorInfo, onFavorite }) => {
  const {
    portrait,
    doctorName,
    docTitle,
    organName,
    deptName,
    followStatus,
    // introduction,
    // profession,
    patientNumByPerson,
    receptionNumByPerson,
    fans,
  } = doctorInfo;

  return (
    <div className={styles.topInfo}>
      <div className={styles.bgCont}>
        <div className={styles.topBg}>
          <div
            className={styles.doctorInfo}
            key={doctorInfo?.doctorId}
            style={{
              background: `url(${doctorMainBg})`,
              backgroundSize: '100%',
            }}
          >
            <div className={styles.info}>
              <img src={portrait || doctor_defalut_logo} alt="" className={styles.avatar} />
              <div className={styles.mainInfo}>
                <p>
                  <span className={styles.name}>{doctorName}</span>
                  <span className={styles.docTitle}>{docTitle}</span>
                </p>
                <p>
                  <span className={styles.hospital}>{organName}</span>
                  <span>{deptName}</span>
                </p>
              </div>
              {followStatus === 2 ? (
                <div className={styles.favoriteCont} onClick={onFavorite}>
                  <div className={styles.favorite}>
                    <img src={add_logo} alt="" className={styles.addIcon} />
                    关注
                  </div>
                </div>
              ) : (
                <div className={styles.selectFavorite} onClick={onFavorite}>
                  已关注
                </div>
              )}
            </div>
            <div className={styles.time}>
              <p>
                <span>{patientNumByPerson || '-'}</span>
                <span>在线服务患者</span>
              </p>
              <p>
                <span>{receptionNumByPerson || '-'}</span>
                <span>在线问诊数</span>
              </p>
              <p>
                <span>{fans || '-'}</span>
                <span>关注数</span>
              </p>
            </div>
          </div>
        </div>

        {/* <div className={styles.doctorbriefInfo} onClick={goBriefInfo}>
          <div className={styles.introduction}>简介：{introduction || '暂无内容'}</div>
          <div className={styles.profession}>
            <div className={styles.professionText}>擅长：{profession || '暂无内容'}</div>
            <img src={arrow_circle} alt="" className={styles.goBriefInfo} />
          </div>
        </div> */}
      </div>
    </div>
  );
};

export default TopInfo;
