import React, { useState } from 'react';
import { Toast, Modal } from 'antd-mobile';
import { history, Dispatch, connect, useLocation } from 'umi';

import { DoctorInfoType, ChildServiceType, NumberSourceItemType } from '@/typings/doctor.d';
import AppScheme from '@/utils/AppScheme';
import { getToken, getOpenId, getOrganCode } from '@/utils/parameter';
import { isWechat, isHytPerson, isHytDoctor, isTfsmy } from '@/utils/platform';
import { HxIcon } from '@/components';
import { sensorsRequest } from '@/utils/sensors';
import { HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import qs from 'query-string';
import styles from './index.less';
import { DOCTOR_ADVISORY_IMG_LIST, SERV_CODE_LIST } from '../../../dataDictionary';

const { toAppointmentRegistration, removeShare } = AppScheme;

interface MiddlePartProps {
  /** 当前中间模块类型 0: 医生服务模块 1: 医学咨询模块 2: 专病服务模块 */
  middleType: number;
  /** 医生信息数据 */
  doctorInfo: DoctorInfoType;
  /** 服务code（从地址栏中获取） */
  servCode: string;
  /** 机构code（从地址栏中获取） */
  organCode: string;
  numberSourceList: any;
  /** 认证记录id（从地址栏中获取） */
  docFillRecordId: string;
  pmi: string;
  /** 改变当前中间模块类型 */
  changeCurrentMiddleType: (type: number) => void;
  /** 获取该医生号源信息 */
  fetchNumberSource: (callback: (list: NumberSourceItemType[]) => void) => void;
  /** 校验token是否失效 */
  doCheckToken: (callback: () => void) => void;
  applayDisabled?: boolean; // 申请按钮是否可用
  dispatch: Dispatch;
  channel?: string; // 省医健通跳转参数
}

// 申请之前判断有误号源逻辑
const isNosourceBeforeApply = ['zxyz', 'ypzx', 'hlzx'];

const MiddlePart: React.FC<MiddlePartProps> = (props) => {
  // props
  const { query }: any = useLocation();
  const {
    middleType = 0,
    doctorInfo,
    servCode,
    fetchNumberSource,
    doCheckToken,
    organCode = 'HID0101',
    docFillRecordId,
    pmi,
    applayDisabled = false,
    dispatch,
    channel = '',
  } = props;

  const [modalVisible, setModalVisible] = useState(false); // 号源是否可用弹窗
  const {
    serviceList = [],
    doctorId,
    nodeCode,
    organName,
    doctorCode,
    organId,
    teamList = [],
    workOrganId,
    doctorType,
    doctorName,
    breathNotice,
    deptCode,
    deptName,
  } = doctorInfo;

  const tiNode = (
    <div className={styles.notice}>
      <div className={styles.content} dangerouslySetInnerHTML={{ __html: breathNotice }} />
    </div>
  );
  const toMobileProject = (url: string) => {
    // console.log('toMobileProject');
    // 先清除原生的分享按钮
    removeShare();
    setTimeout(() => {
      window.location.href = url;
    }, 100);
  };

  /**
   * 渲染服务图标
   * @param serviceCode 服务code
   * @param status 服务状态 1: 开启 0: 关闭
   */
  const renderServiceImg = (serviceCode: string, status: number) => {
    const imgItem = DOCTOR_ADVISORY_IMG_LIST.find((item) => item.serviceCode === serviceCode);
    return <img src={status === 1 ? imgItem?.onImgSrc : imgItem?.offImgSrc} alt="" />;
  };

  /**
   * 获取医生号源的回调
   * @param list 医生号源列表
   */
  const fetchNumberSourceCallback = (list: NumberSourceItemType[]) => {
    if (list.length === 1) {
      if (isHytPerson()) {
        // 跳转到原生的预约挂号
        const appointmentRegistrationParams = `todayTreat=1&doctorId=${doctorId}&hospitalAreaCode=${
          list[0].hospitalAreaCode || ''
        }&organCode=${organCode}&nodeCode=${nodeCode}&organName=${organName}&deptCode=${list[0].deptCode}`;
        toAppointmentRegistration(appointmentRegistrationParams);
      } else {
        const queryObj: AnyObject = {
          doctorId,
          hospitalCode: organCode,
          hospitalAreaCode: list[0].hospitalAreaCode,
        };
        if (list[0].deptCode) {
          queryObj.deptCode = list[0].deptCode;
        }
        // 跳转至微信公众号的预约挂号
        doCheckToken(() => {
          history.push({
            pathname: '/appointment/doctorDetail',
            query: queryObj,
          });
        });
      }
    } else {
      // 跳转到选择科室页面
      doCheckToken(() => {
        history.push({
          pathname: '/doctor/departmentlist',
          query: {
            doctorId,
            organCode,
            nodeCode,
          },
        });
      });
    }
  };

  /**
   * 点击对应的服务的交互
   * @param serviceCode 服务code
   * @param status 服务状态 0【开启】 1【关闭】
   * @param servType 服务类型
   */
  const onClickService = (serviceCode: string, status: number, servType: number) => {
    if (status === 0) {
      return;
    }

    // if (isWechat() || isHytPerson() || isHytDoctor() || isTfsmy()) {
    // const noticePathname = `${API_ZXMZ}/online/notice?Dcode=${doctorCode}&doctorId=${doctorId}&organId=${organId}&organCode=HID0101&token=${getToken()}`;
    const noticePathname = `${API_ZXMZ}/noticeRouter/noticeRouter?Dcode=${doctorCode}&doctorId=${doctorId}&organId=${organId}&servCode=zxmz&organCode=HID0101&token=${getToken()}&nextPage=doctorDetail`;
    switch (serviceCode) {
      case 'yygh': // 预约挂号
        fetchNumberSource(fetchNumberSourceCallback);
        break;

      case 'yxzx': // 医学咨询
        Toast.info('服务暂未开放，敬请期待');
        // changeCurrentMiddleType(1);
        break;

      case 'zxmz': // 在线门诊
        // 先清空model层的数据
        // this.clearDoctorDetail();
        // 跳转到在线门诊须知页面（阅读须知后直接到在线门诊医生详情）
        if (isWechat() || isTfsmy()) {
          doCheckToken(() => {
            toMobileProject(noticePathname);
          });
        } else {
          toMobileProject(noticePathname);
        }
        break;

      case 'zbfw': // 专病服务
        Toast.info('服务暂未开放，敬请期待');
        // 请求慢病服务包
        // this.fetchSpecialDiseaseService();
        break;
      case 'yxzximg': // 影像云咨询
        if (isWechat() || isTfsmy()) {
          // doCheckToken(() => {

          // });
          toMobileProject(noticePathname);
        } else {
          toMobileProject(noticePathname);
        }
        break;
      case 'tdzx': // 团队咨询
        // 进入团队咨询选择页面
        history.push({
          pathname: '/doctor/teamconsultationlist',
          query: {
            docId: doctorId,
            servType,
            organId,
            organCode,
            doctorName,
          },
        });
        break;

      default:
        break;
    }
    // }
  };

  /**
   * 服务对应的图标
   * @param {*} servType 服务类型
   */
  const findServiceIcon = (servType: number) => {
    const servItem = SERV_CODE_LIST.find((item) => item.type === servType);
    return servItem?.icon;
  };

  /** 跳转到相应页面 */
  const toRelativePage = async (advisory: ChildServiceType) => {
    console.log(advisory);
    console.log(servCode, '--------------servCode----------------');
    const { servType, price, servTime, numLimit, status, serviceId } = advisory;
    const initOrganCode = SERV_CODE_LIST.find((item) => item.type === servType)?.initOrganCode;
    if (status === 0) {
      Toast.info('暂未开通服务', 1);
      return;
    }
    if (applayDisabled) {
      return;
    }
    if (servCode === 'zxyz') {
      // 在线义诊要点申请按钮才能去查询是否有号无号，真是坑
      let flag;
      // 查询是否有号源
      await dispatch({
        type: 'doctor/getDoctorIsUse',
        payload: {
          doctorType,
          doctorId,
          organId,
          servType,
          volunteerId: serviceId,
        },
        callback: (res) => {
          if (String(res) === 'false') {
            flag = String(res);
            setModalVisible(true);
          }
        },
      });
      if (flag === 'false') {
        return;
      }
    }
    if (servType === 3) {
      const params = {
        hospitalCode: getOrganCode(),
        deptCategoryCode: deptCode,
        deptCategoryName: deptName,
        doctorName,
        doctorId,
      };
      sensorsRequest('SF_DOCTOR_DETAIL', { ...params }); // 随访门诊医生详情页埋点
    }
    if (isWechat() || isHytPerson() || isHytDoctor() || isTfsmy()) {
      // const cardInfo = localStorage.getItem('cardInfo') || '{}';
      // const cardInfo = localStorage.getItem('cardInfo') || '{}';

      const callback = () => {
        const params: AnyObject = {
          price,
          servTime,
          organId: workOrganId,
          servType,
          doctorId,
          doctorType,
          numLimit,
          doctorName,
          organCode: middleType !== 0 ? (docFillRecordId ? 'HID0101' : organCode) : initOrganCode,
          token: getToken(),
          openId: getOpenId(),
          deptCode,
          deptName,
        };
        /* 在线义诊 省医健通跳转的渠道编码和cardNo */
        if (servCode === 'zxyz') {
          channel && (params.yjtChannel = channel);
          const ehealth_card_id = query.ehealth_card_id || HxSessionStorage.get(StorageEnum.YJT_CARD_NO);
          const yzActId = query.actId || HxSessionStorage.get(StorageEnum.YJT_ACTID);
          ehealth_card_id && (params.ehealth_card_id = ehealth_card_id);
          yzActId && (params.yzActId = yzActId);
        }
        // 跳转到咨询购买页面
        const onlineVerifyPathname = `/online/verify?${qs.stringify(params)}`;

        switch (servType) {
          case 1:
          case 3: // 在线门诊 随访门诊
            // 申请“呼吸内科”医生的在线门诊/门特时，展示温馨提示信息
            if (breathNotice) {
              Modal.alert('温馨提示', tiNode, [
                { text: '取消', onPress: () => {} },
                {
                  text: '继续问诊',
                  onPress: () => {
                    toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}`);
                  },
                },
              ]);
            } else if (docFillRecordId) {
              const cardInfo = localStorage.getItem('cardInfo');
              const speMedicinePathname = `/online/verify?price=${price}&servTime=${servTime}&organId=${workOrganId}&docFillRecordId=${docFillRecordId}&cardInfo=${cardInfo}&servType=${servType}&doctorId=${doctorId}&doctorType=${doctorType}&numLimit=${numLimit}&doctorName=${doctorName}&organCode=${
                middleType !== 0 ? (docFillRecordId ? 'HID0101' : organCode) : initOrganCode
              }&token=${getToken()}&openId=${getOpenId()}&deptCode=${deptCode}&deptName=${deptName}`;
              toMobileProject(`${API_ZXMZ}${speMedicinePathname}`);
            } else {
              // 跳转到咨询购买页面
              toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}&pmi=${pmi}`);
            }
            break;
          case 2: // 就医咨询（在线咨询）
          case 20: // 特需咨询
          case 21: // 固定排班
            // 跳转到咨询购买页面
            toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}`);
            break;
          case 22: // 团队咨询
            // 判断是否有多个团队
            if (teamList.length > 1) {
              // 多个团队，跳转到选择团队页面
              history.push({
                pathname: '/doctor/teamlist',
                query: {
                  doctorCode,
                  doctorId,
                  organCode,
                  servCode,
                },
              });
            } else {
              const { price, servTime } = teamList[0];
              toMobileProject(
                `${API_ZXMZ}/online/verify?price=${price}&servTime=${servTime}&organId=${workOrganId}&servType=${servType}&doctorId=${doctorId}&doctorType=${doctorType}&doctorName=${doctorName}&organCode=HYTAPP&token=${getToken()}&openId=${getOpenId()}&deptCode=${deptCode}&deptName=${deptName}`,
              );
            }
            break;
          case 28: // 影像云咨询
            // 跳转到咨询购买页面
            toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}`);
            break;

          case 29: // 在线义诊
            toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}&volunteerId=${serviceId}`);
            break;
          case 31: // 用药咨询
            // 跳转到咨询购买页面
            toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}`);
            break;
          case 32: // 护理咨询
            // 跳转到咨询购买页面
            toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}`);
            break;
          default:
            break;
        }
      };

      if (isWechat()) {
        doCheckToken(callback);
      } else {
        callback();
      }
    }
  };

  /** 渲染医生服务模块 */
  // ui改版 所有聚合主页样式同渲染医学资讯模块
  const renderDoctorService = () => {
    return (
      <div className={styles.servicesWrapper}>
        {serviceList.map((item) => (
          <div key={item.serviceId} className={styles.advisory}>
            {renderServiceImg(item.servCode, item.status)}
            <div className={styles.advisoryInfo}>
              <div className={styles.serviceName}>{item.serviceName}</div>
              <div className={styles.serviceDesc}>{item.serviceDesc || ''}</div>
            </div>
            <div
              style={{ width: 76 }}
              className={item.status === 0 || applayDisabled ? styles.button_gray : styles.button_green}
              onClick={() => onClickService(item.servCode, item.status, item.servType)}
            >
              {item.status === 0 || applayDisabled ? '暂未开通' : item.servCode === 'yygh' ? '去挂号' : '去咨询'}
              {/* 非聚合主页或者无状态不显示 */}
              {middleType !== 0 || item.status === 0 || applayDisabled ? null : (
                <HxIcon className={styles.arrow} iconName="youjiantoubai" />
              )}
            </div>
          </div>
        ))}
      </div>
    );
  };

  /**  渲染医学咨询模块 */
  const renderDoctorAdvisory = () => {
    const { serviceList = [] } = doctorInfo || {};
    let advisoryServiceList = [];
    if (middleType !== 0) {
      // 为非聚合主页，取serviceList
      advisoryServiceList = serviceList;
    } else {
      // 否则为聚合主页，取医学咨询下的childService
      const serviceAdvisoryItem = serviceList.find((item) => item.servCode === 'yxzx');
      advisoryServiceList = serviceAdvisoryItem?.childService || [];
    }
    return (
      <div>
        <div className={styles.titleBox}>
          <div className={styles.title}>医生服务</div>
          {/* middleType标示其他网页跳转过来，决定中间显示什么 */}
          <div
            className={styles.otherService}
            onClick={() => {
              history.push({
                pathname: '/doctor/homejhzy',
                query: {
                  doctorCode,
                  doctorId,
                  organCode,
                  servCode: 'jhzy',
                  middleType: 0,
                },
              });
            }}
          >
            其他服务
            <HxIcon className={styles.arrowRight} iconName="arrow-right" />
            {/* <span> &gt;</span> */}
          </div>
        </div>
        <div className={styles.advisories}>
          {advisoryServiceList.map((item) => (
            <div key={item.serviceId} className={styles.advisory}>
              <img src={findServiceIcon(item.servType)} alt="" />
              <div className={styles.advisoryInfo}>
                <div className={styles.serviceName}>{item.serviceName}</div>
                <div className={styles.serviceDesc}>{item.serviceDesc}</div>
                <div className={styles.servicePrice}>{`¥${item.price === undefined ? '-' : item.price}/${
                  item.servTime === undefined ? '-' : item.servTime
                }小时(${item.numLimit === undefined ? '-' : item.numLimit}条)`}</div>
              </div>
              <div
                className={item.status === 0 || applayDisabled ? styles.button_gray : styles.button_green}
                onClick={() => {
                  toRelativePage(item);
                }}
              >
                申请
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  /** 渲染显示的模块 */
  const renderComponent = () => {
    switch (middleType) {
      case 0: // 渲染医生服务模块
        return renderDoctorService();
      case 1: // 渲染医学咨询模块
        return renderDoctorAdvisory();

      // case 2: // 渲染专病服务模块
      //   return this.renderSpecialDiseaseService();

      default:
        break;
    }
  };

  return (
    <div className={styles.container}>
      {renderComponent()}
      <Modal
        visible={modalVisible}
        transparent
        maskClosable
        onClose={() => setModalVisible(false)}
        title="温馨提示"
        footer={[
          {
            text: <div style={{ color: '#3AD3C1' }}>确定</div>,
            onPress: () => {
              setModalVisible(false);
            },
          },
        ]}
      >
        <div>
          <span>该医生号源已约满，您可申请该科室其他医生</span>
        </div>
      </Modal>
    </div>
  );
};

// export default MiddlePart;

export default connect(({ doctor }: { doctor: any }) => ({
  doctor,
}))(MiddlePart);
