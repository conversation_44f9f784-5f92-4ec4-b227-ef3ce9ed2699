.container {
  // position: relative;
  min-height: 100vh;
  padding-bottom: 24px;
  font-size: 24px;
  background-color: #f5f6fa;

  .notice {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 24px 24px 24px;
    padding: 24px;
    background-color: #fff;
    border-radius: 12px;

    .doctorNotice {
      width: 84px;
      height: 84px;
      margin-right: 20px;
    }

    .noticeCont {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: space-between;

      .noticeIcon {
        width: 13px;
        height: 21px;
        margin-left: 3px;
      }
    }
  }
  .intro {
    margin: 0 24px 24px 24px;
    padding: 24px;
    font-size: 24px;
    background-color: #fff;
    border-radius: 12px;
    .introduce {
      position: relative;
      display: -webkit-box;
      width: 654px;
      margin-bottom: 24px;
      overflow: hidden;
      text-indent: 2.2rem;
      text-overflow: ellipsis;

      /*! autoprefixer: off */
      -webkit-box-orient: vertical;

      /* autoprefixer: on */
      -webkit-line-clamp: 2;
    }
    .introduceExpend {
      position: relative;
      margin-bottom: 24px;
      text-indent: 2.2rem;
    }
    .goodAt {
      position: absolute;
      top: 8px;
      left: 0;
      width: 54px;
      height: 26px;
      // float: left; // 手机上使用浮动会出现收起之后图标不显示的问题
      // margin-top: 9px;
      // margin-right: 7px;
    }
    .introContent {
      color: #989eb4;
      font-size: 28px;
    }
    .arrowBox {
      display: flex;
      justify-content: center;
      .introArrow {
        width: 36px;
        height: 36px;
      }
    }
  }
  .health {
    margin: 0 24px 24px 24px;
    background: #fff;
    border-radius: 12px;

    .title {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 32px 24px 32px 24px;
      .left {
        color: #03081a;
        font-weight: 600;
        font-size: 36px;
        // line-height: 50px;
      }
      .right {
        display: flex;
        flex-direction: row;
        align-items: center;
        color: #989eb4;
        font-size: 26px;

        .more {
          margin-left: 3px;
          font-size: 22px;
        }
      }
    }
  }
}
