.container {
  position: relative;
  min-height: 100vh;
  font-size: 24px;
  background-color: #fff;
  .header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 30px;
    background-color: #32b9aa;
    .left {
      color: #fff;
      .doctorName {
        font-weight: bold;
        font-size: 36px;
      }
      .two,
      .organName {
        display: flex;
        flex-direction: row;
        font-size: 28px;
        .titleName {
          margin-right: 30px;
        }
      }
    }
    .img {
      width: 140px;
      height: 140px;
      border-radius: 50%;
    }
  }
  .otherOccupation {
    margin-top: 40px;
    padding: 0 30px;
    .title {
      display: flex;
      flex-direction: row;
      align-items: center;
      .img {
        width: 34px;
        height: 34px;
      }
      .name {
        margin-left: 4px;
        color: #333;
        font-weight: bold;
        font-size: 30px;
      }
    }
    .item {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 20px 0;
      border-bottom: 1px solid #eee;
      .left {
        display: flex;
        flex-direction: row;
        align-items: center;
        color: #666;
        font-size: 26px;
        .dot {
          width: 12px;
          height: 12px;
          background-color: #666;
          border-radius: 50%;
        }
        .organName {
          margin-left: 20px;
        }
        .deptName {
          margin-left: 30px;
        }
      }
      .img {
        width: 13px;
        height: 21px;
      }
    }
  }
  .contentPart {
    margin-top: 40px;
    padding: 0 30px;
    .title {
      display: flex;
      flex-direction: row;
      align-items: center;
      .img {
        width: 34px;
        height: 34px;
      }
      .name {
        margin-left: 4px;
        color: #333;
        font-weight: bold;
        font-size: 30px;
      }
    }
    .content {
      margin-top: 30px;
      color: #666;
      font-size: 26px;
    }
  }
}
