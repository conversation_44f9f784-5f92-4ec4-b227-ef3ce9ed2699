import React, { useEffect, useState } from 'react';
import { connect } from 'umi';
import _ from 'lodash';

import { DoctorModelState, WorkType } from '@/typings/doctor.d';
import { isWechat, isHyt<PERSON>erson, isHytDoctor } from '@/utils/platform';
import AppScheme from '@/utils/AppScheme';
import { GuideToExternalBrowser } from '@/components';
import openAppUtil from '@/utils/openApp';
import { addShareToApp } from '../util';
import styles from './index.less';

const { toOrganHome, removeShare } = AppScheme;
const introductionIcon =
  'http://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/img/doctor/ico_%E7%AE%80%E4%BB%8B%402x.png';
const professionIcon =
  'http://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/img/doctor/icon_%E6%93%85%E9%95%BF%402x.png';

interface BriefInfoProps {
  doctor: DoctorModelState;
  location: {
    query: AnyObject;
  };
}

interface ContentProps {
  icon: string;
  title: string;
  content: string;
}

const Content: React.FC<ContentProps> = ({ icon, title, content }) => (
  <div className={styles.contentPart}>
    <div className={styles.title}>
      <img src={icon} alt="图片不见了！" className={styles.img} />
      <div className={styles.name}>{title}</div>
    </div>
    <div className={styles.content}>{content || '暂无内容'}</div>
  </div>
);

const BriefInfo: React.FC<BriefInfoProps> = (props) => {
  // props
  const {
    doctor: { doctorInfo },
    location: { query },
  } = props;
  const {
    doctorName,
    docTitle,
    deptName,
    organName,
    portrait,
    workVos = [],
    introduction,
    profession,
    doctorCode,
    organCode,
    doctorId,
  } = doctorInfo;
  const { servCode = 'jhzy', middleType } = query;

  // state
  const [guideImageVisible, setGuideImageVisible] = useState(false);

  useEffect(() => {
    return () => {
      removeShare();
    };
  }, []);

  const doAddShareToApp = () => {
    const pathData = {
      doctorCode,
      organCode,
      doctorId,
      servCode,
      middleType,
    };
    const otherData = {
      portrait,
      doctorName,
      docTitle,
      organName,
      deptName,
      servCode,
    };
    addShareToApp(pathData, otherData);
  };

  useEffect(() => {
    if (_.isEmpty(doctorInfo)) {
      return;
    }
    doAddShareToApp();
  }, [doctorInfo]);

  /**
   * 跳转到原生的机构页面
   * @param organData 机构数据
   */
  const pushOrganHome = (organData: WorkType) => {
    const { organCode } = organData;

    // 微信浏览器中引导打开app
    if (isWechat()) {
      setGuideImageVisible(true);
      return;
    }
    // 华医通app中跳转原生机构页面
    if (isHytPerson() || isHytDoctor()) {
      const param = `organCode=${organCode}`;
      toOrganHome(param);
      return;
    }
    // 外部浏览器弹出弹框打开app
    openAppUtil.openAppModal({
      appPageUrl: `hxgyappscheme://startapp/native?actionCode=organHome&organCode=${organCode}`,
    });
  };

  return (
    <div className={styles.container}>
      <GuideToExternalBrowser
        visible={guideImageVisible}
        onClick={() => {
          setGuideImageVisible(false);
        }}
      />
      <div>
        <div className={styles.header}>
          <div className={styles.left}>
            <div className={styles.doctorName}>{doctorName}</div>
            <div className={styles.two}>
              <div className={styles.titleName}>{docTitle}</div>
              <div className={styles.deptName}>{deptName}</div>
            </div>
            <div className={styles.organName}>{organName}</div>
          </div>
          <img
            src={
              portrait ||
              'http://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/img/doctor/%E5%8C%BB%E7%94%9F%E5%A4%B4%E5%83%8F%402x.png'
            }
            alt="图片不见了！"
            className={styles.img}
          />
        </div>
        <div className={styles.otherOccupation}>
          <div className={styles.title}>
            <img
              src="http://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/img/doctor/ico_%E6%89%A7%E4%B8%9A%E7%82%B9%402x.png"
              alt="图片不见了！"
              className={styles.img}
            />
            <div className={styles.name}>其他执业点</div>
          </div>
          {workVos.length > 0 ? (
            workVos.map((item) => {
              const { deptName = '', organName = '', organId = '' } = item;
              return (
                <div
                  className={styles.item}
                  key={organId}
                  onClick={() => {
                    pushOrganHome(item);
                  }}
                >
                  <div className={styles.left}>
                    <div className={styles.dot} />
                    <div className={styles.organName}>{organName}</div>
                    <div className={styles.deptName}>{deptName}</div>
                  </div>
                  <img
                    src="http://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/img/doctor/icon_%E7%AE%AD%E5%A4%B4%E5%90%91%E5%8F%B3%402x.png"
                    alt="图片不见了！"
                    className={styles.img}
                  />
                </div>
              );
            })
          ) : (
            <div style={{ fontSize: 13 }}>暂无其他执业点</div>
          )}
        </div>
        <Content icon={introductionIcon} title="简介" content={introduction} />
        <Content icon={professionIcon} title="擅长" content={profession} />
      </div>
    </div>
  );
};

export default connect(({ doctor }: { doctor: DoctorModelState }) => ({ doctor }))(BriefInfo);
