/*
 * @Author: <PERSON>
 * @Date: 2022-03-18 16:28:30
 * @LastEditor: <PERSON>
 * @LastEditTime: 2025-01-02 15:43:01
 * @FilePath: /hyt-person/src/pages/DoctorDetailAggregation/index.tsx
 * Copyright (c) 2022 by <PERSON>, All Rights Reserved.
 * @Description:
 */
import React from 'react';
import qs from 'query-string';
import { ModuleEnum } from '@/utils/enum';
import { getChannelCode, getOrganCode } from '@/utils/parameter';
import { HxRedirect } from '@/utils/interceptor';

// export default (props: any) => {
//   console.log(props);
//   // 组装参数
//   const search: any = qs.parse(window.location.href.split('?')[1]) || {};
//   search.channelCode = search?.channelCode
//     ? search.channelCode
//     : getChannelCode()
//     ? getChannelCode()
//     : 'PATIENT_WECHAT';
//   search.organCode = search.organCode ? search.organCode : getOrganCode() ? getOrganCode() : 'HID0101';
//   // 重定向地址
//   const redirectUrl = `/${ModuleEnum.MODULE_DOCTORHOME}/${
//     !search.organCode.includes('HID0101') || search.servCode === 'zxyz' ? 'home' : 'hxhome'
//   }`.toLowerCase();

//   Object.keys(search).forEach((key) => {
//     if (search[key] === 'undefined') {
//       search[key] = undefined;
//     }
//   });
//   // purePlayParam中的参数
//   const purePlayParamObj: AnyObject = {};

//   // 重定向存在，必要参数从purePlayParam里面取（减少路由带的参数）
//   if (search.purePlayKey === 'DH') {
//     const purePlayParamArr = search.purePlayParam.split('|');
//     purePlayParamArr.forEach((item: any) => {
//       const keyAndValue = item.split('@');
//       if (keyAndValue && keyAndValue.length === 2) {
//         const objKey = keyAndValue[0];
//         const objValue = keyAndValue[1] === 'undefined' ? undefined : keyAndValue[1];
//         purePlayParamObj[objKey] = objValue;
//       }
//     });
//   }
//   const { doctorId } = search.purePlayKey === 'DH' ? purePlayParamObj : search;

//   history.replace({
//     pathname: redirectUrl,
//     query: {
//       ...props.location.query,
//       ...search,
//     },
//   });

//   return</>;
// };

// 是否需要访问口令
// const needAccessToken = false;
// 组装参数

export default () => {
  const query: any = qs.parse(window.location.href.split('?')[1]) || {};
  query.channelCode = query?.channelCode ? query.channelCode : getChannelCode() ? getChannelCode() : 'PATIENT_WECHAT';
  query.organCode = query.organCode
    ? query.organCode
    : query.hospitalCode
    ? query.hospitalCode
    : getOrganCode()
    ? getOrganCode()
    : 'HID0101';

  /**
   * 华西医院医生主页- doctor/hxhome
   * 平台医院新医生主页- doctor/platformhome（上锦）
   * 其他医院医生主页- doctor/home
   */
  // 重定向地址
  const getRedirectDestination = (organCode, servCode) => {
    if (!['HID0101', 'HID0102'].includes(organCode) || servCode === 'zxyz') {
      return 'home';
    }
    return organCode === 'HID0101' ? 'hxhome' : 'platformhome';
  };
  const destination = getRedirectDestination(query.organCode, query.servCode);
  const redirectUrl = `/${ModuleEnum.MODULE_DOCTORHOME}/${destination}`.toLowerCase();

  const defaultParams = {
    pathname: redirectUrl,
    search: `${qs.stringify(query)}`,
  };

  return <HxRedirect params={defaultParams} />;
};
