import React, { useCallback, useEffect, useState } from 'react';
import { history, useDispatch } from 'umi';
import styles from './index.less';
import { DatePicker, ImageViewer, Modal, Toast } from 'antd-mobile-v5';
import { AppointMentType, OutpatientType, YesOrNo } from '../enum';
import qs from 'qs';
import dayjs from 'dayjs';
import { addAppointment, queryDetailById, queryScheduleUrl } from '../service';
import FormRadio from '../components/FormRadio';
import FormInput from '../components/FormInput';
import FormPicker from '../components/FormPicker';

const RegistrationForm: React.FC = () => {
  const dispatch = useDispatch();
  const { cardId, id } = qs.parse(location.search.slice(1));
  const [cardInfo, setCardInfo] = useState<any>({});
  const [formData, setFormData] = useState<any>({
    name: '',
    appointmentType: AppointMentType.GH,
    idCard: '',
    self: 1,
    relativeName: '',
    relativeIdCard: '',
    telephone: '',
    doctorName: '',
    appointmentDate: '',
    relativePhone: '',
    outpatientType: '',
    cardId,
  });
  const [visible, setVisible] = useState(false);
  const [dateVisible, setDateVisible] = useState(false);
  const [paiBanImg, setPaiBanImg] = useState('');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const validate = (body) => {
    for (const key in body) {
      if (body[key] === '') {
        Toast.show({
          content: '请填写完整信息',
        });
        return false;
      }
    }
    if (body.hasOwnProperty('telephone')) {
      if (!/^1\d{10}$/.test(body.telephone)) {
        Toast.show({
          content: '请输入正确的电话号码',
        });
        return false;
      }
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const handler = Toast.show({
      icon: 'loading',
    });
    try {
      const body = {
        ...formData,
      };
      if (body.self === 1) {
        delete body.relativeName;
        delete body.relativeIdCard;
      }
      if (body.self === 0) {
        body.telephone = body.relativePhone;
      }
      delete body.relativePhone;
      if (validate(body)) {
        const res = await addAppointment(body);
        if (res && res.code === '1') {
          Toast.show({
            icon: 'success',
            content: '预约成功',
          });
          setTimeout(() => {
            history.go(-1);
          }, 1000);
        } else {
          Toast.show({
            content: res.message || '预约失败',
          });
        }
      }
    } catch (error) {
      Toast.show({
        content: '提交失败，请重试！',
      });
    } finally {
      handler?.close();
    }
  };

  const labelRenderer = useCallback((type: string, data: number) => {
    switch (type) {
      case 'year':
        return data + '年';
      case 'month':
        return data + '月';
      case 'day':
        return data + '日';
      case 'hour':
        return data + '时';
      case 'minute':
        return data + '分';
      case 'second':
        return data + '秒';
      default:
        return data;
    }
  }, []);

  const getCardInfo = (initValues) => {
    return new Promise<void>((resolve) => {
      dispatch({
        type: 'patientCard/cardInfo',
        payload: {
          cardId,
        },
        callback(res) {
          console.log('cardInfo', res);
          setCardInfo(res);
          if (initValues && initValues?.self === 0) {
            initValues.relativePhone = initValues?.telephone;
            initValues.telephone = res ? res?.tel : '';
          }
          setFormData((prev) => ({
            ...prev,
            idCard: res.credNo,
            name: res.patientName,
            telephone: res.tel || '',
            ...(initValues || {}),
          }));
          resolve();
        },
      });
    });
  };

  const queryScheduleUrlByConfig = async () => {
    const data = await queryScheduleUrl();
    setPaiBanImg(data);
  };

  const init = async () => {
    const handler = Toast.show({
      icon: 'loading',
      maskClickable: false,
    });
    let initValues = null;
    if (id) {
      const data = await queryDetailById(id);
      initValues = data;
    }
    await queryScheduleUrlByConfig();
    await getCardInfo(initValues);
    handler.close();
  };

  useEffect(() => {
    init();
  }, []);

  return (
    <div className={styles.container}>
      <img src={require('../assets/images/ghdj.png')} />
      <div>
        <div>登记须知</div>
        <div>
          1、该登记表仅适用于有效服务期内的“健康人生”签约客户在华西医院门诊信息预约登记。
          <br />
          2、该登记表仅作为信息预登记，挂号情况以实际为准。
          <br />
          3、”健康人生"签约处工作人员将于2个工作日内联系客户告知挂号情况。
          <br />
          4、若您需了解特需门诊挂号情况，请拨打专属服务电话85422408。其余问题，请您联系华西健康人生微信。
        </div>
      </div>
      <div className={styles.form}>
        <div className={styles.formTitle}>请填写就诊人员信息</div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>1、签约客户本人姓名</span>
          </div>
          <FormInput name="name" value={formData.name} onChange={handleInputChange} placeholder="请输入" disabled />
        </div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>2、签约客户本人身份证号码</span>
          </div>
          <FormInput name="idCard" value={formData.idCard} onChange={handleInputChange} placeholder="请输入" disabled />
        </div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>3、是否签约客户本人就诊</span>
          </div>
          <FormRadio
            options={YesOrNo}
            value={formData.self}
            onChange={(val) => setFormData((pre) => ({ ...pre, self: val }))}
          />
        </div>
        {formData.self === 0 ? (
          <>
            <div className={styles.formItem}>
              <div>
                <span className={styles.required}>*</span>
                <span>4、就诊人员姓名</span>
              </div>
              <FormInput
                name="relativeName"
                value={formData.relativeName}
                onChange={handleInputChange}
                placeholder="请输入"
              />
            </div>
            <div className={styles.formItem}>
              <div>
                <span className={styles.required}>*</span>
                <span>5、就诊人员身份证号</span>
              </div>
              <FormInput
                name="relativeIdCard"
                value={formData.relativeIdCard}
                onChange={handleInputChange}
                placeholder="请输入"
                ocr
                ocrChange={(res) => {
                  const { number, name, birth, sex } = res || {};
                  setFormData((pre) => ({
                    ...pre,
                    relativeName: name,
                    relativeIdCard: number,
                  }));
                }}
              />
            </div>
            <div className={styles.formItem}>
              <div>
                <span className={styles.required}>*</span>
                <span>6、就诊人员联系电话</span>
              </div>
              <FormInput
                name="relativePhone"
                value={formData.relativePhone}
                onChange={handleInputChange}
                placeholder="请输入"
              />
            </div>
            <div className={styles.formItem}>
              <div>
                <span className={styles.required}>*</span>
                <span>7、门诊类别</span>
              </div>
              <FormRadio
                options={OutpatientType}
                value={formData.outpatientType}
                onChange={(val) => setFormData((pre) => ({ ...pre, outpatientType: val }))}
              />
            </div>
          </>
        ) : (
          <>
            <div className={styles.formItem}>
              <div>
                <span className={styles.required}>*</span>
                <span>4、就诊人员电话号码</span>
              </div>
              <FormInput
                name="telephone"
                value={formData.telephone}
                onChange={handleInputChange}
                placeholder="请输入"
              />
            </div>
            <div className={styles.formItem}>
              <div>
                <span className={styles.required}>*</span>
                <span>5、门诊类别</span>
              </div>
              <FormRadio
                options={OutpatientType}
                value={formData.outpatientType}
                onChange={(val) => setFormData((pre) => ({ ...pre, outpatientType: val }))}
              />
            </div>
          </>
        )}
      </div>
      <div className={styles.form}>
        <div className={styles.formTitle}>请填写您拟挂号的专家信息</div>
        <div className={styles.formTips}>
          <div className={styles.formTipsHeader}>
            <img src={require('../assets/images/tips.png')} />
            <span>请于专家坐诊日期前2-7个工作日登记信息</span>
          </div>
          <div className={styles.formTipsContent}>
            <div>医生排班表</div>
            <span>（图片可点击放大）</span>
            <img onClick={() => setVisible(true)} src={paiBanImg} />
            <ImageViewer
              image={paiBanImg}
              visible={visible}
              onClose={() => {
                setVisible(false);
              }}
            />
          </div>
        </div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>1、专家姓名</span>
          </div>
          <FormInput
            name="doctorName"
            value={formData.doctorName}
            onChange={handleInputChange}
            placeholder="若您不指定专家姓名，也可输入科室名称"
          />
        </div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>2、专家坐诊日期</span>
          </div>
          <FormPicker
            onClick={() => setDateVisible(true)}
            value={formData.appointmentDate}
            iconUrl={require('../assets/images/calendar.png')}
            picker={
              <DatePicker
                title="请选择专家坐诊日期"
                visible={dateVisible}
                min={new Date()}
                style={
                  {
                    '--adm-color-primary': '#c97923',
                  } as React.CSSProperties
                }
                onClose={() => {
                  setDateVisible(false);
                }}
                renderLabel={labelRenderer}
                onConfirm={(val) => {
                  setFormData((pre) => ({ ...pre, appointmentDate: dayjs(val).format('YYYY-MM-DD') }));
                }}
              />
            }
          />
        </div>
      </div>
      <div className={styles.footer}>
        <div onClick={handleSubmit}>提交</div>
      </div>
    </div>
  );
};

export default RegistrationForm;
