import React, { FC, useEffect, useState } from 'react';
import { history } from 'umi';
import { isHytHarmonyOS, isHytPerson, isJhsh, isWechat } from '@/utils/platform';
import { HxSessionStorage } from '@/utils/storage';
import { ModuleEnum, StorageEnum } from '@/utils/enum';
import { getOrganCode } from '@/utils/parameter';
import AppScheme from '@/utils/AppScheme';
import submitSuccessPng from '../assets/images/submitSuccess.png';
import styles from './index.less';

interface IProps {}

const HealthyLifeSignSuccess: FC<IProps> = () => {
  const [paycountDownTime, setPayCountDownTime] = useState<number>(10);
  const goHome = () => {
    if (isHytPerson()) {
      // const payload = { organCode: getOrganCode(), organName: '四川大学华西医院', nodeCode: 'hxyy' };
      // AppScheme.goOrganHome(queryString.stringify(payload));
      AppScheme.closeWeb();
      return;
    }
    if (getOrganCode() === 'HID0101') {
      history.push('/hxHome');
    } else {
      history.push({
        pathname: '/home',
      });
    }
  };
  /* 去支付 */
  const goPay = () => {
    const cardInfo = HxSessionStorage.get(StorageEnum.BIZ_PATIENTCARD_DATA);
    /* app */
    if (isHytPerson() && !isHytHarmonyOS()) {
      const urlData = `cardBean=${JSON.stringify(cardInfo)}&organCode=${getOrganCode()}`;
      AppScheme.toPayList(decodeURIComponent(urlData));
      return;
    }
    /* 微信或建行生活 */
    if (isWechat() || isJhsh() || isHytHarmonyOS()) {
      HxSessionStorage.set(StorageEnum.PATIENTCARD_DATA, cardInfo);
      history.push({ pathname: `/${ModuleEnum.MODULE_OUTPATIENT_PAYMENT}/home` });
    }
  };

  /* 点击立即支付倒计时 */
  const startCountDown = () => {
    const timer = setInterval(() => {
      setPayCountDownTime((time) => {
        if (time === 1) {
          clearInterval(timer);
        }
        return time - 1;
      });
    }, 1000);
  };

  useEffect(() => {
    startCountDown();
  }, []);
  return (
    <div className={styles.signSuccessBox}>
      <img src={submitSuccessPng} alt="" />
      <div className={styles.title}>提交成功</div>
      <span>您已经成功提交签约申请，请等待审核结果并点击按钮进行支付</span>
      <div className={styles.btnList}>
        {paycountDownTime > 0 ? (
          <div className={styles.beforePay}>正在为你开具医嘱，请耐心等待进行支付--{paycountDownTime}s</div>
        ) : (
          <div onClick={goPay}>立即支付</div>
        )}
        {/* <div>在线咨询</div> */}
      </div>
    </div>
  );
};

export default HealthyLifeSignSuccess;
