import React, { useCallback, useEffect, useRef, useState } from 'react';
import { history, useDispatch } from 'umi';
import styles from './index.less';
import classNames from 'classnames';
import { DatePicker, <PERSON>Viewer, Picker, Toast } from 'antd-mobile-v5';
import { Calendar } from 'antd-mobile';
import FormRadio from '../components/FormRadio';
import { AppointMentType, YesOrNo } from '../enum';
import qs from 'qs';
import FormInput from '../components/FormInput';
import FormPicker from '../components/FormPicker';
import dayjs from 'dayjs';
import { HxLocalStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import { addAppointment, getAppointDateList, queryDetailById } from '../service';
import FormTextArea from '../components/FormTextArea';

const RegistrationForm: React.FC = () => {
  const dispatch = useDispatch();
  const { cardId, id } = qs.parse(location.search.slice(1));
  const [cardInfo, setCardInfo] = useState<any>({});
  /* 民族 */
  const ethnic = (HxLocalStorage.get(StorageEnum.BASE_DATA_ETHNIC) || [])?.map((item) => ({
    label: item.dicName,
    value: item.dicCode,
  }));
  /* 职业 */
  const profession = (HxLocalStorage.get(StorageEnum.BASE_DATA_PROFESSION) || [])?.map((item) => ({
    label: item.dicName,
    value: item.dicCode,
  }));
  const SEX_LIST = [
    {
      label: '男',
      value: 1,
    },
    {
      label: '女',
      value: 2,
    },
  ];
  const VIP_SELF = [
    {
      label: '会员本人体检',
      value: 1,
    },
    {
      label: '会员转赠亲属体检',
      value: 0,
    },
  ];
  const AREA_LIST = [
    {
      label: '华西本部',
      value: 'HID0101',
    },
    {
      labelRender: (
        <>
          <span>华西温江体检中心</span>
          <i className={styles.tj}></i>
        </>
      ),
      value: 'F0017',
    },
    {
      labelRender: (
        <>
          <span>华西锦江体检中心</span>
          <i className={styles.tj}></i>
        </>
      ),
      value: 'HID0103',
    },
  ];
  const SELF_ADD = [
    {
      label: '无（使用免费赠送套餐）',
      value: 0,
    },
    {
      label: '有（仅支持华西本部体检选择）',
      value: 1,
    },
  ];
  const [formData, setFormData] = useState<any>({
    name: '',
    appointmentType: AppointMentType.TJ,
    idCard: '',
    self: VIP_SELF[0]?.value,
    relativeName: '',
    relativeIdCard: '',
    appointmentDate: '',
    cardId,
    areaCode: AREA_LIST[0].value,
    age: '',
    sex: SEX_LIST[0].value,
    marry: YesOrNo[0].value,
    selfAdd: SELF_ADD[0].value,
    selfAddContent: '',
    telephone: '',
    nation: '',
    profession: '',
    relativePhone: '',
    relativeAge: '',
  });
  const [visible, setVisible] = useState(false);
  const [professionVisible, setProfissionvisible] = useState(false);
  const [dateVisible, setDateVisible] = useState(false);
  const disabledDate = useRef<any>([]);
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const validate = (body) => {
    for (const key in body) {
      if (body[key] === '') {
        Toast.show({
          content: '请填写完整信息',
        });
        return false;
      }
    }
    if (body.hasOwnProperty('telephone')) {
      if (!/^1\d{10}$/.test(body.telephone)) {
        Toast.show({
          content: '请输入正确的电话号码',
        });
        return false;
      }
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const handler = Toast.show({
      icon: 'loading',
    });
    try {
      const body = {
        ...formData,
      };
      if (body.self === 1) {
        delete body.relativeName;
        delete body.relativeIdCard;
      }
      if (body.self === 0) {
        body.telephone = body.relativePhone;
        body.age = body.relativeAge;
      }
      if (body.selfAdd === 0) {
        delete body.selfAddContent;
      }
      delete body.relativePhone;
      delete body.relativeAge;
      if (validate(body)) {
        body.nationName = ethnic?.find((v) => v.value === formData.nation)?.label;
        body.professionName = profession?.find((v) => v.value === formData.profession)?.label;
        const res = await addAppointment(body);
        if (res && res.code === '1') {
          Toast.show({
            icon: 'success',
            content: '预约成功',
          });
          setTimeout(() => {
            history.go(-1);
          }, 1000);
        } else {
          Toast.show({
            content: res.message || '预约失败',
          });
        }
      }
    } catch (error) {
      Toast.show({
        content: '提交失败，请重试！',
      });
    } finally {
      handler?.close();
    }
  };

  const getCardInfo = (initValues) => {
    return new Promise<void>((resolve) => {
      const handler = Toast.show({
        icon: 'loading',
        maskClickable: false,
      });
      dispatch({
        type: 'patientCard/cardInfo',
        payload: {
          cardId,
        },
        callback(res) {
          console.log('cardInfo', res);
          setCardInfo(res);
          if (initValues && initValues?.self === 0) {
            initValues.relativePhone = initValues?.telephone;
            initValues.relativeAge = initValues?.age;
            initValues.telephone = res ? res?.tel : '';
          }
          setFormData((prev) => ({
            ...prev,
            idCard: res.credNo,
            name: res.patientName,
            telephone: res.tel || '',
            age: res.patientAge || '',
            ...(initValues || {}),
          }));
          resolve();
          handler.close();
        },
      });
    });
  };

  const getAppointmentDate = async (areaCode) => {
    if (areaCode) {
      const handler = Toast.show({
        icon: 'loading',
        duration: 0,
        maskClickable: false,
      });
      const data = await getAppointDateList(areaCode);
      if (data) {
        disabledDate.current = data;
      }
      handler.close();
    }
  };

  const getDateExtra = (date) => {
    const disabledExtra = disabledDate.current
      .filter((item) => {
        if (date instanceof Date) {
          const find = dayjs(item.appointmentDate).format('YYYY-MM-DD') === dayjs(date).format('YYYY-MM-DD');
          return find;
        }
      })
      .map((item) => {
        if (date instanceof Date) {
          if (item.ifHolidays + '' === '1') {
            return {
              info: '',
              disable: true,
            };
          }
          if (item.remainderNum + '' === '0') {
            return {
              info: '已满',
              disable: true,
            };
          }
          return {
            disable: false,
          };
        }
      });
    return disabledExtra.length > 0 ? disabledExtra[0] : { disable: true };
  };

  const init = async () => {
    const handler = Toast.show({
      icon: 'loading',
      maskClickable: false,
    });
    let initValues = null;
    if (id) {
      const data = await queryDetailById(id);
      initValues = data;
    }
    await getCardInfo(initValues);
    await getAppointmentDate(formData?.areaCode);
    handler.close();
  };

  useEffect(() => {
    init();
  }, []);

  return (
    <div className={styles.container}>
      <img src={require('../assets/images/tjbg.png')} />
      <div className={styles.form}>
        <div className={styles.formTitle}>请选择体检方式</div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>1、会员本人体检还是转赠亲属体检</span>
          </div>
          <FormRadio
            options={VIP_SELF}
            value={formData.self}
            onChange={(val) => setFormData((pre) => ({ ...pre, self: val }))}
          />
        </div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>2、您要预约的体检地址</span>
          </div>
          <FormRadio
            options={AREA_LIST}
            value={formData.areaCode}
            onChange={(val) => {
              getAppointmentDate(val);
              setFormData((pre) => ({ ...pre, areaCode: val }));
            }}
          />
        </div>
      </div>

      <div className={styles.form}>
        <div className={styles.formTitle}>请填写体检人信息</div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>1、体检人姓名</span>
          </div>
          <FormInput
            disabled={formData.self === 1}
            placeholder="请输入体检人姓名"
            name={formData.self === 1 ? 'name' : 'relativeName'}
            value={formData.self === 1 ? formData.name : formData.relativeName}
            onChange={handleInputChange}
          />
        </div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>2、体检人身份证号</span>
          </div>
          <FormInput
            disabled={formData.self === 1}
            placeholder="请输入（务必准确填写）"
            name={formData.self === 1 ? 'idCard' : 'relativeIdCard'}
            value={formData.self === 1 ? formData.idCard : formData.relativeIdCard}
            ocr
            onChange={handleInputChange}
            ocrChange={(res) => {
              const { number, name, birth, sex } = res || {};
              setFormData((pre) => ({
                ...pre,
                relativeName: name,
                relativeIdCard: number,
              }));
            }}
          />
        </div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>3、体检人员联系电话</span>
          </div>
          <FormInput
            name={formData.self === 1 ? 'telephone' : 'relativePhone'}
            value={formData.self === 1 ? formData.telephone : formData.relativePhone}
            onChange={handleInputChange}
            placeholder="请输入"
          />
        </div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>4、您要预约体检日期</span>
          </div>
          <FormPicker
            onClick={() => setDateVisible(true)}
            value={formData.appointmentDate}
            iconUrl={require('../assets/images/calendar.png')}
            picker={
              <Calendar
                getDateExtra={getDateExtra}
                type="one"
                visible={dateVisible}
                minDate={new Date()}
                style={
                  {
                    '--adm-color-primary': '#c97923',
                    position: 'fixed',
                    left: 0,
                    zIndex: 9999,
                  } as React.CSSProperties
                }
                onCancel={() => {
                  setDateVisible(false);
                }}
                onConfirm={(val) => {
                  setDateVisible(false);
                  setFormData((pre) => ({ ...pre, appointmentDate: dayjs(val).format('YYYY-MM-DD') }));
                }}
              />
            }
          />
        </div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>5、体检人年龄</span>
          </div>
          <FormInput
            type="number"
            placeholder="请输入体检人年龄"
            name={formData.self === 1 ? 'age' : 'relativeAge'}
            value={formData.self === 1 ? formData.age : formData.relativeAge}
            onChange={handleInputChange}
          />
        </div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>6、性别</span>
          </div>
          <FormRadio
            options={SEX_LIST}
            value={formData.sex}
            onChange={(val) => setFormData((pre) => ({ ...pre, sex: val }))}
          />
        </div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>7、是否已婚</span>
          </div>
          <FormRadio
            options={YesOrNo}
            value={formData.marry}
            onChange={(val) => setFormData((pre) => ({ ...pre, marry: val }))}
          />
        </div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>8、选择民族</span>
          </div>
          <FormPicker
            onClick={() => setVisible(true)}
            iconUrl={require('../assets/images/rightArrow.png')}
            value={ethnic?.find((v) => v.value === formData.nation)?.label}
            picker={
              <Picker
                title="请选择选择民族"
                visible={visible}
                columns={[ethnic]}
                style={
                  {
                    '--adm-color-primary': '#c97923',
                  } as React.CSSProperties
                }
                onClose={() => {
                  setVisible(false);
                }}
                onConfirm={(val) => {
                  setFormData((pre) => ({ ...pre, nation: val[0] }));
                }}
              />
            }
          />
        </div>
        <div className={styles.formItem}>
          <div>
            <span className={styles.required}>*</span>
            <span>9、选择职业</span>
          </div>
          <FormPicker
            onClick={() => setProfissionvisible(true)}
            value={profession?.find((v) => v.value === formData.profession)?.label}
            iconUrl={require('../assets/images/rightArrow.png')}
            picker={
              <Picker
                title="请选择职业"
                visible={professionVisible}
                columns={[profession]}
                style={
                  {
                    '--adm-color-primary': '#c97923',
                  } as React.CSSProperties
                }
                onClose={() => {
                  setProfissionvisible(false);
                }}
                onConfirm={(val) => {
                  setFormData((pre) => ({ ...pre, profession: val[0] }));
                }}
              />
            }
          />
        </div>
      </div>

      <div className={styles.form}>
        <div className={styles.formTitle}>请选择有无体检自费加项</div>
        <div className={styles.formTips}>
          <div className={styles.formTipsHeader}>
            <img src={require('../assets/images/tips.png')} />
            <div>
              <span> 温馨提示</span>
              <div>1、如在温江或锦江体检，自费加项填无。</div>
              <div>2、加项直接与现场工作人员确认。</div>
            </div>
          </div>
        </div>
        <div className={styles.formItem}>
          <div className={styles.none}></div>
          <FormRadio
            options={SELF_ADD}
            value={formData.selfAdd}
            onChange={(val) => setFormData((pre) => ({ ...pre, selfAdd: val }))}
          />
          {formData.selfAdd === 1 && (
            <div className={styles.formItemMt}>
              <FormTextArea
                name="selfAddContent"
                value={formData.selfAddContent}
                placeholder="请输入自费加项内容…"
                onChange={handleInputChange}
              />
            </div>
          )}
        </div>
      </div>

      <div className={styles.footer}>
        <div onClick={handleSubmit}>提交</div>
      </div>
    </div>
  );
};

export default RegistrationForm;
