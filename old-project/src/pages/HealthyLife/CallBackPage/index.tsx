import React, { useState, useEffect, FC } from 'react';
import { isJhsh } from '@/utils/platform';
import { getOrganCode } from '@/utils/parameter';
import { DotLoading, Toast } from 'antd-mobile-v5';
import { Face } from '@/utils/useFace';
import { queryApplyInfoByDealSeq } from '../service';

interface PageProps {
  location: {
    query: {
      dealSeq: string;
    };
  };
}
const HealthyCallBackPage: FC<PageProps> = (props) => {
  const goPage = async (res) => {
    const { id } = res || {};
    const callBackUrl = `${THE_DOMAIN}/HealthyLife/PreviewProtocol?id=${id}`;
    const { credNo = '', patientName = '', credType = '' } = res || {};
    if (getOrganCode() === 'HID0101' || isJhsh()) {
      Face({
        bussinessCode: 'jk-pat-sign',
        credNo,
        credType,
        callBackUrl,
        patientName,
      });
    }
  };
  const queryData = async () => {
    const {
      location: { query = {} },
    } = props;
    const { dealSeq = '' }: any = query;
    try {
      const res = await queryApplyInfoByDealSeq({
        dealSeq,
      });
      if (res.code === '1') {
        goPage(res?.data);
      } else {
        Toast.show('订单查询失败');
      }
    } catch (error) {
      console.log('error:', error);
      Toast.show('订单查询失败');
    }
  };
  useEffect(() => {
    queryData();
  }, []);
  return (
    <div style={{ width: '100%', height: '100vh', textAlign: 'center', fontSize: '20px', lineHeight: '100vh' }}>
      正在为你跳转
      <DotLoading color="primary" />
    </div>
  );
};

export default HealthyCallBackPage;
