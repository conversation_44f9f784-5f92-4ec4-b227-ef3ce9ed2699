import React, { FC, useEffect, useState } from 'react';
import { history } from 'umi';
import { HxEmpty } from '@/components';
import styles from './index.less';

interface IProps {
  handleConfirm: any;
  protocolUrl?: string;
  location?: {
    query: {
      id: string;
    };
  };
}

const PreviewProtocol: FC<IProps> = (props) => {
  const { handleConfirm, protocolUrl = 'https%3A%2F%2Fcdnhyt.cd120.com%2Fperson%2Fassets%2Fjkrsqy.pdf' } = props;
  /* 去签字 */
  const goSign = () => {
    const { location: { query: { id = '' } = {} } = {} } = props;
    history.push({
      pathname: '/HealthyLife/Signature',
      query: {
        id,
      },
    });
  };

  return (
    <div className={styles.previewDetail}>
      {protocolUrl ? (
        // eslint-disable-next-line jsx-a11y/iframe-has-title
        <iframe
          className={styles.content}
          frameBorder={0}
          title=""
          src={`https://cdnhyt.cd120.com/3/pdfjs/web/viewer.html?file=${protocolUrl}`}
        />
      ) : (
        <div style={{ height: '70vh' }}>
          <HxEmpty emptyMsg="协议暂未生成" canRefresh={false} />
        </div>
      )}

      <div
        className={styles.footer}
        onClick={() => {
          /* 作为组件查看 */
          if (handleConfirm) {
            handleConfirm();
          } else {
            /* 作为页面跳转到签字页面 */
            goSign();
          }
        }}
      >
        <span>确认</span>
      </div>
    </div>
  );
};

export default PreviewProtocol;
