.previewDetail {
  .content {
    width: 750px;
    height: calc(100vh - 96px - 60px - 60px);
    margin-top: 60px;
    overflow: hidden;
  }
  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 0;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    > span {
      display: inline-block;
      width: 702px;
      height: 96px;
      background: #3ad3c1;
      border-radius: 48px;
      box-shadow: 0px 0px 8px 0px #ebedf5;
      color: #fff;
      text-align: center;
      line-height: 96px;
      font-size: 36px;
    }
  }
}
