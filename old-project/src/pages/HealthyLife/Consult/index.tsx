import React, { useEffect, useMemo, useState } from 'react';
import styles from './index.less';
import { queryAdmissionRecord, selectPatient } from '../service';
import qs from 'qs';
import { useDispatch } from 'umi';
import { getChannelCode, getToken } from '@/utils/parameter';
import { Toast } from 'antd-mobile-v5';
import { Modal } from 'antd-mobile';
import dayjs from 'dayjs';
import AppScheme from '@/utils/AppScheme';
import { isHytPerson } from '@/utils/platform';

const Consult: React.FC = () => {
  const { cardId, id } = qs.parse(location.search.slice(1));
  const dispatch = useDispatch();
  const [cardInfo, setCardInfo] = useState<any>({});
  const [list, setList] = useState<any>([]);

  const empty = useMemo(() => {
    return list.length === 0;
  }, [list]);

  const getCardInfo = async () => {
    const res = await selectPatient({
      servType: '51',
      isshowOrganCode: true,
      channel: 'WECHAT',
    });
    if (res && res.code === '1') {
      const data = res.data || [{ cards: [] }];
      const list = data[0] || { cards: [] };
      const cards = list.cards || [];
      const card = cards.find((item: any) => item.cardId === cardId);
      setCardInfo(card);
      return card;
    }
  };
  const getList = async (patientId) => {
    const body = {
      patientId,
      organId: '2cc97d8a641c73f601641c73f6f70000', // 华西
    };
    const res = await queryAdmissionRecord(body);
    if (res && res.code === '1') {
      setList(res?.data || []);
    }
  };

  const toZixun = () => {
    const item = list.filter((item: any) => ['1', '2'].includes(item.status + ''));
    if (item && item.length > 0) {
      Modal.alert(
        <div>提醒</div>,
        <div style={{ textAlign: 'left', color: '#03081A' }}>当前有正在咨询的会话，是否跳转到咨询页面</div>,
        [
          {
            text: '取消',
            onPress: () => {},
          },
          {
            text: '确定',
            onPress: () => {
              const data = item[0];
              if (isHytPerson()) {
                AppScheme.toOnlineChat({
                  docImAccount: data.account,
                  admId: data.admId,
                });
              } else {
                window.location.href = `${API_ZXMZ}/online/im?admissionId=${data.admId}&imGroupId=${
                  data.account
                }&token=${getToken()}&organCode=HID0101&channelCode=${getChannelCode()}`;
                // window.location.href = `${API_ZXMZ}/online/imList?token=${getToken()}&servCode=zxmz&organCode=HID0101&channelCode=${getChannelCode()}`;
              }
            },
          },
        ],
      );
      return;
    }
    localStorage.setItem('ZxmzPatientInfo', JSON.stringify(cardInfo));
    setTimeout(() => {
      window.location.href = `${API_ZXMZ}/online/zxmzcaseList?token=${getToken()}&type=&servType=&sku=&mode=1`;
    }, 0);
  };

  const init = async () => {
    const ref = Toast.show({ icon: 'loading', duration: 0 });
    try {
      const data = await getCardInfo();
      if (data && data.patientId) {
        await getList(data.patientId);
      }
    } catch (error) {
    } finally {
      ref.close();
    }
  };

  useEffect(() => {
    init();
  }, []);

  return (
    <div className={styles.container}>
      {empty ? (
        <div className={styles.empty}>
          <img src={require('../assets/images/empty.png')} alt="" />
          <span>暂无咨询记录</span>
        </div>
      ) : (
        <div className={styles.content}>
          {list.map((item: any) => {
            return (
              <div className={styles.item} key={item.admId}>
                <div>
                  <img src={require('../assets/images/zx.png')} alt="" />
                  <div>
                    <span>{dayjs(item.admissionTime).format('YYYY年MM月DD日')}</span>
                    <span>{item.statusDesc || '--'}</span>
                  </div>
                </div>
                <div>
                  <span>接待医生：{item?.doctorName}</span>
                </div>
              </div>
            );
          })}
        </div>
      )}
      <div className={styles.footer}>
        <div onClick={toZixun}>发起咨询</div>
      </div>
    </div>
  );
};

export default Consult;
