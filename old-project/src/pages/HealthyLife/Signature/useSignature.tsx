import { useEffect, useRef } from 'react';
import SignaturePad from 'signature_pad';
import { Toast } from 'antd-mobile-v5';
import queryString from 'query-string';
import { history } from 'umi';
import { jkSignPdf } from '../service';

async function rotateImg(imgSrc: string): Promise<string> {
  return new Promise<string>((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d') as CanvasRenderingContext2D;

    const img = new Image();
    img.src = imgSrc;
    img.onload = function () {
      canvas.width = img.height;
      canvas.height = img.width;

      ctx.translate(img.height / 2, img.width / 2);
      ctx.rotate(-Math.PI / 2);
      ctx.drawImage(img, -img.width / 2, -img.height / 2);

      resolve(canvas.toDataURL());
    };
    img.onerror = function (error) {
      reject(error);
    };
  });
}

function extractBase64Image(str: string): string | null {
  const prefix = 'data:image/png;base64,';
  if (str.startsWith(prefix)) {
    return str.slice(prefix.length);
  }
  return null;
}

export default function useSignature() {
  const signaturePadRef = useRef<SignaturePad>();
  const canvasdRef = useRef<HTMLCanvasElement | any>();
  useEffect(() => {
    const canvas = document.querySelector('#signature');
    canvasdRef.current = canvas;
    if (canvas) {
      signaturePadRef.current = new SignaturePad(canvas as any, {
        backgroundColor: '#fff',
      });
    }
  }, []);

  const clear = () => {
    signaturePadRef.current?.clear();
  };

  const undo = () => {
    const data = signaturePadRef.current?.toData();
    if (data) {
      data.pop(); // remove the last dot or line
      signaturePadRef.current?.fromData(data);
    }
  };

  const submit = async () => {
    if (signaturePadRef.current?.isEmpty()) {
      Toast.show('请签名');
      return;
    }
    try {
      Toast.show({
        icon: 'loading',
        duration: 0,
      });
      const { id } = queryString.parse(window.location.search);
      const data = signaturePadRef.current?.toDataURL() as any;
      const rotateImgBase64 = await rotateImg(data);
      const res = await jkSignPdf({ id, imgBase64: extractBase64Image(rotateImgBase64) });
      if (res.code === '1') {
        Toast.show({
          content: '签章成功！',
          afterClose() {
            history.push({ pathname: '/HealthyLife/HealthyLifeSignSuccess' });
          },
        });
      }
    } catch (error) {
      console.log(error);
    } finally {
      Toast.clear();
    }
  };

  return {
    submit,
    undo,
    clear,
  };
}
