import React from 'react';
import styles from './index.less';
import useSignature from './useSignature';

export default function Signature() {
  const { clear, undo, submit } = useSignature();

  return (
    <>
      <div className={styles.footer}>
        <span className={styles.reset} onClick={clear}>
          重置
        </span>
        <span className={styles.return} onClick={undo}>
          撤销
        </span>
        <span className={styles.submit} onClick={submit}>
          提交
        </span>
      </div>
      <canvas id="signature" width={window.innerWidth} height={window.innerHeight} />
    </>
  );
}
