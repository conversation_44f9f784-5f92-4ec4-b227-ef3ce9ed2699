.footer {
  > span {
    position: fixed;
    left: 0;
    bottom: 0;
    transform: rotate(90deg) translateY(70%);
    box-shadow: 0px 0px 8px 0px #ebedf5;
    border-radius: 38px;
    text-align: center;
    line-height: 76px;
    font-size: 32px;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #ffffff;
    display: inline-block;
    width: 208px;
    height: 76px;
  }
  .reset {
    background: #568df2;
    bottom: calc(2 * 208px + 2 * 30px + 80px);
  }
  .return {
    background: #568df2;
    bottom: calc(208px + 1 * 30px + 80px);
  }
  .submit {
    background: #3ad3c1;
    bottom: calc(80px);
  }
}
