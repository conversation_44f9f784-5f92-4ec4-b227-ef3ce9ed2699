import React from 'react';
import styles from './index.less';
import classNames from 'classnames';
import { HxLocalStorage } from '@/utils/storage';
import { useDispatch } from 'umi';
import { Toast } from 'antd-mobile-v5';
import OssUpload from '@/utils/ossUpload';
import { StorageEnum } from '@/utils/enum';
import { getChannelCode, getOrganCode } from '@/utils/parameter';

// 新增: 定义 props 的 interface
interface FormInputProps {
  placeholder?: string;
  name?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  ocrChange?: (res: any) => void;
  ocr?: boolean;
  disabled?: boolean;
  type?: string;
}

export default function (props: FormInputProps) {
  const {
    placeholder = '请输入',
    name = '',
    value,
    type = 'text',
    onChange,
    ocrChange,
    ocr = false,
    disabled = false,
  } = props;
  const dispatch = useDispatch();

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    e.target.value = '';
    if (file) {
      /* ocr识别 */
      const handler = Toast.show({
        icon: 'loading',
      });
      try {
        const imgObj = new OssUpload();

        const ocrUrl = await imgObj.uploadImg({
          file,
          name: file.name,
        });
        if (ocrUrl) {
          const userInfo = HxLocalStorage.get(StorageEnum.USER_INFO);
          dispatch({
            type: 'patientCard/queryOcrCardInfo',
            payload: {
              businessCode: 'card-register',
              ocrType: 'HX_HW_OCR',
              channelCode: getChannelCode(),
              url: ocrUrl,
              organCode: 'HID0101',
              userId: userInfo?.userId,
              credTypeCode: '01',
            },
            callback: (res: any) => {
              handler?.close();
              const { number, name, birth, sex } = res || {};
              // setFormData((pre) => ({
              //   ...pre,
              //   relativeName: name,
              //   relativeIdCard: number,
              // }));
              ocrChange && ocrChange(res);
            },
          });
        }
      } catch (error) {
        handler?.close();
        console.log('error:', error);
      }
    }
  };

  return (
    <div className={styles.formItemContent}>
      <input type={type} name={name} value={value} onChange={onChange} disabled={disabled} placeholder={placeholder} />
      {ocr && !disabled && (
        <img
          src={require('../../assets/images/camera.png')}
          onClick={() => {
            const fileInput: any = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = 'image/*'; // 限制为图片类型
            fileInput.style.display = 'none';
            document.body.appendChild(fileInput);
            fileInput.click();
            fileInput.onchange = handleFileChange; // 绑定文件选择事件
            document.body.removeChild(fileInput);
          }}
        />
      )}
    </div>
  );
}
