import classNames from 'classnames';
import styles from './index.less';
import React from 'react';

export default function (props) {
  const { onClick, value, picker, iconUrl } = props;
  return (
    <div className={classNames(styles.formItemContent, styles.pickerContainer)}>
      <div onClick={onClick}>
        <span
          style={{
            color: value ? '#3b3b5b' : '#bbbeca',
          }}
        >
          {value || '请选择'}
        </span>
        {!!iconUrl && (
          <i
            style={{
              backgroundImage: `url(${iconUrl})`,
            }}
          ></i>
        )}
      </div>
      {picker}
    </div>
  );
}
