.formItemContent {
  width: 654px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f5f6fa;
  border-radius: 16px;
  overflow: hidden;
}
.pickerContainer {
  > div {
    width: 654px;
    height: 104px;
    background: #f5f6fa;
    border-radius: 16px;
    border: none;
    padding: 0 24px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    > span {
      height: 40px;
      font-size: 28px;
      font-family: PingFang SC, PingFang SC-Regular;
      font-weight: Regular;
      text-align: left;
      color: #bbbeca;
      line-height: 40px;
    }
    > i {
      display: inline-block;
      content: '';
      width: 48px;
      height: 48px;
      background-size: cover;
    }
  }
}
