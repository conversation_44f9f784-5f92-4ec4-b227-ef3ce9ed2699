import React, { useEffect, useState } from 'react';
import { Dispatch, Loading, history } from 'umi';
import AppScheme from '@/utils/AppScheme';
import { isHytPerson } from '@/utils/platform';
import { getOrganCode, getOpenId, getToken } from '@/utils/parameter';
import { Button } from 'antd-mobile-v5';
import styles from './index.less';

import submitSuccessPng from '../assets/images/submitSuccess.png';

interface IProps {
  dispatch: Dispatch;
  loading: boolean;
}
const HealthyLifeSignSuccess: React.FC<IProps> = (props) => {
  const goHome = () => {
    if (isHytPerson()) {
      AppScheme.closeWeb();
      return;
    }
    if (getOrganCode() === 'HID0101') {
      history.push('/hxHome');
    } else {
      history.push({
        pathname: '/home',
      });
    }
  };
  return (
    <div className={styles.successContainer}>
      <div className={styles.innerBox}>
        <img src={submitSuccessPng} alt="" />
        <span>签约成功</span>
        <Button color="primary" fill="outline" shape="rounded" className={styles.btn} onClick={goHome}>
          返回首页
        </Button>
      </div>
    </div>
  );
};

export default HealthyLifeSignSuccess;
