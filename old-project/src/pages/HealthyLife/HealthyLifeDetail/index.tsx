import React, { FC, useState, useEffect } from 'react';
import { connect, Dispatch, HealthyLifeModelState, history, Loading } from 'umi';
import classNames from 'classnames';
import { Popup, Toast } from 'antd-mobile-v5';
import { isHytHarmonyOS, isHyt<PERSON>erson, isJhsh, isWechat } from '@/utils/platform';
import { ModuleEnum, StorageEnum } from '@/utils/enum';
import { HxSessionStorage } from '@/utils/storage';
import { getOrganCode } from '@/utils/parameter';
import AppScheme from '@/utils/AppScheme';
import dayjs from 'dayjs';
import styles from './index.less';
import CountDown from '../components/countDown';
import dshPng from '../assets/images/dsh.png';
import shtgPng from '../assets/images/shtg.png';
import enterPng from '../assets/images/enter.png';
import PreviewProtocol from '../PreviewProtocol';

const idCardFrontBase = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/front_of_ID_card.png';
const idCardBackBase = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/reverse_side_of_ID_card.png';

interface IProps {
  dispatch: Dispatch;
  loading: Loading;
  healthyLifestyle: HealthyLifeModelState;
  location: {
    query: {
      id: string;
    };
  };
}

const HealthyLifeSignSuccess: FC<IProps> = (props) => {
  const {
    dispatch,
    loading = false,
    healthyLifestyle,
    location: {
      query: { id = '' },
    },
  } = props;
  const { recordDetail = {} } = healthyLifestyle || {};
  const {
    userName = '',
    idCardNo = '',
    telephone = '',
    applyTime = '',
    idCardFront = '',
    idCardBack = '',
    pmiNo = '',
    auditStatus = 0, // 1 待审核 2 审核通过 3 审核未通过
    reportUrls = [],
    expireTime = '', // 结束时间
    status = -1,
  } = recordDetail;
  const [visibleAgreement, setVisibleAgreement] = useState<boolean>(false); // 查看健康人生协议
  const [paycountDownTime, setPayCountDownTime] = useState<number>(10);

  const countDownTime = expireTime && dayjs(expireTime).isValid() ? dayjs(expireTime).unix() * 1000 : 0;

  const btnObj = {
    1: { label: '待审核', desc: '请耐心等待', icon: dshPng },
    2: { label: '审核通过', desc: '请等待医嘱开具，完成缴费', icon: shtgPng },
    3: { label: '审核不通过', desc: '审核未通过请重新申请', icon: dshPng },
    4: { label: '待支付', desc: '', icon: dshPng },
    5: { label: '待签约', desc: '', icon: dshPng },
    6: { label: '已过期', desc: '', icon: dshPng },
    7: { label: '已签约', desc: '', icon: dshPng },
  };

  /* 点击立即支付倒计时 */
  const startCountDown = () => {
    const timer = setInterval(() => {
      setPayCountDownTime((time) => {
        if (time === 1) {
          clearInterval(timer);
        }
        return time - 1;
      });
    }, 1000);
  };

  const fetchData = async () => {
    try {
      Toast.show({ icon: 'loading', duration: 0 });
      const res = await dispatch({
        type: 'healthyLifestyle/queryApplyRecordDetail',
        payload: {
          id: id ? Number(id) : '',
        },
      });
      if (res?.status === 4) {
        startCountDown();
      }
    } catch (error) {
      console.log('error:', error);
    } finally {
      Toast.clear();
    }
  };

  /**
   * 去支付
   * @return {*}
   */
  const goToPay = () => {
    /* app */
    const cardInfo = HxSessionStorage.get(StorageEnum.BIZ_PATIENTCARD_DATA);
    if (isHytPerson() && !isHytHarmonyOS()) {
      const urlData = `cardBean=${JSON.stringify(cardInfo)}&organCode=${getOrganCode()}`;
      AppScheme.toPayList(decodeURIComponent(urlData));
      return;
    }
    if (isWechat() || isJhsh() || isHytHarmonyOS()) {
      HxSessionStorage.set(StorageEnum.PATIENTCARD_DATA, cardInfo);
      history.push({ pathname: `/${ModuleEnum.MODULE_OUTPATIENT_PAYMENT}/home` });
    }
  };

  useEffect(() => {
    fetchData();
  }, []);
  return (
    <div className={styles.signDetail}>
      <div className={styles.signDetailHeader}>
        <div>
          <img src={btnObj[status]?.icon || dshPng} alt="" />
          <span>{btnObj[status]?.label}</span>
        </div>
        {status === 4 && !!expireTime && <CountDown endTime={countDownTime} />}
        <span>{btnObj[status]?.desc}</span>
      </div>
      <div className={classNames(styles.signDetailPatientInfo, styles.card)}>
        <p>患者信息</p>
        <div>
          <span>姓名</span>
          <span>{userName}</span>
        </div>
        <div>
          <span>身份证号码</span>
          <span>{idCardNo}</span>
        </div>
        <div>
          <span>联系电话</span>
          <span>{telephone}</span>
        </div>
      </div>
      <div className={classNames(styles.card, styles.cardInfo)}>
        <p>身份证信息</p>
        <div>
          <img src={idCardFront || idCardFrontBase} alt="" />
          <img src={idCardBack || idCardBackBase} alt="" />
        </div>
        <div>
          <span>身份证人脸面</span>
          <span>身份证国徽面</span>
        </div>
      </div>
      <div className={classNames(styles.card, styles.inspectList)}>
        <p>检查检验报告</p>
        <div>
          {reportUrls.map((v) => {
            return (
              <div
                className={styles.uploadItem}
                key={v}
                style={{
                  backgroundImage: `url(${v})`,
                }}
              />
            );
          })}
        </div>
      </div>
      <div className={classNames(styles.card, styles.jkxy)}>
        <div>
          <span>《健康人生服务协议》</span>
          <div
            onClick={() => {
              setVisibleAgreement(true); // 弹框查看协议
            }}
          >
            <span>预览</span>
            <img src={enterPng} alt="" />
          </div>
        </div>
      </div>
      {status === 4 && (
        <>
          {paycountDownTime > 0 ? (
            <div className={styles.footer}>
              <span className={styles.beforePay}>正在为你开具医嘱，请耐心等待进行支付--{paycountDownTime}s</span>
            </div>
          ) : (
            <div className={styles.footer} onClick={goToPay}>
              <span>立即支付</span>
            </div>
          )}
        </>
      )}

      <Popup
        visible={visibleAgreement}
        bodyStyle={{
          height: '100vh',
        }}
      >
        <PreviewProtocol
          handleConfirm={() => {
            setVisibleAgreement(false);
          }}
        />
      </Popup>
    </div>
  );
};

export default connect(
  ({ healthyLifestyle, loading }: { healthyLifestyle: HealthyLifeModelState; loading: Loading }) => ({
    healthyLifestyle,
    loading: loading.effects['healthyLifestyle/queryApplyRecordDetail'],
  }),
)(HealthyLifeSignSuccess);
