.signDetail {
  background-image: url(../assets/images/signBg.png);
  background-size: 750px 250px;
  background-repeat: no-repeat;
  height: 100vh;
  overflow: auto;
  padding-bottom: 152px;
  .signDetailHeader {
    padding-top: 40px;
    padding-left: 24px;
    > div {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      > img {
        height: 48px;
        width: 48px;
      }
      > span {
        height: 40px;
        font-size: 40px;
        font-family: PingFang SC, PingFang SC-Medium;
        font-weight: 600;
        text-align: left;
        color: #000000;
        line-height: 40px;
        margin-left: 16px;
      }
    }
    > span {
      height: 28px;
      font-size: 28px;
      font-family: PingFang SC, PingFang SC-Regular;
      font-weight: 400;
      text-align: left;
      color: #989eb4;
      line-height: 28px;
      margin-left: 64px;
    }
  }
  .card {
    width: 702px;
    background: #ffffff;
    border-radius: 24px;
    margin: 40px auto 0;
    padding: 24px;
    padding-bottom: 32px;
    > p {
      margin: 0;
      padding: 0;
      font-size: 32px;
      font-family: PingFang SC, PingFang SC-Semibold;
      font-weight: 600;
      text-align: left;
      color: #03081a;
      line-height: 40px;
    }
  }
  .signDetailPatientInfo {
    > div {
      margin-top: 32px;
      display: flex;
      justify-content: space-between;
      > span:nth-child(1) {
        height: 28px;
        font-size: 28px;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: Regular;
        text-align: left;
        color: #989eb4;
        line-height: 28px;
      }
      > span:nth-child(2) {
        height: 28px;
        font-size: 28px;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: Regular;
        text-align: right;
        color: #03081a;
        line-height: 28px;
      }
    }
  }
  .cardInfo {
    > div {
      display: flex;
      justify-content: space-between;
      margin-top: 32px;
      img {
        width: 306px;
        height: 212px;
      }
      img + img {
        margin-left: 42px;
      }
      > span {
        flex: 1;
        text-align: center;
        height: 34px;
        font-size: 24px;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: Regular;
        text-align: center;
        color: #989eb4;
        line-height: 34px;
      }
    }
  }
  .inspectList {
    > div {
      margin-top: 32px;
      display: grid;
      grid-template-columns: repeat(4, 1fr); /* 设置四列，每列宽度相等 */
      grid-gap: 26px; /* 设置子元素之间的间距 */
      .uploadItem {
        width: 144px;
        height: 144px;
        position: relative;
        background-size: 100% 100%;
      }
    }
  }
  .jkxy {
    > div {
      display: flex;
      align-items: center;
      justify-content: space-between;
      > span {
        height: 40px;
        font-size: 32px;
        font-family: PingFang SC, PingFang SC-Semibold;
        font-weight: 600;
        text-align: left;
        color: #03081a;
        line-height: 40px;
      }
      > div {
        display: flex;
        align-items: center;
        justify-content: space-between;
        span {
          height: 40px;
          font-size: 32px;
          font-family: PingFang SC, PingFang SC-Semibold;
          font-weight: 600;
          text-align: left;
          color: #3ad3c1;
          line-height: 40px;
          margin-right: 6px;
        }
        img {
          width: 28px;
          height: 28px;
        }
      }
    }
  }
  .footer {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px 0;
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #fff;
    .beforePay {
      font-size: 26px;
      opacity: 0.5;
    }
    > span {
      display: inline-block;
      width: 702px;
      height: 96px;
      background: #3ad3c1;
      border-radius: 48px;
      box-shadow: 0px 0px 8px 0px #ebedf5;
      color: #fff;
      text-align: center;
      line-height: 96px;
      font-size: 36px;
    }
  }
}
