/* eslint-disable react/button-has-type */
import React from 'react';
import definedAvatar from '@/assets/winterbreath/icon-doctor.png';
import styles from './index.less';
// import multidisciplinaryConsultationAvatar from '../../../assets/winterbreath/multidisciplinaryConsultationAvatar.png';

const introImg = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-main-intro.png';
const goodAt = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/shanchang.png';

const servListMap = {
  txzx: '特需咨询',
  tdzx: '团队咨询',
  jyzx: '就医咨询',
  zxmz: '在线门诊',
  gdpb: '快速排班',
};

const DoctorItem = (props) => {
  const {
    headPortraits = '',
    doctorName = '',
    titelName = '',
    organName = '',
    deptName = '',
    profession = '',
    satisfaction = 0,
    servTimes = 0,
    servTime = 0,
    teamName = '',
    price = 0,
    receptionLevel = 1,
    servCodes = [],
    introduction = '',
  } = props.rowData;
  const { onClick = null } = props;
  return (
    <div
      className={styles.doctorItem}
      onClick={() => {
        onClick && onClick();
      }}
    >
      <div className={styles.doctorItemBox}>
        <span
          className={styles.avatar}
          style={{
            backgroundImage: `url(${headPortraits || definedAvatar})`,
          }}
        />
        <div className={styles.content}>
          <div className={styles.header}>
            <div className={styles.doctorName}>{props.type === 'tdzx' ? teamName : doctorName}</div>
            {props.type !== 'tdzx' && <div className={styles.titelName}>{titelName}</div>}
          </div>
          <div className={styles.orgDep}>
            <span className={styles.organName}>{organName}</span>
            {props.type !== 'tdzx' && <span className={styles.deptName}>{deptName}</span>}
          </div>
          <div className={styles.profession}>
            <img src={props.type !== 'tdzx' ? goodAt : introImg} alt="" className={styles.goodAt} />
            <div className={styles.introContent}>{props.type !== 'tdzx' ? `${profession}` : `${introduction}`}</div>
          </div>
          {props.type !== 'tdzx' && (
            <div className={styles.satisfTimes}>
              好评率<span> {satisfaction}% </span> / 问诊量
              <span> {servTimes}</span>
            </div>
          )}
          {(props.type === 'zxmz' || props.type === 'jyzx') && (
            <div className={styles.price}>
              图文<span>￥{price}</span>
              <span> | </span>平均接诊时间
              <span>{receptionLevel === 1 ? '1小时内' : receptionLevel === 2 ? '2小时内' : '大于2小时'}</span>
            </div>
          )}
          <div className={styles.inquiryBox}>
            {props.type === 'gdpb' && (
              <div className={styles.price}>
                图文<span>￥{price}</span>
              </div>
            )}
            {props.type === 'zxyz' && (
              <div className={styles.price}>
                <span>￥{price}</span>
              </div>
            )}
            {props.type === 'tdzx' && (
              <div className={`${styles.price} ${styles.price_}`}>
                ￥{price}/<span>{servTime}小时</span>
              </div>
            )}
            {props.type === 'tdzx' && <button>申请问诊</button>}
          </div>

          {props.type === 'yxzx' && (
            <div className={styles.servList}>
              {servCodes.length > 0 &&
                servCodes.map((item) => {
                  return <div className={styles.servItem}>{servListMap[item] ? servListMap[item] : ''}</div>;
                })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DoctorItem;
