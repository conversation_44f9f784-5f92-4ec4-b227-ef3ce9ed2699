/* eslint-disable react/button-has-type */
import React, { useEffect, useRef, useState } from 'react';
import { ListView, Toast, Modal } from 'antd-mobile';
import EmptyView from '@/components/StyleComponents/EmptyView';
import { HxParameter } from '@/utils/parameter';
import { history, connect } from 'umi';
import styles from './index.less';
import DoctorItem from './DoctorItem';

const SERVCODE = {
  zzzx: '1',
  ypzx: '2',
};

function Index(props) {
  const [active, setActive] = useState(SERVCODE.zzzx);
  const activeRef = useRef(false);
  const [loading, setLoading] = useState(false);
  const [pageInfo, setPageInfo] = useState({
    pageNum: 1,
    pageSize: 100,
  });
  const [total, setTotal] = useState(0);
  const [dataList, setDataList] = useState<any[]>([]);

  const goToDoctorDetailsPage = (item) => {
    const { doctorId, deptName, organId } = item;
    const { channelCode, openId, organCode } = HxParameter;
    history.push({
      pathname: '/doctor/hxhome',
      query: {
        organCode,
        openId,
        doctorId,
        servCode: 'zzzx',
        middleType: 1,
        channelCode,
        deptName,
        organId,
        sku: 18, // 糖尿病专区
      },
    });
  };

  const fetchData = () => {
    setLoading(true);
    Toast.loading('加载中...', 0);
    const { organCode } = HxParameter;
    props.dispatch({
      type: 'SpeicalOutpatient/getAllDoctors',
      payload: {
        organCode,
        servCode: 'zzzx',
        zxType: active === SERVCODE.ypzx ? 0 : 1,
        ...pageInfo,
      },
      cb: (result: any) => {
        const { list = [], total = 0 } = result || {};
        setDataList([...list]);
        setTotal(total);
        setLoading(false);
        Toast.hide();
        activeRef.current = false;
      },
    });
  };

  useEffect(() => {
    fetchData();
  }, [active]);

  return (
    <div className={styles.winterBreathContainer}>
      <div className={styles.winterBreathBody}>
        <div className={styles.header} />
        <div className={styles.content}>
          <div className={styles.headerTitle}>推荐医生</div>
          {dataList.length ? (
            <div className={styles.tabBody}>
              {dataList.map((doctorItem) => {
                return (
                  <DoctorItem
                    key={doctorItem.doctorId}
                    type="zxmz"
                    onClick={() => {
                      goToDoctorDetailsPage(doctorItem);
                    }}
                    rowData={doctorItem}
                  />
                );
              })}
            </div>
          ) : (
            <EmptyView text="暂无数据" />
          )}
        </div>
      </div>
    </div>
  );
}

export default connect()(Index);
