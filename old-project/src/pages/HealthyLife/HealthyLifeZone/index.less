.winterBreathContainer {
  height: 100vh;
  padding-bottom: 24px;
  overflow: auto;
  background-color: #f5f6fa;
  .winterBreathBody {
    .header {
      width: 750px;
      height: 316px;
      background: url('../assets/images/bg.png');
      background-size: 100% 100%;
    }
    .content {
      transform: translateY(-36px);
    }
    .headerTitle {
      font-size: 36px;
      font-family: PingFang SC, PingFang SC-Semibold;
      font-weight: 600;
      text-align: left;
      color: #03081a;
      line-height: 50px;
      background-color: #fff;
      padding-top: 32px;
      padding-left: 24px;
      border-radius: 24px 24px 0px 0px;
      width: 750px;
    }
  }
  .loadingMore {
    width: 32px;
    height: 32px;
  }
}

.agreeTitle {
  color: #03081a;
  font-weight: 600;
  font-size: 32px;
}

.selectIcon {
  display: flex;
  align-items: center;
  width: 100%;
  height: 70px;
  margin-top: 22px;
  background-color: #fff;
  // padding-bottom: 0.24rem;
  .image {
    width: 48px;
    height: 48px;
    margin-right: 16px;
  }
  .agree {
    color: #03081a;
    font-weight: 500;
    font-size: 28px;
  }
}
.modalContent {
  height: 400px;
  overflow-y: scroll;
  color: #03081a;
  font-size: 28px;
  line-height: 50px;
  text-align: left;
  text-indent: 2em;
}
