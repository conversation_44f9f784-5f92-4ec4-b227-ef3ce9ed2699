import request from '@/utils/request';

export const querySettlementItem = async (data: object): Promise<object> =>
  request('/cloud/medicalService/discharge/querySettlementItem', {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
    prefix: API_HXYY_NEW,
  });
export const settlement = async (data: object): Promise<object> =>
  request('/cloud/medicalService/discharge/settlement', {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
    prefix: API_HXYY_NEW,
  });
