.container {
  background: @bg-color-gray;

  .headerHint {
    box-sizing: border-box;
    width: 750px;
    height: 162px;
    padding: 32px 24px;
    background: #3ad3c1;
    .info {
      position: relative;
      display: flex;
      align-items: center;
      height: 88px;
      color: #fff;
      .patientName {
        height: 50px;
        color: #fff;
        font-weight: 600;
        font-size: 36px;
        font-family: PingFangSC-Semibold, PingFang SC;
        line-height: 50px;
      }
      .avatar {
        width: 88px;
        height: 88px;
      }
      .action1 {
        position: absolute;
        right: 0;
        width: 48px;
        height: 48px;
      }
      .gender,
      .age {
        margin-left: 30px;
      }
    }
    .patientInfo {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
    .button {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      width: 190px;
      height: 52px;
      background: #fff;
      border-radius: 26px;
    }
  }

  .noRecord {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 702px;
    height: 804px;
    margin-top: 24px;
    margin-left: 24px;
    background: #fff;
    border-radius: 16px;
    .img {
      width: 320px;
      height: 320px;
    }

    .text {
      height: 44px;
      margin-top: 24px;
      color: #03081a;
      font-weight: 600;
      font-size: 32px;
      font-family: PingFangSC-Semibold, PingFang SC;
      line-height: 44px;
    }
  }

  .sumMoney {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 702px;
    margin-top: 24px;
    margin-left: 24px;
    padding: 32px 24px 0 24px;
    background: #fff;
    border-radius: 16px;
    .sumMoney_item {
      width: 100%;
      height: 32px;
      margin-bottom: 32px;
      font-weight: 400;
      font-size: 32px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 32px;
      .label {
        width: 276px;
        color: #989eb4;
      }
      .content {
        color: #03081a;
      }
    }
    .pageRemark {
      width: 100%;
      margin-top: 64px;
      color: #e03636;
      font-weight: 400;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 44px;
    }
  }
}
.nodataImg {
  width: 160px;
  height: 160px;
  margin-bottom: 24px;
}
.action {
  position: fixed;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 750px;
  height: 184px;
  background: #fff;
  .button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 702px;
    height: 96px;
    color: #fff;
    font-weight: 600;
    font-size: 36px;
    border-radius: 48px;
  }
}
.toPay {
  display: flex;
  justify-content: space-between;
  width: 702px;
  height: 96px;
  margin-top: 24px;
  margin-left: 24px;
  padding: 0 24px;
  color: #03081a;
  font-weight: 400;
  font-size: 32px;
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 96px;
  background: #fff;
  border-radius: 16px;
}
.tips {
  width: 702px;
  margin-top: 24px;
  margin-left: 24px;
  padding: 0 24px;
  color: #03081a;
  font-weight: 400;
  font-size: 32px;
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 64px;
}
