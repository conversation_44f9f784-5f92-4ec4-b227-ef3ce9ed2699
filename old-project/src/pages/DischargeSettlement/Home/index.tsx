import React from 'react';
import { history, Loading, IHospitalizationPrepaymentState, REGISTER_FROM } from 'umi';
import { Button, Result } from 'antd-mobile-v5';
import queryString from 'query-string';
import { connect } from 'dva';
import { Dispatch, AnyAction } from 'redux';
import { HxLocalStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import { getOrganCode, getOpenId } from '@/utils/parameter';
import avatar from '@/assets/default_avatar.png';
import addressSelectArea from '@/assets/矩形备份@2x.png';
import nodataImg from '@/assets/缺省页／暂无订单@2x.png';
//  缺省页／暂无患者@2x.png
import nodataImg2 from '@/assets/暂无患者@2x.png';
import manImg from '@/assets/basicInformation_avatar.png';
import womanIMg from '@/assets/成年女性头像@2x.png';
import styles from './index.less';
import { querySettlementItem, settlement } from '../service';
import { testRes } from './res';

interface IProps {
  loading?: boolean;
  dispatch: Dispatch<AnyAction>;
  location: {
    search: string;
  };
}

interface IState {
  inHospNo: string;
  rechargeList: any[];
  page: number;
  patientName: any;
  patientSex: any;
  // credNo: string;
  patientAge: string;
  surplusMoney: string;
  credNo: string;
  totalPages: any;
  startDate: string;
  pageNum: number;
  pageSize: number;
  firstDeposit: any;
  urlInfo: any;
}

interface Item {
  channelName: string;
  depositTime: string;
  money: string;
  id: string;
}
const isFirst = (flag: any) => {
  switch (flag) {
    case null:
    case 0:
    case 0.0:
    case '':
    case '0':
    case '0.0':
    case '0.00':
      return 0;
    default:
      return flag;
  }
};
class Detail extends React.Component<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      inHospNo: '',
      patientName: '',
      patientSex: '',
      patientAge: '',
      surplusMoney: '',
      credNo: '',
      cardId: '',
      detailInfo: null,
      isFInish: false,
      pmiNo: '',
    };
  }

  componentDidMount() {
    this.queryBalance();
  }

  // 查询住院预交金余额
  queryBalance = () => {
    const {
      location: { search = '' },
    } = this.props;
    const { data = '' }: any = queryString.parse(search) || {};
    localStorage.setItem('_DischargeSettlement', search);
    if (!data) {
      // 取缓存
      const __DischargeSettlement = localStorage.getItem('__DischargeSettlement');
      const { cardId = '', patientName = '', credNo, age, gender, pmiNo } = JSON.parse(__DischargeSettlement) || {};
      this.setState(
        {
          patientName,
          credNo,
          patientSex: gender,
          patientAge: age,
          cardId,
          pmiNo,
        },
        () => {
          this.queryData();
        },
      );
    } else {
      const { cardId = '', patientName = '', credNo, age, gender, pmiNo } = JSON.parse(data) || {};
      this.setState(
        {
          patientName,
          credNo,
          patientSex: gender,
          patientAge: age,
          cardId,
          pmiNo,
        },
        () => {
          this.queryData();
        },
      );
    }
    window.backToSettlement = () => {
      const _s = this.props.location.search;
      history.push(`/dischargeSettlement/home${_s}`);
    };
  };

  applysettlement = async () => {
    const { pmiNo } = this.state;
    // 直接完成
    const res1 = await settlement({
      organPmiNo: pmiNo,
    });
    if (res1.code == '1') {
      this.setState({
        isFInish: true,
      });
      this.queryData();
    }
  };

  settlement = async () => {
    const { detailInfo, pmiNo } = this.state;
    if (detailInfo) {
      switch (detailInfo.goToType) {
        case 0:
          this.applysettlement();
          break;
        case 1:
          this.applysettlement();
          break;
        case 2:
          // 需补缴预交金
          const { cardId, patientSex, patientAge } = this.state;
          history.push(
            `/hospitalizationprepayment/recharge${this.props.location.search}&cardId=${cardId}&startDate=&inHospNo=${detailInfo.patAdm}&firstDeposit=${detailInfo.disAmt}&patientAge=${patientAge}&patientSex=${patientSex}&from=DischargeSettlement&channelType=4`,
          );
          break;
        case 3:
          // 未登记银行卡号

          break;
        default:
          break;
      }
    }
  };

  queryData = async () => {
    const { pmiNo } = this.state;
    const res = await querySettlementItem({
      organPmiNo: pmiNo,
    });
    this.setState({
      detailInfo: res.data,
    });
  };

  changeCard = () => {
    history.go(-1);
  };

  render() {
    const { patientSex, detailInfo, credNo, isFInish } = this.state;
    return (
      <div className={styles.container}>
        <div className={styles.headerHint}>
          {detailInfo ? (
            <div className={styles.info} onClick={this.changeCard}>
              <img className={styles.avatar} src={detailInfo.patSex === '男' ? manImg : womanIMg} alt="" />
              <div style={{ marginLeft: '16px' }}>
                <div>
                  <span className={styles.patientName}>{detailInfo.patName}</span>
                  <span className={styles.gender}>{detailInfo.patSex}</span>
                  <span className={styles.age}>{detailInfo.patAge}岁</span>
                </div>
                <div>就诊卡：{`${credNo.slice(0, 4)}****${credNo.slice(credNo.length - 4)}`}</div>
              </div>
              <img src={addressSelectArea} alt="" className={styles.action1} />
            </div>
          ) : null}
        </div>
        {detailInfo ? (
          <div className={styles.content}>
            <div className={styles.sumMoney}>
              {isFInish ? (
                <div>
                  <Result status="success" title="结算成功" description="结算成功" />
                </div>
              ) : (
                <div style={{ width: '100%' }}>
                  <div className={styles.sumMoney_item}>
                    <span className={styles.label}>登记银行卡号：</span>
                    <span className={styles.content}> {detailInfo.bankCardNumber}</span>
                  </div>
                  <div className={styles.sumMoney_item}>
                    <span className={styles.label}> 总费用（¥）：</span>
                    <span className={styles.content}> {detailInfo.sumAmount}</span>
                  </div>
                  <div className={styles.sumMoney_item}>
                    <span className={styles.label}> 住院预交金（¥）：</span>
                    <span className={styles.content}> {detailInfo.depositAmt}</span>
                  </div>
                  <div className={styles.sumMoney_item}>
                    <span className={styles.label}> 预交金余额（¥）：</span>
                    <span className={styles.content} style={{ color: '#FC4553' }}>
                      {' '}
                      {detailInfo.disAmt > 0 ? 0 : Math.abs(detailInfo.disAmt)}
                    </span>
                  </div>
                </div>
              )}
              {!isFInish && (
                <div className={styles.action}>
                  <div
                    className={styles.button}
                    onClick={() => {
                      if (detailInfo.goToType == 1 || detailInfo.goToType == 0) {
                        this.settlement();
                      }
                    }}
                    style={{
                      background: detailInfo.goToType == 1 || detailInfo.goToType == 0 ? '#3AD3C1' : '#C6C9D6',
                    }}
                  >
                    自助结算
                  </div>
                </div>
              )}
            </div>
            {detailInfo.goToType == 2 ? (
              <div className={styles.toPay} onClick={this.settlement}>
                <span>充值住院预缴金</span>
                <span style={{ color: '#FC4553' }}>去充值</span>
              </div>
            ) : null}
            {isFInish ? null : (
              <div className={styles.tips}>
                <div>
                  {detailInfo.goToType == 1 ? (
                    <div>
                      <div>温馨提示:</div>
                      <div>
                        您的预交金有结余，将申请退款
                        <span style={{ color: '#FC4553' }}>{detailInfo.disAmt}</span>
                        元.
                        <span style={{ color: '#FC4553' }}>请确认，登记姓名与持卡人一致，否则无法退款</span>
                      </div>
                    </div>
                  ) : null}
                  {detailInfo.goToType == 2 ? (
                    <div>
                      <div>温馨提示:</div>
                      <div>
                        您的住院预缴金无结余,须补缴住院金 <span style={{ color: '#FC4553' }}>{detailInfo.disAmt}</span>
                        元,方可办理出院结算
                      </div>
                    </div>
                  ) : null}
                  {detailInfo.goToType == 3 ? (
                    <div>
                      <div>温馨提示:</div>
                      <div>
                        您的住院预缴金有结余,将申请退款
                        <span style={{ color: '#FC4553' }}> {Math.abs(detailInfo.disAmt)}</span>
                        元.
                        <span style={{ color: '#FC4553' }}>您未登记银行卡号，请前往窗口办理银行卡登记!</span>
                      </div>
                    </div>
                  ) : null}
                  {detailInfo.goToType == 4 ? (
                    <div>
                      <div>温馨提示</div>
                      <div style={{ color: '#FC4553' }}>已结算，请勿重复结算</div>
                    </div>
                  ) : null}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className={styles.noRecord}>
            <img src={nodataImg2} className={styles.img} alt="" />
            <div className={styles.text}>暂无住院记录</div>
          </div>
        )}
      </div>
    );
  }
}
export default connect(({ loading }: { loading: Loading }) => ({
  loading: loading.effects['hospitalizationPrepayment/getInpDetail'],
}))(Detail);
