/*
 * @Author: <PERSON>
 * @Date: 2022-03-17 11:15:04
 * @LastEditor: <PERSON>
 * @LastEditTime: 2024-01-25 17:16:01
 * @FilePath: /hyt-person/src/pages/DoctorDetailAggregationHX/util.ts
 * Copyright (c) 2022 by <PERSON>, All Rights Reserved.
 * @Description:
 */
import AppScheme from '@/utils/AppScheme';

interface PathDataType {
  doctorCode: string;
  organCode: string;
  doctorId: string;
  servCode: string;
  middleType?: string;
}

interface OtherDataType {
  portrait: string;
  doctorName: string;
  docTitle: string;
  organName: string;
  deptName: string;
  servCode: string;
}

const { docHomeShareUrl } = AppScheme;

/**
 * 给原生传二维码需要的数据
 * @param pathData 医生聚合主页需要的参数
 * @param otherData 原生需要的其他参数
 */
const addShareToApp = (pathData: PathDataType, otherData: OtherDataType) => {
  const { doctorCode, doctorId, servCode, middleType } = pathData;
  const pathname = `${THE_DOMAIN}/doctor/hxhome?organCode=HID0101&channelCode=PATIENT_WECHAT&appCode=HXGYAPP`;
  // 重新登录后的重定向路由
  const purePlayParam = `doctorCode@${doctorCode}|doctorId@${doctorId}|servCode@${servCode}|middleType@${middleType}|openApp@true|sku@1`;
  const url = `${pathname}&purePlayKey=DH&purePlayParam=${purePlayParam}`;
  const params = {
    url,
    ...otherData,
  };
  // console.log('addShareToApp', pathname.length, url, params);
  docHomeShareUrl(encodeURIComponent(JSON.stringify(params)));
};

export { addShareToApp };
