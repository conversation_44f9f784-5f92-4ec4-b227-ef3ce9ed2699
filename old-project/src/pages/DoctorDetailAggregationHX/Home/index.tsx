import React, { useCallback, useEffect, useLayoutEffect, useRef, useState } from 'react';
import { connect, Dispatch, history, Loading } from 'umi';
import { Toast, Modal, Tabs } from 'antd-mobile';
import { PullToRefresh, Toast as ToastV5 } from 'antd-mobile-v5';
import _ from 'lodash';
import { DoctorModelState } from '@/typings/doctor.d';
import { Comment, StyleComponents, HxDrawer, HxIcon } from '@/components';

import AppScheme from '@/utils/AppScheme';
import { isWechat, isHytDoctor, isHytPerson, isTfsmy, isAlipay } from '@/utils/platform';
import { SingWechatJSSDKDataType } from '@/typings/global';
import { configWechatShareStyle, getCurEvnHref, getSignUrl } from '@/utils/tool';
import { HxLocalStorage, HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import { getChannelCode, getOrganCode } from '@/utils/parameter';
import qs from 'query-string';
import HealthScience from '@/pages/Department/Components/HealthScience';
import { followAction } from '@/pages/Appointment/service';
import { TopInfo, BusinessRotation } from './components';
import { addShareToApp } from '../util';
import styles from './index.less';

const doctor_defalut_logo = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/default-doctor-logo.png';
const doctor_notice = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor_notice.png';
const icon_right = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor_arrow.png';
const { PartialComment } = Comment;
const { removeShare } = AppScheme;

interface DoctorDetailAggregationProps {
  hxdoctor: DoctorModelState;
  dispatch: Dispatch;
  location: {
    query: {
      doctorCode: string;
      doctorId: string;
      organCode: string;
      servCode: string;
      hospitalCode: string;
      hospitalAreaCode: string;
      tabAreaCode: string;
      appCode: string;
      channelCode: string;
      deptCategoryCode: string;
      deptCode: string;
      docFillRecordId: string; // 认定记录id
      /** 医生主页中间显示模块类型（number类型） 0:医生服务 1:医学资讯列表 2: 专病服务包列表 */
      middleType: string;
      /** 是否是二维码扫描出来的页面（boolean类型） */
      openApp: string;
      /** 重定向需要的参数 */
      purePlayKey: 'DH';
      /** 重定向路由的必要参数组成的字符串 */
      purePlayParam: string;
      organId: string;
      pmi: string;
      selectedIndex: number;
      student: string;
      pageChannelType?: number;
      /** 互联网总医院接入（医健通页面跳转过来携带的机构参数） */
      channel?: string;
      doctor_id?: string;
      sku?: number /* 渠道统计 */;
      scheduleDate?: string; // 排班日期
    };
  };
  detailLoading: boolean | undefined;
  scheduleLoading: boolean | undefined;
}

const DoctorDetailAggregationHX: React.FC<DoctorDetailAggregationProps> = (props) => {
  // props
  const {
    hxdoctor: { doctorInfo: doctorInfoData, numberSourceList, doctorCommentData, selectCardServCode },
    dispatch,
    location: { query },
    detailLoading,
    scheduleLoading,
  } = props;
  const { doctorInfo = {}, inquiryServices = [] }: any = doctorInfoData;
  const [isEmptyDoctorInfo, setIsEmptyDoctorInfo] = useState<boolean>(true);
  const [serviceList, setServiceList] = useState<Array<any>>([]);

  // doctorInfo.organCode
  // doctorInfo.workOrganId;
  // 将query中的字符串undefined转为undefined类型
  Object.keys(query).forEach((key) => {
    if (query[key] === 'undefined') {
      query[key] = undefined;
    }
  });
  // purePlayParam中的参数
  const purePlayParamObj: AnyObject = {};

  // 重定向存在，必要参数从purePlayParam里面取（减少路由带的参数）
  if (query.purePlayKey === 'DH') {
    const purePlayParamArr = decodeURIComponent(query.purePlayParam).split('|');
    purePlayParamArr.forEach((item) => {
      const keyAndValue = item.split('@');
      if (keyAndValue && keyAndValue.length === 2) {
        const objKey = keyAndValue[0];
        const objValue = keyAndValue[1] === 'undefined' ? undefined : keyAndValue[1];
        purePlayParamObj[objKey] = objValue;
      }
    });
  }
  const { whetherAlipay }: any = window;
  let {
    doctorCode,
    doctorId,
    organCode = 'HID0101',
    servCode,
    middleType,
    openApp,
    appCode,
    channelCode,
    selectedIndex = whetherAlipay ? 0 : 1,
    isStaff = '-1',
    pageChannelType,
    sku,
  } = query.purePlayKey === 'DH' ? purePlayParamObj : query;
  const channel = query?.channel || HxSessionStorage.get(StorageEnum.YJT_CHANNEL);

  if (channel === 'HLWZYY') {
    doctorId = query?.doctor_id || HxSessionStorage.get(StorageEnum.YJT_DOCTOR_ID);
  }

  /* 支付宝隐藏健康科普 */
  if (whetherAlipay) {
    selectedIndex = selectedIndex == 0 ? selectedIndex : +selectedIndex - 1;
  }

  const {
    workOrganId,
    organName,
    standardTitleName,
    hospitalTitleName,
    portrait = doctor_defalut_logo,
    doctorName,
    deptName,
    notice,
    deptId,
    baseDeptCode: deptCode,
    refDeptName,
    refDeptCode,
  } = doctorInfo;

  // const { query }: any = useLocation();
  const { commentList = [] } = doctorCommentData || {};

  const docTitle = hospitalTitleName || standardTitleName;

  const { docFillRecordId, pmi } = query;
  /* 处理organId */
  // const speMedOrganId = query?.organId;
  // doctorInfo.workOrganId = docFillRecordId ? speMedOrganId : workOrganId;
  // state
  const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
  // 健康科普列表
  const [healthList, setHealthList] = useState<Array<any>>([]);
  const [followStatus, setFollowStatus] = useState(2);
  // 关注数
  const [fansCount, setFansCount] = useState<number>(0);
  /**
   * @name: 获取评论列表
   * @param {*}
   * @return {*}
   */
  const getCommentList = () => {
    return new Promise((resolve) => {
      dispatch({
        type: 'hxdoctor/getDoctorCommentListNew',
        payload: {
          pageNum: 1,
          pageSize: 1,
          query: {
            organCode,
            appCode,
            channelCode,
            enterCode: 'DOC_HOME',
            toAppraiserId: doctorId,
            doctorId,
          },
        },
        callback: (res) => {
          resolve(res);
        },
      });
    });
  };

  /**
   * @name: 获取排班数据
   * @return {*}
   */

  const getScheduleData = () => {
    /* 传卡id后台判断外省号源 */
    const { cardId = '' } = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA) || {};
    const { deptCategoryCode, hospitalCode, hospitalAreaCode, tabAreaCode, deptCode } = query;
    props.dispatch({
      type: 'hxdoctor/selHxDoctorDetails',
      payload: {
        appointmentType: 1,
        deptCode, // 科室编码
        // deptCategoryCode, // 二级科室编码 //后端让去掉这个参数
        doctorId, // 医生ID
        hospitalAreaCode, // 选中医生的院区编码
        hospitalCode: hospitalCode || 'HID0101', // 医院编码
        // tabAreaCode, // 选中院区的院区编码
        cardId,
      },
    });
  };

  /**
   * @name: 获取pi团队数据
   */
  const getPiTeam = () => {
    // p1团队流程
    dispatch({
      type: 'department/getP1TeamList',
      payload: {
        doctorId,
        organCode: getOrganCode(),
      },
    });
  };

  /**
   * 获取医生详情
   */
  const fetchDoctorInfo = async () => {
    let newPageChannelType = query.student ? 1 : Number(pageChannelType) || null;
    if (query && String(newPageChannelType) === '2') {
      newPageChannelType = 2;
    }
    const res = await dispatch({
      type: 'hxdoctor/queryHxDoctorInfo',
      payload: {
        doctorCode,
        // doctorId: '00000000747c842301747d05f20d0068',
        doctorId,
        organCode,
        servCode,
        pageChannelType: newPageChannelType,
      },
    });
    const { doctorInfo } = res;
    if (doctorInfo) {
      setIsEmptyDoctorInfo(false);
    }
  };

  /* 获取关注数 */
  const getFollowers = () => {
    dispatch({
      type: 'appointment/followStatus',
      payload: {
        doctorId,
      },
      callback: (data: any = {}) => {
        const { status = 2, fansCount = 30 } = data;
        setFansCount(fansCount);
        setFollowStatus(status);
      },
    });
  };

  /** 给原生二维码传参 */
  const doAddShareToApp = () => {
    const pathData = {
      doctorCode,
      organCode,
      doctorId,
      servCode,
      middleType,
    };
    const otherData = {
      portrait,
      doctorName,
      docTitle,
      organName,
      deptName,
      servCode,
    };
    addShareToApp(pathData, otherData);
  };

  const addEventListener = () => {
    window.addEventListener('pageshow', function () {
      doAddShareToApp();
      const param = 'color=blue';
      AppScheme.changeColor(param);
    });
    window.addEventListener('pagehide', function () {
      removeShare();
      const param = 'color=white';
      AppScheme.changeColor(param);
    });
  };

  /** 设置分享给朋友的样式 */
  const defineShareAppStyle = () => {
    // 在线门诊医生主页中的快速问诊【固定排班】分享后显示在线门诊业务 （需求by龚蓉蓉+康惠子）
    let link = getCurEvnHref().replace('gdpb', 'zxmz');
    /* 医健通跳转过来未登录的状态会去登录 doctor_id 会存在本地 */
    if (channel === 'HLWZYY') {
      const queryParams: AnyObject = {
        // doctorId: HxSessionStorage.get(StorageEnum.YJT_DOCTOR_ID),
        organCode: getOrganCode(),
        channelCode: getChannelCode(),
      };
      if (!query.doctor_id) queryParams.doctorId = HxSessionStorage.get(StorageEnum.YJT_DOCTOR_ID);
      link = `${link}${link?.indexOf('?') > -1 ? '&' : '?'}${qs.stringify(queryParams)}`;
    }
    dispatch({
      type: 'global/singWechatJSSDK',
      payload: {
        url: getSignUrl(),
      },
      callback: (data: SingWechatJSSDKDataType) => {
        const hospitalName = doctorInfo.organCode === 'HID0101' ? '四川大学华西医院/四川大学华西互联网医院' : '';
        const title = `${doctorName}-${deptName}-${docTitle}-${hospitalName} `;
        const desc = `${organName} ${deptName}`;

        const configData = {
          ...data,
          debug: false,
          jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'],
        };

        const shareData = {
          title,
          desc,
          imgUrl: portrait,
          link,
          success: () => {
            // console.log('lcc-分享成功');
          },
        };
        configWechatShareStyle(configData, shareData);
      },
    });
  };

  const enterCode = 'PAT_DOCTOR_HOME';
  // 健康科普列表（默认5条）
  const getHealthList = () => {
    return new Promise((resolve) => {
      const params = {
        pageNum: 1,
        pageSize: 4,
        query: {
          appCode,
          organCode,
          channelCode,
          attrId: doctorId,
          enterCode,
          operator: 1,
        },
      };
      dispatch({
        type: 'department/getDepartmentHealthList',
        payload: params,
        cb: (res: any) => {
          setHealthList(res || []);
          resolve(1);
        },
      });
    });
  };

  const jumpTo = (pathname: string, query: any = {}) => {
    history.push({ pathname, query });
  };

  const goMore = (pathname = '/department/healthlist') => {
    jumpTo(pathname, { appCode, channelCode, organCode, enterCode, attrId: doctorId });
  };

  /** 关注医生 */
  const onFavorite = async (value: number) => {
    try {
      ToastV5.show({ icon: 'loading', duration: 0 });
      const data = await followAction({ actionType: value, doctorId });
      const { status } = data;
      if (status === 1) {
        Toast.info('您已成功关注该医生', 1);
      } else if (status === 2) {
        Toast.info('已取消关注该医生', 1);
      }
      setFollowStatus(status);
    } catch (error) {
      console.log('error:', error);
    } finally {
      ToastV5.clear();
    }

    /* 关注之后刷新关注数 */
    getFollowers();
  };

  const goBriefInfo = () => {
    const data = Object.assign(doctorInfo);
    const pathname = '/doctor/hxbriefinfo';
    history.push({
      pathname,
      query: {
        ...data,
      },
    });
  };

  const goDepartment = () => {
    // 支付宝小程序不跳转
    if (whetherAlipay) {
      return;
    }
    history.push({
      pathname: `/department/detail/${deptId}`,
      query: {
        appCode,
        channelCode,
        organCode,
        deptId,
        deptName,
        deptCode,
        refDeptName,
        deptCodes: refDeptCode ? refDeptCode.join(',') : '',
      },
    });
  };

  const goToAllComment = () => {
    if (isWechat() || isHytPerson() || isHytDoctor() || isTfsmy() || isAlipay()) {
      const callback = () => {
        history.push({
          pathname: '/allcomment',
          query: {
            teamOrDoctorId: doctorId,
            organCode,
            type: 'DOC_HOME',
          },
        });
      };
      if (isWechat()) {
        doCheckToken(callback);
      } else {
        callback();
      }
    }
  };

  useEffect(() => {
    /* 上锦医院会重定向到platformhome页面 */
    if (getOrganCode() === 'HID0102') {
      return;
    }
    fetchDoctorInfo().then(() => {
      getHealthList();
      getCommentList();
      getFollowers();
      getPiTeam();
      addEventListener();
    });
    getScheduleData();

    HxLocalStorage.set('isStaff', isStaff); // 职工通道参数设置
    return () => {
      ToastV5.clear();
      // 清除原生二维码传参
      removeShare();
      /* 评论数据清除 */
      dispatch({
        type: 'hxdoctor/updateState',
        payload: {
          doctorCommentData: {
            commentList: [],
            totalCount: 0,
            satisfaction: 0,
          },
        },
      });
    };
  }, []);

  /** 微信定义分享给朋友的样式 */
  /* 修改wechat/jsparam/query 重复调用bug */
  const [isInit, setInit] = useState(true);
  useEffect(() => {
    if (isInit) {
      setInit(false);
    } else {
      isWechat() && !_.isEmpty(doctorInfo) && defineShareAppStyle();
    }
  }, [isEmptyDoctorInfo]);

  useEffect(() => {
    if (detailLoading || scheduleLoading) {
      ToastV5.show({ icon: 'loading', duration: 0 });
    } else {
      ToastV5.clear();
    }
  }, [detailLoading, scheduleLoading]); /* followStatusLoading 6.4.7取消了这个 */

  /**
   * 验证token是否失效
   * @param callback token存在后的回调
   */
  const doCheckToken = (callback: () => void) => {
    dispatch({
      type: 'global/checkToken',
      callback,
    });
  };

  useEffect(() => {
    document.title = doctorName || '医生主页';
    if (_.isEmpty(doctorInfo) || openApp === 'true') {
      return;
    }
    doAddShareToApp();
  }, [doctorInfo]);

  useLayoutEffect(() => {
    if (getOrganCode() === 'HID0102') {
      history.replace(`/doctor/platformhome${window.location.search}`);
    }
    return () => {
      ToastV5.clear();
    };
  }, []);

  return (
    <div className={styles.container} key={doctorId}>
      <PullToRefresh
        onRefresh={async () => {
          getScheduleData();
          await fetchDoctorInfo();
        }}
      >
        <TopInfo
          doctorInfo={doctorInfoData}
          followStatus={followStatus}
          onFavorite={onFavorite}
          goBriefInfo={goBriefInfo}
          goDepartment={goDepartment}
          fansCount={fansCount}
        />
        {notice && notice.trim() && (
          <div className={styles.notice} id="toppoint">
            <img src={doctor_notice} alt="" className={styles.doctorNotice} />
            <div className={styles.noticeCont} onClick={() => setDrawerOpen(true)}>
              <div className={styles.introContent}> {notice}</div>
              <img src={icon_right} alt="" className={styles.noticeIcon} />
            </div>
          </div>
        )}
        <HxDrawer
          drawerShow={drawerOpen}
          title="医生公告"
          drawerContent={notice}
          clickBg={() => setDrawerOpen(false)}
          closeBtn={() => setDrawerOpen(false)}
        />
        <BusinessRotation
          serviceList={inquiryServices}
          doctorInfo={doctorInfo}
          servCode={servCode}
          doCheckToken={doCheckToken}
          numberSourceList={numberSourceList}
          organCode={organCode}
          docFillRecordId={docFillRecordId}
          pmi={pmi}
          channel={channel}
          sku={sku}
          {...props}
        />

        <div className={styles.health}>
          {/* 评价-只有在线门诊才显示 */}
          {commentList?.length > 0 && selectCardServCode !== 'yygh' && (
            <PartialComment
              style={{ borderRadius: '8px', margin: '12px 0 0 0' }}
              commentList={commentList}
              character={<HxIcon iconName="Star1" />}
              goToAllComment={goToAllComment}
            />
          )}
          {/* 健康科普 */}
          {healthList?.length > 0 && !whetherAlipay && (
            <div className={styles.healthList}>
              <div className={styles.title}>
                <div className={styles.left}>健康科普</div>
                <div className={styles.right} onClick={() => goMore('/department/healthscience')}>
                  <div className={styles.hint}>查看更多</div>
                  <HxIcon iconName="arrow-right" className={styles.more} />
                </div>
              </div>
              <HealthScience list={healthList} />
            </div>
          )}
        </div>
      </PullToRefresh>
    </div>
  );
};

export default connect(({ hxdoctor, loading }: { hxdoctor: DoctorModelState; loading: Loading }) => ({
  hxdoctor,
  detailLoading: loading.effects['hxdoctor/queryHxDoctorInfo'],
  scheduleLoading: loading.effects['hxdoctor/selHxDoctorDetails'],
}))(DoctorDetailAggregationHX);
