import { Effect, Subscription, Reducer } from 'umi';
import { parse } from 'qs';

import {
  queryDoctorInfo,
  queryHxDoctorInfo,
  docFollow,
  selDoctorDetails,
  selHxDoctorDetails,
  getDoctorCommentListNew,
  queryTeamByServTypes,
  getDoctorIsUse,
  queryHxDoctorLineInfo,
} from '@/services/doctor';
import { DoctorModelState, CommentItemType, NumberSourceItemType } from '@/typings/doctor.d';

interface DoctorModelType {
  namespace: string;
  state: DoctorModelState;
  effects: {
    /** 获取医生信息 */
    queryDoctorInfo: Effect;
    /** 获取华西医生聚合主页信息 */
    queryHxDoctorInfo: Effect;
    /** 关注医生 */
    docFollow: Effect;
    /** 查询医生号源详情 */
    selDoctorDetails: Effect;
    /** 查询医生号源详情 */
    selHxDoctorDetails: Effect;
    /** 获取医生/团队评论列表(new,获取全部) */
    getDoctorCommentListNew: Effect;
    /** 根据服务类型查询医生所有开通这些服务的团队 */
    queryTeamByServTypes: Effect;
    /** 查询医生的服务是否可用 */
    getDoctorIsUse: Effect;
    /** 查询MDT线上团队数据 */
    queryHxDoctorLineInfo: Effect;
  };
  reducers: {
    updateState: Reducer;
  };
  subscriptions: {
    setup: Subscription;
  };
}

const DoctorModel: DoctorModelType = {
  namespace: 'hxdoctor',
  state: {
    doctorInfo: {},
    selDoctorDetails: {},
    selHxDoctorDetails: {},
    numberSourceList: [],
    doctorCommentData: {
      totalCount: 0,
      commentList: [],
      satisfaction: 0,
    },
    serviceTeamList: [],
    piTeamList: [],
    selectCardServCode: '',
  },
  effects: {
    *queryDoctorInfo({ payload, callback }, { call, put }) {
      const data = yield call(queryDoctorInfo, payload);
      yield put({
        type: 'updateState',
        payload: {
          doctorInfo: data || {},
        },
      });
      callback && callback(data);
    },
    *queryHxDoctorInfo({ payload, callback }, { call, put }) {
      try {
        const data = yield call(queryHxDoctorInfo, payload);
        yield put({
          type: 'updateState',
          payload: {
            doctorInfo: data || {},
          },
        });
        // callback && callback(data);
        return data;
      } catch (error) {
        return {};
      }
    },
    *queryHxDoctorLineInfo({ payload, callback }, { call }) {
      const data = yield call(queryHxDoctorLineInfo, payload);
      callback && callback(data);
    },
    *docFollow({ payload, callback }, { call }) {
      const res = yield call(docFollow, payload);
      const { code } = res;
      code === '1' && callback && callback();
    },
    *selDoctorDetails({ payload, callback }, { call, put }) {
      const data = yield call(selDoctorDetails, payload);
      const { sourceItemsRespVos = [] } = data || {};

      // sourceItemsRespVos是按排班来的，需要提取出可挂号的科室列表
      const departmenList: NumberSourceItemType[] = [];
      sourceItemsRespVos.forEach((item: NumberSourceItemType) => {
        const hasDepartment = departmenList.find((department) => department.deptName === item.deptName);
        // status为1表示有号
        !hasDepartment && departmenList.push(item);
      });

      yield put({
        type: 'updateState',
        payload: {
          numberSourceList: departmenList,
          selDoctorDetails: data || {},
        },
      });
      callback && callback(departmenList);
    },
    *selHxDoctorDetails({ payload, callback }, { call, put }) {
      const data = yield call(selHxDoctorDetails, payload);
      const { sourceItems = [] } = data || {};
      yield put({
        type: 'updateState',
        payload: {
          numberSourceList: sourceItems,
          selDoctorDetails: data || {},
        },
      });
      callback && callback(data);
    },

    *getDoctorCommentListNew({ payload, callback }, { call, put, select }) {
      const data = yield call(getDoctorCommentListNew, payload);
      const oldCommentList: CommentItemType[] = yield select((state: any) => {
        return state.doctor.doctorCommentData.commentList;
      });
      const { commentList = {}, favorableRate = 0 } = data || {};
      const { content = [], total = 0 } = commentList;
      yield put({
        type: 'updateState',
        payload: {
          doctorCommentData: {
            commentList: oldCommentList.concat(content),
            totalCount: total,
            satisfaction: favorableRate,
          },
        },
      });
      callback && callback(content);
    },
    *queryTeamByServTypes({ payload }, { call, put }) {
      const data = yield call(queryTeamByServTypes, payload);
      yield put({
        type: 'updateState',
        payload: {
          serviceTeamList: data || [],
        },
      });
    },
    *getDoctorIsUse({ payload, callback }, { call }) {
      const data = yield call(getDoctorIsUse, payload);
      call && callback(data);
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
  subscriptions: {
    setup({ history, dispatch }) {
      history.listen(({ pathname, search }) => {
        const { doctorCode, doctorId, organCode, servCode } = parse(search.split('?')[1]);
        if (pathname === '/doctor/briefinfo' || pathname === '/doctor/teamlist') {
          dispatch({
            type: 'queryDoctorInfo',
            payload: {
              doctorCode,
              doctorId,
              organCode,
              pageNum: 1,
              pageSize: 5,
              servCode: servCode || 'jhzy',
            },
          });
          return;
        }
        dispatch({
          type: 'updateState',
          payload: {
            doctorInfo: {},
            selDoctorDetails: {},
            selHxDoctorDetails: {},
            numberSourceList: [],
            doctorCommentData: {
              totalCount: 0,
              commentList: [],
              satisfaction: 0,
            },
            serviceTeamList: [],
            piTeamList: [],
            selectCardServCode: '',
          },
        });
      });
    },
  },
};

export default DoctorModel;
