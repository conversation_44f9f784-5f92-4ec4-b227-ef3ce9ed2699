.container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  padding: 1.6rem 0.96rem;
  // padding-top: 2rem;
  // padding-bottom: 2.83rem;
  font-size: 1.08rem;
  background: #f5f6f7 url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-header-bg.png')
    no-repeat;
  background-size: contain;
  background-position: center -7.04rem;

  .headerBg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    background: #ecf8f6;

    img {
      width: 100%;
    }
  }

  .notice {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.96rem;
    padding: 0.96rem 0.64rem 0.96rem 0.96rem;
    background-color: #fff;
    border-radius: 0.96rem;
    height: 5.44rem;

    .doctorNotice {
      width: 3.52rem;
      height: 3.52rem;
      margin-right: 0.64rem;
    }

    .noticeCont {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: space-between;
      color: #03081a;
      height: 100%;

      .introContent {
        display: -webkit-box;
        overflow: hidden;
        color: #03081a;
        font-size: 1.04rem;
        color: #03081a;
        line-height: 1.76rem;
        height: 100%;

        text-overflow: ellipsis;

        /*! autoprefixer: off */
        -webkit-box-orient: vertical;

        /* autoprefixer: on */
        -webkit-line-clamp: 2;
      }

      .noticeIcon {
        width: 1.28rem;
        height: 1.28rem;
        margin-left: 0.64rem;
      }
    }
  }

  .health {
    border-radius: 16px;
    overflow: hidden;
    .healthList {
      background: #fff;
      border-radius: 16px;
      margin-top: 24px;

      .title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.86rem;

        .patientComment {
          padding-left: 22px;
          color: #03081a;
          font-weight: 600;
          font-size: 36px;
        }

        .allComment {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding-right: 24px;
          color: #989eb4;
          font-size: 26px;

          .more {
            margin-left: 3px;
            font-size: 22px;
          }
        }
      }
    }

    .title {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 32px 24px 32px 24px;

      .left {
        color: #03081a;
        font-weight: 600;
        font-size: 36px;
        // line-height: 50px;
      }

      .right {
        display: flex;
        flex-direction: row;
        align-items: center;
        color: #989eb4;
        font-size: 26px;

        .more {
          margin-left: 3px;
          font-size: 22px;
        }
      }
    }
  }

  .bottomFn {
    position: fixed;
    bottom: 2rem;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 1.43rem;

    .btn {
      width: 9.14rem;
      height: 2.86rem;
      padding-left: 1.5rem;
      color: #fff;
      line-height: 2.86rem;
      background-color: #3ad3c1;
      border-radius: 1.43rem;
      box-shadow: 0 0.14rem 0.29rem 0 rgba(58, 211, 193, 0.5);

      img {
        width: 2.14rem;
        height: 2.14rem;
        margin-right: 0.57rem;
      }
    }
  }
}
