.detailsMain {
  display: flex;
  flex: 1;
  flex-direction: column;
  box-sizing: border-box;
  margin-top: 0.86rem;
  // padding: 0.86rem;
  overflow-x: hidden;
  min-height: 200px;
  // background-color: #fff;
  .emptyBox {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    background-color: #fff;
    height: 350px;
    border-radius: 16px;
    img {
      width: 128px;
      height: 128px;
    }
    span {
      font-size: 24px;
      color: #989eb4;
      line-height: 34px;
      margin-top: 16px;
    }
  }

  .headerText {
    box-sizing: border-box;
    height: 80px;
    padding: 0 30px;
    color: #333;
    font-weight: 500;
    font-size: 28px;
    line-height: 80px;
  }

  .main {
    padding: 24px;
    background-color: #fff;
    border-radius: 24px;
    .noMore {
      height: 77px;
      overflow: hidden;
      color: #ddd;
      font-weight: 400;
      font-size: 28px;
      font-family: PingFang SC;
      line-height: 33px;
      text-align: center;

      p {
        margin: 0;
        margin-top: 21px;
      }
    }

    .hospital {
      padding-top: 8px;
      .scheduleWrap {
        border-radius: 16px;
        overflow: hidden;
        .schedule {
          &.active {
            background: #eefbf9;
            border: 2px solid #a5ebe2;
            border-radius: 16px;
            .dayDesc {
              background-color: #ffffff;
              color: #3ad3c1;
            }
          }
          &:last-child {
            .borderBottom {
              display: none;
            }
          }
        }
      }
      &:not(:first-child) {
        margin-top: 32px;
      }
      .hospitalTitle {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .hospitalName {
          color: #FC4553;
          font-weight: 500;
          font-size: 1.07rem;
        }

        .spot {
          width: 0.57rem;
          height: 0.57rem;
          margin-right: 0.5rem;
          background: #FC4553;
          border-radius: 0.285rem;
          opacity: 0.6;
        }
      }
      .expand {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100;
        padding: 0.4rem;
        color: #989eb4;
        font-size: 0.93rem;
        background-color: #fbfbfd;

        img {
          width: 1.29rem;
          margin-left: 0.1rem;
        }
      }
    }

    .schedule {
      display: flex;
      flex-direction: column;
      padding: 14px 0 0 0;
      color: #333;
      font-size: 1rem;
      background: #fbfbfd;

      .scheduleItem,
      .subScheduleItem {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        height: auto;
        padding: 0 30px;
        // border-bottom: 1px solid #eee;
        .borderBottom {
          position: absolute;
          bottom: 0;
          left: 1rem;
          width: calc(~'100% - 2rem');
          height: 1px;
          background-color: #eee;
        }

        .date {
          display: flex;
          flex-direction: column;
          padding-bottom: 20px;
          color: #03081a;
          .dayDesc {
            background: #ebedf5;
            border-radius: 8px;
            font-size: 20px;
            color: #51586d;
            height: 36px;
            padding: 0 8px;
            margin-left: 16px;
            display: inline-flex;
            align-items: center;
          }
          div:first-child {
            display: flex;
            align-items: center;
            font-size: 28px;
          }
          div:nth-child(2) {
            font-size: 24px;
            margin-top: 8px;

            span:nth-child(1) {
              color: #989eb4;
            }

            span:nth-child(2) {
              margin-left: 20px;
              color: #989eb4;
            }
          }
        }

        .appoinmentBtn {
          display: flex;
          align-items: center;
          color: #999;
          font-size: 1rem;

          .btn {
            min-width: 104px;
            min-height: 56px;
            padding: 0 8px;
            color: #fff;
            font-size: 1rem;
            line-height: 56px;
            text-align: center;
            background: linear-gradient(90deg, #b0b3bf 0%, #cdd2e4 100%);
            border: none;
            border-radius: 1rem;
          }

          .activeBtn {
            min-width: 104px;
            min-height: 56px;
            padding: 0 8px;
            color: #fff;
            font-size: 1rem;
            line-height: 56px;
            text-align: center;
            background: linear-gradient(90deg, #3ad3c1 0%, #68e9db 100%);
            border: none;
            border-radius: 1rem;
          }

          .redBtn {
            min-width: 104px;
            min-height: 56px;
            padding: 0 8px;
            color: #fff;
            font-size: 1rem;
            line-height: 56px;
            text-align: center;
            background: linear-gradient(90deg, #fc4553 0%, #ff7782 100%);
            border: none;
            border-radius: 1rem;
          }

          .activeBtnDown {
            min-width: 140px;
            min-height: 52px;
            padding: 8px 8px;
            color: #fc4553;
            font-size: 0.93rem;
            line-height: 52px;
            text-align: center;
            border: none;
            border-radius: 8px;
          }

          .arrowDown {
            width: 16px;
            height: 10px;
            margin-left: 20px;
          }
        }
      }
    }
  }
}
.docotor-item {
  width: 7rem;
  height: 9.33rem;
  background: #fbfbfd;
  border-radius: 0.5rem;
  img {
    width: 5rem;
    height: 5rem;
    border: 0.17rem solid #fcdda4;
    border-radius: 50%;
  }
}
.hospitalTitle {
  margin-bottom: 0.5rem;
}
.piTeamWrap {
  // margin-top: 24px;
}
.piTeamList {
  display: flex;
  align-items: center;
  width: 100%;
  height: 68px;
  padding: 16px 32px;
  color: #989eb4;
  font-weight: 400;
  font-size: 24px;
  font-family: PingFangSC-Regular, PingFang SC;
  background: #ebedf5;
  border-radius: 16px;
  img {
    width: 36px;
    height: 36px;
    margin-right: 8px;
  }
}

:global {
  .scheduleInfoWrap {
    .adm-mask-content {
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
      height: 100%;
      .head {
        border-radius: 20px 20px 0 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 95px;
        padding: 0 30px;
        color: #333;
        font-family: PingFang SC Bold, PingFang SC Bold-Bold;
        line-height: 95px;
        text-align: center;
        background-color: #fff;
        border: none !important;
        border-radius: 20px 20px 0 0;
        position: relative;

        .name {
          max-width: 240px;
          color: #333;
          font-weight: bolder;
          font-size: 32px;
          line-height: 36px;
          letter-spacing: 1px;
          text-align: center;
        }

        .times {
          color: #999;
          font-weight: 500;
          font-size: 28px;
        }

        .price {
          color: #ff7474;
          font-weight: 700;
          font-size: 30px;
          font-family: DIN Bold, DIN Bold-Bold;
          text-align: center;
          span {
            font-size: 13px;
          }
        }
      }

      .nthStatusList {
        height: 80px;
        background-color: #fff;
        display: flex;
        align-items: center;
        padding: 0 25px;

        .ItemBox {
          height: 100%;
          display: flex;
          align-items: center;
          margin-right: 20px;

          .statusBox {
            height: 40px;
            width: 40px;
            border-radius: 8px;
            margin-right: 10px;
          }

          .text {
            font-size: 24px;
          }
        }
      }

      .box {
        min-height: 110px;
        max-height: 60%;
        overflow-y: scroll;
        background-color: #fff;
        position: relative;

        ul {
          display: flex;
          flex-wrap: wrap;
          min-height: 100%;
          margin-bottom: 0;
          padding: 0 25px;
          padding-top: 30px;
          list-style: none;
          li {
            position: relative;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 220px;
            min-height: 140px;
            margin-right: 17px;
            margin-bottom: 20px;
            padding: 2px;
            background: #fff;
            border: 0.5px solid #aaa;
            border-radius: 8px;
            color: #7f7f7f;
            &:nth-child(3n) {
              margin-right: 0;
            }
            p {
              margin: 0;
            }
            .andendNoWrap {
              width: 100%;
              font-size: 28px;
              text-align: center;
              span {
                margin-right: 2px;
              }
            }

            .chosedIcon {
              position: absolute;
              right: 0;
              top: 0;
              height: 30px;
              width: 30px;
              color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              background-color: #32b9aa;
              border-bottom-left-radius: 4px;
            }
          }
          .vanish {
            color: #fff;
            background: #aaaaaa;
            border: 0.5px solid #aaaaaa;
          }
          .nthSelected {
            color: #32b9aa;
            background: #fff;
            border: 4px solid #32b9aa;
          }
        }
      }

      .showStatus {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 88px;
        color: #fff;
        font-weight: 500;
        font-size: 32px;
        font-family: PingFang SC Medium, PingFang SC Medium-Medium;
        text-align: center;
        background: #32b9aa;
        position: relative;
      }

      .showNoStatus {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 88px;
        color: rgb(250, 250, 250);
        font-weight: 500;
        font-size: 32px;
        font-family: PingFang SC Medium, PingFang SC Medium-Medium;
        text-align: center;
        background: #e0dede;
      }
    }
  }
}
