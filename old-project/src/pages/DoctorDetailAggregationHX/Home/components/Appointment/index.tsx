/*
 * @Author: <PERSON>
 * @Date: 2022-03-18 16:34:32
 * @LastEditor: <PERSON>
 * @LastEditTime: 2025-01-23 09:42:39
 * @FilePath: /hyt-person/src/pages/DoctorDetailAggregationHX/Home/components/Appointment/index.tsx
 * Copyright (c) 2022 by <PERSON>, All Rights Reserved.
 * @Description:
 */

import React, { PureComponent } from 'react';
import { history, connect, Loading } from 'umi';
import { Dispatch, AnyAction } from 'redux';
import { getOrganCode } from '@/utils/parameter';
import { HxLocalStorage, HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import { Toast, Modal } from 'antd-mobile';
import { Mask } from 'antd-mobile-v5';
import qs from 'query-string';
import { DoctorModelState } from '@/typings/doctor';
import _ from 'lodash';
import classnames from 'classnames';
import { ISourceOpenData, ISourceItemsRespVos, ISourceItemsDetailData } from '../../../../Appointment/data';
import styles from './index.less';
import PITeam from '../PITeam';

const piTeamIcon = require('../../../../../assets/<EMAIL>');
const emptyImg = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/hx_schedule_empty.png';

const nthTimeFrameStatusList: any = [
  { borderColor: '#7F7F7F', backGroudColor: '#fff', text: '可选' },
  { borderColor: '#AAAAAA', backGroudColor: '#AAAAAA', text: '已无号' },
  { borderColor: '#32b9aa', backGroudColor: '#fff', text: '已选中' },
];

interface IProps {
  dispatch: Dispatch<AnyAction>;
  hxdoctor: DoctorModelState;
  department: any;
  loading?: boolean;
  numberSourceLoading?: boolean;
  location: {
    query: {
      hospitalCode: string;
      sign: string;
      hospitalAreaCode: string;
      tabAreaCode: string;
      departmentCode: string;
      deptCode: string;
      doctorId: string;
      docName: string;
      hospitalName: string;
      deptCategoryCode: string;
      channelElderCode: string; // 老年专区channelCode
      sourceChannel: string;
      teamName?: string;
      /** 医生列表选中了时段带到医生主页高亮显示  */
      scheduleDate?: string;
    };
  };
}

interface IState {
  deptTips: any[];
  // showSubSource: string;
  showCountDown: string;
  countDownStr: string;
  visible: boolean;
  hospitalCode: string;
  sign: number;
  hospitalAreaCode: string;
  tabAreaCode: string;
  deptCode: string;
  doctorId: string;
  scheduleData: Array<any>;
  sourceInfo: ISourceOpenData;
  hospitalOpenSourceTime: string;
  tips: string;
  htmlTips: string;
  // docName: string;
  // hospitalName: string;
  listDocName: string;
  listHospitalName: string;
  // hideTime: number;
  // showTime: number;
  sysScheduleId: string;
  currentAvaliable: number;
  followStatus: number;
  showBox: boolean;
  chooseValue: any;
  chooseNthValueKey: string;
  chooseNthValue: any;
  chooseStartNoAndendNo: string;
  reportInterpretationImageVisible: boolean;
  bannerImage: string;
  doctorNotice: string;
  commentList: any;
  workOrganId: string;
  satisfaction: number;
  docInfo: any;
  sourceItemsDetailRespVos?: Array<any>;
  theTeamList: Array<any>;
  deptCategoryCode: string;
  channelElderCode: string;
  selectScheduleDate: string;
}

const week = ['周天', '周一', '周二', '周三', '周四', '周五', '周六'];
const scheduleRange = ['上午', '下午', '全天', '夜间门诊'];
const sourceStatus: any = {
  1: '预约',
  2: '约满',
  3: '停诊',
  4: '待放',
};

let timer: any = null;
let timer1: any = null;
class AppointmentSchedule extends PureComponent<IProps, IState> {
  visibilityChange = document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'hidden') {
      clearInterval(timer);
    } else {
      const { sysScheduleId, currentAvaliable } = this.state;
      if (sysScheduleId !== '') {
        this.selSourceOpenTime(sysScheduleId, currentAvaliable);
      }
    }
  });

  constructor(props: IProps) {
    super(props);
    this.state = {
      visible: false,
      countDownStr: '',
      showCountDown: '',
      hospitalCode: '',
      sign: 1,
      hospitalAreaCode: '',
      tabAreaCode: '',
      deptCode: '',
      doctorId: '',
      hospitalOpenSourceTime: '',
      tips: '',
      htmlTips: '',
      listDocName: '',
      scheduleData: [],
      sourceInfo: {},
      // hideTime: 0,
      // showTime: 0,
      sysScheduleId: '',
      currentAvaliable: 0,
      showBox: false,
      chooseValue: [],
      chooseNthValueKey: '',
      chooseNthValue: {},
      chooseStartNoAndendNo: '',
      deptCategoryCode: '',
      channelElderCode: '',
    };
  }

  componentDidMount() {
    const {
      hxdoctor,
      location: {
        query: {
          hospitalCode = '',
          sign = '1',
          hospitalAreaCode = '',
          tabAreaCode = '',
          deptCode = '',
          doctorId: queryDoctorId = '',
          docName,
          hospitalName: listHospitalName = '',
          deptCategoryCode = '',
          channelElderCode = '',
          scheduleDate = '',
        },
      },
    } = this.props;
    const doctorId = queryDoctorId || hxdoctor?.doctorInfo?.doctorInfo?.doctorId;
    const listDocName = docName || hxdoctor?.doctorInfo?.doctorInfo?.doctorName;
    const {
      selDoctorDetails: { tips = '', htmlTips = '', docName: fetchDocName = '', deptTips = [] },
    } = hxdoctor;
    this.setState({
      hospitalCode: hospitalCode || getOrganCode(),
      sign: Number(sign),
      hospitalAreaCode,
      doctorId,
      listDocName: listDocName || fetchDocName,
      deptCategoryCode,
      channelElderCode,
      tips,
      htmlTips,
      selectScheduleDate: scheduleDate,
      deptTips,
    });
  }

  componentWillUnmount() {
    this.setState = () => {
      return false;
    };
  }

  // 查询号源开放情况
  selSourceOpenTime = (sysScheduleId: string, avaliable: number) => {
    const { sign, hospitalCode } = this.state;
    const t1 = Math.floor(new Date().getTime() / 1000);
    this.props.dispatch({
      type: 'appointment/selSourceOpenTime',
      payload: {
        appointmentType: sign,
        hospitalCode,
        sysScheduleId,
      },
      callback: (data: ISourceOpenData = {}) => {
        this.setState(
          {
            sourceInfo: data,
          },
          () => {
            const { channelElderCode } = this.state;
            const { sourceChannel, teamName } = this.props.location.query;
            /* 组装参数 */
            const queryObj: any = {
              sourceChannel,
            };
            teamName && (queryObj.teamName = teamName);
            channelElderCode && (queryObj.channelCode = channelElderCode);

            const redirect = '/hxappointment/detail';
            history.push({
              pathname: redirect,
              search: qs.stringify(queryObj),
            });
          },
        );
      },
    });
  };

  // 判断是否存在时段号源
  sourceClickItem = (value: ISourceItemsRespVos) => {
    const { deptTips = [] } = this.state;
    const hasDeptTips = deptTips.find((item: any) => item?.tips && item?.deptCode === value?.deptCode);
    const clickFn = () => {
      this.setState(
        {
          chooseValue: value,
        },
        () => {
          const { isPrecise, status, sysScheduleId } = value;
          if (isPrecise === 1 && status === 1) {
            this.props.dispatch({
              type: 'appointment/getSelTimeSchedule',
              payload: {
                sysScheduleId,
                hospitalCode: getOrganCode(),
              },
              callback: (res: any) => {
                if (res.sourceItemsDetailRespVos) {
                  const { sourceItemsDetailRespVos = [] } = res;
                  this.setState({
                    sourceItemsDetailRespVos,
                    showBox: true,
                  });
                }
              },
            });
          } else if (isPrecise === 0 && status === 1) {
            this.onClickItem(value);
          }
        },
      );
    };
    if (hasDeptTips && hasDeptTips?.tips) {
      Modal.alert('提示', hasDeptTips?.tips, [
        {
          text: '取消',
          onPress: () => {},
        },
        {
          text: '确定',
          onPress: () => {
            clickFn();
          },
        },
      ]);
    } else {
      clickFn();
    }
  };

  onClickItem = (value: ISourceItemsRespVos, subValue?: ISourceItemsDetailData) => {
    const { sign, hospitalCode, hospitalAreaCode, listDocName, channelElderCode } = this.state;
    const { hxdoctor } = this.props;
    const {
      admLocation = '',
      deptName = '',
      isPrecise,
      regFee,
      serviceFee,
      scheduleDate,
      scheduleRange,
      sysScheduleId = '',
      availableCount,
      docName,
      hospitalName,
    } = value;
    const {
      selDoctorDetails: { docHeadImage, regTitelName },
      doctorInfo: { doctorInfo: { deptName: scheduleDeptName = '' } = {} } = {},
    } = hxdoctor;
    const { startTime = '', endTime = '', avaliable, sysTimeArrangeId = '', startNoAndendNo = '' } = subValue || {};
    const currentAvaliable = (isPrecise === 1 ? avaliable : availableCount) || 0;
    const { deptDirectionCode, deptDirectionName, sourceChannel, teamName } = this.props.location.query;
    let data: any = {
      sign,
      hospitalCode,
      hospitalAreaCode,
      docName: docName || listDocName,
      admLocation,
      isPrecise,
      regFee,
      serviceFee,
      scheduleRange,
      scheduleDate,
      sysScheduleId,
      deptDirectionName,
      deptDirectionCode,
      hospitalName,
      docHeadImage,
      regTitelName,
      ...value,
      deptName: scheduleDeptName || deptName,
    };
    if (subValue) {
      data = {
        ...data,
        startTime,
        endTime,
        sysTimeArrangeId,
        startNoAndendNo,
      };
    }
    this.setState(
      {
        sysScheduleId,
        currentAvaliable,
      },
      () => {
        /* 存数据 */
        HxLocalStorage.set(StorageEnum.APPOINTMENT_SOURCE_DETAIL, data);
        /* 组装参数 */
        const queryObj: any = {
          sourceChannel,
        };
        teamName && (queryObj.teamName = teamName);
        channelElderCode && (queryObj.channelCode = channelElderCode);

        /*
         * 获取就诊卡
         * 判断是否已经选卡 如果选了卡直接去详情，没选卡则去选择卡
         */
        const patientCardInfo = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA);
        const redirect = '/hxappointment/detail';
        if (patientCardInfo) {
          history.push({
            pathname: redirect,
            search: qs.stringify(queryObj),
          });
          return;
        }
        queryObj.redirect = redirect;
        history.push({
          pathname: '/patientcard/home',
          search: qs.stringify(queryObj),
        });
      },
    );
  };

  onCloseConfirm = () => {
    this.setState({
      tips: '',
      htmlTips: '',
    });
  };

  // 保存所选择的分时段数据
  saveChoose = (nthValue: any) => {
    this.setState({
      chooseNthValue: nthValue,
      chooseNthValueKey: nthValue.sysTimeArrangeId,
      chooseStartNoAndendNo: nthValue.startNoAndendNo,
    });
  };

  sortList = () => {
    const {
      hxdoctor,
      location: {
        query: { tabAreaCode = '' },
      },
    } = this.props;
    const { numberSourceList = [] } = hxdoctor;
    try {
      const tabAreaIndex = numberSourceList.findIndex((item) => item?.areaCode === tabAreaCode);

      if (!tabAreaCode || numberSourceList.length === 1 || tabAreaIndex === -1) return numberSourceList;
      /* 将选择的院区排到第一位 */
      const _numberSourceList = numberSourceList.slice();
      const item = _numberSourceList.splice(tabAreaIndex, 1)[0];
      _numberSourceList.unshift(item);
      return _numberSourceList;
    } catch (error) {
      return [];
    }
  };

  render() {
    const {
      showCountDown,
      visible,
      hospitalOpenSourceTime,
      tips,
      htmlTips,
      listDocName,
      showBox,
      chooseValue,
      chooseNthValueKey,
      chooseNthValue,
      sourceItemsDetailRespVos = [],
      chooseStartNoAndendNo,
      doctorId = '0',
      selectScheduleDate = '',
    } = this.state;
    const { numberSourceLoading = false, department: { piTeamData = {} } = {}, hxdoctor } = this.props;
    const { numberSourceList = [] } = hxdoctor;
    const { regFee, serviceFee, scheduleDate, scheduleRange: curretnrRnge = 0, scheduleRangeOtherName } = chooseValue;
    return (
      <div key={doctorId} className={styles.detailsMain}>
        {numberSourceList?.length === 0 && !numberSourceLoading ? (
          <>
            {!_.isEmpty(piTeamData) ? (
              <div className={styles.piTeamWrap}>
                <div className={styles.piTeamList}>
                  <img src={piTeamIcon} alt="" />
                  <span>医生暂无号源，您可以选择专家团队问诊。</span>
                </div>
              </div>
            ) : (
              <div className={styles.emptyBox}>
                <img src={emptyImg} alt="" />
                <span>暂无排班</span>
              </div>
            )}
          </>
        ) : (
          <div className={styles.main}>
            {(this.sortList() || []).map((hosSchedule) => {
              const { sourceItemsRespVos, areaName = '', areaCode } = hosSchedule;
              return (
                <div key={areaCode} className={styles.hospital}>
                  <div className={styles.hospitalTitle}>
                    <span className={styles.spot} />
                    <span className={styles.hospitalName}>{areaName}</span>
                  </div>
                  <div className={styles.scheduleWrap}>
                    {sourceItemsRespVos.map((item: ISourceItemsRespVos) => {
                      const {
                        scheduleDate = '',
                        scheduleRange: curretnrRnge = 0,
                        serviceFee = 0,
                        regFee = 0,
                        status = 0,
                        scheduleRangeOtherName,
                        remainingNum = 0,
                        dayDesc = '',
                      } = item;
                      return (
                        <div
                          className={classnames(styles.schedule, selectScheduleDate === scheduleDate && styles.active)}
                          key={item.sysScheduleId}
                        >
                          <div className={styles.scheduleItem}>
                            <div className={styles.borderBottom} />
                            <div className={styles.date}>
                              <div>
                                {scheduleDate} {week[new Date(scheduleDate).getDay()]}{' '}
                                {curretnrRnge === 99 ? scheduleRangeOtherName : scheduleRange[curretnrRnge]}
                                {dayDesc && <span className={styles.dayDesc}>{dayDesc}</span>}
                              </div>
                              <div>
                                <span>{item.deptName}</span>
                                <span>¥{(Number(regFee) + Number(serviceFee)).toFixed(2)}</span>
                              </div>
                            </div>
                            <div className={styles.appoinmentBtn}>
                              <div
                                className={`${styles.btn} ${item.status === 1 && styles.activeBtn} ${
                                  item.status === 3 && styles.redBtn
                                }`}
                                onClick={() => {
                                  item.status === 1 ? this.sourceClickItem(item) : () => {};
                                }}
                              >
                                {sourceStatus[status]}
                              </div>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
          </div>
        )}
        {/* pi团队流程 */}
        {!_.isEmpty(piTeamData) && <PITeam data={piTeamData} />}

        <Modal
          visible={!!tips || !!htmlTips}
          title="提示"
          transparent
          maskClosable={false}
          footer={[
            {
              text: '确认',
              onPress: () => {
                this.onCloseConfirm();
              },
            },
          ]}
        >
          <div className={styles.content} style={{ textAlign: 'left' }}>
            {!!tips.length && !htmlTips.length && (
              <div>
                {tips.split('\n').map((item) => {
                  return (
                    <div className="text" key={item}>
                      {item}
                    </div>
                  );
                })}
              </div>
            )}
            {!!htmlTips.length && (
              <div>
                <div className="text" dangerouslySetInnerHTML={{ __html: htmlTips }} />
              </div>
            )}
          </div>
        </Modal>
        <Mask
          visible={showBox}
          getContainer={() => document.body}
          destroyOnClose
          className="scheduleInfoWrap"
          onMaskClick={() => this.setState({ showBox: false, chooseNthValue: {}, chooseNthValueKey: '' })}
        >
          <>
            <div className="head">
              <div className="name">{listDocName}</div>
              <div className="times">
                {scheduleDate} {week[new Date(scheduleDate).getDay()]}{' '}
                {curretnrRnge === 99 ? scheduleRangeOtherName : scheduleRange[curretnrRnge]}
              </div>
              <div className="price">
                <span>¥</span>
                {(Number(regFee) + Number(serviceFee)).toFixed(2)}
              </div>
            </div>
            <div className="nthStatusList">
              {_.map(nthTimeFrameStatusList, (item) => {
                return (
                  <div className="ItemBox" key={item.text}>
                    <div
                      className="statusBox"
                      style={{ border: `2px solid ${item.borderColor}`, backgroundColor: item.backGroudColor }}
                    />
                    <div className="text">{item.text}</div>
                  </div>
                );
              })}
            </div>
            <div className="box">
              <ul>
                {sourceItemsDetailRespVos.map((item: any) => {
                  return (
                    <li
                      key={`${item.sysTimeArrangeId}${item.startNoAndendNo}`}
                      className={
                        item.avaliable > 0
                          ? item.sysTimeArrangeId === chooseNthValueKey &&
                            item.startNoAndendNo === chooseStartNoAndendNo
                            ? 'nthSelected'
                            : ''
                          : 'vanish'
                      }
                      onClick={
                        item.avaliable > 0
                          ? () => this.saveChoose(item)
                          : () => {
                              Toast.fail('该号源已被预约', 1);
                            }
                      }
                    >
                      {getOrganCode() === 'HID0101' && (
                        <div className="andendNoWrap">
                          门诊号段：
                          <span>
                            {item.startNoAndendNo}号{}
                          </span>
                        </div>
                      )}
                      <p>{`${item.startTime}-${item.endTime}`}</p>
                    </li>
                  );
                })}
              </ul>
            </div>
            <div
              className={chooseNthValue.avaliable ? 'showStatus' : 'showNoStatus'}
              onClick={chooseNthValue.avaliable ? () => this.onClickItem(chooseValue, chooseNthValue) : () => {}}
            >
              确定
            </div>
          </>
        </Mask>
      </div>
    );
  }
}

// export default AppointmentSchedule;

export default connect(({ loading, department }: { loading: Loading; department: any }) => ({
  numberSourceLoading: loading.effects['hxdoctor/selHxDoctorDetails'],
  department,
}))(AppointmentSchedule);
