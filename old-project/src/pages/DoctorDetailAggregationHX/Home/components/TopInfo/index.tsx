/*
 * @Author: <PERSON>
 * @Date: 2022-03-17 11:15:04
 * @LastEditor: <PERSON>
 * @LastEditTime: 2025-01-20 10:48:01
 * @FilePath: /hyt-person/src/pages/DoctorDetailAggregationHX/Home/components/TopInfo/index.tsx
 * Copyright (c) 2022 by <PERSON>, All Rights Reserved.
 * @Description:
 */
import React from 'react';

import { DoctorInfoType } from '@/typings/doctor.d';
import { HxDoctorAvatar } from '@/components';
import { getOrganCode } from '@/utils/parameter';
import classnames from 'classnames';

import styles from './index.less';

const followIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/appoinmentHx/followIcon.png';
const followInIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/appoinmentHx/followInIcon.png';

const arrowRightBlackIcon =
  'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-home-arrow-right-black.png';
const arrowRightIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/appoinmentHx/doctor-info-arrow-right.png';
const rareIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-rare-disease.png';
const introduceIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-main-intro.png';
const goodAtIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-main-goodat.png';
const zan = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/zan.png';
const star = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/star.png';

// const arrow_circle = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/arrow_circle%402x.png';

interface TopInfoProps {
  /** 医生相关数据 */
  doctorInfo: DoctorInfoType;
  followStatus: number;
  /** 关注医生 */
  onFavorite: (followStatus: number) => void;
  /** 跳转至医生简介页面 */
  goBriefInfo: () => void;
  /** 跳转至科室页面 */
  goDepartment: () => void;
  fansCount: number;
}

const TopInfo: React.FC<TopInfoProps> = ({
  doctorInfo: doctorInfoData,
  onFavorite,
  goBriefInfo,
  goDepartment,
  followStatus,
  fansCount,
}) => {
  const { doctorInfo = {}, statistics = {} } = doctorInfoData;
  const {
    portrait,
    doctorName,
    organName,
    deptName,
    // introduction,
    // profession,
    profession,
    introduction,
    rareDiseaseProfession,
    hospitalTitleName,
    standardTitleName,
    level,
    organTag,
    receptionLevel,
  } = doctorInfo;

  const docTitle = hospitalTitleName || standardTitleName || '';
  sessionStorage.setItem('docTitle', docTitle);

  const { patientCounts, satisfaction, serviceCounts, accessNum, todayAccessNum, satisfactionStar } = statistics;

  /* 感谢 */
  const handleThank = () => {
    const { deptName, doctorName, doctorId, deptId } = doctorInfo;
    if (!window.whetherAlipay && getOrganCode() === 'HID0101') {
      window.location.href = `${YH_HX_BASE}/hxgzh/index.html#/praise?deptName=${deptName}&doctorName=${doctorName}&doctorId=${doctorId}&deptId=${deptId}`;
    }
  };

  return (
    <div key={doctorName} className={styles.topContainer}>
      <div className={styles.avatarContainer}>
        <HxDoctorAvatar src={portrait} alt="" className={styles.avatar} />
        <div className={styles.praise} onClick={handleThank}>
          <img src={zan} alt="" />
          <span>表扬</span>
        </div>
      </div>
      <div className={styles.mainInfo}>
        <div className={styles.title}>
          <span className={styles.name}>{doctorName}</span>
          {docTitle ? '|' : ''}
          <span className={styles.docTitle}>{` ${docTitle || ''}`}</span>
          {/* 关注按钮 */}
          <div className={styles.favoriteCont} onClick={() => onFavorite && onFavorite(followStatus === 2 ? 1 : 2)}>
            <div className={classnames(styles.favorite, followStatus === 1 && styles.active)}>
              <img src={followStatus === 2 ? followIcon : followInIcon} alt="" className={styles.addIcon} />
              {followStatus === 2 ? '关注' : '已关注'}
            </div>
          </div>
        </div>
        <p onClick={() => getOrganCode() === 'HID0101' && goDepartment()}>
          <span>{deptName}</span>
          {getOrganCode() === 'HID0101' && <img className={styles.arrowR} src={arrowRightBlackIcon} alt="" />}
        </p>
        <p>
          {level && <span className={styles.rank}>{level}</span>} <span className={styles.hospital}>{organName}</span>
        </p>
        {/* {organTag ? (
          <p>
            <span className={styles.HXYLT}>入驻华西互联网医院</span>{' '}
          </p>
        ) : null} */}
      </div>
      <div className={styles.statistics}>
        <p>
          <img src={star} alt="" />
          <span className={styles.value}>{satisfactionStar}</span>
        </p>
        <p>
          <span className={styles.label}>接诊数</span>
          <span className={styles.value}>
            {serviceCounts
              ? serviceCounts > 9999
                ? `${Number(serviceCounts / 10000).toFixed(0)}万`
                : serviceCounts
              : '-'}
          </span>
        </p>
        <p>
          <span className={styles.label}>关注数</span>
          <span className={styles.value}>
            {fansCount > 9999 ? Number(fansCount / 10000).toFixed(1) : fansCount}
            {fansCount > 9999 ? '万' : ''}
          </span>
        </p>
      </div>
      {/* 医生擅长和简介 */}
      <div className={styles.intro}>
        {rareDiseaseProfession && (
          <div className={styles.introduce}>
            <img src={rareIcon} alt="" className={styles.goodAt} />
            <span className={styles.introContent}>{rareDiseaseProfession || '-'}</span>
          </div>
        )}
        <div className={styles.good}>
          <img src={goodAtIcon} alt="" className={styles.goodAt} />
          <div className={classnames(styles.introContent, styles.introContent2)}>
            {profession || '-'}
            <div className={styles.more} onClick={goBriefInfo}>
              <span>医生信息</span>
              <img src={arrowRightIcon} alt="" />
            </div>
          </div>
        </div>
        {/* <div className={styles.introduce}>
          <img src={introduceIcon} alt="" className={styles.goodAt} />
          <span className={styles.introContent}>{introduction || '-'}</span>
        </div> */}
      </div>

      {/* <div className={styles.subscribe}>
        <span>
          累计访问:&nbsp; {accessNum > 9999 ? Number(accessNum / 10000).toFixed(1) : accessNum}
          {accessNum > 9999 ? '万' : ''}
        </span>
        <span>
          今日访问:&nbsp; {todayAccessNum > 9999 ? Number(todayAccessNum / 10000).toFixed(1) : todayAccessNum}
          {todayAccessNum > 9999 ? '万' : ''}
        </span>{' '}
        <span>
          关注数:&nbsp; {fansCount > 9999 ? Number(fansCount / 10000).toFixed(1) : fansCount}
          {fansCount > 9999 ? '万' : ''}
        </span>
      </div> */}
    </div>
  );
};

export default TopInfo;
