.subscribe {
  width: 100%;
  height: 1.76rem;
  background: rgba(58, 211, 193, 0.08);
  border-radius: 0.32rem;
  margin-top: 0.96rem;
  font-size: 0.8rem;
  color: #989eb4;
  display: flex;
  align-items: center;
  justify-content: space-around;
  span {
    display: flex;
    align-items: center;
    &::before {
      content: '';
      width: 0.32rem;
      height: 0.32rem;
      background: #3ad3c1;
      border-radius: 50%;
      margin-right: 0.32rem;
      display: inline-block;
    }
  }
}

.topContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  padding: 0.96rem;
  background-color: #fff;
  //background: #ecf8f6;
  border-radius: 0.96rem;
  min-height: 12rem;

  .mainInfo {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-between;
    padding: 0;
    color: #03081a;
    font-size: 1rem;
    .title {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 0.2rem;
      .docTitle {
        max-width: 6.8rem;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
      .favoriteCont {
        display: flex;
        align-items: center;
        margin-left: 24px;
        .favorite {
          font-size: 28px;
          line-height: 40px;
          color: #989eb4;
          &.active {
            color: #ffc047 !important;
          }
          .addIcon {
            width: 32px;
            height: 32px;
            margin-right: 4px;
          }
        }
      }
    }

    .arrowR {
      height: 1rem;
    }

    p {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 0.2rem;

      &:first-child {
        align-items: baseline;
      }
    }

    .name {
      margin-right: 0.6rem;
      font-weight: 600;
      font-size: 1.29rem;
    }

    .docTitle {
      margin-left: 0.6rem;
      font-size: 1rem;
    }

    .rank {
      margin-right: 0.43rem;
      padding: 0 0.21rem;
      color: #fff;
      font-size: 0.64rem;
      text-align: center;
      background-color: #568df2;
      border-radius: 0.29rem;
    }

    .hospital {
      margin-right: 1.2rem;
      font-size: 1rem;
    }
  }

  .avatarContainer {
    position: absolute;
    top: -1.28rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 5.6rem;
    height: 7.68rem;
    // background-color: #fff;

    .avatar {
      width: 5.6rem;
      height: 5.6rem;
      overflow: hidden;
      border: 0.16rem solid #ffffff;
      border-radius: 50%;
    }
    .praise {
      width: 5.12rem;
      height: 2.08rem;
      background: #ffffff;
      border-radius: 1.04rem;
      box-shadow: 0rem 0rem 0.48rem 0rem rgba(0, 0, 0, 0.06);
      margin-top: -1.04rem;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        font-size: 0.96rem;
        font-weight: bold;
        color: #3ad3c1;
        line-height: 1.36rem;
      }
      img {
        width: 24px;
        height: 1.04rem;
        margin-right: 0.32rem;
        flex-shrink: 0;
      }
    }

    .selectFavorite {
      width: 4.57rem;
      height: 1.86rem;
      color: #d7daea;
      font-size: 0.93rem;
      line-height: 1.86rem;
      text-align: center;
      background-color: #fff;
      border-radius: 0.93rem;
    }
  }
  .statistics {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    margin-top: 0.16rem;
    font-size: 24px;
    p {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      margin: 0;
      &:not(:first-child) {
        margin-left: 16px;
        &::before {
          content: '';
          width: 0.08rem;
          height: 1.12rem;
          background: #ebedf5;
          margin-right: 16px;
        }
      }
    }
    img {
      width: 28px;
      height: 28px;
    }
    .value {
      font-size: 28px;
      font-weight: 500;
      color: #3ad3c1;
      margin-left: 8px;
      line-height: 36px;
    }
    .label {
      font-size: 24px;
      color: #989eb4;
      line-height: 32px;
    }
  }

  .intro {
    color: #03081a;
    font-size: 0.93rem;
    margin-top: 0.56rem;
    width: 100%;
    //background-color: #fff;

    .good {
      position: relative;
      display: flex;
      justify-content: flex-start;
      padding: 0.1rem 0;
      position: relative;
      width: 100%;
      > img {
        margin-top: 8px;
      }
    }

    .introduce {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 0.1rem 0;
    }

    .introContent {
      display: -webkit-box;
      margin-left: 0.2rem;
      overflow: hidden;
      color: #03081a;
      font-size: 26px;
      text-overflow: ellipsis;

      /*! autoprefixer: off */
      -webkit-box-orient: vertical;

      /* autoprefixer: on */
      -webkit-line-clamp: 1;
    }
    .introContent2 {
      -webkit-line-clamp: 2;
      line-height: 40px;
      flex: 1;
      > span {
        line-height: 40px;
      }
      img {
        margin-right: 0;
      }
      .more {
        align-items: center;
        align-self: flex-end;
        justify-content: center;
        color: #3AD3C1;
        font-size: 0.86rem;
        display: flex;
        position: absolute;
        right: 0;
        bottom: 0;
        background-color: #fff;
        height: 42px;
        &::before {
          content: '';
          width: 30px;
          height: 100%;
          background: linear-gradient(270deg, #fff, hsla(0, 0%, 100%, 0));
          position: absolute;
          left: -30px;
          top: 50%;
          transform: translateY(-50%);
        }

        img {
          width: 36px;
          height: 36px;
        }
      }
    }

    img {
      height: 0.93rem;
      margin-right: 0.2rem;
    }
  }
}

.HXYLT {
  height: 1.27rem;
  padding: 0 0.5rem;
  color: #fc4553;
  font-weight: 400;
  font-size: 0.64rem;
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 1.27rem;
  text-align: center;
  background: rgba(252, 69, 83, 0.1);
  border-radius: 0.18rem;
}
