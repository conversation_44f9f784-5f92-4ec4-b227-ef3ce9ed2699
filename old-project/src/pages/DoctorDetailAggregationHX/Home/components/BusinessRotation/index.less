.container {
  margin-top: 24px;
  color: #333;
  font-size: 24px;
  border-radius: 12px;
  .titleBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .title {
    color: #03081a;
    font-weight: 600;
    font-size: 36px;
  }

  .services {
    display: flex;
    justify-content: space-between;
    margin-top: 40px;
    padding: 0 28px;

    .service {
      display: flex;
      flex-direction: column;

      img {
        width: 90px;
        margin-bottom: 12px;
      }
    }
  }

  .otherService {
    display: flex;
    align-items: center;
    max-width: 140px;
    color: #989eb4;
    .arrowRight {
      flex-shrink: 0;
      width: 15px;
      height: 26px;
      margin-left: 5px;
    }
  }

  .advisories,
  .servicesWrapper {
    .advisory {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      margin: 0.86rem;
      padding: 0.71rem;
      background: #f9f9fa;
      border-radius: 0.43rem;

      img {
        width: 3.29rem;
      }

      .advisoryInfo {
        flex: 1;
        margin-left: 40px;

        .serviceName {
          color: #03081a;
          font-weight: 600;
          font-weight: 600;
          font-size: 32px;
        }
        .serviceDesc {
          color: #666;
          font-weight: 600;
        }
        .servicePrice {
          color: #f47f1b;
          font-weight: 600;
          font-size: 28px;
        }
      }
      &:last-child {
        margin-bottom: 10px;
      }
    }
  }
  .servicesWrapper {
    padding: 0;
    .advisory {
      background: #fff;
    }
  }

  .servicePackages {
    padding: 20px 0;
    .servicePackage {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 0 40px 0;
      border-bottom: 1px solid #eee;

      img {
        width: 90px;
        height: 90px;
      }

      .info {
        .packageName {
          font-size: 28px;
        }

        .packageIntroduce {
          width: 440px;
          overflow: hidden;
          color: #999;
          white-space: nowrap;
          text-overflow: ellipsis;
        }

        .packagePrice {
          color: #f47f1b;
        }
      }
    }
  }

  .button_green {
    width: 120px;
    height: 60px;
    color: #fff;
    font-size: 28px;
    line-height: 60px;
    text-align: center;
    background: linear-gradient(90deg, #3ad3c1 0%, #68e9db 100%);
    border: none;
    border-radius: 30px;
    .arrow {
      margin-left: 8px;
      color: #fff;
      font-size: 20px;
    }
  }
  .button_gray {
    width: 120px;
    height: 60px;
    color: #fff;
    font-size: 28px;
    line-height: 60px;
    text-align: center;
    background: #b0b3bf;
    border: none;
    border-radius: 30px;
  }
}

.notice {
  .content {
    max-height: 500px;
    padding-right: 24px;
    overflow: auto;
    text-align: left;
  }
}
