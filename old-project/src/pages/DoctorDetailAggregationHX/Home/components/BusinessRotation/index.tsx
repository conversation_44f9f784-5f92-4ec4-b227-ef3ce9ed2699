import React, { useEffect, useState } from 'react';
import { Toast, Modal } from 'antd-mobile';
import { history, Dispatch, connect, useLocation } from 'umi';

import { DoctorInfoType, ChildServiceType, NumberSourceItemType, DoctorModelState } from '@/typings/doctor.d';
import AppScheme from '@/utils/AppScheme';
import { getToken, getOpenId, getOrganCode, getChannelCode } from '@/utils/parameter';
import { isWechat } from '@/utils/platform';
import { sensorsRequest } from '@/utils/sensors';
import { HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import qs from 'query-string';
import { getDoctorIsUse } from '@/services/doctor';
import _ from 'lodash';
import styles from './index.less';
import { SERV_CODE_LIST } from '../../../../DoctorDetailAggregation/dataDictionary';
import EmblaCarousel from '../EmblaCarousel';
import Icon from '../../../../../assets/提示@2x.png';

const { toAppointmentRegistration, removeShare } = AppScheme;

interface MiddlePartProps {
  /** 当前中间模块类型 0: 医生服务模块 1: 医学咨询模块 2: 专病服务模块 */
  serviceList: Array<any>;
  /** 医生信息数据 */
  doctorInfo: DoctorInfoType;
  /** 服务code（从地址栏中获取） */
  servCode: string;
  /** 机构code（从地址栏中获取） */
  organCode: string;
  numberSourceList: any;
  /** 认证记录id（从地址栏中获取） */
  docFillRecordId: string;
  pmi: string;
  /** 校验token是否失效 */
  doCheckToken: (callback: () => void) => void;
  applayDisabled?: boolean; // 申请按钮是否可用
  dispatch: Dispatch;
  channel?: string; // 省医健通跳转参数
  sku?: string;
}

const BusinessRotation: React.FC<MiddlePartProps> = (props) => {
  // props
  const { query }: any = useLocation();
  const {
    serviceList = [],
    doctorInfo,
    servCode,
    doCheckToken,
    organCode = 'HID0101',
    docFillRecordId,
    pmi,
    applayDisabled = false,
    dispatch,
    channel = '',
    sku = '-1',
  } = props;

  const [modalVisible, setModalVisible] = useState(false); // 号源是否可用弹窗
  const [showmodalVisible, setShowModalVisible] = useState(true); // 吸入装置弹窗
  /* 新版医生主页服务列表-新增预约挂号 */
  const [newSeverList, setNewSeverList] = useState([]); // 过滤后的服务列表
  const {
    doctorId,
    doctorCode,
    organId: workOrganId,
    teamList = [],
    doctorType,
    doctorName,
    breathNotice,
    baseDeptCode: deptCode,
    deptName,
    baseWorkDeptCode,
    tips,
  } = doctorInfo;

  const tiNode = (
    <div className={styles.notice}>
      <div className={styles.content} dangerouslySetInnerHTML={{ __html: breathNotice }} />
    </div>
  );
  const toMobileProject = (url: string) => {
    // 先清除原生的分享按钮
    removeShare();
    setTimeout(() => {
      window.location.href = url;
    }, 100);
  };
  /** 跳转到相应页面 */
  const toRelativePage = async (advisory: ChildServiceType) => {
    Toast.loading('跳转中...', 0);
    const { servType, price, servTime, numLimit, status, serviceId, isTel, phoneTime, level } = advisory;
    const initOrganCode = SERV_CODE_LIST.find((item) => item.type === servType)?.initOrganCode;
    if (status === 0) {
      Toast.info('暂未开通服务', 1);
      return;
    }
    if (applayDisabled) {
      return;
    }

    // 固定排班
    if (servType == 21) {
      const params: AnyObject = {
        price,
        servTime,
        organId: workOrganId,
        servType,
        doctorId,
        doctorType,
        numLimit,
        doctorName,
        organCode: docFillRecordId ? 'HID0101' : organCode,
        token: getToken(),
        openId: getOpenId(),
        deptCode: baseWorkDeptCode,
        deptName,
        isTel,
        phoneTime,
        level,
        teamName: query.teamName ? query.teamName : '',
        sourceChannel: query.sourceChannel ? query.sourceChannel : '',
        isStaff: query.isStaff,
        piTeamId: query.piTeamId ? query.piTeamId : '',
        sku,
      };
      /* 省医健通跳转的渠道编码和cardNo */
      channel && (params.yjtChannel = channel);
      const ehealth_card_id = query.ehealth_card_id || HxSessionStorage.get(StorageEnum.YJT_CARD_NO);
      ehealth_card_id && (params.ehealth_card_id = ehealth_card_id);
      // 跳转到咨询购买页面
      const onlineVerifyPathname_ = `/online/verify?${qs.stringify(params)}`;
      toMobileProject(`${API_ZXMZ}${onlineVerifyPathname_}`);
      return;
    }

    let flag;
    // 查询是否有号源
    try {
      const res = await getDoctorIsUse({ doctorType, doctorId, organId: workOrganId, servType });
      Toast.hide();
      if (String(res) === 'false') {
        flag = String(res);
        setModalVisible(true);
      }
    } catch (error) {
      console.log('error:', error);
      Toast.hide();
    }
    if (flag === 'false') {
      return;
    }

    if (servType === 3) {
      const params = {
        hospitalCode: getOrganCode(),
        deptCategoryCode: deptCode,
        deptCategoryName: deptName,
        doctorName,
        doctorId,
      };
      sensorsRequest('SF_DOCTOR_DETAIL', { ...params }); // 随访门诊医生详情页埋点
    }

    const callback = () => {
      const params: AnyObject = {
        price,
        servTime,
        organId: workOrganId,
        servType,
        doctorId,
        doctorType,
        numLimit,
        doctorName,
        organCode: docFillRecordId ? 'HID0101' : organCode,
        token: getToken(),
        openId: getOpenId(),
        deptCode: baseWorkDeptCode,
        deptName,
        isTel,
        phoneTime,
        teamName: query.teamName ? query.teamName : '',
        sourceChannel: query.sourceChannel ? query.sourceChannel : '',
        isStaff: query.isStaff,
        piTeamId: query.piTeamId ? query.piTeamId : '',
        sku,
      };
      /* 省医健通跳转的渠道编码和cardNo */
      channel && (params.yjtChannel = channel);
      const ehealth_card_id = query.ehealth_card_id || HxSessionStorage.get(StorageEnum.YJT_CARD_NO);
      ehealth_card_id && (params.ehealth_card_id = ehealth_card_id);
      const onlineVerifyPathname = `/online/verify?${qs.stringify(params)}`;
      switch (servType) {
        case 1:
        case 3:
        case 47: // 在线门诊 随访门诊 便民门诊
          // 申请“呼吸内科”医生的在线门诊/门特时，展示温馨提示信息
          if (breathNotice) {
            Modal.alert('温馨提示', tiNode, [
              {
                text: '取消',
                onPress: () => {},
              },
              {
                text: '继续问诊',
                onPress: () => {
                  toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}`);
                },
              },
            ]);
          } else if (docFillRecordId) {
            const cardInfo = localStorage.getItem('cardInfo');
            const speMedicinePathname = `/online/verify?price=${price}&servTime=${servTime}&organId=${workOrganId}&docFillRecordId=${docFillRecordId}&cardInfo=${cardInfo}&servType=${servType}&doctorId=${doctorId}&doctorType=${doctorType}&numLimit=${numLimit}&doctorName=${doctorName}&organCode=${
              docFillRecordId ? 'HID0101' : organCode
            }&token=${getToken()}&openId=${getOpenId()}&deptCode=${deptCode}&deptName=${deptName}&teamName=${
              query.teamName ? query.teamName : ''
            }&sourceChannel=${query.sourceChannel ? query.sourceChannel : ''}&isStaff=${query.isStaff}&&piTeamId=${
              query.piTeamId ? query.piTeamId : ''
            }`;
            toMobileProject(`${API_ZXMZ}${speMedicinePathname}`);
          } else {
            // 跳转到咨询购买页面
            toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}&pmi=${pmi}`);
          }
          break;
        case 2: // 就医咨询（在线咨询）
        case 20: // 特需咨询
          toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}`);
        // eslint-disable-next-line no-fallthrough
        case 22: // 团队咨询
          // 判断是否有多个团队
          if (teamList.length > 1) {
            // 多个团队，跳转到选择团队页面
            history.push({
              pathname: '/doctor/teamlist',
              query: {
                doctorCode,
                doctorId,
                organCode,
                servCode,
              },
            });
          } else {
            const { price, servTime } = teamList[0];
            toMobileProject(
              `${API_ZXMZ}/online/verify?price=${price}&servTime=${servTime}&organId=${workOrganId}&servType=${servType}&doctorId=${doctorId}&doctorType=${doctorType}&doctorName=${doctorName}&organCode=HYTAPP&token=${getToken()}&openId=${getOpenId()}&deptCode=${deptCode}&deptName=${deptName}&teamName=${
                query.teamName ? query.teamName : ''
              }&sourceChannel=${query.sourceChannel ? query.sourceChannel : ''}&isStaff=${query.isStaff}&piTeamId=${
                query.piTeamId ? query.piTeamId : ''
              }`,
            );
          }
          break;
        case 28: // 影像云咨询
          // 跳转到咨询购买页面
          toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}`);
          break;

        case 29: // 在线义诊
          toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}&volunteerId=${serviceId}`);
          break;
        case 30: // 特药续方页面
          // eslint-disable-next-line no-case-declarations
          const url = `${COMMON_DOMAIN}/medicine/SpecificMedicinePrefecture/index?accessToken=${getToken()}&token=${getToken()}&organCode=${getOrganCode()}&channelCode=${getChannelCode()}&teamName=${
            query.teamName ? query.teamName : ''
          }&sourceChannel=${query.sourceChannel ? query.sourceChannel : ''}&piTeamId=${
            query.piTeamId ? query.piTeamId : ''
          }`;
          toMobileProject(url);
          break;
        case 31: // 用药咨询
          // 跳转到咨询购买页面
          toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}`);
          break;
        case 32: // 护理咨询
          // 跳转到咨询购买页面
          toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}`);
          break;
        case 37: // 症状咨询
          // 跳转到咨询购买页面
          toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}`);
          break;
        case 38: // 医学生咨询
          // 跳转到咨询购买页面
          toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}`);
          break;
        case 42: // 个人义诊
          // 跳转到咨询购买页面
          toMobileProject(`${API_ZXMZ}${onlineVerifyPathname}`);
          break;
        default:
          break;
      }
    };

    if (isWechat()) {
      doCheckToken(callback);
    } else {
      callback();
    }
    // }
  };

  const showTips = async (advisory: ChildServiceType) => {
    // 拦截弹窗
    if (tips && tips !== '' && showmodalVisible) {
      setShowModalVisible(false);
      Modal.alert(
        <div>
          {' '}
          <img src={Icon} alt="" style={{ width: '24px', height: '24px' }} /> 温馨提示
        </div>,
        <div
          style={{ textAlign: 'left' }}
          dangerouslySetInnerHTML={{
            __html: tips,
          }}
        />,
        [
          {
            text: '取消',
            onPress: () => {},
          },
          {
            text: '继续问诊',
            onPress: () => {
              toRelativePage(advisory);
            },
          },
        ],
      );
      return;
    }
    toRelativePage(advisory);
  };

  useEffect(() => {
    /** 组装服务列表-新增预约挂号到服务列表 */
    if (serviceList && !_.isEmpty(doctorInfo)) {
      const _serviceList: any = _.cloneDeep(serviceList);
      _serviceList.unshift({
        serviceName: '预约挂号',
        servCode: 'yygh',
        price: '',
        scheduleList: [],
        img: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/appoinmentHx/appointment_icon.png',
      });
      setNewSeverList(_serviceList);
    }
  }, [serviceList, doctorInfo]);

  /** 渲染医生服务模块 */
  // ui改版 所有聚合主页样式同渲染医学资讯模块
  const renderDoctorService = () => {
    return (
      <div>
        <div className={styles.servicesWrapper}>
          {!!newSeverList.length && (
            <EmblaCarousel
              {...props}
              doctorId={doctorId}
              organCode={organCode}
              serviceList={newSeverList}
              btnOnClick={(item) => {
                showTips(item);
              }}
              servCode={servCode}
            />
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={styles.container}>
      {renderDoctorService()}
      <Modal
        visible={modalVisible}
        transparent
        maskClosable
        onClose={() => setModalVisible(false)}
        title="温馨提示"
        footer={[
          {
            text: <div style={{ color: '#3AD3C1' }}>确定</div>,
            onPress: () => {
              setModalVisible(false);
            },
          },
        ]}
      >
        <div>
          <span>该医生号源已约满，您可申请该科室其他医生</span>
        </div>
      </Modal>
    </div>
  );
};

export default connect(({ hxdoctor }: { hxdoctor: DoctorModelState }) => ({
  hxdoctor,
}))(BusinessRotation);
