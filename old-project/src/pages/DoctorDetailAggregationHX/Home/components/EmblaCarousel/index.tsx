/*
 * @Author: <PERSON>
 * @Date: 2022-03-21 02:35:02
 * @LastEditor: <PERSON>
 * @LastEditTime: 2025-04-01 14:56:47
 * @FilePath: /hyt-person/src/pages/DoctorDetailAggregationHX/Home/components/EmblaCarousel/index.tsx
 * Copyright (c) 2022 by <PERSON>, All Rights Reserved.
 * @Description:
 */

import React, { useState, useEffect, useCallback, memo } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import request from '@/utils/request';
import { Modal } from 'antd-mobile';
import { Grid, Modal as ModalV5 } from 'antd-mobile-v5';
import HxModal from '@/components/HxModal';
import queryString from 'query-string';
import _ from 'lodash';
import { connect, Dispatch } from 'umi';
import { DoctorInfoType, DoctorModelState } from '@/typings/doctor';
import styles from './index.less';
import Appointment from '../Appointment';

interface EmblaCarouselProps {
  serviceList: Array<any>;
  btnOnClick: (item: any) => void;
  servCode: string;
  doctorId: string;
  organCode: string;
  dispatch: Dispatch;
  hxdoctor: DoctorModelState;
  doctorInfo: DoctorInfoType;
}

interface ContentProps {
  selected: boolean;
  onClick: () => void;
  imgSrc: string;
  name: string;
  keyValue: string;
  calculateAppointmentAmount: () => void;
}

const Thumb: React.FC<ContentProps> = ({ selected, onClick, imgSrc, name, keyValue, calculateAppointmentAmount }) => (
  <div className={`${selected ? styles.embla__slide__selected : styles.embla__slide}`} key={keyValue}>
    <div
      onClick={onClick}
      className={`${selected ? styles.embla__slide__inner__selected : styles.embla__slide__inner}`}
    >
      <img className={styles.embla__slide__thumbnail} src={imgSrc} alt="" />
      <span className={styles.embla__slide__name}>{name}</span>
      <span className={styles.price}>{calculateAppointmentAmount()}</span>
    </div>
  </div>
);

const EmblaCarousel: React.FC<EmblaCarouselProps> = (props) => {
  const { serviceList = [], btnOnClick, servCode, organCode, location, hxdoctor, doctorId } = props;
  const { selDoctorDetails = {} } = hxdoctor;
  const { sku } = queryString.parse(window.location.search);
  // 铁路专区
  if (Number(sku) === 13) {
    serviceList.forEach((item) => {
      if (item.serviceName === '个人义诊') {
        item.serviceName = '高原专区';
      }
    });
  }
  const [selectedIndex, setSelectedIndex] = useState(0);
  const [applayDisabled, setApplayDisabled] = useState(false);
  const [selectedItem, setSelectedItem] = useState({});

  const [emblaRef, emblaThumbs] = useEmblaCarousel({
    containScroll: 'keepSnaps',
    draggable: true,
    dragFree: true,
  });
  useEffect(() => {
    if (servCode === 'gdpb') {
      const flag = serviceList.findIndex((e) => {
        return e.servCode === 'gdpb';
      });
      if (flag > -1) {
        setSelectedIndex(flag);
      }
    }
  }, [servCode]);
  const [whetherThereIsANumber, set_whetherThereIsANumber] = useState(true);

  /* 获取是否可以约诊 购买 */
  const get_an_appointment = async () => {
    const res = await request('/gdpbSchedule/cheakGdpbSchedulePossibleToBuy', {
      method: 'GET',
      params: {
        doctorId,
        organCode,
      },
      prefix: PLATFORM_TRANSFORM,
    });
    set_whetherThereIsANumber(res.check);
    if (!res.check) {
      HxModal.show({
        title: '温馨提示',
        content: <div>{res.tips}</div>,
        actions: [
          {
            text: '确定',
            key: 'confirm',
            className: 'primary',
            onClick: () => {
              /* 排班约诊没有号源 默认展示线上门诊 */
              const index = serviceList.findIndex((v) => v.servCode === 'zxmz');

              if (index !== -1) {
                setSelectedIndex(index);
                setSelectedItem(serviceList[index]);
                set_whetherThereIsANumber(true);
              }
              ModalV5.clear();
            },
          },
        ],
      });
    }
  };
  /**
   * description: 点击缩略图触发的事件
   * @param {number} index 索引
   * @param {any} item 点击的item
   * @return {*}
   */
  const onThumbClick = async (index, item) => {
    if (item.serviceName === '排班约诊') {
      await get_an_appointment();
    } else {
      set_whetherThereIsANumber(true);
    }
    if (!emblaThumbs) return;
    if (emblaThumbs.clickAllowed()) {
      emblaThumbs.scrollTo(index);
      setSelectedIndex(index);
      setSelectedItem(serviceList[index]);
      /* 设置选择的服务 */
      props.dispatch({
        type: 'hxdoctor/updateState',
        payload: {
          selectCardServCode: serviceList[index]?.servCode,
        },
      });
    }
  };

  /**
   * description: 申请电话问诊
   * @param {any} itemObj 获取电话问诊需要传的参数
   * @return {*}
   */

  const applyPhoneConsultation = () => {
    let itemObj = {};
    if (selectedItem?.dcInquiryServiceConfigExpandEntity) {
      const { dcInquiryServiceConfigExpandEntity: item } = selectedItem;
      const { servType, price, servTime, numLimit, status, serviceId } = selectedItem;
      itemObj = {
        servType,
        price: item.price,
        servTime,
        numLimit: item.phoneTime,
        status,
        serviceId,
        phoneTime: item.phoneTime,
        isTel: 1 /* 判断是否是电话问诊 */,
        level: sessionStorage.getItem('docTitle'),
      };
    }
    whetherThereIsANumber && btnOnClick && btnOnClick(itemObj);
  };

  /**
   * @description: 计算预约挂号金额
   * @return {*}
   */
  const calculateAppointmentAmount = (item) => {
    if (item.servCode === 'yygh') {
      try {
        const { numberSourceList } = props;
        if (numberSourceList.length > 0) {
          const priceList = numberSourceList.map((item) =>
            (item?.sourceItemsRespVos || []).reduce((min, current) => {
              return current.regFee < min.regFee ? current : min;
            }),
          );
          const minPriceObj = priceList.reduce((min, current) => {
            return current.regFee < min.regFee ? current : min;
          });
          return <>&yen;{`${Number(minPriceObj.regFee) + Number(minPriceObj.serviceFee)}起`}</>;
        }
        return '';
      } catch (error) {
        return '';
      }
    }
    return <>&yen;{item?.price ?? '-'}</>;
  };

  useEffect(() => {
    const { zxmzFlag = 'false' } = location.query;

    if (!serviceList.length) return; // 如果 serviceList 为空，直接返回

    const index = serviceList.findIndex((v) => v.servCode === servCode);

    // 如果服务代码存在且是排班约诊
    if (servCode && index > -1 && serviceList[index].serviceName === '排班约诊') {
      get_an_appointment();
    }

    // 处理在线门诊相关
    if (index > -1) {
      setSelectedItem(serviceList[index]);
      setSelectedIndex(index);
    } else {
      /* 列表点击在线门诊按钮默认选中第一个服务 */
      /* serveCode jhzy 选中线上门诊第一个tab */
      if ((zxmzFlag === 'true' || servCode === 'jhzy') && serviceList.length > 1) {
        setSelectedItem(serviceList[1]);
        setSelectedIndex(1);
        return;
      }
      // 默认选择预约挂号
      setSelectedItem(serviceList[0]);
      setSelectedIndex(0);
    }
    /* 设置选择的服务-model */
    props.dispatch({
      type: 'hxdoctor/updateState',
      payload: {
        selectCardServCode: index > -1 ? serviceList[index].servCode : 'yygh',
      },
    });
  }, [serviceList]);

  const renderTxt = (str: string) => {
    if (!str) {
      return <div className={styles.serviceDesc} />;
    }
    if (str.includes('<br/>')) {
      return str.split('<br/>').map((notice: string) => {
        return (
          <div className={styles.serviceDesc} key={notice}>
            {notice}
          </div>
        );
      });
    }
    if (str.includes('<br />')) {
      return str.split('<br />').map((notice: string) => {
        return (
          <div className={styles.serviceDesc} key={notice}>
            {notice}
          </div>
        );
      });
    }
    return <div className={styles.serviceDesc}>{str}</div>;
  };
  return (
    <>
      <div className={`${styles.embla} `}>
        <div className={styles.embla__viewport} ref={emblaRef}>
          <div className={`${styles.embla__container}`}>
            {serviceList.map((item, index) => (
              <Thumb
                key={item.servCode}
                keyValue={item.servCode}
                onClick={() => onThumbClick(index, item)}
                selected={index === selectedIndex}
                imgSrc={item.img}
                name={item.serviceName === '线上排班约诊' ? '排班约诊' : item.serviceName}
                calculateAppointmentAmount={() => calculateAppointmentAmount(item)}
              />
            ))}
          </div>
        </div>
      </div>
      {/* 渲染服务内容 */}
      {!_.isEmpty(selectedItem) && (
        <>
          {selectedItem?.servCode === 'yygh' ? (
            !_.isEmpty(selDoctorDetails) && <Appointment {...props} />
          ) : (
            <div className={styles.serviceContent}>
              {selectedItem?.isRenderServiceConfig === 1 && selectedItem?.scheduleList.length === 0 && (
                <div className={styles.advisory}>
                  <div className={styles.advisoryInfo}>
                    <div className={styles.left}>
                      <img src={selectedItem.img} alt="" className={styles.serviceIcon} />
                      <div className={styles.serviceInfo}>
                        <div className={styles.serviceName}>
                          {selectedItem.serviceName === '排班约诊'
                            ? '图文问诊'
                            : selectedItem.serviceName === '线上门诊'
                            ? '图文问诊'
                            : selectedItem.serviceName}
                        </div>
                        {/* <div className={styles.serviceName}>图文问诊 </div> */}
                        <div className={styles.servicePrice}>{`¥${
                          selectedItem.price === undefined ? '-' : selectedItem.price
                        }/${selectedItem.servTime === undefined ? '-' : selectedItem.servTime}小时(${
                          selectedItem.numLimit === undefined ? '-' : selectedItem.numLimit
                        }条)`}</div>
                      </div>
                    </div>
                    <div
                      className={
                        selectedItem.status === 0 || applayDisabled || !whetherThereIsANumber
                          ? styles.button_gray
                          : styles.button_green
                      }
                      onClick={() => {
                        whetherThereIsANumber && btnOnClick && btnOnClick(selectedItem);
                      }}
                    >
                      申请
                    </div>
                  </div>
                  {selectedItem?.serviceDesc ? (
                    <div className={styles.serviceDescWrap}>{renderTxt(selectedItem?.serviceDesc)}</div>
                  ) : null}
                </div>
              )}
              {/* 电话问诊 */}
              {selectedItem?.dcInquiryServiceConfigExpandEntity && selectedItem?.servCode === 'gdpb' && (
                <div className={styles.advisory}>
                  <div className={styles.advisoryInfo}>
                    <div className={styles.left}>
                      <img src={selectedItem.img} alt="" className={styles.serviceIcon} />
                      <div className={styles.serviceInfo}>
                        <div className={styles.serviceName}>电话问诊</div>
                        <div className={styles.servicePrice}>{`¥${
                          selectedItem.dcInquiryServiceConfigExpandEntity.price === undefined
                            ? '-'
                            : selectedItem.dcInquiryServiceConfigExpandEntity.price
                        }/${
                          selectedItem.dcInquiryServiceConfigExpandEntity.phoneTime &&
                          selectedItem.dcInquiryServiceConfigExpandEntity.phoneTime
                        }分钟`}</div>
                      </div>
                    </div>
                    <div
                      className={
                        selectedItem.status === 0 || applayDisabled || !whetherThereIsANumber
                          ? styles.button_gray
                          : styles.button_green
                      }
                      onClick={applyPhoneConsultation}
                    >
                      申请
                    </div>
                  </div>
                  {selectedItem?.extendDesc ? (
                    <div className={styles.serviceDescWrap}>{renderTxt(selectedItem?.extendDesc)}</div>
                  ) : null}
                </div>
              )}
              {/* 医生关闭在线门诊时，自定义排班功能打开，且有数据时：显示医生设置的自定义排班，显示最近7天的排班 */}
              {/* 新增特需门诊txzx */}
              {!!selectedItem?.scheduleList.length &&
                (selectedItem?.servCode === 'zxmz' || selectedItem?.servCode === 'txzx') && (
                  <div className={styles.zxmzScheduleList}>
                    <div className={styles.zxmzScheduleTitle}>请在以下时间申请问诊</div>
                    <div className={styles.zxmzScheduleListWrap}>
                      {selectedItem.scheduleList.map((item, index) => {
                        return (
                          <div className={styles.zxmzScheduleItem} key={index}>
                            <div className={styles.monthTime}>
                              <div className={styles.time1}>{item.dateTime}</div>
                              <div className={styles.time2}>{item.weekDesc}</div>
                            </div>
                            <div className={styles.time}>
                              <Grid columns={6} gap={[4, 0]} style={{ margin: '0 auto' }}>
                                {item.rangeList.map((val, inx) => (
                                  <Grid.Item span={2} key={inx}>
                                    <span>
                                      {val?.startTime}-{val?.endTime}
                                    </span>
                                  </Grid.Item>
                                ))}
                              </Grid>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
            </div>
          )}
        </>
      )}
    </>
  );
};

export default connect(({ hxdoctor }: { hxdoctor: DoctorModelState }) => ({
  hxdoctor,
}))(EmblaCarousel);
