.embla {
  position: relative;
  max-width: 100%;
  height: 208px;
}

.embla__viewport {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.embla__viewport.is_draggable {
  cursor: move;
  cursor: grab;
}

.embla__viewport.is_dragging {
  cursor: grabbing;
}

.embla__container {
  display: flex;
  -khtml-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  .price {
    font-size: 24px;
    color: #fc4553;
    line-height: 24px;
    margin-top: 8px;
  }
  .embla__slide__name {
    font-size: 32px;
    font-weight: bold;
    color: #03081a;
    line-height: 40px;
    margin-top: 16px;
  }
}

.embla__slide__img {
  position: absolute;
  top: 50%;
  left: 50%;
  display: block;
  width: auto;
  min-width: 100%;
  max-width: none;
  min-height: 100%;
  transform: translate(-50%, -50%);
}

.embla_thumb {
  margin-top: 0.223rem;
  padding-top: 0;
}

.embla__slide {
  // margin-bottom: 1rem;

  span {
    margin-top: 0.056rem;
  }
}

.embla__slide {
  .embla__slide__inner__selected {
    height: 100%;
  }
}

.embla__slide__inner {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  //justify-content: space-between;
  width: 208px;
  margin-right: 24px;
  padding: 16px 0 24px 0;
  font-size: 1rem;
  border: 0.019rem solid transparent;
  background: #ffffff;
  border-radius: 24px;
  height: 192px;
}

.embla__slide__selected {
  //height: 30vw;

  .embla__slide__inner__selected {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 208px;
    margin-right: 24px;
    padding: 16px 0 40px 0;
    font-size: 1rem;
    //border: 0.019rem solid rgba(86, 141, 242, 0.6);
    background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/hx_card_select.png') no-repeat;
    background-size: 100% 100%;
    border-radius: 0.43rem;
    height: 208px;
  }
}

.embla__slide__thumbnail {
  width: 64px;
  height: 64px;
}

.serviceContent {
  width: 100%;
  background: #ffffff;
  border-radius: 24px;
  overflow: 'hidden';
  padding: 24px;
  margin-top: 24px;
}

.advisory {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  font-size: 0.24rem;
  font-weight: 400;
  color: #989eb4;
  &:not(:first-child) {
    margin-top: 24px;
  }
  &:not(:last-child) {
    border-bottom: 2px solid #f6f8ff;
    padding-bottom: 24px;
  }

  img {
    width: 1.823rem;
  }
  .serviceDescWrap {
    margin-top: 16px;
    background: #fbfbfd;
    width: 100%;
    padding: 20px 24px;
    border-radius: 16px;
  }
  .serviceDesc {
    font-size: 0.86rem;
    font-weight: 400;
    color: #989eb4;
    line-height: 40px;
    border-radius: 16px;
    color: #989eb4;
  }

  .advisoryInfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    > .left {
      display: flex;
      align-items: center;
      .serviceIcon {
        width: 72px;
        height: 72px;
      }
      .serviceInfo {
        margin-left: 24px;
        .serviceName {
          font-size: 32px;
          font-weight: bold;
          text-align: left;
          color: #03081a;
          line-height: 44px;
        }

        .servicePrice {
          font-size: 24px;
          font-weight: 500;
          text-align: left;
          color: #fc4553;
          line-height: 34px;
          margin-top: 8px;
        }
      }
    }

    .button_green {
      width: 104px;
      height: 56px;
      color: #fff;
      font-size: 1rem;
      text-align: center;
      background: linear-gradient(90deg, #3ad3c1, #68e9db);
      border: none;
      border-radius: 0.93rem;
      display: flex;
      align-items: center;
      justify-content: center;

      .arrow {
        margin-left: 0.149rem;
        color: #fff;
        font-size: 0.372rem;
      }
    }

    .button_gray {
      width: 104px;
      height: 56px;
      color: #fff;
      font-size: 1rem;
      text-align: center;
      background: #b0b3bf;
      border: none;
      border-radius: 0.93rem;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  &:last-child {
    margin-bottom: 0.186rem;
  }
}

.zxmzScheduleList {
  width: 100%;
  background: #f7faff;
  border: 1px solid rgba(86, 141, 242, 0.7);
  border-radius: 12px;
  padding: 24px;
  .zxmzScheduleTitle {
    font-size: 28px;
    text-align: left;
    color: #568df2;
    line-height: 40px;
  }
  .zxmzScheduleListWrap {
    margin-top: 24px;
    .zxmzScheduleItem {
      display: flex;
      .monthTime {
        width: 110px;
        min-height: 92px;
        background: rgba(86, 141, 242, 0.1);
        border-radius: 12px;
        flex-shrink: 0;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        .time1 {
          font-size: 28px;
          text-align: center;
          color: #03081a;
        }
        .time2 {
          font-size: 24px;
          text-align: center;
          color: #989eb4;
        }
      }
      .time {
        // justify-content: center;
        margin-left: 16px;
        flex: 1;
        min-height: 92px;
        background: #ffffff;
        border-radius: 12px;
        font-size: 24px;
        text-align: center;
        color: #03081a;
        padding: 26px 10px;
        span {
          max-width: 134px;
        }
      }
      &:not(:first-child) {
        margin-top: 16px;
      }
    }
  }
}
