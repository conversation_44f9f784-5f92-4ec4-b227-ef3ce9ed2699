import React, { useState, useEffect, FC, useCallback } from 'react';
import { connect, Dispatch, history } from 'umi';
import useEmblaCarousel from 'embla-carousel-react';
import styles from './index.less';
import avatar from '../../../../../assets/speMedicine/default.jpg';

interface PageProps {
  data: any;
}
const App: FC<PageProps> = (props) => {
  const goInfo = () => {
    history.push({
      pathname: '/doctor/team/home',
      query: {
        teamId: props.data.teamId,
        teamWorkId: props.data.teamWorkId,
      },
    });
  };

  const [emblaRef, emblaThumbs] = useEmblaCarousel({
    containScroll: 'keepSnaps',
    draggable: true,
    dragFree: true,
  });
  const onThumbClick = useCallback(async () => {
    goInfo();
  }, [emblaThumbs]);
  return (
    <div className={styles.main}>
      <div className={styles.teamName}>
        {' '}
        {props.data.teamName} <span className={styles.tag}>可预约专家号</span>{' '}
      </div>
      <div ref={emblaRef}>
        <div className={`${styles.doctor_box}`}>
          {props.data.teamMember.map((item, index) => (
            <div
              onClick={() => onThumbClick(index, item)}
              className={styles.docotor_item}
              style={
                item.roleCode === 'dz'
                  ? {
                      background: '#FFF6E6',
                    }
                  : {}
              }
            >
              <img
                className={item.roleCode === 'dz' ? styles.leader : ''}
                src={item.portrait ? item.portrait : avatar}
                alt=""
              />
              <div className={styles.doctorName}>{item.doctorName}</div>
              {/* {item.roleCode === 'dz' ? <div className={styles.pileader}>首席专家</div> : null} */}
            </div>
          ))}
        </div>
      </div>
      <div className={styles.btn} onClick={goInfo}>
        申请问诊
      </div>
    </div>
  );
};
export default App;
