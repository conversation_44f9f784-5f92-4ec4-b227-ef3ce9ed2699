/*
 * @Author: <PERSON>
 * @Date: 2022-03-18 14:38:30
 * @LastEditor: <PERSON>
 * @LastEditTime: 2025-01-07 17:11:37
 * @FilePath: /hyt-person/src/pages/DoctorDetailAggregationHX/constance.ts
 * Copyright (c) 2022 by <PERSON>, All Rights Reserved.
 * @Description:
 */

/**
 */
export const DOCTOR_HOME_TAB_ENUM = [
  {
    title: '健康科普',
    value: [],
    tabValue: 0,
    showRedIcon: false,
  },
  {
    title: '线上问诊',
    value: [],
    tabValue: 1,
    showRedIcon: false,
  },
  {
    title: '预约挂号',
    value: [],
    tabValue: 2,
    showRedIcon: false,
  },
];