.container {
  position: relative;
  background-color: #f5f6fa;

  .info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.86rem 0.86rem 2.79rem 0.86rem;
    object-fit: fill;
    background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor_info_header_bg.png') bottom
      center no-repeat;
    background-size: 100% 100%;
    .mainInfo {
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: space-between;
      padding: 5px 0;
      color: #03081a;
      font-size: 1rem;

      p {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 0.2rem;

        &:first-child {
          align-items: baseline;
        }
      }

      .name {
        margin-right: 0.6rem;
        font-weight: 600;
        font-size: 1.29rem;
      }

      .docTitle {
        margin-left: 0.6rem;
        font-size: 1rem;
      }

      .rank {
        height: 0.93rem;
        margin-right: 0.43rem;
        padding: 0 0.21rem;
        color: #fff;
        font-size: 0.64rem;
        line-height: 0.93rem;
        text-align: center;
        background-color: #568df2;
        border-radius: 0.29rem;
      }

      .hospital {
        margin-right: 16px;
        font-size: 1rem;
      }
    }

    .avatar {
      width: 5.71rem;
      height: 5.71rem;
      overflow: hidden;
      border-radius: 2.865rem;
    }
  }

  .contentPart {
    margin: 0 0.86rem 0.86rem;
    padding: 1.43rem 0.71rem;
    background-color: #fff;
    border-radius: 0.43rem;

    .title {
      display: flex;
      flex-direction: row;
      align-items: center;
      .img {
        width: 1.5rem;
        height: 1.5rem;
      }
      .name {
        margin-left: 0.43rem;
        color: #333;
        font-weight: bold;
        font-size: 30px;
      }
    }
    .content {
      margin-top: 30px;
      color: #666;
      font-size: 26px;
      white-space: pre-wrap;
    }
  }
}
