import React, { CSSProperties, useEffect, useState } from 'react';
import { connect } from 'umi';
import _ from 'lodash';

import { DoctorModelState, WorkType } from '@/typings/doctor.d';
import AppScheme from '@/utils/AppScheme';
import { HxDoctorAvatar } from '@/components';
import { addShareToApp } from '../util';
import styles from './index.less';

const { removeShare } = AppScheme;
const introductionIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor_info_brief.png';
const professionIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor_info_goot_at.png';
const rareDiseaseIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor_info_rare_disease.png';

interface BriefInfoProps {
  location: {
    query: AnyObject;
  };
}

interface ContentProps {
  icon: string;
  title: string;
  content: string;
  style?: CSSProperties;
}

const Content: React.FC<ContentProps> = ({ icon, title, content, style }) => (
  <div style={style} className={styles.contentPart}>
    <div className={styles.title}>
      <img src={icon} alt="图片不见了！" className={styles.img} />
      <div className={styles.name}>{title}</div>
    </div>
    <div className={styles.content}>{content || '暂无内容'}</div>
  </div>
);

const BriefInfo: React.FC<BriefInfoProps> = (props) => {
  // props
  const {
    location: { query },
  } = props;
  const {
    doctorName,
    docTitle,
    deptName,
    organName,
    portrait,
    level,
    introduction,
    profession,
    doctorCode,
    organCode,
    doctorId,
    servCode,
    rareDiseaseProfession,
    hospitalTitleName,
  } = query;
  // console.log('props', props);
  // const { servCode = 'jhzy', middleType } = query;

  useEffect(() => {
    return () => {
      removeShare();
    };
  }, []);

  const doAddShareToApp = () => {
    const pathData = {
      doctorCode,
      organCode,
      doctorId,
      servCode,
      // middleType,
    };
    const otherData = {
      portrait,
      doctorName,
      docTitle,
      organName,
      deptName,
      servCode,
    };
    addShareToApp(pathData, otherData);
  };

  useEffect(() => {
    if (_.isEmpty(doctorId)) {
      return;
    }
    doAddShareToApp();
  }, [doctorId]);

  return (
    <div className={styles.container}>
      <div className={styles.info}>
        <div className={styles.mainInfo}>
          <p>
            <span className={styles.name}>{doctorName}</span>
            {docTitle || hospitalTitleName ? '|' : ''}
            <span className={styles.docTitle}>{` ${docTitle || hospitalTitleName || ''}`}</span>
          </p>
          <p>
            <span>{deptName}</span>
          </p>
          <p>
            {level && <span className={styles.rank}>{level}</span>}
            <span className={styles.hospital}>{organName}</span>
          </p>
        </div>
        <HxDoctorAvatar src={portrait} alt="" className={styles.avatar} />
      </div>
      <Content style={{ marginTop: '-2rem' }} icon={professionIcon} title="擅长疾病" content={profession} />
      <Content icon={introductionIcon} title="个人简介" content={introduction} />
      {rareDiseaseProfession && <Content icon={rareDiseaseIcon} title="罕见病擅长" content={rareDiseaseProfession} />}
    </div>
  );
};

export default connect(({ doctor }: { doctor: DoctorModelState }) => ({ doctor }))(BriefInfo);
