.lungTransplantCouYsjs {
  width: 100%;
  margin-top: 32px;
  // padding: 0 24px;
  padding-bottom: 0;
  border-radius: 16px;
  // background-color: #fff;

  .lungTransplantCouZjjsBody:nth-last-child(1) {
    // margin-bottom: 0;
  }

  .lungTransplantCouZjjsBody {
    width: 100%;
    // height: 224px;
    // border-radius: 12px;
    background-color: #fff;
    border-bottom: 2px solid #f5f6fa;
    display: flex;
    align-items: center;
    padding: 24px;
    box-sizing: border-box;
    position: relative;
    // margin-bottom: 24px;

    .lungTransplantCouZjjsAvatar {
      min-width: 124px;
      max-width: 124px;
      min-height: 124px;
      max-height: 124px;
      border-radius: 50%;
      background-size: 100% 100%;
      display: inline-block;
      margin-right: 24px;
    }

    .lungTransplantCouZjjsDes {
      // height: 176px;
      display: flex;
      flex-direction: column;

      .lungTransplantCouZjjsTitle {
        > span:nth-child(1) {
          height: 52px;
          font-size: 36px;
          font-weight: 600;
          color: #03081a;
          line-height: 52px;
          margin-right: 24px;
        }

        > span:nth-child(2) {
          height: 40px;
          font-size: 28px;
          font-weight: 400;
          color: #03081a;
          line-height: 40px;
        }
      }

      .lungTransplantCouZjjsSubTitle {
        height: 44px;
        font-size: 28px;
        font-weight: 400;
        color: #03081a;
        line-height: 44px;
        margin-top: 4px;

        > span:nth-child(1) {
          margin-right: 16px;
        }
      }

      .lungTransplantCouZjjsFooter {
        position: relative;
        width: 502px;

        > i {
          display: inline-block;
          width: 50px;
          height: 24px;
          background-image: url(../../../assets/LungTransplantCoun/sc.png);
          background-size: 100% 100%;
          position: absolute;
          left: 0;
          top: 4px;
        }

        > span {
          text-indent: 54px;
          display: block;
          color: #989eb4;
          // width: 458px;
          font-size: 24px;
          font-weight: 400;
          line-height: 34px;
          height: 64px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }

      .lungTransplantCouZjjsSubTitlerow1 {
        font-size: 24px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #989eb4;
        margin-top: 16px;

        span {
          font-size: 32px;
          font-family: DINAlternate, DINAlternate;
          font-weight: bold;
          color: #3ad3c1;
        }

        i {
          font-size: 24px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #989eb4;
          margin: 0 8px;
        }
      }

      .row2Box {
        width: 100%;
        display: flex;
        justify-content: flex-end;
        margin-top: 16px;

        .lungTransplantCouZjjsSubTitlerow2 {
          // width: 112px;
          // height: 40px;
          padding: 6px 24px;
          border-radius: 24px;
          box-sizing: border-box;
          font-size: 28px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          // line-height: 40px;
          text-align: center;
          background-image: linear-gradient(to left, rgb(59, 212, 194), rgb(104, 233, 219));
        }
      }
    }

    > i {
      width: 526px;
      height: 2px;
      background: #f5f6fa;
      right: -24px;
      bottom: 0;
      position: absolute;
    }
  }
}

.container {
  background: #f5f6fa;
  width: 100vw;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 24px 92px 24px;
  box-sizing: border-box;

  .bannar {
    width: 100vw;
    height: 338px;
  }

  .briefIntroduction {
    width: 100%;
    height: 592px;
    border-radius: 16px;
    background-color: #ffffff;
    position: relative;

    .bref {
      width: 100%;
      position: absolute;
      top: -34px;
      background-color: #ffffff;
      border-radius: 16px;
      padding: 32px 24px;
      box-sizing: border-box;

      .bannerTitle {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 24px;

        img {
          width: 40px;
          height: 28px;
        }

        span {
          font-size: 36px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 600;
          color: #03081a;
          margin: 0 12px;
        }
      }

      .brefContent {
        width: 100%;
        font-size: 28px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        color: #03081a;
        // padding-top: 32px;

        .desc {
          font-size: 28px;
          font-family: PingFangSC, PingFang SC;
          font-weight: 400;
          color: #03081a;
          position: relative;
          line-height: 48px;

          .highlight {
            font-size: 26px;
            font-family: PingFangSC, PingFang SC;
            font-weight: 400;
            color: #3ad3c1;
            position: absolute;
            bottom: 0;
            right: 0;
            display: flex;
            align-items: center;

            // background-color: #fff;
            span {
              background-color: #fff;

              img {
                width: 36px;
                height: 36px;
                vertical-align: sub;
              }
            }

            i {
              display: inline-block;
              // width: 50px;
              // height: 40px;
              width: 5em;
              height: 1em;
              background-image: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 1));
            }
          }
        }
      }
    }
  }
}

.line4 {
  // width:200px;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line1 {
  // width:200px;
  word-break: break-all;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.popBox {
  :global {
    .adm-popup-body {
      border-radius: 16px 16px 0px 0px;
    }
  }

  .contentBox {
    padding: 40px 32px;
    box-sizing: border-box;
    position: relative;

    .contHeader {
      margin-bottom: 24px;
      text-align: center;

      span {
        font-size: 40px;
        font-family: PingFangSC, PingFang SC;
        font-weight: 500;
        color: #03081a;
      }

      img {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        position: absolute;
        top: 24px;
        right: 24px;
      }
    }

    .contStr {
      font-size: 28px;
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      color: #03081a;
      max-height: 766px;
      overflow: auto;
    }
  }
}

.lungTransplantCouZjjsTitle1 {
  margin-top: 32px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 40px;
    height: 28px;
  }

  span {
    font-size: 36px;
    font-family: PingFangSC, PingFang SC;
    font-weight: 600;
    color: #03081a;
    // margin: 0 12px;
  }
}

.headers {
  padding: 16px 24px;
  box-sizing: border-box;
  background: #dbf6f2;
  font-size: 28px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: rgba(3, 8, 26, 0.75);
}

.headersBox {
  margin-bottom: 32px;
  background-color: #fff;
  border-radius: 16px;
  overflow: hidden;
}

.headersBox:nth-last-child(1) {
  margin-bottom: 0;
}
