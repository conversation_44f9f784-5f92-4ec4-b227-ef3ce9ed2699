import React, { FC, useEffect, useState } from 'react';
import { Popup } from 'antd-mobile-v5';
import { connect, IChildSkinModelState, Dispatch, history } from 'umi';
import { HxIcon, HxEmpty } from '@/components';
import styles from './index.less';
import banner from '@/assets/childSkin/childSkinBanner.png';
import definedAvatar from '@/assets/childSkin/childSkinBanner.png';
import icon1 from '@/assets/childSkin/icon1.png';
import icon2 from '@/assets/childSkin/icon2.png';
import _ from 'lodash';

interface IProps {
  dispatch: Dispatch;
  childSkin: IChildSkinModelState;
  location: {
    query: {
      data: string;
    };
  };
}

const ChildSkinHome: FC<IProps> = (props) => {
  const {
    dispatch,
    childSkin,
    location: { query = {} },
  } = props;
  const {}: any = childSkin;
  const {}: any = query;
  const [docData, setDocData] = useState<any>({});
  const TEAM_DATA = [
    {
      value: 'ssmg',
      label: '色素性/脉管性皮肤病（胎记）团队',
    },
    {
      value: 'btfy',
      label: '变态反应性皮肤病（特应性皮炎/湿疹）团队',
    },
    {
      value: 'ycpf',
      label: '遗传性皮肤病团队',
    },
    {
      value: 'grpf',
      label: '感染性皮肤病团队',
    },
    {
      value: 'pfzl',
      label: '皮肤肿瘤团队',
    },
  ];
  /* 获取医生数据 */
  const getDoc = (val: string) => {
    dispatch({
      type: 'childSkin/patientLtxDoctors',
      payload: {
        pageSize: 9999,
        pageNum: 1,
        randomDocListType: val,
      },
      callback(res: any) {
        console.log(res, 9999);
        setDocData((pre) => {
          let _pre = _.cloneDeep(pre);
          _pre[val] = res?.data?.list;
          return _pre;
        });
      },
    });
  };
  /* 跳转医生主页 */
  const toDetail = (item: any) => {
    history.push({
      pathname: '/doctor/index',
      query: {
        ...query,
        doctorId: item?.doctorId,
        deptName: item?.deptName,
        organId: item?.organId,
        servCode: item?.servCode,
      },
    });
  };
  useEffect(() => {
    TEAM_DATA.map((item) => {
      getDoc(item.value);
    });
  }, []);

  return (
    <>
      <div className={styles.container}>
        <img className={styles.bannar} src={banner} alt="图片飞走了！" />
        <div className={styles.briefIntroduction}>
          <div className={styles.bref}>
            <div className={styles.bannerTitle}>
              <img src={icon1} alt="图片飞走了！" />
              <span>儿童皮肤病专区</span>
              <img src={icon2} alt="图片飞走了！" />
            </div>
            <div className={styles.brefContent}>
              <div className={`${styles.desc}`}>
                儿童皮肤病是皮肤科的重要亚专业，无论是病种还是诊疗都与成人有很多的不同，华西医院皮肤科在儿童皮肤病的诊疗实力位于国内先进水平，设备完善，具有特色优势。
                <br />
                儿童皮肤病中心将博采众长，不仅对包括色素性/脉管性、变态反应性、感染性皮肤病等儿童常见病进行规范化诊疗，还将致力于对遗传性皮肤病、儿童皮肤肿瘤等罕见病进行全程管理。通过建立儿童皮肤病门诊满足患儿的就诊需求，提高患儿及其家人的生活质量。
                {/* <span className={styles.highlight}>
                <i></i>
                <span>
                  更多简介
                  <img src={icon3} alt="图片飞走了！" />
                </span>
              </span> */}
              </div>
            </div>
          </div>
        </div>
        <div className={styles.lungTransplantCouZjjsTitle1}>
          <img src={icon1} alt="图片飞走了！" />
          <span>推荐医生</span>
          <img src={icon2} alt="图片飞走了！" />
        </div>
        <div className={styles.lungTransplantCouYsjs}>
          {/* {doctorList.map((doctorItem: any) => { */}
          {TEAM_DATA.map((item: any) => {
            return (
              docData[item?.value] &&
              docData[item?.value]?.length > 0 && (
                <div className={styles.headersBox}>
                  <div className={styles.headers}>{item?.label}</div>
                  {docData[item?.value].map((doctorItem: any) => {
                    return (
                      <div
                        className={styles.lungTransplantCouZjjsBody}
                        key={doctorItem.doctorId}
                        onClick={() => toDetail(doctorItem)}
                      >
                        <span
                          className={styles.lungTransplantCouZjjsAvatar}
                          style={{
                            backgroundImage: `url(${doctorItem.headPortraits || definedAvatar})`,
                          }}
                        />
                        <div className={styles.lungTransplantCouZjjsDes}>
                          <div className={styles.lungTransplantCouZjjsTitle}>
                            <span>{doctorItem.doctorName || '-'}</span>
                            <span>{doctorItem.titelName || '-'}</span>
                          </div>
                          <div className={styles.lungTransplantCouZjjsSubTitle}>
                            <span>{doctorItem.organName || '-'}</span>
                            <span>{doctorItem.deptName || '-'}</span>
                          </div>
                          <div className={styles.lungTransplantCouZjjsFooter}>
                            <i />
                            <span>{doctorItem.profession || '-'}</span>
                          </div>
                          <div className={styles.lungTransplantCouZjjsSubTitlerow1}>
                            好评率
                            <span>{doctorItem?.satisfaction}%</span>
                            <i>/</i>
                            问诊量
                            <span>{doctorItem?.servTimes}</span>
                          </div>
                          <div className={styles.row2Box}>
                            <div className={styles.lungTransplantCouZjjsSubTitlerow2}>申请问诊</div>
                          </div>
                        </div>
                        {/* <i /> */}
                      </div>
                    );
                  })}
                </div>
              )
            );
          })}
        </div>
      </div>
    </>
  );
};

export default connect(({ childSkin }: { childSkin: IChildSkinModelState }) => ({
  childSkin,
}))(ChildSkinHome);
