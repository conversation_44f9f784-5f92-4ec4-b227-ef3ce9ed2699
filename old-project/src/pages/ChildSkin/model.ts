import { Reducer } from 'redux';
import { Effect } from 'umi';
import { createAction } from '@/utils/common';
import { patientLtxDoctors } from './service';

export interface IChildSkinModelState {}

export interface IChildSkinModel {
  namespace: 'childSkin';
  state: IChildSkinModelState;
  effects: {
    patientLtxDoctors: Effect;
  };
  reducers: {
    updateState: Reducer<IChildSkinModelState>;
  };
}

const ChildSkinModel: IChildSkinModel = {
  namespace: 'childSkin',

  state: {},
  effects: {
    *patientLtxDoctors({ payload, callback }, { put }) {
      const res = yield patientLtxDoctors({ ...payload }) || {};
      callback && callback(res);
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
export default ChildSkinModel;
