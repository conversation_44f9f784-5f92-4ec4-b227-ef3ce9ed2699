import React, { PureComponent } from 'react';
import qs from 'query-string';
import { Toast, Button } from 'antd-mobile';
import { HxIcon } from '@/components';
import { history } from 'umi';
import { Dispatch, AnyAction } from 'redux';
import styles from './index.less';
import { obtainAuthorization } from '../Appointment/service';

interface IProps {
  location: {
    query: any;
    hospitalCode: string;
    sysAppointmentId: string;
  };
  dispatch: Dispatch<AnyAction>;
}

interface IState {
  isAgree: boolean;
}

class Notice extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      isAgree: false,
    };
  }

  clickAgreement = () => {
    Toast.info('《当日挂号须知》', 1);
  };

  clickConfirm = async () => {
    const { isAgree } = this.state;
    if (!isAgree) {
      Toast.info('请阅读并同意挂号须知', 1);
      return;
    }
    const {
      location: {
        query: { hospitalCode = '', sysAppointmentId = '' },
      },
    } = this.props;
    const res = await obtainAuthorization({
      organCode: 'HXGZYJYDQRMYY01',
      hospitalCode: 'HXGZYJYDQRMYY01',
      sysAppointmentId,
    });
    console.log(99999, res);
    if (res?.code === '1') {
      Toast.success('授权成功', 1);
      setTimeout(() => {
        // history.goBack();
        history.push('/appointment/order/list');
      }, 1000);
    }
  };

  clickAgree = () => {
    const { isAgree } = this.state;
    this.setState({
      isAgree: !isAgree,
    });
  };

  render() {
    const { isAgree } = this.state;
    return (
      <div className={styles.container}>
        <div className={styles.content}>
          <p>在您使用广东健康档案互通系统前，请仔细阅读以下内容：</p>
          <p>
            1.广东健康档案互通系统是根据《基本医疗卫生与健康促进法》等法律法规，以及《国家基本公共卫生服务规范（第三版）》、《广东省卫生健康委居民健康档案管理规范》等文件要求而设立的，用于建设居民健康档案，并对健康信息进行连续记录、动态更新。按照《民法典》《网络安全法》《个人信息保护法》《数据安全法》等有关规定，已采取合理可行的步骤去保障系统内各类信息的私隐、保密及安全。
          </p>
          <p>
            2.广东健康档案互通系统的健康档案是基于电子健康码为标识而建立的，并对个人的健康信息进行连续记录、动态更新。目前仅支持查看本人2021年1月以来在广东省二级及以上医疗机构的诊疗记录、健康体检、检验检查结果等信息，以及疫苗接种、献血用血等记录。
          </p>
          <p>
            3.广东健康档案互通系统仅用于居民查阅本人的健康档案，或经本人授权允许医疗机构查看个人的健康档案，用于改善医护服务效率和质量。当您点击本用户须知下方的“同意”按钮后，即表示允许所就诊的医疗机构可在30天内查看您的健康档案。如您需要取消所就诊的医疗机构查看权限，需在“粤健通”微信小程序的“健康档案调阅授权”进行取消操作。另外，您可通过“粤健通”微信小程序等移动服务平台查阅本人的健康档案信息。
          </p>
          <p>
            4.广东健康档案互通系统仅调取、汇聚医疗卫生机构提供的诊疗等信息，不负责对信息内容及时性、完整性、准确性进行审核。如您对健康档案中的信息有疑问，或需要订正资料的，请反馈给接诊医生，由接诊医生在广东健康档案互通系统的
            “问题反馈” 页面登记问题，系统后台将自动分发给数源单位进行处理。您也可以通过 “粤健通”
            微信小程序进行反映，或者咨询健康档案有关事宜。
          </p>
          <p>
            5. 除根据法律法规有关规定及经本人同意外，广东健康档案互通系统的信息不向第三者转移或披露任何收集所得的资料。
          </p>
          <p>
            6.
            广东健康档案互通系统调用的信息，并非医疗机构完整的诊疗记录，不能取代医疗机构存储的任务记录；所展示的健康档案信息仅供参考，不作为任何凭证或依据使用。
          </p>
        </div>
        <div className={styles.agreement}>
          <div className={styles.icon}>
            <HxIcon onClick={() => this.clickAgree()} iconName={isAgree ? 'checkbox-selected' : 'checkbox-normal'} />
          </div>
          <p>阅读并同意</p>
        </div>
        <div className={styles.buttonBox}>
          <Button onClick={() => this.clickConfirm()} className={styles.confirm} type="primary">
            确认
          </Button>
        </div>
      </div>
    );
  }
}

export default Notice;
