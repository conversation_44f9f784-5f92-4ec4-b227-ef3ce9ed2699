@import '~@/styles/mixin.less';

.container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  height: -webkit-fill-available;
  padding: 36px 20px;

  p {
    margin-bottom: 0;
  }

  .content {
    overflow-x: hidden;
    background: #fff;
    padding: 10px;
    padding-bottom: 30px;
    max-height: 1500px;
    overflow-y: scroll;
  }

  .agreement {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    height: 40px;
    margin-top: 40px;
    margin-bottom: 110px;

    .icon {
      width: 30px;
      height: 30px;
      font-size: 30px;
    }

    p {
      margin-top: 6px;
      margin-left: 10px;
      color: #333;
      font-size: 28px;
      span {
        color: @brand-primary;
      }
    }
  }

  .buttonBox {
    position: absolute !important;
    right: 0;
    bottom: 20px;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    .confirm {
      .hxButton();

      margin: 0 auto;
    }
  }
}

:global(.am-button) {
  height: 90px !important;
  font-size: 32px !important;
  line-height: 90px !important;
}
