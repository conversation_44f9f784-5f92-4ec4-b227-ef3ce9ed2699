import React, { useEffect, useState } from 'react';
import { IElectronInvoiceState, Loading } from 'umi';
import styles from './index.less';
import { connect } from 'dva';
import { Button, Toast } from 'antd-mobile-v5';
import AppScheme from '@/utils/AppScheme';
import { isHytPerson, isWechat } from '@/utils/platform';
import { HxSessionStorage } from '@/utils/storage';

const { downLoadImg } = AppScheme;

const ImgView = (props: any) => {
  const {
    location: {
      query: { docId = '', docType = '', patAdmId = '', patNo = '' },
    },
    electronInvoice: { imgViewUrl = '' },
  } = props || {};
  const imgUrl = imgViewUrl || HxSessionStorage.get('imgViewUrl');
  useEffect(() => {}, []);

  // const base64ToBlob = (dataurl) => {
  //   var arr = dataurl.split(','); //分割为数组，分割到第一个逗号
  //   let mime: any = arr[0].match(/:(.*?);/)[1]; //获取分割后的base64前缀中的类型
  //   let bstr: any = window.atob(arr[1]);
  //   let n: any = bstr.length;
  //   let u8arr: any = new Uint8Array(n);
  //   while (n--) {
  //     u8arr[n] = bstr.charCodeAt(n);
  //   }
  //   return new Blob([u8arr], {
  //     type: mime, //文件类型格式
  //   });
  // };
  // const downloadIamge = async (url, fileName) => {
  //   if (!url) return;
  //   let blob: any = '';
  //   if (url.startsWith('data:image/png;base64')) {
  //     blob = base64ToBlob(url);
  //   } else {
  //     blob = await fetch(url).then(
  //       (res) => res.blob(),
  //       (res) => {
  //         console.info('error', res);
  //       },
  //     );
  //   }
  //   const a = document.createElement('a');
  //   a.style.display = 'none';
  //   a.target = '_self';
  //   fileName && (a.download = fileName);
  //   a.href = window.URL.createObjectURL(blob);
  //   document.body.appendChild(a);
  //   a.click();
  //   document.body.removeChild(a);
  // };
  /* 图片下载 */
  const downloadIamge = () => {
    if (isHytPerson()) {
      if (imgUrl) {
        let _obj = {
          docId,
          docType,
          patAdmId,
          patNo,
        };
        let str1 = btoa(JSON.stringify(_obj));
        const urlData = `${API_HXYY_NEW}/cloud/hosplatcustomer/electronicBill/downloadBill?param=${str1}`;
        downLoadImg(`downloadPath=${decodeURIComponent(urlData)}`);
      } else {
        Toast.show('暂时无法下载');
      }
    } else {
      Toast.show('请打开华医通app进行下载');
    }
  };

  return (
    <div className={styles.container}>
      <img id="imgbox" src={imgUrl} alt="" />
      <div className={styles.btnbox}>
        <Button
          className={`${styles.btn} ${isWechat() ? styles.wxbtn : ''}`}
          color={isWechat() ? 'default' : 'primary'}
          onClick={() => (isWechat() ? '' : downloadIamge())}
          disabled={isWechat() ? true : false}
        >
          {isWechat() ? '长按图片保存到手机' : '保存到手机'}
        </Button>
      </div>
    </div>
  );
};

export default connect(({ electronInvoice }: { electronInvoice: IElectronInvoiceState }) => ({
  electronInvoice,
}))(ImgView);
