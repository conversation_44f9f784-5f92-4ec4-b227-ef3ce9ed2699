import mockjs from 'mockjs';

import { commonSuccessResponse } from '../../../mock/util';

/**
 * 电子发票-获取电子发票列表数据
 * @param req
 * @param res
 */
const getInvoicelist = (req: any, res: any) => {
  const response = mockjs.mock({
    ...commonSuccessResponse,
    'data|0-12': [
      {
        'category|1': ['发票标题1', '发票标题2', '发票标题3'],
        createTime: new Date(),
        'totalAmt|1': ['99', '199', '299'],
      },
    ],
  });
  // 模拟请求延迟
  setTimeout(() => {
    return res.status(200).send(response);
  }, 500);
};

/**
 * 电子发票-获取电子发票列表数据
 * @param req
 * @param res
 */
const getInvoiceDetail = (req: any, res: any) => {
  const response = mockjs.mock({
    ...commonSuccessResponse,
    data: {
      ossUrl:
        'https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1596173371777&di=7eca506f3a8b00f3d045bbe3dd90ba15&imgtype=0&src=http%3A%2F%2Fa0.att.hudong.com%2F56%2F12%2F01300000164151121576126282411.jpg',
      'imgPath|1': ['imgPath1', 'imgPath2', 'imgPath3'],
    },
  });
  // 模拟请求延迟
  setTimeout(() => {
    return res.status(200).send(response);
  }, 500);
};

export default {
  'POST /cloud/ehospitalcustomer/invoice/getListAll': getInvoicelist,
  'POST /cloud/ehospitalcustomer/invoice/getInvoiceDetail': getInvoiceDetail,
};
