.main {
  background: #f5f6f7;
  padding-bottom: 128px;
  .tip {
    width: 750px;
    height: 208px;
    padding: 24px;
    color: #fc4553;
    font-weight: 600;
    font-size: 24px;
    font-family: PingFangSC-Semibold, PingFang SC;
    line-height: 40px;
    background: #ffedee;
  }
  .drawback {
    display: flex;
    justify-content: space-between;
    width: 750px;
    height: 96px;
    margin-top: 24px;
    padding: 24px;
    color: #fc4553;
    font-weight: 600;
    font-size: 32px;
    font-family: PingFangSC-Semibold, PingFang SC;
    line-height: 48px;
    background: #fff;
  }
  .guidance {
    display: flex;
    justify-content: space-between;
    width: 750px;
    height: 96px;
    margin-top: 24px;
    padding: 24px;
    background: #fff;
    span:nth-child(1) {
      color: #03081a;
      font-weight: 600;
      font-size: 32px;
      font-family: PingFangSC-Semibold, PingFang SC;
      line-height: 48px;
    }
    span:nth-child(2) {
      width: 136px;
      height: 48px;
      color: #fff;
      font-weight: 600;
      font-size: 24px;
      line-height: 48px;
      text-align: center;
      background: linear-gradient(270deg, #6cebe2 0%, #3ad3c1 100%);
      border-radius: 30px;
    }
  }
  .info {
    width: 750px;
    height: 332px;
    padding: 32px 24px;
    background: #fff;
    .infoTitle {
      height: 44px;
      color: #03081a;
      font-weight: 600;
      font-size: 32px;
      font-family: PingFangSC-Semibold, PingFang SC;
      line-height: 44px;
    }
    .infoDetail {
      width: 702px;
      height: 200px;
      margin-top: 24px;
      padding: 24px;
      color: #989eb4;
      font-weight: 400;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 48px;
      background: #f5f6fa;
      border-radius: 8px;
      .cardData {
        span {
          &:nth-child(2) {
            color: #03081a;
          }
        }
      }
    }
  }
  .detail {
    width: 750px;
    margin-top: 24px;
    padding: 32px 0 0 0;
    background: #fff;
    .detailTitle {
      height: 44px;
      padding-left: 24px;
      color: #03081a;
      font-weight: 600;
      font-size: 32px;
      font-family: PingFangSC-Semibold, PingFang SC;
      line-height: 44px;
    }
    .listItem {
      min-height: 88px;
      padding: 24px;
      .tags {
        display: flex;
        align-items: center;
        margin-top: 18px;
        padding-left: 56px;
        .tag {
          border-radius: 18px;
          font-size: 24px;
          line-height: 32px;
          text-align: center;
          padding: 2px 12px;
          &:not(:first-child) {
            margin-left: 16px;
          }
        }
      }
    }
    .detailList {
      display: flex;
      justify-content: space-between;
      color: #03081a;
      font-weight: 400;
      font-size: 28px;
      line-height: 40px;
      .detailListInfo {
        display: flex;
        width: 600px;
        .detailListicon {
          margin-right: 16px;
        }
        .detailListname {
          width: 500px;
          color: #03081a;
          font-weight: 400;
          font-size: 28px;
          line-height: 40px;
        }
      }

      .detailListprice {
        width: 150px;
        text-align: right;
      }
    }
    .detailAmount {
      height: 80px;
      padding-right: 24px;
      color: #03081a;
      font-weight: 400;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 80px;
      text-align: right;
    }
  }
  .order {
    width: 750px;
    margin-top: 24px;
    padding: 18px 24px 18px 24px;
    background: #fff;
    .orderItem {
      height: 40px;
      margin-top: 18px;
      color: #03081a;
      font-weight: 400;
      font-size: 28px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 40px;
    }
  }
}
.action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 750px;
  height: 104px;
  margin-top: 24px;
  padding: 0 24px;
  background: #fff;
  box-shadow: 0 0 8px 0 #ebedf5;
  position: fixed;
  bottom: 0;
  div:nth-child(1) {
    span:nth-child(1) {
      width: 128px;
      height: 104px;
      color: #03081a;
      font-weight: 400;
      font-size: 32px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 104px;
      text-shadow: 0 0 8px #ebedf5;
    }
    span:nth-child(2) {
      height: 104px;
      color: #fc4553;
      font-weight: 600;
      font-size: 32px;
      line-height: 60px;
    }
    span:nth-child(3) {
      height: 104px;
      color: #fc4553;
      font-weight: 600;
      font-size: 48px;
      line-height: 60px;
    }
  }
  div:nth-child(2) {
    width: 192px;
    height: 72px;
    color: #fff;
    font-weight: 400;
    font-size: 32px;
    font-family: PingFangSC-Regular, PingFang SC;
    line-height: 72px;
    text-align: center;
    background: #3ad3c1;
    border-radius: 40px;
    box-shadow: 0 0 8px 0 #ebedf5;
  }
}
.modalBox {
  .title {
    padding: 32px 0 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    img {
      width: 40px;
      height: 40px;
      margin-right: 6px;
    }

    color: #03081a;
    font-weight: 600;
    font-size: 36px;
    font-family: PingFangSC-Semibold, PingFang SC;
    text-align: center;
  }
  .content {
    height: 336px;
    margin-top: 12px;
    color: #03081a;
    font-weight: 400;
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    line-height: 48px;
    background: #f5f6fa;
    border-radius: 8px;
    text-align: left;
    padding: 12px 12px 0;
  }
  .check {
    height: 40px;
    margin-top: 12px;
    color: #03081a;
    font-weight: 400;
    font-size: 28px;
    font-family: PingFangSC-Regular, PingFang SC;
    line-height: 40px;
  }
  .actions {
    display: flex;
    height: 100px;
    margin-top: 36px;
    background: #fff;
    border: 1px solid #ebedf5;
    border-radius: 0 0 12px 12px;
    div {
      width: 50%;
      height: 100px;
      color: #03081a;
      font-weight: 400;
      font-size: 32px;
      font-family: PingFangSC-Regular, PingFang SC;
      line-height: 100px;
      text-align: center;
      border: 1px solid #ebedf5;
      border-bottom: none;
    }
    div:nth-child(2) {
      color: #3ad3c1;
    }
  }
}

.payMoneyBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 20px;
  padding: 24px 20px;
  padding-left: 20px;
  color: #fc4553;
  font-weight: bolder;
  font-size: 36px;
  background: #fff;

  .addCalc {
    color: #03081a !important;
    font-size: 28px;
  }
}
:global .adm-modal-body .adm-modal-content {
  padding: 0 !important;
}
