import React, { useState, useEffect, FC } from 'react';
import queryString from 'query-string';
import { connect, dispatch } from 'dva';
import { Dispatch } from 'redux';
import { history, Loading } from 'umi';
import { Modal, Toast, Checkbox, Button } from 'antd-mobile-v5';
import icon from '@/assets/已勾选.png';
import icon2 from '@/assets/提示@2x.png';
import { HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import styles from './index.less';
import { detailAuditStatus, refundStatus } from '../dataDictionary';
import { IReceiptContentItem } from '../data';
import { applyRefund2His, receiptDetails } from '../service';

interface PageProps {
  dispatch: Dispatch;
  location: {
    query: {
      receiptId: string;
      payMode: number;
      invPrtAmt: string;
      /* 退号状态 */
      refFlag: string;
      /* 审核状体啊 */
      auditFlag: string;
    };
  };
}
enum PAY_MODE {
  /* 自费 */
  SELF = '0',
  /* 医保 */
  MEDICAL_INSURANCE = '1',
}
enum REFUND_STATUS {
  /** 可退费 */
  REFUNDABLE = '1',
  /** 已退费 */
  REFUND = '2',
  /** 不可退费 */
  UNREFUNDABLE = '3',
}
const App: FC<PageProps> = (props) => {
  const {
    dispatch,
    location: {
      query: { receiptId = '', payMode, invPrtAmt, refFlag, auditFlag },
    },
  } = props;
  const [list, setlist] = useState<Partial<IReceiptContentItem>[]>([]);
  const [visible, setVisible] = useState(false);
  const [isapply, setisapply] = useState(true);
  const [checked, setchecked] = useState(false);
  const [sumNUm, setsumNUm] = useState(0);
  const [loading, setloading] = useState(false);
  const [chooseItem, setChooseItem] = useState<string[]>([]);
  /* 退费金额 */
  const [refundAmount, setrefundAmount] = useState<number>(0);

  const cardInfo = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA);
  const getDetail = async () => {
    try {
      setloading(true);
      const res = await receiptDetails({ receiptId, cardId: cardInfo.cardId });
      if (!res) return;
      const { receiptContentList = [] } = res || {};
      setlist(receiptContentList);
      if (receiptContentList.length) {
        /* 自费 */
        if (payMode.toString() === PAY_MODE.SELF) {
          try {
            /* 自费没有可退费项目 */
            const refundFlag = receiptContentList.some((val) => val.refFlag === REFUND_STATUS.REFUNDABLE);
            if (!refundFlag) {
              setChooseItem([]);
              return;
            }
            const refundAbleList = receiptContentList.filter((item) => item.refFlag === REFUND_STATUS.REFUNDABLE);
            /* 设置初始选中checkBox */
            setChooseItem(refundAbleList.map((item) => item.ordDr));
          } catch (error) {
            console.log('error:', error);
          }
        }
        /* 医保可退费 */
        if (payMode.toString() === PAY_MODE.MEDICAL_INSURANCE && refFlag === REFUND_STATUS.REFUNDABLE) {
          setChooseItem(receiptContentList.map((item) => item.ordDr));
        }
      }
    } catch (error) {
      console.log('error:', error);
    } finally {
      setloading(false);
    }
  };

  const apply = async () => {
    try {
      setloading(true);
      const res = await applyRefund2His({
        cardId: cardInfo.cardId,
        receiptId, // 发票id
        agree: 1, // 是否同意，1=同意，2=不同意
        ordDrs: chooseItem, // 医嘱记录ID
        payMode: Number(payMode), // 支付方式
      });
      if (res.code === '1') {
        setVisible(false);
        Toast.show({
          icon: 'success',
          content: '申请成功',
          duration: 1000,
          afterClose: () => {
            history.go(-1);
          },
        });
        // 更新数据
        // getDetail();
      }
    } catch (error) {
      console.log('error:', error);
    } finally {
      setloading(false);
    }
  };
  const makeApply = () => {
    if (loading) {
      return;
    }
    if (checked) {
      apply();
    } else {
      Toast.show('请阅读知情同意书');
    }
  };

  const auditData = (auditFlag: string) => {
    const item = detailAuditStatus.find((item) => item.value === auditFlag);
    return {
      color: item?.color,
      bgColor: item?.bgColor,
      label: item?.label,
    };
  };

  const refData = (refFlag: string) => {
    const item = refundStatus.find((item) => item.value === refFlag);
    return {
      color: item?.color,
      bgColor: item?.bgColor,
      label: item?.label,
    };
  };

  /* 医保可退费禁止选择 */
  const checkBoxDisabled = () =>
    payMode.toString() === PAY_MODE.MEDICAL_INSURANCE && refFlag === REFUND_STATUS.REFUNDABLE;

  /* 是否展示checkBox */
  const showCheckBox = (item: Partial<IReceiptContentItem>) => {
    /* 医保并且发票可退费 */
    if (payMode.toString() === PAY_MODE.MEDICAL_INSURANCE && refFlag === REFUND_STATUS.REFUNDABLE) {
      return true;
    }
    /* 自费 */
    if (payMode.toString() === PAY_MODE.SELF) {
      const refundFlag = list.some((val) => val.refFlag === REFUND_STATUS.REFUNDABLE);
      if (!refundFlag) return false;
      return item.refFlag === REFUND_STATUS.REFUNDABLE;
    }
  };

  /**
   * 是否展示申请退费按钮
   * @description 发票记录是否有”不可在线退费“、”科室审核中“、”财务审核中“、”已退费“标记，有则不可发起退费
   * @description 发票记录是”审核不通过“标记，则判断是否是医保结算，是则不可退费，不是则可弹出风险告知
   */
  const showFooter = () => {
    if (payMode.toString() === PAY_MODE.MEDICAL_INSURANCE && auditFlag === '4') {
      return false;
    }
    return !['3', '2'].includes(refFlag) || (auditFlag && !['2', '3'].includes(auditFlag));
  };

  const confirmCheck = (val) => {
    /* 医保不可点击 */
    if (payMode.toString() === PAY_MODE.MEDICAL_INSURANCE) return;
    setChooseItem([...val]);
  };

  useEffect(() => {
    getDetail();
  }, []);

  useEffect(() => {
    if (loading) {
      Toast.show({ icon: 'loading', duration: 0 });
    } else {
      Toast.clear();
    }
  }, [loading]);

  useEffect(() => {
    try {
      const refundList = list.filter((item) => chooseItem.includes(item?.ordDr || ''));
      const sum = refundList.reduce((acc, cur) => acc + cur?.sumAmt, 0);
      setrefundAmount(Number(sum.toFixed(2)));
    } catch (error) {
      console.log('error:', error);
    }
  }, [chooseItem]);

  return (
    <div className={styles.main}>
      <div className={styles.info}>
        <div className={styles.infoTitle}>患者信息</div>
        <div className={styles.infoDetail}>
          <div>
            <span
              style={{
                fontWeight: 600,
                color: ' #03081A',
              }}
            >
              {cardInfo.patientName} {`${cardInfo.age}岁`}{' '}
              {cardInfo.gender === '' ? '' : cardInfo.gender === 1 ? '男' : '女'}
            </span>
          </div>
          <div className={styles.cardData}>
            <span>就诊卡号：</span>
            <span>{cardInfo.credNo}</span>
          </div>
          <div className={styles.cardData}>
            <span>登记号：</span>
            <span>{cardInfo.pmiNo}</span>
          </div>
        </div>
      </div>
      <div className={styles.detail}>
        <div className={styles.detailTitle}>四川大学华西医院</div>
        <Checkbox.Group value={chooseItem} onChange={confirmCheck}>
          {list.map((item) => {
            return (
              <div className={styles.listItem} key={item.ordDr}>
                <div className={styles.detailList}>
                  <div className={styles.detailListInfo}>
                    {showCheckBox(item) && (
                      <span className={styles.detailListicon}>
                        <Checkbox value={item.ordDr} style={{ '--icon-size': '20px' }} disabled={checkBoxDisabled()} />
                        {/* <img src={icon} alt="" /> */}
                      </span>
                    )}

                    <span className={styles.detailListname}>
                      {item.itmMastName} x {item.num}{' '}
                    </span>
                  </div>
                  <span className={styles.detailListprice}>￥{item.sumAmt}</span>
                </div>
                <div className={styles.tags} style={{ paddingLeft: showCheckBox(item) ? '28px' : '0px' }}>
                  <div
                    className={styles.tag}
                    style={{
                      color: refData(item.refFlag)?.color,
                      backgroundColor: refData(item.refFlag)?.bgColor,
                    }}
                  >
                    {refData(item.refFlag)?.label}
                  </div>
                  {item.auditFlag && (
                    <div
                      className={styles.tag}
                      style={{
                        color: auditData(item.auditFlag)?.color,
                        backgroundColor: auditData(item.auditFlag)?.bgColor,
                      }}
                    >
                      {auditData(item.auditFlag)?.label}
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </Checkbox.Group>
        <div className={styles.detailAmount}>合计：{invPrtAmt}</div>
      </div>
      {showFooter() && (
        <div className={styles.action}>
          <div>
            <span>可退费：</span>
            <span>¥</span> <span>{refundAmount}</span>
          </div>
          <Button
            color="primary"
            shape="rounded"
            onClick={() => {
              setVisible(true);
            }}
            style={{
              fontSize: '16px',
            }}
            disabled={!chooseItem.length}
          >
            申请退费
          </Button>
        </div>
      )}

      {/* <div className={styles.payMoneyBox}>
        <div className={styles.addCalc}>退费合计:</div>
        <div>￥ {invPrtAmt}</div>
      </div> */}

      <Modal
        visible={visible}
        closeOnMaskClick
        bodyClassName="hxModalBody"
        bodyStyle={{
          padding: '24px 0px 0 0px',
        }}
        afterClose={() => {
          setchecked(false);
        }}
        content={
          <div className={styles.modalBox}>
            <div className={styles.title}>
              {' '}
              <img src={icon2} alt="" /> 退费风险告知书
            </div>
            <div>
              <div className={styles.content}>
                尊敬的患者：
                <br />
                医嘱是医生根据您就诊时的病情开具的，如果您自行申请退费，医嘱会自动停止，未遵医嘱可能存在病情加重的风险，请您慎重考虑。如您确定需要退费，责任自负。
              </div>
              <div className={styles.check}>
                <Checkbox
                  checked={checked}
                  style={{
                    '--icon-size': '18px',
                  }}
                  onChange={(val) => {
                    setchecked(val);
                  }}
                />{' '}
                本人已认真阅读并完全理解和接受
              </div>
            </div>
          </div>
        }
        closeOnAction
        actions={[
          {
            text: '取消',
            key: '1',
            onClick: () => {
              setVisible(false);
            },
          },
          {
            text: '确认',
            key: '2',
            className: 'primary',
            onClick: makeApply,
          },
        ]}
      />
    </div>
  );
};
export default connect(({ loading }: { loading: Loading }) => ({
  loading: loading.effects['electronInvoice/getInvoicelist'],
}))(App);
