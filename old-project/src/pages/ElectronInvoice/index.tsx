import React from 'react';
import { HxRedirect, getModuleHomeUrl } from '@/utils/interceptor';
import { ModuleEnum } from '@/utils/enum';

// 是否需要选卡
const needChooseCard = true;

// 重定向地址
const redirectUrl = getModuleHomeUrl(ModuleEnum.MODULE_ELECTRON_INVOICE);

const defaultParams = {
  pathname: needChooseCard ? getModuleHomeUrl(ModuleEnum.MODULE_PATIENT_CARD) : redirectUrl,
  search: needChooseCard ? `redirect=${redirectUrl}` : '',
};

export default () => {
  return <HxRedirect params={defaultParams} />;
};
