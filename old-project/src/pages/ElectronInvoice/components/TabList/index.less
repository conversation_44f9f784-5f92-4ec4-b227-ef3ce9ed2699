@import '~@/styles/mixin.less';
.container {
  min-height: 100vh;
  background-color: #f5f6fa;
  .tabhead {
    background-color: #fff;
    width: 100%;
    display: flex;
    height: 88px;
    padding-left: 40px;
    align-items: center;
    margin: 24px 0;
    .tabItem {
      margin-right: 80px;
      height: 100%;
      span {
        display: block;
        height: 100%;
        border-radius: 4px;
        line-height: 88px;
        text-align: center;
      }
      .activeSpan {
        border-bottom: 8px solid #6cebe2;
      }
    }
  }
}
.invoiceListItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 127px;
  padding: 0 30px;
  background-color: #fff;
  .hxOnepxT();

  .itemLeft {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    span:nth-child(1) {
      margin-bottom: 10px;
      color: #333;
      font-size: 28px;
    }

    span:nth-child(2) {
      color: #999;
      font-size: 26px;
    }
  }

  .itemRight {
    display: flex;
    align-items: center;
    color: #333;
    font-size: 30px;

    .arrowIcon {
      width: 25px;
      height: 35px;
      margin-left: 17px;
    }
  }
}
