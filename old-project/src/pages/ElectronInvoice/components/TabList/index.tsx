import React, { FC, useEffect, useState } from 'react';
import { connect, Dispatch } from 'umi';
import styles from './index.less';
import { ListView } from 'antd-mobile';
import moment from 'moment';
import { HxIcon } from '@/components';
import Blank from '../Blank';
import { HxSessionStorage } from '@/utils/storage';

interface IProps {
  dispatch: Dispatch;
  electronInvoice: any;
  // location: any;
  invoiceList: any;
  goInvoiceDetail: (p) => void;
  fetchData?: () => void;
  fetchData3?: () => void;
  otherTypeNum: string;
}

const TabList: FC<IProps> = (props) => {
  const {
    dispatch,
    electronInvoice,
    // location: { query = {} },
    invoiceList = [],
    goInvoiceDetail = () => {},
    fetchData = () => {},
    fetchData3 = () => {},
    otherTypeNum = '1',
  } = props;

  const [tabActive, setTabActive] = useState(1);

  useEffect(() => {
    let tabFlag = HxSessionStorage.get('tmpFlag');
    if (tabFlag) {
      return;
    } else {
      setTabActive(+otherTypeNum);
      if (otherTypeNum === '2') {
        fetchData3();
      }
    }
    return () => {
      HxSessionStorage.set('tmpFlag', false);
    };
  }, [otherTypeNum]);

  /* tab切换 */
  const tabChang = (type: number) => {
    HxSessionStorage.set('tmpFlag', true);
    switch (type) {
      case 1: // 电子发票
        setTabActive(type);
        fetchData();
        break;

      case 2: // 其他票据
        setTabActive(type);
        fetchData3();
        break;

      default:
        break;
    }
  };

  const ds = new ListView.DataSource({ rowHasChanged: (r1: any, r2: any) => r1 !== r2 });

  return (
    <>
      <div className={styles.container}>
        <div className={styles.tabhead}>
          <div className={styles.tabItem} onClick={() => tabChang(1)}>
            <span className={`${tabActive === 1 ? styles.activeSpan : ''}`}>电子发票</span>
          </div>
          <div className={styles.tabItem} onClick={() => tabChang(2)}>
            <span className={`${tabActive === 2 ? styles.activeSpan : ''}`}>其他票据</span>
          </div>
        </div>
        {invoiceList.length === 0 ? (
          <Blank />
        ) : tabActive === 1 ? (
          <ListView
            dataSource={ds.cloneWithRows(invoiceList)}
            renderRow={(rowData) => (
              <div className={styles.invoiceListItem} onClick={() => goInvoiceDetail({ ...rowData, detailType: 'dz' })}>
                <div className={styles.itemLeft}>
                  <span>{rowData.category}</span>
                  <span>{moment(rowData.createTime).format('YYYY-MM-DD')}</span>
                </div>
                <div className={styles.itemRight}>
                  {rowData.totalAmt && `￥ ${rowData.totalAmt}`}
                  <HxIcon iconName="arrow-right" className={styles.arrowIcon} />
                </div>
              </div>
            )}
            useBodyScroll
            initialListSize={10}
            onEndReachedThreshold={200}
          />
        ) : (
          <ListView
            dataSource={ds.cloneWithRows(invoiceList)}
            renderRow={(rowData) => (
              <div className={styles.invoiceListItem} onClick={() => goInvoiceDetail({ ...rowData, detailType: 'zy' })}>
                <div className={styles.itemLeft}>
                  <span>{rowData.docName}</span>
                  <span>{moment(rowData.disChargeDate).format('YYYY-MM-DD')}</span>
                </div>
                <div className={styles.itemRight}>
                  <HxIcon iconName="arrow-right" className={styles.arrowIcon} />
                </div>
              </div>
            )}
            useBodyScroll
            initialListSize={10}
            onEndReachedThreshold={200}
          />
        )}
      </div>
    </>
  );
};

export default connect(({ electronInvoice }: { electronInvoice: any }) => ({
  electronInvoice,
}))(TabList);
