import { Reducer } from 'redux';
import { Effect } from 'umi';
import { createAction } from '@/utils/common';
import {
  getInvoicelist,
  getInvoiceDetail,
  getInvoiceSend,
  getTips,
  getHXInvoiceDetail,
  billList,
  billDetail,
  getHtmlInvoiceDetail,
  receiptList,
  receiptDetails,
  applyRefund2His,
} from './service';
import { InvoiceListItem } from './data.d';

export interface IElectronInvoiceState {
  invoiceList: InvoiceListItem[];
  invoiceDetail: IInvoiceDetail;
  imgViewUrl: string;
}
export interface IInvoiceDetail {
  ossUrl?: string;
  imgKey?: string;
}
export interface INvoiceListModel {
  namespace: 'electronInvoice';
  state: IElectronInvoiceState;
  effects: {
    getInvoicelist: Effect;
    getInvoiceDetail: Effect;
    getInvoiceSend: Effect;
    getTips: Effect;
    getHXInvoiceDetail: Effect;
    billList: Effect;
    billDetail: Effect;
    getHtmlInvoiceDetail: Effect;
    getReceiptList: Effect;
    getreceiptDetails: Effect;
    getapplyRefund2His: Effect;
  };
  reducers: {
    updateState: Reducer<IElectronInvoiceState>;
  };
}

const invoiceListModel: INvoiceListModel = {
  namespace: 'electronInvoice',
  state: {
    invoiceList: [],
    invoiceDetail: {},
    imgViewUrl: '',
  },
  effects: {
    *getInvoicelist({ payload }, { call, put }) {
      const res = yield call(getInvoicelist, payload);
      yield put(createAction('updateState')({ invoiceList: res.items }));
    },
    *getInvoiceDetail({ payload }, { call, put }) {
      const res = yield call(getInvoiceDetail, payload);
      yield put(createAction('updateState')({ invoiceDetail: res }));
    },
    *getHXInvoiceDetail({ payload, callback }, { call }) {
      const res = yield call(getHXInvoiceDetail, payload);
      // yield put(createAction('updateState')({ invoiceDetail: res }));
      callback(res);
    },
    *getInvoiceSend({ payload, callback }, { call, put }) {
      const res = yield call(getInvoiceSend, payload);
      yield put(createAction('updateState')({ invoiceSend: res }));
      callback();
    },
    *getTips({ payload, callback }, { call, put }) {
      const res = yield call(getTips, payload);
      yield put(createAction('updateState')({ tips: res }));
      callback(res);
    },
    *billList({ payload, callback }, { call, put }) {
      const res = yield call(billList, payload);
      yield put(createAction('updateState')({ invoiceList: res }));
      callback(res);
    },
    *billDetail({ payload, callback }, { call, put }) {
      const res = yield call(billDetail, payload);
      yield put(createAction('updateState')({ imgViewUrl: res }));
      callback(res);
    },
    *getHtmlInvoiceDetail({ payload, callback }, { call }) {
      const res = yield call(getHtmlInvoiceDetail, payload);
      // yield put(createAction('updateState')({ invoiceDetail: res }));
      callback(res);
    },
    *getReceiptList({ payload, callback }, { call }) {
      const res = yield call(receiptList, payload);
      callback(res);
    },
    *getreceiptDetails({ payload, callback }, { call }) {
      const res = yield call(receiptDetails, payload);
      callback(res);
    },
    *getapplyRefund2His({ payload, callback }, { call }) {
      const res = yield call(applyRefund2His, payload);
      callback(res);
    },
  },
  reducers: {
    updateState(state: any, { payload }: any) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
export default invoiceListModel;
