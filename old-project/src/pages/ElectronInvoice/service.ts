import request from '@/utils/request';
import { getOrganCode } from '@/utils/parameter';

let node = '/cloud';
if (APP_ENV === 'prod') {
  node = getOrganCode() === 'HID0101' || getOrganCode() === 'HYT' ? `${API_HXYY}/cloud` : `${API_BASE}/cloud`;
}

const handleOrganCode = (data: any = {}) => {
  const { hospitalCode = '' } = data;
  if (getOrganCode() === 'HYT' && hospitalCode === 'HYT') {
    return {
      ...data,
      hospitalCode: 'HID0101',
    };
  }
  return data;
};

// 获取电子发票列表数据 ///hosplatcustomer/call/medicalaffairs/ele-bill/picture/query
export const getInvoicelist = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/ele-bill/list/query`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

// 获取电子发票详情（正安县中医院医院，目前只上了这两个医院）
export const getInvoiceDetail = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/ele-bill/picture/query`, {
    method: 'POST',
    data: handleOrganCode(data),
  });
// 获取电子发票详情（华西医院）
export const getHXInvoiceDetail = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/ele-bill/url/query`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

// 发送电子邮件
export const getInvoiceSend = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/ele-bill/mail/send`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

// 获取提示
export const getTips = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/tipConfig`, {
    method: 'POST',
    data: handleOrganCode(data),
  });
// 住院票据列表
export const billList = async (data: object): Promise<any> =>
  request('/cloud/medicalService/electronicBill/hospitalization/billList', {
    method: 'POST',
    data,
    prefix: API_HXYY_NEW,
  });
// 住院票据详情
export const billDetail = async (data: object): Promise<any> =>
  request('/cloud/medicalService/electronicBill/hospitalization/billDetail', {
    method: 'POST',
    data,
    prefix: API_HXYY_NEW,
  });
// 获取电子发票详情（三方H5页面）
export const getHtmlInvoiceDetail = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/medicalaffairs/ele-bill/url/query`, {
    method: 'POST',
    data: handleOrganCode(data),
  });
// 退费列表
export const receiptList = async (data: any): Promise<any> => {
  return request('/cloud/medicalService/refund/receiptList', {
    method: 'POST',
    data,
    prefix: API_HXYY_NEW,
  });
};
// 查询详情
export const receiptDetails = async (data: any): Promise<any> => {
  return request('/cloud/medicalService/refund/receiptDetails', {
    method: 'POST',
    data,
    prefix: API_HXYY_NEW,
  });
};
// 申请退费
export const applyRefund2His = async (data: any): Promise<any> => {
  return request('/cloud/medicalService/refund/applyRefund2His', {
    method: 'POST',
    data: { ...data, showOriginData: true },
    prefix: API_HXYY_NEW,
  });
};
// 查询是否能够显示退费入口
export const validateAccount = async () => {
  return request('/cloud/hosplatcustomer/refundApi/validateAccount', {
    method: 'POST',
    data: { skipError: true, showOriginData: true },
    prefix: API_HXYY_NEW,
  });
};
