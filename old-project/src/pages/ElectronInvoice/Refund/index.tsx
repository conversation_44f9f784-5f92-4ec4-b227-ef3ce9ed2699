import React, { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import { history, Loading, IElectronInvoiceState } from 'umi';
import { DatePicker, ListView, Toast, Tabs } from 'antd-mobile';
import { Toast as ToastV5 } from 'antd-mobile-v5';
import { Dispatch } from 'redux';
import queryString from 'query-string';
import { HxIcon, HxIndicator, HxEmpty } from '@/components';
import { HxParameter, getOrganCode, getToken, getChannelCode } from '@/utils/parameter';
import moment from 'moment';
import icon3 from '@/assets/日历icon.png';
import { InvoiceListItem } from '../data.d';
import styles from './index.less';
import { receiptList } from '../service';
import Blank from '../components/Blank';
import { auditStatus, refundStatus } from '../dataDictionary';

const userAvatar = require('@/assets/user-avatar.png');

const deadline = 1000 * 60 * 60 * 24 * 365; // 验证时间周期,以前写的1000 * 60 * 60 * 24 * 30 * 12
const sortUpDate = (a, b) => {
  return Date.parse(b.receiptDate) - Date.parse(a.receiptDate);
};
export interface IProps {
  dispatch: Dispatch;
  loading?: boolean;
  invoiceList: InvoiceListItem[];
  location: {
    search: string;
    query: any;
  };
}

interface IState {
  startDate: any;
  endDate: any;
  suits: any;
  userInfo: any;
  loading: boolean;
  cardId: string;
  age: string;
  invoiceList: any;
}

class Home extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      startDate: '',
      endDate: '',
      userInfo: {},
      age: '',
      cardId: '',
      invoiceList: [],
      loading: false,
    };
  }

  componentDidMount() {
    const nowTimeStamp = Date.now();
    const now = new Date(nowTimeStamp);
    const { organCode }: any = HxParameter;
    const {
      location: { search = '' },
    } = this.props;
    const { data = '' }: any = queryString.parse(search) || {};
    const { cardId } = JSON.parse(data);
    this.setState({ cardId });
    if (!data) {
      // app内选就诊卡跳转过来参数放在 data里的，微信消息推送跳转过来参数没有包在data里
      this.fetchCardDetail(cardId); // 根据就诊卡ID查询就诊卡详情
    } else {
      const { pmi, patientName = '', credNo = '', gender = '', pmiNo = '', age = '' } = JSON.parse(data) || {};
      this.setState({ userInfo: { patientName, credNo, gender, pmiNo, age } });
      const lastDay = new Date(nowTimeStamp - 24 * 60 * 60 * 365 * 1000);
      this.setState({ startDate: lastDay, endDate: now }, () => this.fetchData());
    }
  }

  fetchCardDetail = (cardId: string) => {
    // 微信消息推送跳转过来获取就诊卡信息
    const { channelCode, organCode = '' } = HxParameter;
    const nowTimeStamp = Date.now();
    const now = new Date(nowTimeStamp);
    this.props.dispatch({
      type: 'patientCard/cardInfo',
      payload: {
        cardId,
        channelCode,
      },
      callback: (cardInfoRes: any) => {
        const { patientName, credNo, gender, age } = cardInfoRes;
        this.setState({ userInfo: { patientName, credNo, gender, age } });
        const lastDay = new Date(nowTimeStamp - 24 * 60 * 60 * 365 * 1000);
        this.setState({ startDate: lastDay, endDate: now }, () => this.fetchData());
      },
    });
  };

  fetchData = async () => {
    const { startDate, endDate, cardId } = this.state;
    const { channelCode, organCode } = HxParameter;
    const query = {
      organCode,
      channelCode,
      startDate: moment(startDate).format('YYYY-MM-DD'),
      endDate: moment(endDate).format('YYYY-MM-DD'),
      cardId,
    };

    try {
      this.setState({ loading: true });
      ToastV5.show({ icon: 'loading', duration: 0 });
      const res = await receiptList(query);
      if (res) {
        const { receiptList } = res;
        const _receiptList = receiptList.sort(sortUpDate);
        this.setState({
          invoiceList: _receiptList,
        });
      }
    } catch (error) {
      console.log('error:', error);
    } finally {
      ToastV5.clear();
      this.setState({ loading: false });
    }

    // this.props.dispatch({
    //   type: 'electronInvoice/getReceiptList',
    //   payload: query,
    //   callback: (res) => {
    //     if (res) {
    //       const { receiptList } = res;
    //       const _receiptList = receiptList.sort(sortUpDate);
    //       this.setState({
    //         invoiceList: _receiptList,
    //       });
    //     }
    //   },
    // });
  };

  // 选择开始时间
  changeStartTime = (startDate: any) => {
    const { endDate } = this.state;
    if (endDate - startDate > deadline) {
      Toast.info('请选择一年内的时间段', 2);
    } else if (moment(startDate).unix() * 1000 > moment().unix() * 1000) {
      Toast.info('开始日期不能大于今天', 2);
    } else {
      this.setState(
        {
          startDate,
          endDate: new Date(moment(startDate).unix() * 1000 + 24 * 60 * 60 * 30 * 1000),
        },
        () => {
          this.fetchData();
        },
      );
    }
  };

  // 获取提示

  // 选择结束时间
  changeEndTime = (endDate: any) => {
    const { startDate } = this.state;
    if (endDate - startDate > deadline) {
      Toast.info('请选择一年内的时间段', 2);
    } else {
      this.setState({ endDate }, () => {
        this.fetchData();
      });
    }
  };

  // 跳转至详情页
  goInvoiceDetail = (rowData: any) => {
    history.push({
      pathname: '/electroninvoice/RefundDetail',
      query: {
        receiptId: rowData.receiptId,
        payMode: rowData.payMode,
        invPrtAmt: rowData.invPrtAmt,
        refFlag: rowData.refFlag,
        auditFlag: rowData.auditFlag,
      },
    });
  };

  auditData = (auditFlag: string) => {
    const item = auditStatus.find((item) => item.value === auditFlag);
    return {
      color: item?.color,
      bgColor: item?.bgColor,
      label: item?.label,
    };
  };

  refData = (refFlag: string) => {
    const item = refundStatus.find((item) => item.value === refFlag);
    return {
      color: item?.color,
      bgColor: item?.bgColor,
      label: item?.label,
    };
  };

  render() {
    const {
      startDate,
      endDate,
      userInfo: { patientName, credNo, gender, age },
      loading = false,
    } = this.state;
    const ds = new ListView.DataSource({ rowHasChanged: (r1: any, r2: any) => r1 !== r2 });
    const { invoiceList = [] } = this.state;
    const avatar = '';
    return (
      <div className={styles.container}>
        <div className={styles.personalInfo}>
          <div className={styles.cardInfo}>
            <div className={styles.userDetail}>
              <img src={avatar === '' ? userAvatar : avatar} alt="" />
              <div className={styles.personalInfoRight}>
                <span>
                  {patientName} {gender === '' ? '' : gender === 1 ? '男' : '女'} {`${age}岁`}
                </span>
                <span>就诊卡号：{`${credNo?.slice(0, 4)} **** ${credNo?.slice(credNo.length - 4)}`}</span>
              </div>
            </div>
            <div className={styles.tip}>
              1.目前只支持通过华医通平台支付的华西门诊药房药品医嘱线上退费，其它医嘱项目请前往线下窗口进行退费。 <br />
              2.已发药的医嘱不可退费。
            </div>
          </div>
        </div>

        <div className={styles.dateContainer}>
          <div className={styles.selectDate}>
            <DatePicker
              mode="date"
              value={startDate}
              onChange={(selectDate) => this.changeStartTime(selectDate)}
              // maxDate={lastDay}
            >
              <div className={styles.datePickerContent}>
                <img src={icon3} alt="" className={styles.dateIcon} />
                {startDate ? moment(startDate).format('YYYY-MM-DD') : '请选择'}
              </div>
            </DatePicker>
          </div>
          <div>至</div>
          <div className={styles.selectDate}>
            <DatePicker
              mode="date"
              value={endDate}
              // minDate={startDate}
              onChange={(selectDate) => this.changeEndTime(selectDate)}
              // maxDate={maxDate}
            >
              <div className={styles.datePickerContent}>
                <img src={icon3} alt="" className={styles.dateIcon} />
                {endDate ? moment(endDate).format('YYYY-MM-DD') : '请选择'}
              </div>
            </DatePicker>
          </div>
        </div>
        {invoiceList.length && !loading ? (
          <div>
            {invoiceList.map((rowData) => (
              <div
                className={styles.invoiceListItem}
                onClick={() => this.goInvoiceDetail(rowData)}
                key={rowData.receiptId}
              >
                <div className={styles.title}>
                  <span>{rowData.deptName}</span>
                </div>
                <div className={styles.info}>
                  <div className={styles.infoItem}>
                    <span>实付金额：</span>
                    <span>¥ {rowData.invPrtAmt}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span>支付时间：</span>
                    <span>{rowData.receiptDate}</span>
                  </div>
                  {rowData.apply && rowData.applyStatus > 0 ? (
                    <div className={styles.infoItem}>
                      <span>订单状态：</span>
                      <span
                        style={{
                          color: '#FC4553',
                        }}
                      >
                        {rowData.applyStatusStr}
                      </span>
                    </div>
                  ) : null}
                  {rowData.invPrtAmtRE ? (
                    <div className={styles.infoItem}>
                      <span> {rowData.applyStatus === 1 ? '待退金额：' : '已退金额：'} </span>
                      <span
                        style={{
                          color: '#FC4553',
                        }}
                      >
                        {rowData.invPrtAmtRE}
                      </span>
                    </div>
                  ) : null}
                  <div />
                </div>
                <div className={styles.tagWrap}>
                  <div className={styles.tags}>
                    <div
                      className={styles.tag}
                      style={{
                        color: this.refData(rowData.refFlag)?.color,
                        backgroundColor: this.refData(rowData.refFlag)?.bgColor,
                      }}
                    >
                      {this.refData(rowData.refFlag)?.label}
                    </div>
                    {rowData.auditFlag && (
                      <div
                        className={styles.tag}
                        style={{
                          color: this.auditData(rowData.auditFlag)?.color,
                          backgroundColor: this.auditData(rowData.auditFlag)?.bgColor,
                        }}
                      >
                        {this.auditData(rowData.auditFlag)?.label}
                      </div>
                    )}
                  </div>
                  <HxIcon iconName="arrow-right" className={styles.arrowIcon} />
                </div>
              </div>
            ))}
          </div>
        ) : loading ? null : (
          <div style={{ paddingTop: '40px' }}>
            <HxEmpty canRefresh={false} isNewImg emptyMsg="暂无数据" />
          </div>
        )}
      </div>
    );
  }
}

export default connect(
  ({ electronInvoice, loading }: { electronInvoice: IElectronInvoiceState; loading: Loading }) => ({
    loading: loading.effects['electronInvoice/getInvoicelist'],
    electronInvoice,
  }),
)(Home);
