@import '~@/styles/mixin.less';

.container {
  height: 100%;
  font-family: PingFang SC;
  background-color: #f5f6fa;

  // 顶部提示
  .prompt {
    box-sizing: border-box;
    height: 240px;
    margin-bottom: 10px;
    padding: 20px 30px;
    color: #f47f1b;
    font-size: 24px;
    background-color: #fdf2c3;

    p {
      line-height: 36px;
    }

    p:first-child {
      margin-bottom: 10px;
    }
  }

  // 个人信息栏
  .personalInfo {
    display: flex;
    justify-content: center;
    width: 750px;
    height: 320px;
    background: linear-gradient(0deg, #f5f6fa, #3ad3c1);
    .cardInfo {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 702px;
      height: 362px;
      margin-top: 24px;
      padding: 32px 24px;
      background: #fff;
      border-radius: 16px;
      .tip {
        width: 654px;
        height: 168px;
        margin-top: 24px;
        padding: 24px;
        color: #ffb72f;
        font-weight: 400;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        line-height: 40px;
        background: #fff8eb;
      }
      .userDetail {
        display: flex;
        width: 654px;

        img {
          width: 90px;
          height: 90px;
          margin: 0 30px;
        }
        .personalInfoRight {
          display: flex;
          flex-direction: column;
          align-items: flex-start;

          span:nth-child(1) {
            margin-bottom: 11px;
            color: #03081a;
            font-weight: bold;
            font-size: 30px;
          }

          span:nth-child(2) {
            color: #03081a;
            font-size: 26px;
          }
        }
      }
    }
  }

  // 选择日期栏
  .dateContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 702px;
    height: 90px;
    margin-top: 90px;
    margin-bottom: 24px;
    margin-left: 24px;
    padding: 0 88px;
    font-size: 26px;
    background: #fff;
    background-color: #fff;
    border-radius: 16px;
    .selectDate {
      .datePickerContent {
        display: flex;
        align-items: center;
        height: 44px;
        color: #3ad3c1;
        font-weight: 600;
        font-size: 32px;
        font-family: PingFangSC-Semibold, PingFang SC;
        line-height: 44px;
        .dateIcon {
          width: 36px;
          height: 36px;
          margin-right: 12px;
        }
      }
    }
  }

  // 无电子发票
  .noInvoice {
    margin-top: 270px;
    color: #ccc;
    font-size: 26px;
    text-align: center;
  }

  // 发票列表item
  .invoiceListItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 702px;
    margin-top: 24px;
    margin-left: 24px;
    padding: 24px 24px 0;
    background: #fff;
    border-radius: 16px;
    .tagWrap {
      display: flex;
      align-items: center;
      height: 88px;
      width: 100%;
      .tags {
        flex: 1;
        display: flex;
        align-items: center;
        .tag {
          border-radius: 18px;
          font-size: 24px;
          line-height: 32px;
          text-align: center;
          padding: 2px 12px;
          &:not(:first-child) {
            margin-left: 16px;
          }
        }
      }
      .arrowIcon {
        width: 24px;
        height: 24px;
        flex-shrink: 0;
      }
    }
    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 654px;
      height: 44px;
      color: #03081a;
      font-weight: 600;
      font-size: 32px;
      font-family: PingFangSC-Semibold, PingFang SC;
      line-height: 44px;
    }
    .info {
      width: 654px;
      padding: 24px 0;
      font-weight: 400;
      font-size: 28px;
      line-height: 40px;
      border-bottom: 2px solid #f6f8ff;
      .infoItem {
        &:not(:first-child) {
          margin-top: 16px;
        }
        span:nth-child(1) {
          color: #989eb4;
        }
        span:nth-child(2) {
          color: #03081a;
        }
      }
    }
  }
}
