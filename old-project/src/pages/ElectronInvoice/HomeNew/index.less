@import '~@/styles/mixin.less';

.container {
  height: 100%;
  font-family: PingFang SC;
  background-color: #f5f6f7;

  // 顶部提示
  .prompt {
    box-sizing: border-box;
    height: 240px;
    margin-bottom: 10px;
    padding: 20px 30px;
    color: #f47f1b;
    font-size: 24px;
    background-color: #fdf2c3;

    p {
      line-height: 36px;
    }

    p:first-child {
      margin-bottom: 10px;
    }
  }

  // 个人信息栏
  .personalInfo {
    display: flex;
    align-items: center;
    height: 130px;
    background-color: #fff;
    .hxOnepxB();

    img {
      width: 90px;
      height: 90px;
      margin: 0 30px;
    }

    .personalInfoRight {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      span:nth-child(1) {
        margin-bottom: 11px;
        color: #333;
        font-weight: bold;
        font-size: 30px;
      }

      span:nth-child(2) {
        color: #666;
        font-size: 26px;
      }
    }
  }

  // 选择日期栏
  .dateContainer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 96px;
    margin-bottom: 20px;
    padding: 0 88px;
    font-size: 26px;
    background-color: #fff;

    .selectDate {
      .datePickerContent {
        display: flex;
        align-items: center;
        color: #32b9aa;

        .dateIcon {
          width: 35px;
          height: 35px;
          margin-right: 4px;
        }
      }
    }
  }

  // 无电子发票
  .noInvoice {
    margin-top: 270px;
    color: #ccc;
    font-size: 26px;
    text-align: center;
  }

  // 发票列表item
  .invoiceListItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 127px;
    padding: 0 30px;
    background-color: #fff;
    .hxOnepxT();

    .itemLeft {
      display: flex;
      flex-direction: column;
      align-items: flex-start;

      span:nth-child(1) {
        margin-bottom: 10px;
        color: #333;
        font-size: 28px;
      }

      span:nth-child(2) {
        color: #999;
        font-size: 26px;
      }
    }

    .itemRight {
      display: flex;
      align-items: center;
      color: #333;
      font-size: 30px;

      .arrowIcon {
        width: 25px;
        height: 35px;
        margin-left: 17px;
      }
    }
  }
}
