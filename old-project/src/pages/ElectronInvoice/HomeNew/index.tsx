import React, { PureComponent, Fragment } from 'react';
import { connect } from 'dva';
import { history, Loading, IElectronInvoiceState } from 'umi';
import { DatePicker, ListView, Toast, Tabs } from 'antd-mobile';
import { Dispatch } from 'redux';
import queryString from 'query-string';
import { HxIcon, HxIndicator } from '@/components';
import { HxParameter, getOrganCode, getToken, getChannelCode } from '@/utils/parameter';
import moment from 'moment';
import { InvoiceListItem } from '../data.d';
import styles from './index.less';
import Blank from '../components/Blank';
import TabList from '../components/TabList';

const userAvatar = require('@/assets/user-avatar.png');

const deadline = 1000 * 60 * 60 * 24 * 365; // 验证时间周期,以前写的1000 * 60 * 60 * 24 * 30 * 12
// const maxDate = new Date(nowTimeStamp + 1e7);
const tabs = [
  { title: '门诊', type: 1 },
  { title: '住院', type: 2 },
];
export interface IProps {
  dispatch: Dispatch;
  loading?: boolean;
  invoiceList: InvoiceListItem[];
  tips?: any;
  location: {
    search: string;
    query: any;
  };
}

interface IState {
  startDate: any;
  endDate: any;
  tips: any;
  suits: any;
  type: number;
  patientId: string;
  userInfo: any;
}

class Home extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      startDate: '',
      endDate: '',
      tips: {},
      suits: {},
      patientId: '',
      userInfo: {},
      type: 1, // 1门诊 2住院
    };
  }

  componentDidMount() {
    const nowTimeStamp = Date.now();
    const now = new Date(nowTimeStamp);
    const { organCode }: any = HxParameter;
    const {
      location: { search = '' },
    } = this.props;
    const { data = '', cardId, typeNum = '1', otherTypeNum = '1' }: any = queryString.parse(search) || {}; //typeNum设置一级tab,otherTypeNum设置住院tab下的二级tab
    if (!data) {
      // app内选就诊卡跳转过来参数放在 data里的，微信消息推送跳转过来参数没有包在data里
      this.fetchCardDetail(cardId, typeNum, otherTypeNum); // 根据就诊卡ID查询就诊卡详情
    } else {
      const { pmi, patientName = '', credNo = '', gender = '', pmiNo = '' } = JSON.parse(data) || {};
      this.setState({ userInfo: { patientName, credNo, gender, pmiNo } });
      // 华西医院默认查询一年数据，正安县医院默认查询两天
      const lastDay =
        organCode === 'HID0101'
          ? new Date(nowTimeStamp - 24 * 60 * 60 * 365 * 1000)
          : ['HXZYZAXZYY0101'].includes(organCode)
          ? new Date(nowTimeStamp - 24 * 60 * 60 * 1000)
          : new Date(nowTimeStamp - 24 * 60 * 60 * 365 * 1000);
      this.setState({ patientId: pmi, startDate: lastDay, endDate: now }, () => this.fetchData());
    }
    this.getTips();
  }

  fetchCardDetail = (cardId: string, typeNum?: any, otherTypeNum?: any) => {
    // 微信消息推送跳转过来获取就诊卡信息(app住院tab下的其他票据推送也走这里)
    const { channelCode, organCode = '' } = HxParameter;
    const nowTimeStamp = Date.now();
    const now = new Date(nowTimeStamp);
    this.props.dispatch({
      type: 'patientCard/cardInfo',
      payload: {
        cardId,
        channelCode,
      },
      callback: (cardInfoRes: any) => {
        console.log('cardInfoRes', cardInfoRes);
        const { patientName, credNo, gender, organPmino } = cardInfoRes;
        this.setState({ userInfo: { patientName, credNo, gender, pmiNo: organPmino } });
        const lastDay =
          organCode === 'HID0101'
            ? new Date(nowTimeStamp - 24 * 60 * 60 * 365 * 1000)
            : ['HXZYZAXZYY0101'].includes(organCode)
            ? new Date(nowTimeStamp - 24 * 60 * 60 * 1000)
            : new Date(nowTimeStamp - 24 * 60 * 60 * 365 * 1000);
        if (['HID0102'].includes(organCode)) {
          this.setState({ patientId: cardInfoRes?.organPmi, startDate: lastDay, endDate: now }, () => this.fetchData());
          return;
        }
        //指定为住院票据时不请求电子票据
        this.setState({ type: +typeNum, patientId: cardInfoRes?.organPmi, startDate: lastDay, endDate: now }, () => {
          if (typeNum === '2' && otherTypeNum === '2') {
            //住院下的其他票据
            this.fetchData3(organPmino);
          } else {
            this.fetchData();
          }
        });
      },
    });
  };

  fetchData = () => {
    const { startDate, endDate, type, patientId } = this.state;
    const { channelCode, organCode } = HxParameter;
    let query = {
      hospitalCode: organCode,
      channelCode,
      patientId, // 50105301,
      startDate: moment(startDate).format('YYYY-MM-DD'),
      endDate: moment(endDate).format('YYYY-MM-DD'),
    };
    if (organCode === 'HID0101') {
      // 正安县中医院没有这个参数，还是采用以前的逻辑
      query = Object.assign(query, { type });
    }
    this.props.dispatch({
      type: 'electronInvoice/getInvoicelist',
      payload: query,
    });
  };

  onTabChange = (tab) => {
    console.log(tab);
    this.setState({ type: tab.type }, () => this.fetchData());
  };

  /* 新版电子票据列表获取 */
  fetchData2 = () => {
    this.setState({ type: 2 }, () => this.fetchData());
  };

  /* 新版住院票据列表获取 */
  fetchData3 = (organPmino?: string) => {
    // 20240102 产品王 要求暂时屏蔽电子票据中其他票据的请求
    const {
      startDate,
      endDate,
      userInfo: { pmiNo },
    } = this.state;
    const query = {
      patNo: pmiNo || organPmino,
      patAdmId: '',
      startDate: moment(startDate).format('YYYY-MM-DD'),
      endDate: moment(endDate).format('YYYY-MM-DD'),
    };
    pmiNo &&
      this.props.dispatch({
        type: 'electronInvoice/billList',
        payload: query,
      });
  };

  // 选择开始时间
  changeStartTime = (startDate: any) => {
    const { endDate } = this.state;
    if (endDate - startDate > deadline) {
      Toast.info('请选择一年内的时间段', 2);
    } else if (moment(startDate).unix() * 1000 > moment().unix() * 1000) {
      Toast.info('开始日期不能大于今天', 2);
    } else {
      this.setState(
        {
          startDate,
          endDate: new Date(moment(startDate).unix() * 1000 + 24 * 60 * 60 * 30 * 1000),
        },
        () => {
          this.fetchData();
        },
      );
    }
  };

  // 获取提示
  getTips = () => {
    // 获取温馨提示
    this.props.dispatch({
      type: 'electronInvoice/getTips',
      payload: {
        organCode: getOrganCode(),
        businessCode: 'ELE_BILL_LIST',
        key: 'tip',
      },
      callback: (data: any = {}) => {
        this.setState({
          tips: data || {},
        });
      },
    });
    // 获取适用范围
    this.props.dispatch({
      type: 'electronInvoice/getTips',
      payload: {
        organCode: getOrganCode(),
        businessCode: 'ELE_BILL_LIST',
        key: 'suit',
      },
      callback: (data: any = {}) => {
        this.setState({
          suits: data || {},
        });
      },
    });
  };

  // 选择结束时间
  changeEndTime = (endDate: any) => {
    const { startDate } = this.state;
    if (endDate - startDate > deadline) {
      Toast.info('请选择一年内的时间段', 2);
    } else {
      this.setState({ endDate }, () => {
        this.fetchData();
      });
    }
  };

  // 跳转至详情页
  goInvoiceDetail = (rowData: any) => {
    console.log('detail', rowData);
    const { organCode, channelCode } = HxParameter;
    const {
      billBatchCode,
      billNo,
      random,
      invoiceFileType = '',
      invDr = '',
      detailType = 'dz',
      docId = '',
      docType = '',
      patAdmId,
    } = rowData;
    const {
      patientId,
      userInfo: { pmiNo },
    } = this.state;
    if (organCode === 'HID0101') {
      // 华西医院直接调接口跳转到第三方，不走自己的详情页（避免第三方点击返回的时候循环进入第三方）
      if (detailType === 'dz') {
        // 电子票据
        this.props.dispatch({
          type: 'electronInvoice/getHXInvoiceDetail',
          payload: {
            hospitalCode: organCode,
            channelCode,
            billNo,
            billBatchCode,
            random,
            channelMode: 1,
          },
          callback: (res: any) => {
            if (res.pictureNetUrl) {
              window.location.href = res.pictureNetUrl;
            } else {
              Toast.info('暂时无法查看');
            }
          },
        });
      } else if (detailType === 'zy') {
        // 住院票据
        this.props.dispatch({
          type: 'electronInvoice/billDetail',
          payload: {
            docId,
            docType,
            patAdmId,
            patNo: pmiNo,
          },
          callback: (res: any) => {
            if (res) {
              history.push(`/electroninvoice/imgView?docId=${docId}&docType=${docType}&patAdmId=&patNo=${pmiNo}`);
            } else {
              Toast.info('暂时无法查看');
            }
          },
        });
      }
    } else if (invoiceFileType === 'H5URL') {
      this.props.dispatch({
        type: 'electronInvoice/getHtmlInvoiceDetail',
        payload: {
          hospitalCode: organCode,
          channelCode,
          billNo,
          billBatchCode,
          random,
          channelMode: 1,
        },
        callback: (res: any = {}) => {
          if (res.pictureNetUrl) {
            window.location.href = res.pictureNetUrl;
          } else {
            Toast.info('暂时无法查看');
          }
        },
      });
    } else if (invoiceFileType === 'PDF_FILE') {
      console.log(invoiceFileType);
      window.location.href = `${COMMON_DOMAIN}/parking-project/electroninvoice/detailnew?organCode=${getOrganCode()}&channelCode=${getChannelCode()}&invDr=${invDr}&patientId=${patientId}&billBatchCode=${billBatchCode}&billNo=${billNo}&token=${getToken()}`;
    } else {
      history.push(`/electroninvoice/detail?&billBatchCode=${billBatchCode}&billNo=${billNo}`);
    }
  };

  renderList = () => {
    const ds = new ListView.DataSource({ rowHasChanged: (r1: any, r2: any) => r1 !== r2 });
    const { loading } = this.props;
    const { invoiceList = [] } = this.props;
    const { organCode } = HxParameter;
    const { type } = this.state;
    const {
      location: { search = '' },
    } = this.props;
    const { otherTypeNum = '1' }: any = queryString.parse(search) || {}; //otherTypeNum设置一级tab中住院tab的其他票据与电子票据两个二级tab;
    return loading ? (
      <HxIndicator style={organCode === 'HXZYZAXZYY0101' ? {} : { position: 'static', transform: 'none' }} />
    ) : organCode === 'HID0101' && type === 2 ? (
      <TabList
        fetchData3={() => this.fetchData3()}
        fetchData={() => this.fetchData2()}
        goInvoiceDetail={(row) => this.goInvoiceDetail(row)}
        invoiceList={invoiceList}
        otherTypeNum={otherTypeNum}
      />
    ) : invoiceList.length === 0 ? (
      // <div className={styles.noInvoice}>暂无电子发票</div>
      <Blank />
    ) : (
      <ListView
        dataSource={ds.cloneWithRows(invoiceList)}
        renderRow={(rowData) => (
          <div className={styles.invoiceListItem} onClick={() => this.goInvoiceDetail(rowData)}>
            <div className={styles.itemLeft}>
              <span>{rowData.category}</span>
              <span>{moment(rowData.createTime).format('YYYY-MM-DD')}</span>
            </div>
            <div className={styles.itemRight}>
              {rowData.totalAmt && `￥ ${rowData.totalAmt}`}
              <HxIcon iconName="arrow-right" className={styles.arrowIcon} />
            </div>
          </div>
        )}
        useBodyScroll
        initialListSize={invoiceList.length}
        onEndReachedThreshold={200}
        // renderFooter={
        //   (() => (
        //     <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
        //       <div style={{ textAlign: 'center', color: '#aaa' }}>正在加载更多...</div>
        //     </div>
        //   ))
        // }
      />
    );
  };

  render() {
    const {
      startDate,
      endDate,
      suits,
      tips,
      type = 1,
      userInfo: { patientName, credNo, gender },
    } = this.state;
    const { tipTitle = '', tipContent = '' } = tips || {};
    const { suitsTitle = '', suitsContent = '' } = suits || {};
    const avatar = '';
    const { organCode } = HxParameter;
    return (
      <div className={styles.container}>
        {organCode === 'HXZYZAXZYY0101' ? (
          <div />
        ) : (
          <Fragment>
            {(tipContent || suitsContent) && (
              <div className={styles.prompt}>
                {tipContent && (
                  <p>
                    {tipTitle}：{tipContent}
                  </p>
                )}
                {suitsContent && (
                  <p>
                    {suitsTitle}：{suitsContent}
                  </p>
                )}
              </div>
            )}
          </Fragment>
        )}
        <div className={styles.personalInfo}>
          <img src={avatar === '' ? userAvatar : avatar} alt="" />
          <div className={styles.personalInfoRight}>
            <span>
              {patientName} {gender === '' ? '' : gender === 1 ? '男' : '女'}
            </span>
            <span>{`${credNo?.slice(0, 4)} **** ${credNo?.slice(credNo.length - 4)}`}</span>
          </div>
        </div>

        <div className={styles.dateContainer}>
          <div className={styles.selectDate}>
            <DatePicker
              mode="date"
              value={startDate}
              onChange={(selectDate) => this.changeStartTime(selectDate)}
              // maxDate={lastDay}
            >
              <div className={styles.datePickerContent}>
                <HxIcon iconName="appointment" className={styles.dateIcon} />
                {startDate ? moment(startDate).format('YYYY-MM-DD') : '请选择'}
              </div>
            </DatePicker>
          </div>
          <div>至</div>
          <div className={styles.selectDate}>
            <DatePicker
              mode="date"
              value={endDate}
              // minDate={startDate}
              onChange={(selectDate) => this.changeEndTime(selectDate)}
              // maxDate={maxDate}
            >
              <div className={styles.datePickerContent}>
                <HxIcon iconName="appointment" className={styles.dateIcon} />
                {endDate ? moment(endDate).format('YYYY-MM-DD') : '请选择'}
              </div>
            </DatePicker>
          </div>
        </div>
        {/** 华西医院才有门诊和住院的区分，正安县还是采用以前的逻辑 */}
        {organCode === 'HID0101' ? (
          <Tabs page={+type - 1} tabs={tabs} animated={false} onChange={(tab) => this.onTabChange(tab)}>
            {this.renderList()}
          </Tabs>
        ) : (
          this.renderList()
        )}
      </div>
    );
  }
}

export default connect(
  ({ electronInvoice, loading }: { electronInvoice: IElectronInvoiceState; loading: Loading }) => ({
    invoiceList: electronInvoice.invoiceList,
    loading: loading.effects['electronInvoice/getInvoicelist'],
    electronInvoice,
  }),
)(Home);
