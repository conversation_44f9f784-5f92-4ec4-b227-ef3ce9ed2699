import React, { FC, useEffect, useState } from 'react';

import { history } from 'umi';
import icon from '@/assets/addressSelectArea.png';
import icon1 from '@/assets/icon_门诊医嘱缴费@2x.png';
import icon3 from '@/assets/icon_门特医嘱记录@2x.png';
import { getOrganCode } from '@/utils/parameter';
import styles from './index.less';
import { validateAccount } from '../service';

interface PageProps {}
const App: FC<PageProps> = () => {
  const [isShow, setisShow] = useState(false);
  useEffect(() => {
    const query = async () => {
      const res = await validateAccount();
      const organCode = getOrganCode();
      if (res.code === '1' && organCode === 'HID0101') {
        setisShow(true);
      }
    };
    query();
  }, []);
  return (
    <div className={styles.main}>
      <div
        className={styles.item}
        onClick={() => {
          history.push(`/electroninvoice/HomeNew${window.location.search}`);
        }}
      >
        <img className={styles.logo} src={icon1} alt="" />
        <div className={styles.txt}>
          <div className={styles.title}>电子票据</div>
          <div className={styles.desc}>电子票据</div>
        </div>
        <img className={styles.action} src={icon} alt="" />
      </div>
      {isShow ? (
        <div
          className={styles.item}
          onClick={() => {
            history.push(`/electroninvoice/Refund${window.location.search}`);
          }}
        >
          <img className={styles.logo} src={icon3} alt="" />
          <div className={styles.txt}>
            <div className={styles.title}>医嘱线上退费</div>
            <div className={styles.desc}>暂只支持华医通平台支付的华西门诊药品医嘱退费</div>
          </div>
          <img className={styles.action} src={icon} alt="" />
        </div>
      ) : null}
    </div>
  );
};
export default App;
