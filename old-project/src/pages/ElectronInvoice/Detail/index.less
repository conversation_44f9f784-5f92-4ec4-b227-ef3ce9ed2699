// 电子发票详情页
@import '~@/styles/mixin.less';

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  font-family: PingFang SC;
  background-color: #f5f6f7;

  .prompt {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    height: 130px;
    margin-bottom: 30px;
    padding: 0 30px;
    color: #f47f1b;
    font-size: 24px;
    background-color: #fdf2c3;
  }

  .invoiceMain {
    width: 690px;
    height: 386px;

    .loading {
      color: #ccc;
      font-size: 26px;
      line-height: 386px;
      text-align: center;
    }

    .img {
      width: 100%;
      height: 100%;
    }
  }

  .btn {
    position: fixed !important;
    bottom: 30px;
    font-size: 32px;
    .hxButton(#32b9aa, #ffffff, 16px);
  }
  .indecator {
    text-align: center;
    margin-top: 40px;
  }
}
.zhenganimg {
  width: 300px;
  height: 300px;
  margin-left: 200px;
}
