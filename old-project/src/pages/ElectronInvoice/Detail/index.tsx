import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Toast, Button, Modal } from 'antd-mobile';
import { Dispatch } from 'redux';
import { Loading, IElectronInvoiceState } from 'umi';
import queryString from 'query-string';
import { HxParameter, getOrganCode } from '@/utils/parameter';
import { IInvoiceDetail } from '../model';

import styles from './index.less';

export interface IProps {
  dispatch: Dispatch;
  invoiceDetail: IInvoiceDetail;
  location: {
    search: string;
    query: any;
  };
}

interface IState {
  tips: any;
}

class Detail extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      tips: {},
    };
  }

  componentDidMount() {
    this.getInvoiceDetail();
    this.getTips();
  }

  // 检查输入的值是否是Email
  isEmail = (Email: string) => {
    const regex = new RegExp(
      /[\w!#$%&'*+/=?^_{|}~-]+(?:.[\w!#$%&’*+/=?^_{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/,
    );
    return regex.test(Email);
  };

  // 获取提示
  getTips = () => {
    // 获取温馨提示
    this.props.dispatch({
      type: 'electronInvoice/getTips',
      payload: {
        organCode: getOrganCode(),
        businessCode: 'ELE_BILL_PIC',
        key: 'tip',
      },
      callback: (data: any) => {
        this.setState({
          tips: data,
        });
      },
    });
  };

  // 查询电子发票详情
  getInvoiceDetail = () => {
    const {
      location: { search = '' },
    } = this.props;
    const { billBatchCode = '', billNo = '' }: any = queryString.parse(search) || {};
    const { organCode, channelCode } = HxParameter;
    const query = {
      hospitalCode: organCode,
      channelCode,
      billNo,
      billBatchCode,
    };
    this.props.dispatch({
      // 正安中医院发票详情
      type: 'electronInvoice/getInvoiceDetail',
      payload: query,
    });
  };

  // 发送成功
  // success = (placeholders: any) => {
  //   Modal.alert('提示', '发送成功，请注意查收！', [
  //     {
  //       text: '返回',
  //       onPress: () => {
  //         placeholders.close();
  //       },
  //     },
  //   ]);
  // };

  // 点击“发送至邮箱”按钮
  sendToEmail = () => {
    const {
      invoiceDetail: { ossUrl = '', imgKey = '' },
    } = this.props;
    const { channelCode, organCode } = HxParameter;
    const placeholders = Modal.prompt('请输入收件邮箱', '', [
      {
        text: '取消',
      },
      {
        text: '确认',
        onPress: (value) =>
          new Promise(() => {
            // Toast.info(`${this.isEmail(value)}`, 1);
            if (!this.isEmail(value)) {
              Toast.info('输入的邮箱格式不正确', 1);
            } else {
              this.props.dispatch({
                type: 'electronInvoice/getInvoiceSend',
                payload: {
                  hospitalCode: organCode,
                  channelCode,
                  emailAddress: value,
                  imgPath: imgKey,
                  ossUrl,
                },
                callback: () => {
                  Toast.info('发送成功，请注意查收', 1);
                },
              });
            }
            placeholders.close();
          }),
      },
    ]);
  };

  render() {
    const {
      // invoiceDetail: { ossUrl = '', imgPath = '' },
      invoiceDetail: { ossUrl = '' },
    } = this.props;
    const { organCode } = HxParameter;
    const { tips } = this.state;
    // const haveData = ossUrl ? true : false;
    const haveData = !!ossUrl;
    return (
      <div className={styles.container}>
        <p className={styles.prompt}>
          {tips.title}：{tips.content}
        </p>
        <div className={styles.invoiceMain}>
          {/* !haveData ? (
              <div className={styles.loading}>发票生成中...</div>
            ) : organCode === 'HXZYZAXZYY0101' ? (
              <img className={styles.zhenganimg} src={ossUrl} alt="" />
            ) : (
              <img className={styles.img} src={ossUrl} alt="" />
            ) */}

          {/** HXZYZAXZYY0101 正安县中医院发票图片样式不同，延至以前的逻辑 */}
          {!haveData ? (
            <div className={styles.loading}>发票生成中...</div>
          ) : (
            <img className={organCode === 'HXZYZAXZYY0101' ? styles.zhenganimg : styles.img} src={ossUrl} alt="" />
          )}
        </div>

        {haveData && (
          <Button disabled={!haveData} className={styles.btn} onClick={() => this.sendToEmail()}>
            发送至邮箱
          </Button>
        )}
      </div>
    );
  }
}

export default connect(
  ({ electronInvoice, loading }: { electronInvoice: IElectronInvoiceState; loading: Loading }) => ({
    invoiceDetail: electronInvoice.invoiceDetail,
    loading: loading.effects['invoiceDetail/getInvoiceDetail'],
  }),
)(Detail);
