import { Toast } from 'antd-mobile';
import { StorageEnum } from '@/utils/enum';
import { Reducer } from 'redux';
import { Effect } from 'umi';
import { createAction } from '@/utils/common';
import { HxLocalStorage, HxSessionStorage } from '@/utils/storage';
import { IAddrerssItem, IAreaData } from './data.d';
import {
  queryFilmList, queryFilmConfigure, filmApply, queryApplyRecord
} from './service';


export interface IFilmPrintingModelState {
  areaData: IAreaData[];
}

export interface IFilmPrintingModel {
  namespace: 'filmPrinting';
  state: IFilmPrintingModelState;
  effects: {
    queryFilmList: Effect;
    queryFilmConfigure: Effect;
    filmApply: Effect;
    queryApplyRecord: Effect;
  };
  reducers: {
    updateState: Reducer<IFilmPrintingModelState>;
  };
}

const FilmPrintingModel: IFilmPrintingModel = {
  namespace: 'filmPrinting',
  state: {
    areaData: [],
  },

  effects: {
    *queryFilmList({ payload, callback }, { call }) {
      const res = yield call(queryFilmList, payload);
      callback(res);
    },
    *queryFilmConfigure({ payload, callback }, { call }) {
      const res = yield call(queryFilmConfigure, payload);
      callback(res);
    },
    *filmApply({ payload, callback }, { call }) {
      const res = yield call(filmApply, payload);
      callback(res);
    },

    *queryApplyRecord({ payload, callback }, { call }) {
      const res = yield call(queryApplyRecord, payload);
      callback(res);
    },

  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};

export default FilmPrintingModel;
