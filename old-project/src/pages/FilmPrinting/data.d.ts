/** 地址信息 */
export interface IAddrerssItem {
  /** id */
  addressId?: string;
  /** 区编码 */
  areaCode?: number;
  /** 区名称 */
  areaName?: string;
  /** 渠道编码 */
  channelCode?: string;
  /** 市编码 */
  cityCode?: string;
  /** 市名称 */
  cityName?: string;
  /** 联系人姓名 */
  contactUserName?: string;
  /** 是否为默认地址，1-是，0-否 */
  defaultAddress?: string;
  /** 详细地址 */
  detailAddress?: string;
  /** 手机号码 */
  phone?: string;
  /** 省编码 */
  provinceCode?: number;
  /** 省名称 */
  provinceName?: string;
  /** 纬度 */
  latitude?: string;
  /** 经度 */
  longitude?: number;
}

/** 省市区数据 */
export interface IAreaData {
  dicName?: string;
  dicCode?: string;
  children?: IAreaData[];
  label?: string;
  value?: string;
}
