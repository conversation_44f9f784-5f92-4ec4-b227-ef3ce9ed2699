import Mock from 'mockjs';
import { commonSuccessResponse } from '../../../mock/util';

const { Random } = Mock;

/**
 * 生成地址列表
 */
const fakeListAddress = (req: any, res: any) => {
  const response = Mock.mock({
    ...commonSuccessResponse,
    'data|20-70': [
      {
        addressId: () => Random.id(),
        'areaCode|+1': ['510104'],
        'areaName|+1': ['锦江区'],
        'channelCode|+1': ['PATIENT_WECHAT'],
        'cityCode|+1': ['510100'],
        'cityName|+1': ['成都市'],
        contactUserName: () => Random.cname(),
        'defaultAddress|+1': ['1', '0'],
        detailAddress: () => Random.county(true),
        'latitude|1-100.1-10': 1,
        'longitude|1-100.1-10': 1,
        'phone|+1': ['15908199450', '18408242978'],
        'provinceCode|+1': ['510000'],
        'provinceName|+1': ['四川省'],
      },
    ],
  });
  // 模拟请求延迟
  setTimeout(() => {
    return res.status(200).send(response);
  }, 500);
};

/**
 * 生成单个地址
 */
const fakeOneAddress = (req: any, res: any) => {
  const response = Mock.mock({
    ...commonSuccessResponse,
    data: {
      addressId: () => Random.id(),
      areaCode: () => Random.zip(),
      areaName: () => Random.county(),
      'channelCode|+1': ['PATIENT_WECHAT'],
      cityCode: () => Random.zip(),
      cityName: () => Random.city(),
      contactUserName: () => Random.cname(),
      'defaultAddress|+1': [1, 0],
      detailAddress: () => Random.county(true),
      'latitude|1-100.1-10': 1,
      'longitude|1-100.1-10': 1,
      phone: '15908199450',
      provinceCode: () => Random.zip(),
      provinceName: () => Random.province(),
    },
  });
  // 模拟请求延迟
  setTimeout(() => {
    return res.status(200).send(response);
  }, 500);
};

/**
 * 生成省市区数据
 */
const fakeAreaList = (req: any, res: any) => {
  const response = Mock.mock({
    ...commonSuccessResponse,
    data: [
      {
        dicCode: '110000',
        dicName: '北京市',
        children: [
          {
            dicName: '北京市',
            dicCode: '110100',
          },
        ],
      },
      {
        dicCode: '120000',
        dicName: '天津市',
        children: [
          {
            dicCode: '120000',
            dicName: '天津市',
          },
        ],
      },
      {
        dicCode: '130000',
        dicName: '河北省',
        children: [
          {
            dicCode: '130100',
            dicName: '石家庄市',
            children: [
              {
                dicCode: '130102',
                dicName: '长安区',
              },
              {
                dicCode: '130104',
                dicName: '新华区',
              },
              {
                dicCode: '130105',
                dicName: '新华区',
              },
            ],
          },
        ],
      },
      {
        dicCode: '510000',
        dicName: '四川省',
        children: [
          {
            dicCode: '510100',
            dicName: '成都市',
            children: [
              {
                dicCode: '510104',
                dicName: '锦江区',
              },
              {
                dicCode: '510105',
                dicName: '青羊区',
              },
              {
                dicCode: '510106',
                dicName: '金牛区',
              },
              {
                dicCode: '510107',
                dicName: '武侯区',
              },
              {
                dicCode: '510108',
                dicName: '成华区',
              },
            ],
          },
          {
            dicCode: '511700',
            dicName: '达州市',
            children: [
              {
                dicCode: '511702',
                dicName: '通川区',
              },
              {
                dicCode: '511725',
                dicName: '渠县',
              },
            ],
          },
        ],
      },
    ],
  });
  // 模拟请求延迟
  setTimeout(() => {
    return res.status(200).send(response);
  }, 100);
};

export default {
  'POST /cloud/hosplatcustomer/address/findlistaddress': fakeListAddress,
  'POST /cloud/hosplatcustomer/address/findoneaddress': fakeOneAddress,
  'POST /cloud/hosplatcustomer/address/area/list': fakeAreaList,
};
