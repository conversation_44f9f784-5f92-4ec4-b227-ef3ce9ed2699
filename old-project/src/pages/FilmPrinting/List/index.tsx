import React, { FC, useEffect, useState } from 'react';
import { PullToRefresh, ListView, Button, Toast, ActivityIndicator } from 'antd-mobile';
import { connect, Dispatch, Loading, ConnectProps, useLocation, IFilmPrintingModelState, history } from 'umi';
import { HxSessionStorage } from '@/utils/storage';
import { HxEmpty } from '@/components';
import { Modal } from 'antd-mobile-v5';
import HxModal from '@/components/HxModal';
import { doPay } from '@/utils/common';
import CalendarTime from '../../ElectronReport/components/CalendarTime';
import styles from './index.less';
import moment from 'moment';
import { StorageEnum } from '@/utils/enum';
import { getOrganCode } from '@/utils/parameter';
import { queryFilmList, queryApplyRecord } from '@/pages/FilmPrinting/service';

const dateIcon = require('@/assets/calendar-icon.png');
const checkedDate = require('@/assets/checkedDate.png');

const recordIcon = 'https://cdnhyt.cd120.com/person/assets/convenienceclinic/recordIcon.png';
const { alert } = Modal;

interface IProps extends ConnectProps {
  filmPrinting: IFilmPrintingModelState;
  dispatch: Dispatch;
  loading?: boolean;
}

const ListPage: FC<IProps> = ({ filmPrinting, dispatch }) => {
  const { query }: any = useLocation();
  const formatData = 'YYYY-MM-DD';
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [loading, setLoading] = useState<boolean>(true);
  const [calendarVisible, setCalendarVisible] = useState<boolean>(false);
  const [pageNum, setPageNum] = useState<number>(1);
  const [filmList, setFilmList] = useState<any>([]);
  const [applyRecordList, setApplyRecordList] = useState<any>([]);
  const [currentDate, setCurrentDate] = useState<number>(0);
  const [tabChiceIndex, setTabChiceIndex] = useState<number>(1); //0胶片申请 1申请记录
  const startDate = moment().subtract('days', 29).format(formatData); //近一个月
  const endDate = moment().add(1, 'days').format('YYYY-MM-DD');
  const [parameter, setParameter] = useState<any>({ startDate, endDate });
  const { pmi = '50105561', patientName } = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA) || {};
  const fetchData = async (startDate, endDate) => {
    const payload = {
      hisCode: getOrganCode(),
      patientId: pmi, //测试
      startDate,
      endDate,
    };
    const res = (await queryFilmList(payload)) || [];
    setLoading(false);
    setFilmList(res);
    setHasMore(false);
  };
  useEffect(() => {
    // fetchData(startDate, endDate);
    queryApplyRecordData();
  }, []);

  /** 上拉至底触发 */
  const onEndReached = () => {
    //申请记录
    if (tabChiceIndex === 1) {
      loadMore();
    }
  };
  /** 加载后一页 */
  const loadMore = () => {
    console.log('加载更多222----', hasMore);
    if (!hasMore) {
      return;
    }
    queryApplyRecordData(pageNum + 1);
  };
  const queryApplyRecordData = async (pageNum: number = 1, orderStatus: any = '') => {
    const payload = {
      pageNum,
      pageSize: 10,
      query: {
        orderStatus,
        hisCode: getOrganCode(),
        patientId: pmi, //测试
      },
    };
    const res = await queryApplyRecord(payload);
    console.log('结果----', res);
    const { content = [], totalPages = 1 } = res;
    setLoading(false);
    setApplyRecordList(content);
    setHasMore(res?.pageNum > totalPages);
    setPageNum(pageNum);
  };
  const chioceItem = (index: number) => {
    setTabChiceIndex(index);
    setCurrentDate(0);
    if (index === 0) {
      fetchData(startDate, endDate);
    } else {
      queryApplyRecordData();
    }
  };
  /** 日期筛选 */
  const onConfirm = (startDateTime: moment.MomentInput, endDateTime: moment.MomentInput) => {
    setCalendarVisible(false);
    setCurrentDate(3);
    fetchData(moment(startDateTime).format(formatData), moment(endDateTime).format(formatData));
  };
  const changeDate = (index) => {
    setCurrentDate(index);
    if (tabChiceIndex === 0) {
      let startDate;
      //近7天
      if (index === 0) {
        startDate = moment().subtract('days', 6).format(formatData);
      } else if (index === 1) {
        startDate = moment().subtract('days', 89).format(formatData);
      } else {
        startDate = moment().subtract('days', 364).format(formatData);
      }
      fetchData(startDate, endDate);
    } else {
      queryApplyRecordData(1, index > 0 ? index - 1 : '');
    }
  };
  const goApply = (type: number, item: any = {}) => {
    const { examName, itemId, orderId } = item;
    if (type === 1) {
      history.push(`/filmPrinting/filmChioce?examName=${examName}&itemId=${itemId}`);
    } else {
      history.push(`/filmPrinting/detail?orderId=${orderId}`);
    }
  };
  const cancelApply = () => {
    //调取消接口，在调列表接口刷新
    HxModal.show({
      title: '温馨提示',
      content: '您确定要取消当前申请订单？',
      actions: [
        {
          text: '取消',
          key: 'cancel',
          onClick: () => {
            Modal.clear();
          },
        },
        {
          text: '确定',
          key: 'confirm',
          className: 'primary',
          onClick: () => {
            //调接口
          },
        },
      ],
    });
  };
  const renderFooter = (): React.ReactElement => {
    if (!loading) {
      return <></>;
    }
    return (
      <div className={styles.listViewFooter}>
        <ActivityIndicator animating size="small" />
        {/* <span>加载中</span> */}
      </div>
    );
  };
  const getOrderStatus = (orderStatus) => {
    switch (orderStatus) {
      case 0:
        return '待缴费';
      case 1:
        return '已缴费';
      case 2:
        return '已缴费';
      default:
        return '已取消';
    }
  };
  const toDetail = (orderId: string) => {
    if (tabChiceIndex === 1) {
      history.push(`/filmPrinting/detail?orderId=${orderId}`);
    }
  };
  const ListItem = (item: any) => {
    const { examName, hisName, imageDate = '', applyDate = '', orderStatus, printStatus, orderId } = item;
    return (
      <div className={styles.item} onClick={() => toDetail(orderId)}>
        <div className={styles.recordTitle}>
          <div className={styles.name}>
            <img src={recordIcon} alt="" />
            <span className={styles.checkName}>{examName}</span>
          </div>
          {tabChiceIndex === 1 && (
            <div
              className={`${styles.status} ${orderStatus === 1 || orderStatus === 2 ? styles.paid : styles.pending}`}
            >
              {getOrderStatus(orderStatus)}
            </div>
          )}
        </div>
        <div className={styles.userInfoCell}>
          <div className={styles.cell}>
            <span className={styles.cellTitle}>申请医院：</span>
            <span className={styles.cellContent}>{hisName}</span>
          </div>
          <div className={styles.cell}>
            <span className={styles.cellTitle}>申请患者：</span>
            <span className={styles.cellContent}>{patientName}</span>
          </div>
          <div className={styles.cell}>
            <span className={styles.cellTitle}>影像日期：</span>
            <span className={styles.cellContent}>{imageDate}</span>
          </div>
          {tabChiceIndex === 1 && (
            <div className={styles.cell}>
              <span className={styles.cellTitle}>申请日期：</span>
              <span className={styles.cellContent}>{applyDate}</span>
            </div>
          )}
          {tabChiceIndex === 1 && (
            <div className={styles.cell}>
              <div
                className={`${styles.tips} ${
                  printStatus === 1 ? styles.blueBg : printStatus === 2 ? styles.printed : styles.tips
                }`}
              >
                <span>温馨提示</span>
                {printStatus === 0
                  ? ' 胶片未出，请耐心等待'
                  : printStatus === 1
                  ? '胶片已出，请前往医院打印'
                  : '胶片已完成打印'}
              </div>
            </div>
          )}
        </div>

        <div className={styles.buttonWrap}>
          {tabChiceIndex === 0 ? (
            <Button size="small" type="primary" className={styles.button} onClick={() => goApply(1, item)}>
              去申请
            </Button>
          ) : orderStatus === 0 ? (
            <>
              {/* <Button size="small" className={styles.cancelButton} onClick={cancelApply}>
                取消订单
              </Button> */}
              <Button size="small" type="primary" className={styles.button} onClick={() => goApply(2, item)}>
                去缴费
              </Button>
            </>
          ) : (
            ''
          )}
        </div>
      </div>
    );
  };
  const dataSource = new ListView.DataSource({
    rowHasChanged: (row1: any, row2: any) => row1 !== row2,
  });
  const dateTab = ['一个月内', '三个月内', '一年内'];
  const headerTab = ['申请记录']; //去掉胶片申请
  const applyTabList = ['全部', '待缴费', '已缴费'];
  const tableData = tabChiceIndex === 0 ? filmList : applyRecordList;
  console.log(tableData, filmList, applyRecordList);
  return (
    <div className={styles.listContent}>
      <div className={styles.header}>
        {/* {headerTab.map((ele, index) => {
          return (
            <div
              key={index}
              className={tabChiceIndex === index ? `${styles.item} ${styles.activeItem}` : styles.item}
              onClick={() => chioceItem(index)}
            >
              {ele}
              {tabChiceIndex === index && <div className={styles.tabLine}></div>}
            </div>
          );
        })} */}
      </div>
      <div className={styles.applyList}>
        {tabChiceIndex === 0 ? (
          ''
        ) : (
          // <div className={styles.dateTabBar}>
          //   {dateTab.map((value, index) => {
          //     return (
          //       <div
          //         key={index}
          //         onClick={() => changeDate(index)}
          //         className={currentDate === index ? styles.activeDate : ''}
          //       >
          //         {value}
          //       </div>
          //     );
          //   })}
          //   <div className={styles.dateIcon}>
          //     <img
          //       src={currentDate === 3 ? checkedDate : dateIcon}
          //       className={styles.icon}
          //       onClick={() => setCalendarVisible(true)}
          //       alt=""
          //     />
          //     <CalendarTime
          //       visible={calendarVisible}
          //       onCancel={() => setCalendarVisible(false)}
          //       onConfirm={onConfirm}
          //     />
          //   </div>
          // </div>
          <div className={styles.dateTabBar}>
            {applyTabList.map((value, index) => {
              return (
                <div
                  key={index}
                  onClick={() => changeDate(index)}
                  className={currentDate === index ? styles.activeBar : styles.tabItem}
                >
                  {value}
                </div>
              );
            })}
          </div>
        )}
      </div>
      {tableData.length ? (
        <div className={styles.filmList}>
          <div className={styles.listView}>
            <ListView
              dataSource={dataSource.cloneWithRows(tableData || [])}
              pullToRefresh={
                <PullToRefresh
                  refreshing={loading}
                  // style={{ overflow: 'auto', height: '100vh' }}
                  getScrollContainer={() => <></>}
                  indicator={{}}
                  direction="down"
                  distanceToRefresh={25}
                  damping={100}
                  onRefresh={() => queryApplyRecordData()}
                />
              }
              renderRow={ListItem}
              renderFooter={renderFooter}
              onEndReached={onEndReached}
              pageSize={10}
              initialListSize={10}
              onEndReachedThreshold={20}
            />
          </div>
        </div>
      ) : (
        <div className={styles.empty}>
          <HxEmpty emptyMsg="暂无内容" emptyIcon="norecord" isNewImg canRefresh={false} />
        </div>
      )}
    </div>
  );
};

export default connect(({ filmPrinting }: { filmPrinting: any }) => ({
  filmPrinting,
}))(ListPage);
