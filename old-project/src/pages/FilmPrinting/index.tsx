import React from 'react';
import qs from 'query-string';
import { HxRedirect, getModuleHomeUrl } from '@/utils/interceptor';
import { ModuleEnum } from '@/utils/enum';

// 是否需要选卡
const needChooseCard = true;

// 是否需要访问口令
const needAccessToken = false;

// 组装参数
const query: any = qs.parse(window.location.href.split('?')[1]) || {};
query.needAccessToken = needAccessToken;

// 重定向地址
// const redirectUrl = getModuleHomeUrl(ModuleEnum.MODULE_ADDRESS);
const redirectUrl = '/filmPrinting/list';

const defaultParams = {
  pathname: needChooseCard ? getModuleHomeUrl(ModuleEnum.MODULE_PATIENT_CARD) : redirectUrl,
  search: needChooseCard ? `redirect=${redirectUrl}&${qs.stringify(query)}` : `${qs.stringify(query)}`,
};

export default () => {
  return <HxRedirect params={defaultParams} />;
};
