import React, { useEffect, useState, useRef, ReactElement } from 'react';
import classnames from 'classnames';
import { Toast, Button } from 'antd-mobile';
import { Modal } from 'antd-mobile-v5';
import HxModal from '@/components/HxModal';
import { connect, Dispatch, IFilmPrintingModelState } from 'umi';
import { doPay } from '@/utils/common';
import { HxIndicator, HxIcon } from '@/components';
import dayjs from 'dayjs';
import styles from './index.less';
import noticeImg from '@/assets/icon_modal.png';
import mapIcon from '@/assets/map.png';
import clockIcon from '@/assets/appoinment/clock.png';
import finishedIcon from '@/assets/select.png';

import { queryFilmDetail } from '@/pages/FilmPrinting/service';
import { HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import { PAY_CHANNEL } from '../dictionary';
const medical_record_icon =
  'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/case-mailing/medical_record_icon.png';

interface FilmChioceProps {
  filmPrinting: IFilmPrintingModelState;
  dispatch: Dispatch;
  location: {
    query: {
      /** 订单号 */
      orderId: string;
    };
  };
  loading: boolean | undefined;
}

const FilmDetail: React.FC<FilmChioceProps> = (props) => {
  // props
  const {
    dispatch,
    location: {
      query: { orderId },
    },
    loading,
  } = props;
  const [filmOrderInfo, setFilmOrderInfo] = useState<any>([]);

  const confirmPay = () => {
    console.log('filmOrderInfo---', filmOrderInfo);
    const { bizSysSeq, dealSeq, merchantSeq } = filmOrderInfo;
    doPay(dealSeq, bizSysSeq, merchantSeq);
  };
  const getApplyDetail = async (orderId) => {
    const res = await queryFilmDetail({ orderId });
    console.log('结果----', res);
    setFilmOrderInfo(res);
  };
  const getOrderStatus = (orderStatus) => {
    switch (orderStatus) {
      case 0:
        return '待缴费';
      case 1:
        return '已缴费';
      case 2:
        return '待缴费';
      default:
        return '已取消';
    }
  };
  useEffect(() => {
    if (orderId) {
      getApplyDetail(orderId);
    }
  }, [orderId]);
  const {
    payAmount,
    payTime,
    bankTradeNo,
    payMethod,
    examName,
    radiographCount,
    radiographPrice,
    radiographType,
    orderStatus,
    radiographTotalPrice,
    createDate,
    dealSeq,
    patientName,
    idCard,
    expirationTime,
    orderStatusDesc,
    notice,
    printStatus,
    printAddress,
  } = filmOrderInfo;
  console.log('radiographType--', printAddress, radiographType);
  return (
    <div className={classnames(styles.container, styles.sjContainer)}>
      {loading && <HxIndicator />}
      {radiographType ? (
        <>
          <div className={styles.header}>
            <div className={styles.orderStatus}>
              <img src={orderStatus === 1 ? finishedIcon : clockIcon} alt="" />
              {orderStatusDesc}
              <div className={`${styles.time} ${printStatus === 1 && styles.finished}`}>
                {notice}
                {/* {orderStatus === 0
                  ? `请在${expirationTime}内完成支付`
                  : printStatus === 0
                  ? ' 胶片未出，请耐心等待'
                  : printStatus === 1
                  ? '胶片已出，请前往医院打印'
                  : '胶片已完成打印'} */}
              </div>
            </div>
          </div>

          <div className={styles.main} style={printAddress ? { top: '-40px' } : !notice ? { top: '-84px' } : {}}>
            {printAddress && (
              <div className={styles.getFilmAddress}>
                <div className={styles.title}>取片地址</div>
                <div className={styles.table}>
                  {Object.keys(printAddress)?.map((campus) => (
                    <div key={campus} className={styles.item}>
                      <div className={styles.th}>
                        <img src={mapIcon} alt="" />
                        {campus}
                      </div>
                      {printAddress[campus].map((address, i) => (
                        <div key={i} className={styles.tr}>
                          {address}
                        </div>
                      ))}
                    </div>
                  ))}
                  {/* <div className={styles.th}>
                  <img src={mapIcon} />
                  华西坝院区
                </div>
                <div className={styles.tr}>第一住院大楼一楼放射科综合服务站窗口外侧</div>
                <div className={styles.tr}>第一住院大楼一楼放射科综合服务站窗口外侧</div>
                <div className={styles.tr}>第一住院大楼一楼放射科综合服务站窗口外侧</div>
                <div className={styles.tr}>第一住院大楼一楼放射科综合服务站窗口外侧</div> */}
                </div>
              </div>
            )}

            <div className={styles.filmName}>
              <img src={medical_record_icon} alt="" />
              {examName}
            </div>
            <div className={styles.info}>
              <div className={styles.infoItem}>
                <span className={styles.labe}>就诊人</span>
                <span className={styles.value}>{patientName}</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.labe}>就诊卡号</span>
                <span className={styles.value}>{idCard}</span>
              </div>
            </div>
            {/* 打印信息 */}
            <div className={styles.printInfo}>
              <div className={styles.title}>打印信息</div>
              <div className={styles.printCount}>
                <div className={styles.left}>胶片打印份数</div>
                <div className={styles.right}>1 份</div>
              </div>
              <div className={styles.filmList}>
                <div className={styles.title}>
                  {/* <img src={filmPrintIndex === index ? selected : unSelected} onClick={() => changeItem(item, index)} /> */}
                  {radiographType}
                </div>
                <div className={styles.fileItem}>
                  <div className={styles.filmInfo}>
                    <div className={styles.row}>
                      <div className={styles.left}>胶片数量</div>
                      <div className={styles.right}>{radiographCount} 张</div>
                    </div>
                    <div className={styles.row}>
                      <div className={styles.left}>单张金额</div>
                      <div className={styles.right}>￥{Number(radiographPrice)?.toFixed(2)}</div>
                    </div>
                    <div className={styles.totalPrice}>
                      <span>总计：</span>￥{Number(radiographTotalPrice)?.toFixed(2)}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* <div className={styles.info}>
              <div className={styles.tips}>
                <img alt="" src={noticeImg} />
                注意事项
              </div>
              <div className={styles.tipsText}>
                文案根据科室提供的进行展示，文案根据科室提供的进行展示文案根据科室提供的进行展示，文案根据科室提供的进行展示文案根据科室提供的进行展示，文案根据科室提供的进行展示，文案根据科室提供的进行展示，文案根据科室提供的进行展示。
              </div>
            </div> */}
            {/* 订单信息 */}
            <div className={styles.info}>
              <div className={styles.infoItem}>
                <span className={styles.labe}>订单编号：{dealSeq}</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.labe}>下单时间：{createDate}</span>
              </div>
              <div className={styles.infoItem}>
                <span className={styles.labe}>
                  订单状态：
                  {getOrderStatus(orderStatus)}
                </span>
              </div>
              {/* 已支付 */}
              {orderStatus === 1 && (
                <div>
                  <div className={styles.infoItem}>
                    <span className={styles.labe}>支付时间：{payTime}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.labe}>支付方式：{PAY_CHANNEL[payMethod]}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.labe}>支付金额：{payAmount}</span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.labe}>支付流水：{bankTradeNo}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </>
      ) : (
        ''
      )}

      {/* 结算按钮 */}
      {orderStatus === 0 && (
        <div className={styles.bottom}>
          <>
            <div>
              <span className={styles.priceUnit}>￥</span>
              <span className={styles.price}>{Number(radiographTotalPrice)?.toFixed(2)}</span>
            </div>
            {/* <Button className={styles.cancelBtn} size="small" onClick={cancelApply}>
              取消订单
            </Button> */}
            <Button
              className={styles.button}
              type="primary"
              size="small"
              style={{ color: '#ffffff' }}
              onClick={confirmPay}
            >
              去支付
            </Button>
          </>
        </div>
      )}
    </div>
  );
};
export default connect(({ filmPrinting }: { filmPrinting: any }) => ({
  filmPrinting,
}))(FilmDetail);
