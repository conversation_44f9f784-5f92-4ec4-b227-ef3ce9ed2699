@import '~@/styles/mixin.less';
.main {
  height: 100vh;
  padding: 16px;
  overflow: scroll;
  background: #f5f6f7;

  .title {
    margin-top: 40px;
    font-weight: 600;
    font-size: 40px;
    line-height: 56px;
    text-align: center;
  }
  .content {
    margin-top: 40px;
    padding: 24px;
    font-size: 32px;
    line-height: 48px;
    color: #51586d;
    div {
      margin-top: 24px;
    }
  }
  .action {
    position: fixed;
    bottom: 0;
    width: 100%;
    // height: 200px;
    background: #fff;
    padding: 36px 32px;
    left: 0;
    right: 0;
    &_btn {
      margin-top: 16px;
    }
    :global .adm-checkbox-content {
      font-size: 28px;
      font-weight: bolder;
    }
  }
  .action_btn {
    // width: 95%;
    font-size: 36px;
    height: 96px;
    line-height: 96px;
    background: #3ad3c1;
    border-radius: 52px;
    color: #fff;
    padding: 0px !important;
    border: none;
  }
}

// .container {
//   position: relative;
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   height: 100%;
//   height: -webkit-fill-available;
//   padding: 36px 20px;

//   p {
//     margin-bottom: 0;
//   }

//   .content {
//     display: flex;
//     flex-direction: column;
//     width: 710px;
//     overflow-x: hidden;
//     background: #fff;
//     border-top: 14px solid @brand-primary;
//     border-radius: 10px;
//     box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
//     padding-bottom: 30px;

//     .icon {
//       width: 140px;
//       height: 140px;
//       margin: 40px auto 0;
//     }

//     .notice {
//       box-sizing: border-box;
//       width: 100%;
//       height: auto;
//       max-height: 700px;
//       padding: 40px;
//       overflow-x: hidden;
//       color: #333;
//       font-size: 28px;
//       line-height: 46px;
//       text-align: left;

//       &::-webkit-scrollbar {
//         display: block;
//         width: 14px;
//         height: 1px;
//         background: #fff;
//       }

//       &::-webkit-scrollbar-thumb {
//         /*滚动条里面小方块*/
//         border-radius: 10px;
//         background: #c2c0c0;
//         -webkit-box-shadow: inset 0 0 5px #c2c0c0;
//       }

//       &::-webkit-scrollbar-track {
//         /*滚动条里面轨道*/
//         padding-right: 2px;
//         border-radius: 0;
//         background: #fff;
//         -webkit-box-shadow: inset 0 0 5px #fff;
//       }
//     }
//   }

//   .agreement {
//     display: flex;
//     flex-direction: row;
//     align-items: center;
//     width: 100%;
//     height: 40px;
//     margin-top: 40px;
//     margin-bottom: 110px;

//     .icon {
//       width: 30px;
//       height: 30px;
//       font-size: 30px;
//     }

//     p {
//       margin-top: 6px;
//       margin-left: 10px;
//       color: #333;
//       font-size: 28px;
//       span {
//         color: @brand-primary;
//       }
//     }
//   }

//   .buttonBox {
//     position: absolute !important;
//     right: 0;
//     bottom: 20px;
//     left: 0;
//     display: flex;
//     align-items: center;
//     justify-content: center;
//     .confirm {
//       .hxButton();

//       margin: 0 auto;
//     }
//   }
// }

// :global(.am-button) {
//   height: 90px !important;
//   font-size: 32px !important;
//   line-height: 90px !important;
// }
