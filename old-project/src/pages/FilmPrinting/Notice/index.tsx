import React, { PureComponent } from 'react';
import qs from 'query-string';
import { HxIcon } from '@/components';
import { connect, Dispatch, Loading, history } from 'umi';
import { getOrganCode } from '@/utils/parameter';
import { isTfsmy } from '@/utils/platform';
import styles from './index.less';
import { Checkbox, Toast, Button } from 'antd-mobile-v5';

interface IProps {
  location: {
    query: {
      examName: string;
      itemId: string;
    };
  };
  dispatch: Dispatch;
}

interface IState {
  isAgree: boolean;
  examName: string;
  notice: string;
  itemId: string;
}

class Notice extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);

    this.state = {
      isAgree: false,
      notice: '',
      examName: '',
      itemId: '',
    };
  }

  componentDidMount() {
    const {
      location: {
        query: { itemId, examName },
      },
    } = this.props;
    console.log(this.props, examName, 'props---');
    this.fetchData();
    this.setState({
      examName,
      itemId,
    });
  }
  fetchData = () => {
    this.props.dispatch({
      type: 'common/queryText',
      payload: {
        textCode: 'image_radiograph_apply_notice',
      },
      callback: (text: string) => {
        console.log('res----', text);
        this.setState({
          notice: text,
        });
      },
    });
  };
  clickConfirm = () => {
    const { isAgree, itemId, examName } = this.state;
    console.log('isAgree---', isAgree);
    if (!isAgree) {
      Toast.show({
        content: '请阅读并同意挂号须知',
      });
      return;
    }

    history.push(`/filmPrinting/filmChioce?examName=${examName}&itemId=${itemId}`);
  };

  clickAgree = (val) => {
    console.log(val, 99999);
    this.setState({
      isAgree: val,
    });
  };

  render() {
    const { notice } = this.state;
    return (
      <div className={styles.main}>
        <div className={styles.title}>胶片打印须知</div>
        <div className={styles.content}>
          <div dangerouslySetInnerHTML={{ __html: notice }} />
        </div>

        <div className={styles.action}>
          <Checkbox
            onChange={(val) => {
              this.clickAgree(val);
            }}
          >
            已阅读 《胶片申请须知》
          </Checkbox>
          <Button block className={styles.action_btn} onClick={this.clickConfirm}>
            确定
          </Button>
        </div>
      </div>
    );
  }
}

// export default Notice;
export default connect(({ loading, filmPrinting }: { loading: Loading; filmPrinting: any }) => ({
  filmPrinting,
}))(Notice);
