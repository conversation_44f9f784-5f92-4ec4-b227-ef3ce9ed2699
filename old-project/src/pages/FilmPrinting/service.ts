import request from '@/utils/request';

/** 查询胶片申请列表 */
export const queryFilmList = async (data: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/radiograph/applyList/query`, {
    method: 'POST',
    data,
  });
/** 查询胶片配置 */
export const queryFilmConfigure = async (data: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/radiograph/config/query`, {
    method: 'POST',
    data,
  });
/** 胶片申请 */
export const filmApply = async (data: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/radiograph/apply`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });
/** 查询胶片配置 */
export const queryApplyRecord = async (data: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/radiograph/order/page/query`, {
    method: 'POST',
    data,
  });
/** 查询胶片订单详情 */
export const queryFilmDetail = async (params: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/radiograph/order/detail/get`, {
    method: 'GET',
    params,
  });

// export const queryFilmList = async (params: object): Promise<any> =>
//   request(`${API_TEST}/imagecloud/radiograph/config/query`, {
//     method: 'GET',
//     params,
//   });