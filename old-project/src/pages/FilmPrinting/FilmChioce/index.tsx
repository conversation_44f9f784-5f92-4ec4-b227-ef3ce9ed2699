import React, { useEffect, useState, ReactElement } from 'react';
import { ClockCircleOutlined, ClockCircleFilled, CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import classnames from 'classnames';
import { Toast, Button } from 'antd-mobile';
import { Modal } from 'antd-mobile-v5';
import HxModal from '@/components/HxModal';
import { connect, Dispatch, IFilmPrintingModelState } from 'umi';
import { doPay } from '@/utils/common';
import { HxIndicator, HxIcon } from '@/components';
import dayjs from 'dayjs';
import styles from './index.less';
import noticeImg from '@/assets/icon_modal.png';
import selected from '@/assets/icon_chose.png';
import unSelected from '@/assets/radio1.png';
import disabledIcon from '@/assets/ElectronReport/disabled.png';
import { HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import { queryFilmConfigure, filmApply } from '@/pages/FilmPrinting/service';
import { getOrganCode } from '@/utils/parameter';

const medical_record_icon =
  'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/case-mailing/medical_record_icon.png';

interface FilmChioceProps {
  filmPrinting: IFilmPrintingModelState;
  dispatch: Dispatch;
  location: {
    query: {
      examName: string;
      itemId: string;
    };
  };
  loading: boolean | undefined;
}

const FilmChioce: React.FC<FilmChioceProps> = (props) => {
  const {
    location: {
      query: { examName = '', itemId = '' },
    },
    loading,
  } = props;
  const [filmPrintIndex, setFilmPrintIndex] = useState<number>(0);
  const [filmConfigureData, setFilmConfigureData] = useState<any>({});
  const [chioceFilmType, setChioceFilmType] = useState<any>({});
  const { credNo, patientName } = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA) || {};
  console.log('就诊卡--', HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA));
  const confirmPay = async () => {
    const payload = {
      itemId,
      radiographPrintCount: filmConfigureData?.maxPrintCount,
      radiographType: chioceFilmType?.code,
      hisCode: getOrganCode(),
    };
    const res = await filmApply(payload);
    const { code, data = {} } = res;
    const { exist, bizSysSeq, dealSeq, merchantSeq } = data;
    if (code === '1') {
      const showText = exist
        ? '该检查已提交过胶片打印申请，可直接前往缴费，请选择是否缴费?'
        : '您即将提交胶片打印申请订单并前往支付，请您确认是否提交？'; //exist为true
      HxModal.show({
        title: '温馨提示',
        content: showText,
        actions: [
          {
            text: '取消',
            key: 'cancel',
            onClick: () => {
              Modal.clear();
            },
          },
          {
            text: '去缴费',
            key: 'confirm',
            className: 'primary',
            onClick: () => {
              Modal.clear();
              doPay(dealSeq, bizSysSeq, merchantSeq);
            },
          },
        ],
      });
    }
  };
  const changeItem = (item, index) => {
    setFilmPrintIndex(index);
    const { count, price } = item;
    setChioceFilmType({ ...item, price: count * price });
  };
  const getFilmPrintList = async () => {
    const payload = {
      itemId,
      hisCode: getOrganCode(),
    };
    const res = await queryFilmConfigure(payload);
    setFilmConfigureData(res);
    const data = res?.radiographTypeList;
    const { count, price } = data?.length ? data[0] : {};
    setChioceFilmType({ ...data?.[0], price: count * price });
  };
  useEffect(() => {
    getFilmPrintList();
  }, []);
  const filmTypeList = filmConfigureData?.radiographTypeList;
  const notice = filmConfigureData?.notice;
  return (
    <div className={classnames(styles.container, styles.sjContainer)}>
      {loading && <HxIndicator />}
      <div className={styles.header}>
        <img src={medical_record_icon} alt="" />
        {examName}
      </div>
      <div className={styles.info}>
        <div className={styles.infoItem}>
          <span className={styles.labe}>就诊人</span>
          <span className={styles.value}>{patientName}</span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.labe}>就诊卡号</span>
          <span className={styles.value}>{credNo}</span>
        </div>
      </div>
      {/* 打印信息 */}
      <div className={styles.printInfo}>
        <div className={styles.title}>请选择打印类型</div>
        <div className={styles.printCount}>
          <div className={styles.left}>
            <img src={disabledIcon} alt="" />
            胶片打印份数
          </div>
          <div className={styles.right}>1 份</div>
        </div>
        {filmTypeList?.length &&
          filmTypeList.map((item, index) => {
            return (
              <div className={styles.filmList} key={index}>
                <div className={styles.title}>
                  <img src={filmPrintIndex === index ? selected : unSelected} onClick={() => changeItem(item, index)} />
                  {item.name}
                </div>
                <div className={styles.fileItem}>
                  <div className={styles.filmInfo}>
                    <div className={styles.row}>
                      <div className={styles.left}>胶片数量</div>
                      <div className={styles.right}>{item.count}张</div>
                    </div>
                    <div className={styles.row}>
                      <div className={styles.left}>单张金额</div>
                      <div className={styles.right}>￥{Number(item.price).toFixed(2)}</div>
                    </div>
                    <div className={styles.totalPrice}>
                      <span>总计：</span>￥{(item.count * item.price).toFixed(2)}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
      </div>
      {/* <div className={styles.info}>
        <div className={styles.tips}>
          <img alt="" src={noticeImg} />
          注意事项
        </div>
        <div className={styles.tipsText}>
          <div dangerouslySetInnerHTML={{ __html: notice }} />
        </div>
      </div> */}

      <div className={styles.bottom}>
        {/* 去缴费 */}
        <Button
          className={styles.sumPrice}
          type="primary"
          size="small"
          style={{ color: '#ffffff' }}
          onClick={confirmPay}
        >
          总计{Number(chioceFilmType?.price)?.toFixed(2)}元，确认提交
        </Button>
      </div>
    </div>
  );
};
export default connect(({ filmPrinting }: { filmPrinting: any }) => ({
  filmPrinting,
}))(FilmChioce);
