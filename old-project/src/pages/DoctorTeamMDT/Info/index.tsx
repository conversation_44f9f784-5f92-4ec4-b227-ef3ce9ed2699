import React, { useState, useEffect, FC } from 'react';
import { connect, Dispatch, history, Loading } from 'umi';
import qs from 'query-string';
import styles from './index.less';
import Icon1 from '../../../assets/team-desc-icon1.png';
import Icon2 from '../../../assets/team-desc-icon2.png';

interface PageProps {}
const App: FC<PageProps> = () => {
  const [info, setInfo] = useState({});
  useEffect(() => {
    const _info = localStorage.getItem('__MDTINFO');
    if (_info) {
      setInfo(qs.parse(_info));
    }
  }, []);
  return (
    <div className={styles.main}>
      <div className={styles.headerBox}>
        <div className={styles.teamTitle}>{info.doctorName ? info.doctorName : ''}</div>
        <div className={styles.teamHospital}>
          <span className={styles.teamHospitalLevel}>三甲</span>
          <span className={styles.teamHospitalName}>四川大学华西医院</span>
        </div>
      </div>
      <div className={styles.center}>
        <div className={styles.centerDescBox}>
          <div className={styles.centerDescBoxHeader}>
            <img className={styles.centerDescBoxHeaderIcon} src={Icon1} alt="" />
            <span className={styles.centerDescBoxHeaderName}>准入条件</span>
          </div>
          <div className={styles.centerDescBoxDesc}>{info.entryCriteria ? info.entryCriteria : ''}</div>
        </div>
        <div className={styles.centerDescBox}>
          <div className={styles.centerDescBoxHeader}>
            <img className={styles.centerDescBoxHeaderIcon} src={Icon2} alt="" />
            <span className={styles.centerDescBoxHeaderName}>团队简介</span>
          </div>
          <div className={styles.centerDescBoxDesc}>{info.introduction ? info.introduction : ''}</div>
        </div>
      </div>
    </div>
  );
};

export default connect(({ loading, piTeam }: { loading: Loading; piTeam: any }) => ({
  loading,
}))(App);
