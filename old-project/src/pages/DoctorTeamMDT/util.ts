/*
 * @Author: <PERSON>
 * @Date: 2022-03-17 11:15:04
 * @LastEditor: <PERSON>
 * @LastEditTime: 2022-03-27 17:48:54
 * @FilePath: /hyt-person/src/pages/DoctorDetailAggregationHX/util.ts
 * Copyright (c) 2022 by <PERSON>, All Rights Reserved.
 * @Description:
 */
import AppScheme from '@/utils/AppScheme';

interface PathDataType {
  doctorCode: string;
  organCode: string;
  doctorId: string;
  servCode: string;
  middleType?: string;
}

interface OtherDataType {
  portrait: string;
  doctorName: string;
  docTitle: string;
  organName: string;
  deptName: string;
  servCode: string;
}

const { docHomeShareUrl } = AppScheme;

/**
 * 给原生传二维码需要的数据
 * @param pathData 医生聚合主页需要的参数
 * @param otherData 原生需要的其他参数
 */
const addShareToApp = (pathData: any, otherData: OtherDataType) => {
  const { doctorId, sourceChannel, teamId, mdtTeamId, servType, organId } = pathData;
  const pathname = `${THE_DOMAIN}/doctor/hxhomemdtline?organCode=HID0101&channelCode=PATIENT_WECHAT&appCode=HXGYAPP`;
  // 重新登录后的重定向路由
  const purePlayParam = `doctorId@${doctorId}|openApp@true|organId@${organId}|servType@${servType}|mdtTeamId@${mdtTeamId}|teamId@${teamId}|sourceChannel@${sourceChannel}`;
  const url = `${pathname}&purePlayKey=MDT&purePlayParam=${purePlayParam}`;
  const params = {
    url,
    ...otherData,
  };
  docHomeShareUrl(encodeURIComponent(JSON.stringify(params)));
};

export { addShareToApp };
