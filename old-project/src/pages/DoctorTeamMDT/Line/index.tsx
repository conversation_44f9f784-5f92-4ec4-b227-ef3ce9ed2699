import React, { useEffect, useRef, useState } from 'react';
import { connect, Dispatch, history, Loading } from 'umi';
import { Toast } from 'antd-mobile';
import _ from 'lodash';
import { DoctorModelState } from '@/typings/doctor.d';
import { GuideToExternalBrowser } from '@/components';
import AppScheme from '@/utils/AppScheme';
import { isWechat } from '@/utils/platform';
import { SingWechatJSSDKDataType } from '@/typings/global';
import { configWechatShareStyle, getCurEvnHref } from '@/utils/tool';
import icon_team from '@/assets/icon_team.png';
import ICON1 from '@/assets/zrtj.png';
import NoNetTips from '@/assets/noNetTips.png';
import ImgView from '@/pages/ReportClip/ImgView';
import Appointment from './components/Appointment';
import styles from './index.less';
import { addShareToApp } from '../util';
import PITeam from './components/PITeam';
import TopInfo from './components/TopInfo';
import Tour from './components/Tour';

const doctor_defalut_logo = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/default-doctor-logo.png';
const doctor_header_bg = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-header-bg.png';

interface DoctorDetailAggregationProps {
  doctor: DoctorModelState;
  dispatch: Dispatch;
  location: {
    query: {
      doctorCode: string;
      doctorId: string;
      organCode: string;
      servCode: string;
      hospitalCode: string;
      hospitalAreaCode: string;
      tabAreaCode: string;
      appCode: string;
      channelCode: string;
      docFillRecordId: string; // 认定记录id
      /** 医生主页中间显示模块类型（number类型） 0:医生服务 1:医学资讯列表 2: 专病服务包列表 */
      middleType: string;
      /** 是否是二维码扫描出来的页面（boolean类型） */
      openApp: string;
      /** 重定向需要的参数 */
      purePlayKey: 'MDT';
      /** 重定向路由的必要参数组成的字符串 */
      purePlayParam: string;
      organId: string;
      pmi: string;
      selectedIndex: number;
      student: string;
    };
  };
  detailLoading: boolean | undefined;
  followStatusLoading: boolean | undefined;
  commentsLoading: boolean | undefined;
  healthLoading: boolean | undefined;
}

const DoctorDetailAggregationHX: React.FC<DoctorDetailAggregationProps> = (props) => {
  const {
    doctor: { doctorInfo: doctorInfoData },
    dispatch,
    location: { query },
  } = props;
  const { doctorInfo = {}, inquiryServices: serviceList = [] }: any = doctorInfoData;
  // 将query中的字符串undefined转为undefined类型
  Object.keys(query).forEach((key) => {
    if (query[key] === 'undefined') {
      query[key] = undefined;
    }
  });

  const purePlayParamObj: AnyObject = {};

  // 重定向存在，必要参数从purePlayParam里面取（减少路由带的参数）
  if (query.purePlayKey === 'MDT') {
    const purePlayParamArr = decodeURIComponent(query.purePlayParam).split('|');
    purePlayParamArr.forEach((item) => {
      const keyAndValue = item.split('@');
      if (keyAndValue && keyAndValue.length === 2) {
        const objKey = keyAndValue[0];
        const objValue = keyAndValue[1] === 'undefined' ? undefined : keyAndValue[1];
        purePlayParamObj[objKey] = objValue;
      }
    });
  }
  const { whetherAlipay }: any = window;
  const {
    doctorId,
    organCode = 'HID0101',
    servCode = 'jhzy',
    openApp,
    appCode,
    channelCode,
    organId,
    servType,
    teamId,
    mdtTeamId,
    sourceChannel,
  } = query.purePlayKey === 'MDT' ? purePlayParamObj : query;
  const {
    workOrganId,
    organName,
    standardTitleName,
    hospitalTitleName,
    portrait = doctor_defalut_logo,
    doctorName,
    deptName,
    deptId,
    baseDeptCode: deptCode,
    refDeptName,
    refDeptCode,
  } = doctorInfo;

  const docTitle = hospitalTitleName || standardTitleName;
  const { docFillRecordId } = query;
  const speMedOrganId = query?.organId;
  doctorInfo.workOrganId = docFillRecordId ? speMedOrganId : workOrganId;
  // state
  const [guideImageVisible, setGuideImageVisible] = useState(false);
  // ref
  const doctorDetailRef = useRef(null);
  const [followInfo, setFollowInfo] = useState({
    fansCount: 0,
    status: 2,
  });
  const [infoData, setinfoData] = useState({});
  const mixinData = (onlineData) => {
    // 线上线下数据整合
    let allData = {};
    if (onlineData) {
      // 整合线上数据
      const { teamName, organLevel, patientNum, servTimes, fans, teamService, totalAccessNum } = onlineData;
      allData = {
        ...onlineData,
        level: organLevel,
        doctorName: teamName,
        patientCounts: patientNum,
        serviceCounts: servTimes,
        fansCount: fans,
        price: teamService ? teamService.price : '',
        servTime: teamService ? teamService.servTime : '',
        totalAccessNum,
      };
    }
    return allData;
  };
  const getOnlineTeamInfo = () => {
    dispatch({
      type: 'doctor/queryHxDoctorLineInfo',
      payload: {
        appCode: 'HXGYAPP',
        channelCode: 'PATIENT_IOS',
        organCode,
        organId,
        servType,
        teamId,
        pageNum: 1,
        pageSize: 5,
        userId: '',
      },
      callback: (res) => {
        const allData = mixinData(res);
        setinfoData(allData);
      },
    });
  };
  /**
   * 获取医生详情
   * @param isJHZY 是否是聚合主页
   */
  const fetchDoctorInfo = () => {
    return new Promise((_resolve) => {
      dispatch({
        type: 'doctor/queryHxDoctorLineInfo',
        payload: {
          appCode: 'HXGYAPP',
          channelCode: 'PATIENT_IOS',
          organCode: 'HID0101',
          servType: '24',
          sourceChannel,
          teamId: mdtTeamId,
        },
        callback: (res) => {
          try {
            const allData = mixinData(res);
            setinfoData(allData);
          } catch (error) {
            console.log(error);
          }
        },
      });
    });
  };
  const getPageInfoData = () => {
    if (sourceChannel === 'lineMdt') {
      // 来自线上团队数据
      getOnlineTeamInfo();
    }
    if (sourceChannel === 'unLineMdt') {
      // 来自线下团队数据
      fetchDoctorInfo().then(() => {});
    }
    history.listen((location, action) => {
      if (action !== 'POP') {
        const param = 'color=white';
        AppScheme.changeColor(param);
        AppScheme.removeShare();
      }
    });
  };
  useEffect(() => {
    return () => {
      AppScheme.removeShare();
    };
  }, []);
  // const getFollowStatus = () => {
  //   return new Promise((resolve) => {
  //     const payload = sourceChannel === 'lineMdt' ? { doctorId: teamId, sourceChannel } : { doctorId, sourceChannel };
  //     dispatch({
  //       type: 'appointment/followStatus',
  //       payload,
  //       callback: (res: any = {}) => {
  //         getPageInfoData();
  //       },
  //     });
  //   });
  // };

  /** 给原生二维码传参 */
  const doAddShareToApp = () => {
    const pathData = {
      sourceChannel,
      doctorId,
      teamId,
      mdtTeamId,
      servType,
      organId,
      organCode,
    };
    const otherData = {
      portrait,
      doctorName,
      docTitle,
      organName,
      deptName,
      servCode,
    };
    addShareToApp(pathData, otherData);
  };

  const addEventListener = () => {
    window.addEventListener('pageshow', function () {
      doAddShareToApp();
      const param = 'color=white';
      AppScheme.changeColor(param);
    });
    window.addEventListener('pagehide', function () {
      const param = 'color=white';
      AppScheme.changeColor(param);
      AppScheme.removeShare();
    });
  };

  /** 设置分享给朋友的样式 */
  const defineShareAppStyle = () => {
    // 在线门诊医生主页中的快速问诊【固定排班】分享后显示在线门诊业务 （需求by龚蓉蓉+康惠子）
    const link = getCurEvnHref().replace('gdpb', 'zxmz');
    dispatch({
      type: 'global/singWechatJSSDK',
      payload: {
        url: getCurEvnHref(),
      },
      callback: (data: SingWechatJSSDKDataType) => {
        const hospitalName = doctorInfo.organCode === 'HID0101' ? '四川大学华西医院/四川大学华西互联网医院' : '';
        const title = `${doctorName}-${deptName}-${docTitle}-${hospitalName} `;
        const desc = `${organName} ${deptName}`;

        const configData = {
          ...data,
          debug: false,
          jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'],
        };

        const shareData = {
          title,
          desc,
          imgUrl: portrait,
          link,
          success: () => {},
        };
        configWechatShareStyle(configData, shareData);
      },
    });
  };

  useEffect(() => {
    setTimeout(() => {
      addEventListener();
      getPageInfoData();
      // getFollowStatus();
    }, 300);
    return () => {
      // 清除原生二维码传参
    };
  }, []);

  /** 微信定义分享给朋友的样式 */
  useEffect(() => {
    console.log('wechatStyle');
    isWechat() && !_.isEmpty(doctorInfo) && defineShareAppStyle();
  }, [infoData]);

  useEffect(() => {
    if (_.isEmpty(infoData) || openApp === 'true') {
      return;
    }
    doAddShareToApp();
  }, [infoData]);
  /** 关注医生 */
  const onFavorite = async (value: number) => {
    // 区分不同参数

    let payload = {};
    if (sourceChannel === 'lineMdt') {
      payload = {
        actionType: value,
        teamId,
        sourceChannel,
      };
    } else {
      payload = {
        actionType: value,
        teamId: mdtTeamId,
        sourceChannel,
        doctorId,
      };
    }
    await dispatch({
      type: 'appointment/followAction',
      payload,
      callback: (data: any = {}) => {
        const { status } = data;
        if (status === 1) {
          Toast.info('您已成功关注该医生', 1);
        } else if (status === 2) {
          Toast.info('已取消关注该医生', 1);
        }
        getPageInfoData();
      },
    });
  };
  const goDepartment = () => {
    // 支付宝小程序不跳转
    if (whetherAlipay) {
      return;
    }
    history.push({
      pathname: `/department/detail/${deptId}`,
      query: {
        appCode,
        channelCode,
        organCode,
        deptId,
        deptName,
        deptCode,
        refDeptName,
        deptCodes: refDeptCode ? refDeptCode.join(',') : '',
      },
    });
  };
  const goBuyTeamService = () => {
    const param = 'color=white';
    AppScheme.changeColor(param);
    AppScheme.removeShare();
    const { teamService = {}, organId, servType, teamId, doctorType, teamName, organCode = '' } = infoData;
    const { numLimit = '', price = '', servTime } = teamService;
    setTimeout(() => {
      window.location.href = `${COMMON_DOMAIN}/mobile/online/verify?price=${price}&servTime=${servTime}&organId=${organId}&servType=${servType}&doctorId=${teamId}&doctorType=${doctorType}&numLimit=${numLimit}&doctorName=${teamName}&organCode=${organCode}&`;
    }, 200);
  };
  return (
    <div className={styles.container} ref={doctorDetailRef} key={doctorId}>
      <GuideToExternalBrowser
        visible={guideImageVisible}
        onClick={() => {
          setGuideImageVisible(false);
        }}
      />
      <div className={styles.headerBg}>
        <img src={doctor_header_bg} alt="" />
      </div>
      <div>
        <TopInfo
          doctorInfo={doctorInfoData}
          onFavorite={onFavorite}
          goDepartment={goDepartment}
          followInfo={followInfo}
          infoData={infoData}
          sourceChannel={sourceChannel}
        />
        {infoData.entryCriteria ? (
          <Tour
            image={ICON1}
            content={infoData.entryCriteria}
            contentRender={(txt, btn) => {
              return (
                <div>
                  <img src={ICON1} alt="" />
                  {txt}

                  {btn}
                </div>
              );
            }}
          />
        ) : null}
        {sourceChannel === 'lineMdt' ? (
          <div className={styles.teamService}>
            <div className={styles.title}>线上问诊</div>
            <div className={styles.content}>
              <div className={styles.left}>
                <img src={icon_team} className={styles.img} alt="" />
                <div className={styles.detail}>
                  <div className={styles.service}>
                    {servType
                      ? servType.toString() === '24'
                        ? '线上多学科综合诊疗门诊'
                        : '线上专家团队诊疗服务'
                      : '线上专家团队诊疗服务'}
                  </div>
                  <div className={styles.serviceContent}>
                    {servType
                      ? servType.toString() === '24'
                        ? '多位专家与患者面对面看诊'
                        : '权威专家团队为您提供优质医疗服务'
                      : '权威专家团队为您提供优质医疗服务'}
                  </div>
                  <div className={styles.price}>
                    ￥{infoData.price}/{infoData.servTime}小时
                  </div>
                </div>
              </div>
              <div className={styles.right} onClick={goBuyTeamService}>
                申请
              </div>
            </div>
          </div>
        ) : (
          <div>
            {doctorId ? (
              <Appointment {...props} doctorId={doctorId} />
            ) : (
              <div className={styles.noNetTips}>
                <img src={NoNetTips} />
                <div>{infoData.reminder}</div>
              </div>
            )}
          </div>
        )}

        <div>
          <PITeam data={infoData.teamMemberList ? infoData.teamMemberList : []} sourceChannel={sourceChannel} />
        </div>
        <div className={styles.access2}>
          <div className={styles.title}>服务内容</div>
          <div>
            <img src={infoData.content} style={{ width: '100%' }} alt="" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default connect(({ doctor, loading }: { doctor: DoctorModelState; loading: Loading }) => ({
  doctor,
  detailLoading: loading.effects['doctor/queryHxDoctorInfo'],
  followStatusLoading: loading.effects['appointment/followStatus'],
  commentsLoading: loading.effects['doctor/getDoctorCommentListNew'],
  healthLoading: loading.effects['department/getDepartmentHealthList'],
}))(DoctorDetailAggregationHX);
