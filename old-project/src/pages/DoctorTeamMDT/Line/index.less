.container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  padding: 2rem 0.86rem;
  // padding-top: 2rem;
  // padding-bottom: 2.83rem;
  font-size: 1.08rem;
  background-color: #f5f6fa;

  .headerBg {
    position: absolute;
    top: 0;
    left: 0;
    width: 94%;
    background: #ecf8f6;

    img {
      width: 100%;
    }
  }

  .notice {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0.86rem 0;
    padding: 0 0.86rem;
    background-color: #fff;
    border-radius: 12px;

    .doctorNotice {
      width: 3rem;
      height: 1.86rem;
      margin-right: 0.57rem;
    }

    .noticeCont {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: space-between;
      padding: 1rem 0.5rem 1rem 1rem;
      color: #03081a;

      .introContent {
        display: -webkit-box;
        margin-left: 0.2rem;
        overflow: hidden;
        color: #03081a;
        font-size: 1rem;
        text-overflow: ellipsis;

        /*! autoprefixer: off */
        -webkit-box-orient: vertical;

        /* autoprefixer: on */
        -webkit-line-clamp: 1;
      }

      .noticeIcon {
        width: 13px;
        height: 21px;
        margin-left: 3px;
      }
    }
  }

  .health {
    margin: 0.86rem 0;
    border-radius: 12px;

    .healthList {
      background: #fff;
      border-radius: 12px;

      .title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0.86rem;

        .patientComment {
          padding-left: 22px;
          color: #03081a;
          font-weight: 600;
          font-size: 36px;
        }

        .allComment {
          display: flex;
          flex-direction: row;
          align-items: center;
          padding-right: 24px;
          color: #989eb4;
          font-size: 26px;

          .more {
            margin-left: 3px;
            font-size: 22px;
          }
        }
      }
    }

    .title {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      padding: 32px 24px 32px 24px;

      .left {
        color: #03081a;
        font-weight: 600;
        font-size: 36px;
        // line-height: 50px;
      }

      .right {
        display: flex;
        flex-direction: row;
        align-items: center;
        color: #989eb4;
        font-size: 26px;

        .more {
          margin-left: 3px;
          font-size: 22px;
        }
      }
    }
  }

  .bottomFn {
    position: fixed;
    bottom: 2rem;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 1.43rem;

    .btn {
      width: 9.14rem;
      height: 2.86rem;
      padding-left: 1.5rem;
      color: #fff;
      line-height: 2.86rem;
      background-color: #3ad3c1;
      border-radius: 1.43rem;
      box-shadow: 0 0.14rem 0.29rem 0 rgba(58, 211, 193, 0.5);

      img {
        width: 2.14rem;
        height: 2.14rem;
        margin-right: 0.57rem;
      }
    }
  }
}
.access {
  padding: 1rem;
  color: #03081a;
  font-weight: 400;
  font-size: 1rem;
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 1.86rem;
  background: #fff;
  border-radius: 0.57rem;
  img {
    width: 5.4rem;
    height: 1.86rem;
  }
}
.access2 {
  margin-top: 1rem;
  padding: 1rem;
  color: #03081a;
  font-weight: 400;
  font-size: 1rem;
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 1.86rem;
  background: #fff;
  border-radius: 0.57rem;
  .title {
    height: 1.57rem;
    margin-bottom: 1rem;
    color: #03081a;
    font-weight: 600;
    font-size: 1.14rem;
    font-family: PingFangSC-Semibold, PingFang SC;
    line-height: 1.57rem;
  }
}
.teamService {
  margin-top: 0.6rem;
  padding: 1rem;
  background: #fff;
  border-radius: 8px;
  .title {
    color: #03081a;
    font-weight: 600;
    font-size: 36px;
  }
  .content {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.6rem;
    padding: 0.6rem;
    background-color: #fafafa;
    .left {
      display: flex;
      flex-direction: row;
      .img {
        width: 3rem;
        height: 3rem;
      }
      .detail {
        max-width: 12rem;
        margin-left: 0.6rem;
        word-break: break-all;
        .service {
          color: #333;
          font-weight: bold;
          font-size: 1rem;
        }
        .serviceContent {
          color: #666;
          font-size: 0.75rem;
        }
        .price {
          color: #f47f1b;
          font-size: 1rem;
        }
      }
    }
    .right {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      width: 3.8rem;
      height: 2rem;
      color: #fff;
      font-size: 1rem;
      background: linear-gradient(to right, #3ad3c1, #68e9db);
      border-radius: 0.8rem;
    }
  }
}
.noNetTips {
  display: flex;
  align-items: center;
  width: 25.07rem;
  height: 2.71rem;
  margin-top: 0.86rem;
  padding-right: 0.9rem;
  color: #fff;
  font-weight: 500;
  font-size: 0.8rem;
  font-family: PingFangSC-Medium, PingFang SC;
  text-align: left;
  background: linear-gradient(90deg, #fbbc44 0%, #fedf7a 100%);
  border-radius: 0.57rem;
  img {
    width: 1.3rem;
    height: 1.3rem;
    margin-right: 0.6rem;
    margin-left: 0.9rem;
  }
}
