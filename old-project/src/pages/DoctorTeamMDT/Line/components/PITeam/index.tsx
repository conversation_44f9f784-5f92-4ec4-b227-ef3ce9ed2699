import React, { useState, useEffect, FC, useCallback } from 'react';
import { history } from 'umi';
import useEmblaCarousel from 'embla-carousel-react';
import { Popup } from 'antd-mobile-v5';
import IconClose from '@/assets/IconClose.png';
import { DownOutline, UpOutline } from 'antd-mobile-icons';
import styles from './index.less';
import avatar from '../../../../../assets/speMedicine/default.jpg';

const goodAtIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-main-goodat.png';
interface PageProps {
  data: any;
  sourceChannel: string;
}
const App: FC<PageProps> = (props) => {
  const { sourceChannel } = props;
  const [visible, setVisible] = useState(false);
  const goInfo = () => {
    setVisible(true);
  };

  const [emblaRef, emblaThumbs] = useEmblaCarousel({
    containScroll: 'keepSnaps',
    draggable: true,
    selectedClass: 'is-selected',
  });
  const onThumbClick = useCallback(
    async (item: any) => {
      const { deptCode, doctorId, hospitalAreaCode, deptCategoryCode } = item;
      const pathname = '/doctor/hxhome';
      history.push({
        pathname,
        query: {
          hospitalAreaCode,
          deptCode,
          deptCategoryCode,
          doctorId,
          selectedIndex: sourceChannel === 'unLineMdt' ? 2 : 1,
          sourceChannel,
          teamName: '',
        },
      });
    },
    [emblaThumbs],
  );
  return (
    <div className={styles.main}>
      <div className={styles.teamName}>团队成员</div>
      <div ref={emblaRef}>
        <div className={`${styles.doctor_box}`}>
          {props.data.map((item) => (
            <div key={item.doctorId} className={styles.docotor_item} onClick={() => onThumbClick(item)}>
              <img src={item.portrait ? item.portrait : avatar} alt="" />
              <div className={styles.doctorName}>{item.doctorName}</div>
            </div>
          ))}
        </div>
      </div>
      <div className={styles.btn} onClick={goInfo}>
        展开详情 <DownOutline />
      </div>
      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false);
        }}
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          minHeight: '40vh',
          overflowY: 'scroll',
        }}
      >
        <div>
          <div className={styles.boxheader}>
            <span>团队成员</span>
            <img
              src={IconClose}
              alt=""
              onClick={() => {
                setVisible(false);
              }}
            />
          </div>
          <div className={styles.teamMembers}>
            {props.data.map((item) => {
              return (
                <div className={styles.teamMembersItem} onClick={() => {}} key={item.doctorId}>
                  <img className={styles.portrait} src={item.portrait ? item.portrait : avatar} alt="" />
                  <div className={styles.info}>
                    <div>
                      <span className={styles.doctorName}> {item.doctorName}</span>
                      <span className={styles.titleName}>{item.title}</span>
                    </div>
                    <div>
                      <span className={styles.titleName} style={{ marginLeft: 0 }}>
                        {' '}
                        {item.deptName}
                      </span>
                    </div>
                    <div>
                      <div className={styles.profession}>
                        {' '}
                        <img src={goodAtIcon} alt="" className={styles.goodAt2} />
                        {item.profession}
                      </div>
                    </div>
                    <div
                      className={styles.btn}
                      onClick={() => {
                        const { deptCode, doctorId, hospitalAreaCode, deptCategoryCode } = item;
                        const pathname = '/doctor/hxhome';
                        // 埋点
                        history.push({
                          pathname,
                          query: {
                            hospitalAreaCode,
                            deptCode,
                            deptCategoryCode,
                            doctorId,
                            selectedIndex: 2,
                            sourceChannel,
                            teamName: '',
                          },
                        });
                      }}
                      style={{ marginLeft: '16px', border: '0.07rem solid #3AD3C1;', color: '#3AD3C1' }}
                    >
                      预约挂号
                    </div>
                    <div
                      className={styles.btn}
                      onClick={() => {
                        const { deptCode, doctorId, hospitalAreaCode, deptCategoryCode } = item;
                        const pathname = '/doctor/hxhome';
                        // 埋点
                        history.push({
                          pathname,
                          query: {
                            hospitalAreaCode,
                            deptCode,
                            deptCategoryCode,
                            doctorId,
                            selectedIndex: 1,
                            sourceChannel,
                            teamName: '',
                          },
                        });
                      }}
                      style={{ border: '0.07rem solid #FBBC44;', color: '#FBBC44' }}
                    >
                      线上门诊
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </Popup>
    </div>
  );
};
export default App;
