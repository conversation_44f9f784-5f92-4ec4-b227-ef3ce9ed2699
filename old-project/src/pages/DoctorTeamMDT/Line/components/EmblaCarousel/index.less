.embla {
  position: relative;
  max-width: 100%;
  background-color: #fff;
}

.embla__viewport {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.embla__viewport.is_draggable {
  cursor: move;
  cursor: grab;
}

.embla__viewport.is_dragging {
  cursor: grabbing;
}

.embla__container {
  display: flex;
  -khtml-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

.embla__slide__img {
  position: absolute;
  top: 50%;
  left: 50%;
  display: block;
  width: auto;
  min-width: 100%;
  max-width: none;
  min-height: 100%;
  transform: translate(-50%, -50%);
}

.embla_thumb {
  margin-top: 0.223rem;
  padding-top: 0;
}

.embla__slide {
  margin-bottom: 1rem;

  span {
    margin-top: 0.056rem;
  }
}

.embla__slide {
  .embla__slide__inner__selected {
    height: 100%;
  }
}

.embla__slide__inner {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  //justify-content: space-between;
  width: 6.14rem;
  margin-right: 0.71rem;
  padding: 1rem 0;
  font-size: 1rem;
  border: 0.019rem solid #eaecf5;
  border-radius: 0.43rem;
}

.embla__slide__selected {
  //height: 30vw;

  .embla__slide__inner__selected {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 6.14rem;
    margin-right: 0.71rem;
    padding: 1rem 0;
    font-size: 1rem;
    //border: 0.019rem solid rgba(86, 141, 242, 0.6);
    background: url('../../../../../assets/checked.png') no-repeat;
    background-size: 100% 100%;
    border-radius: 0.43rem;
    height: 7.6rem;
  }
}

.embla__slide__thumbnail {
  width: 3.29rem;
  height: 3.29rem;
}

.advisory {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  padding: 0.86rem;
  background: #f7faff;
  border: 0.019rem solid rgba(86, 141, 242, 0.35);
  border-radius: 0.57rem;
  margin-top: 14px;

  font-size: 0.24rem;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #989eb4;

  img {
    width: 1.823rem;
  }

  .serviceDesc {
    font-size: 0.86rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #989eb4;
    line-height: 1.43rem;
  }

  .advisoryInfo {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;

    .serviceName {
      font-weight: 600;
      font-size: 1rem;
      color: #03081a;
      font-family: PingFangSC-Medium, PingFang SC;
    }

    .servicePrice {
      margin-left: -4rem;
      color: #f47f1b;
      font-weight: 500;
      font-size: 1rem;
    }

    .button_green {
      width: 4rem;
      height: 1.86rem;
      color: #fff;
      font-size: 1rem;
      line-height: 1.86rem;
      text-align: center;
      background: linear-gradient(90deg, #3ad3c1 0%, #68e9db 100%);
      border: none;
      border-radius: 0.93rem;

      .arrow {
        margin-left: 0.149rem;
        color: #fff;
        font-size: 0.372rem;
      }
    }

    .button_gray {
      width: 4rem;
      height: 1.86rem;
      color: #fff;
      font-size: 1rem;
      line-height: 1.86rem;
      text-align: center;
      background: #b0b3bf;
      border: none;
      border-radius: 0.93rem;
    }
  }

  &:last-child {
    margin-bottom: 0.186rem;
  }
}
