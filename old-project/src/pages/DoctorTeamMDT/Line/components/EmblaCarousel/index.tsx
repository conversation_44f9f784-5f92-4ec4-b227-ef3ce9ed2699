/*
 * @Author: <PERSON>
 * @Date: 2022-03-21 02:35:02
 * @LastEditor: <PERSON>
 * @LastEditTime: 2022-03-29 21:03:41
 * @FilePath: /hyt-person/src/pages/DoctorDetailAggregationHX/Home/components/EmblaCarousel/index.tsx
 * Copyright (c) 2022 by <PERSON>, All Rights Reserved.
 * @Description:
 */

import React, { useState, useEffect, useCallback } from 'react';
import useEmblaCarousel from 'embla-carousel-react';
import styles from './index.less';
import request from '@/utils/request';
import { Modal } from 'antd-mobile';

interface EmblaCarouselProps {
  serviceList: Array<any>;
  btnOnClick: (item: any) => void;
  servCode: string;
  doctorId: string;
  organCode: string;
}

interface ContentProps {
  selected: boolean;
  onClick: () => void;
  imgSrc: string;
  name: string;
  // key: string;
}

const Thumb: React.FC<ContentProps> = ({ selected, onClick, imgSrc, name, key }) => (
  <div className={`${selected ? styles.embla__slide__selected : styles.embla__slide}`} key={key}>
    <div
      onClick={onClick}
      className={`${selected ? styles.embla__slide__inner__selected : styles.embla__slide__inner}`}
    >
      <img className={styles.embla__slide__thumbnail} src={imgSrc} alt="" />
      <span>{name}</span>
    </div>
  </div>
);

const EmblaCarousel: React.FC<EmblaCarouselProps> = (props) => {
  const { serviceList, btnOnClick, servCode, doctorId, organCode } = props;

  const [selectedIndex, setSelectedIndex] = useState(0);
  const [applayDisabled, setApplayDisabled] = useState(false);
  const [selectedItem, setSelectedItem] = useState(serviceList[0]);

  const [emblaRef, emblaThumbs] = useEmblaCarousel({
    containScroll: 'keepSnaps',
    draggable: true,
    selectedClass: 'is-selected',
  });
  useEffect(() => {
    if (servCode === 'gdpb') {
      const flag = serviceList.findIndex((e) => {
        return e.servCode === 'gdpb';
      });
      if (flag > -1) {
        setSelectedIndex(flag);
      }
    }
  }, [servCode]);
  const [whetherThereIsANumber, set_whetherThereIsANumber] = useState(true);

  /*获取是否可以约诊 购买*/
  const get_an_appointment = async () => {
    let res = await request('/gdpbSchedule/cheakGdpbSchedulePossibleToBuy', {
      method: 'GET',
      params: {
        doctorId,
        organCode,
      },
      prefix: PLATFORM_TRANSFORM,
    });
    set_whetherThereIsANumber(res.check);
    if (!res.check) {
      Modal.alert('', res.tips, [
        {
          text: '确定',
          onPress: () => {
            /*排班约诊没有号源 默认展示线上门诊*/
            let index = serviceList.findIndex((v) => v.servCode == 'zxmz');

            if (index != -1) {
              setSelectedIndex(index);
              setSelectedItem(serviceList[index]);
              set_whetherThereIsANumber(true);
            }
          },
        },
      ]);
      // return Toast.fail(res.tips, 1);
    }
  };
  useEffect(() => {
    if (serviceList[0].serviceName == '排班约诊') {
      get_an_appointment();
    }
  }, []);
  const onThumbClick = useCallback(
    async (index, item) => {
      console.log(item.serviceName);
      if (item.serviceName == '排班约诊') {
        await get_an_appointment();
      } else {
        set_whetherThereIsANumber(true);
      }
      if (!emblaThumbs) return;
      if (emblaThumbs.clickAllowed()) {
        emblaThumbs.scrollTo(index);
        setSelectedIndex(index);
        setSelectedItem(serviceList[index]);
      }
    },
    [emblaThumbs],
  );

  /* 获取电话问诊需要传的参数 */
  let itemObj = {};
  if (selectedItem.dcInquiryServiceConfigExpandEntity) {
    const { dcInquiryServiceConfigExpandEntity: item } = selectedItem;
    // console.clear()
    console.log(item);
    const { servType, price, servTime, numLimit, status, serviceId } = selectedItem;
    itemObj = {
      servType,
      price: item.price,
      servTime,
      numLimit: item.phoneTime,
      status,
      serviceId,
      phoneTime: item.phoneTime,
      isTel: 1 /* 判断是否是电话问诊 */,
      level: sessionStorage.getItem('docTitle'),
    };
  }
  const renderTxt = (str: string) => {
    if (!str) {
      return <div className={styles.serviceDesc} />;
    }
    if (str.includes('<br/>')) {
      return str.split('<br/>').map((notice: string) => {
        return (
          <div className={styles.serviceDesc} key={notice}>
            {notice}
          </div>
        );
      });
    }
    if (str.includes('<br />')) {
      return str.split('<br />').map((notice: string) => {
        return (
          <div className={styles.serviceDesc} key={notice}>
            {notice}
          </div>
        );
      });
    }
    return <div className={styles.serviceDesc}>{str}</div>;
  };
  return (
    <>
      <div className={`${styles.embla} `}>
        <div className={styles.embla__viewport} ref={emblaRef}>
          <div className={`${styles.embla__container}`}>
            {serviceList.map((item, index) => (
              <Thumb
                key={item.servCode}
                onClick={() => onThumbClick(index, item)}
                selected={index === selectedIndex}
                imgSrc={item.img}
                name={item.serviceName == '线上排班约诊' ? '排班约诊' : item.serviceName}
              />
            ))}
          </div>
        </div>
      </div>

      {
        // JSON.stringify(selectedItem)
        selectedItem.dcInquiryServiceConfigExpandEntity && selectedItem.servCode == 'gdpb' && (
          <div className={styles.advisory}>
            <div className={styles.advisoryInfo}>
              <div className={styles.serviceName}>电话问诊</div>
              <div className={styles.servicePrice}>{`¥${
                selectedItem.dcInquiryServiceConfigExpandEntity.price === undefined
                  ? '-'
                  : selectedItem.dcInquiryServiceConfigExpandEntity.price
              }/${
                selectedItem.dcInquiryServiceConfigExpandEntity.phoneTime &&
                selectedItem.dcInquiryServiceConfigExpandEntity.phoneTime
              }分钟`}</div>
              <div
                className={
                  selectedItem.status === 0 || applayDisabled || !whetherThereIsANumber
                    ? styles.button_gray
                    : styles.button_green
                }
                onClick={() => {
                  whetherThereIsANumber && btnOnClick && btnOnClick(itemObj);
                }}
              >
                申请
              </div>
            </div>
            {selectedItem?.extendDesc ? renderTxt(selectedItem?.extendDesc) : null}
          </div>
        )
      }
      {selectedItem.isRenderServiceConfig == 1 && (
        <div className={styles.advisory}>
          <div className={styles.advisoryInfo}>
            <div className={styles.serviceName}>
              {selectedItem.serviceName === '排班约诊'
                ? '图文问诊'
                : selectedItem.serviceName === '线上门诊'
                ? '图文问诊'
                : selectedItem.serviceName}
            </div>
            {/* <div className={styles.serviceName}>图文问诊 </div> */}
            <div className={styles.servicePrice}>{`¥${selectedItem.price === undefined ? '-' : selectedItem.price}/${
              selectedItem.servTime === undefined ? '-' : selectedItem.servTime
            }小时(${selectedItem.numLimit === undefined ? '-' : selectedItem.numLimit}条)`}</div>
            <div
              className={
                selectedItem.status === 0 || applayDisabled || !whetherThereIsANumber
                  ? styles.button_gray
                  : styles.button_green
              }
              onClick={() => {
                whetherThereIsANumber && btnOnClick && btnOnClick(selectedItem);
              }}
            >
              申请
            </div>
          </div>
          {selectedItem?.serviceDesc ? renderTxt(selectedItem?.serviceDesc) : null}
        </div>
      )}
      {/*  <Modal
        visible={whetherThereIsANumber}
        title="提示"
        transparent
        maskClosable={false}
        footer={[
          {
            text: '确认',
            onPress: () => {
            },
          },
        ]}
      >55555555</Modal>*/}
    </>
  );
};

export default EmblaCarousel;
