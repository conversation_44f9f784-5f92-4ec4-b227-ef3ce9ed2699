/*
 * @Author: <PERSON>
 * @Date: 2022-03-17 11:15:04
 * @LastEditor: <PERSON>
 * @LastEditTime: 2022-04-24 10:33:51
 * @FilePath: /hyt-person/src/pages/DoctorDetailAggregationHX/Home/components/TopInfo/index.tsx
 * Copyright (c) 2022 by <PERSON>, All Rights Reserved.
 * @Description:
 */
import React from 'react';

import { DoctorInfoType } from '@/typings/doctor.d';
import { HxDoctorAvatar } from '@/components';
import { history } from 'umi';
import qs from 'query-string';
import styles from './index.less';

const followIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-follow.png';

const arrowRightBlackIcon =
  'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-home-arrow-right-black.png';
const arrowRightIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-info-arrow-right.png';
const rareIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-rare-disease.png';
const introduceIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-main-intro.png';
const goodAtIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-main-goodat.png';

// const arrow_circle = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/arrow_circle%402x.png';

interface TopInfoProps {
  /** 医生相关数据 */
  doctorInfo: DoctorInfoType;
  /** 关注医生 */
  onFavorite: (followStatus: number) => void;
  /** 跳转至科室页面 */
  goDepartment: () => void;
  followInfo: {
    fansCount: number;
    status: number;
  };
  infoData: any;
  sourceChannel: string;
}

const TopInfo: React.FC<TopInfoProps> = ({ onFavorite, goDepartment, sourceChannel, infoData }) => {
  const {
    patientCounts,
    satisfaction,
    serviceCounts,
    totalAccessNum,
    todayAccessNum,
    portrait,
    doctorName,
    organName,
    introduction,
    level,
    deptName,
    followStatus,
    fans,
  } = infoData;
  const goInfo = () => {
    window.localStorage.setItem('__MDTINFO', qs.stringify(infoData));
    history.push({
      pathname: '/doctor/hxhomemdtLineInfo',
    });
  };
  return (
    <div>
      <div className={styles.subscribe}>
        <span>
          累计访问:&nbsp; {totalAccessNum > 9999 ? Number(totalAccessNum / 10000).toFixed(1) : totalAccessNum}
          {totalAccessNum > 9999 ? '万' : ''}
        </span>
        <span style={{ marginLeft: '20px' }}>
          今日访问:&nbsp; {todayAccessNum > 9999 ? Number(todayAccessNum / 10000).toFixed(1) : todayAccessNum}
          {todayAccessNum > 9999 ? '万' : ''}
        </span>{' '}
        <span style={{ marginLeft: '20px' }}>
          关注数:&nbsp; {infoData.fans > 9999 ? Number(infoData.fans / 10000).toFixed(1) : infoData.fans}
          {infoData.fans > 9999 ? '万' : ''}
        </span>
      </div>
      <div key={doctorName} className={styles.container}>
        <div className={styles.avatarContainer}>
          <HxDoctorAvatar src={portrait} alt="" className={styles.avatar} />
          {infoData.followStatus === 2 ? (
            <div className={styles.favoriteCont} onClick={() => onFavorite && onFavorite(1)}>
              <div className={styles.favorite}>
                <img src={followIcon} alt="" className={styles.addIcon} />
                关注
              </div>
            </div>
          ) : (
            <div className={styles.selectFavorite} onClick={() => onFavorite && onFavorite(2)}>
              已关注
            </div>
          )}
        </div>
        <div className={styles.mainInfo}>
          <p>
            <span className={styles.name}>{doctorName}</span>
          </p>
          <p onClick={goDepartment}>
            <span>{deptName}</span>
            {/* <img className={styles.arrowR} src={arrowRightBlackIcon} alt="" /> */}
          </p>
          <p>
            {level && <span className={styles.rank}>{level}</span>} <span className={styles.hospital}>{organName}</span>
          </p>
        </div>
        {sourceChannel === 'unLineMdt' ? null : (
          <div className={styles.time}>
            <p>
              <span>问诊数</span>
              <span>
                {serviceCounts
                  ? serviceCounts > 9999
                    ? `${Number(serviceCounts / 10000).toFixed(0)}万`
                    : serviceCounts
                  : '-'}
              </span>
            </p>
            <p>
              <span>
                服务患者<span style={{ fontSize: '0.71rem' }}>(线上)</span>
              </span>
              <span>
                {patientCounts
                  ? patientCounts > 9999
                    ? `${Number(patientCounts / 10000).toFixed(0)}万`
                    : patientCounts
                  : '-'}
              </span>
            </p>
            <p>
              <span>好评率</span>
              <span>{satisfaction || '0'}%</span>
            </p>
          </div>
        )}
        {/* 医生擅长和简介 */}
        <div className={styles.intro}>
          {/* <div className={styles.introduce}>
            <img src={goodAtIcon} alt="" className={styles.goodAt} />
            <span className={styles.introContent}>{profession || '-'}</span>
          </div> */}
          <div className={styles.introduce}>
            <img src={introduceIcon} alt="" className={styles.goodAt} />
            <span className={styles.introContent}>{introduction || '-'}</span>
          </div>
        </div>

        <div className={styles.more} onClick={() => {}}>
          <span onClick={goInfo}>查看更多</span>
          <img src={arrowRightIcon} alt="" />
        </div>
      </div>
    </div>
  );
};

export default TopInfo;
