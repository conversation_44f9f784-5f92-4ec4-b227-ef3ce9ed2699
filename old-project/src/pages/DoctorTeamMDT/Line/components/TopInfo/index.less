.subscribe {
  position: relative;
  height: 1.81rem;
  padding-left: 0.75rem;
  color: #fff;
  //font-weight: 600;
  font-size: 0.86rem;
  font-family: PingFangSC-Medium, PingFang SC;
  line-height: 1.81rem;
  background: #6be6dd;
  border-radius: 0.5rem 0.5rem 0 0;
}

.container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  margin: 0 0 0.86rem 0;
  padding: 0.86rem;
  background-color: #fff;
  //background: #ecf8f6;
  border-radius: 0 0 0.5rem 0.5rem;

  .mainInfo {
    display: flex;
    flex: 1;
    flex-direction: column;
    justify-content: space-between;
    padding: 0;
    color: #03081a;
    font-size: 1rem;

    .arrowR {
      height: 1rem;
    }

    p {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 0.2rem;

      &:first-child {
        align-items: baseline;
      }
    }

    .name {
      margin-right: 0.6rem;
      font-weight: 600;
      font-size: 1.29rem;
    }

    .docTitle {
      margin-left: 0.6rem;
      font-size: 1rem;
    }

    .rank {
      margin-right: 0.43rem;
      padding: 0 0.21rem;
      color: #fff;
      font-size: 0.64rem;
      text-align: center;
      background-color: #568df2;
      border-radius: 0.29rem;
    }

    .hospital {
      margin-right: 1.2rem;
      font-size: 1rem;
    }
  }

  .avatarContainer {
    position: absolute;
    top: -0.55rem;
    right: 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 5rem;
    height: 6.1rem;
    // background-color: #fff;

    .avatar {
      width: 5rem;
      height: 5rem;
      margin-top: 0.2rem;
      margin-bottom: -0.93rem;
      overflow: hidden;
      border: 0.2rem solid #fff;
      border-radius: 2.5rem;
    }

    .favoriteCont {
      width: 4.57rem;
      height: 1.86rem;
      color: #3ad3c1;
      font-size: 0.93rem;
      line-height: 1.86rem;
      background: #fff;
      border-radius: 0.93rem;
      box-shadow: 0 0 0.43rem 0 rgba(0, 0, 0, 0.06);

      .favorite {
        display: flex;
        align-items: center;
        padding-left: 0.8rem;
        color: #3ad3c1;

        .addIcon {
          width: 19px;
          height: 19px;
          margin-right: 6px;
        }
      }
    }

    .selectFavorite {
      width: 4.57rem;
      height: 1.86rem;
      color: #d7daea;
      font-size: 0.93rem;
      line-height: 1.86rem;
      text-align: center;
      background-color: #fff;
      border-radius: 0.93rem;
    }
  }

  .time {
    display: flex;
    flex-direction: row;
    align-items: baseline;
    justify-content: space-between;
    width: 100%;
    margin: 0.2rem 0;
    font-size: 0.93rem;

    p {
      display: flex;
      flex-direction: row;
      align-items: baseline;
      justify-content: center;
      margin-bottom: 0.2rem;
      font-size: 0.93rem;

      span {
        &:first-child {
          margin-right: 0.2rem;
          color: #989eb4;
          font-size: 0.93rem;
        }

        &:nth-child(2) {
          color: #03081a;
          font-weight: bold;
          font-size: 1.1rem;
        }
      }
    }
  }

  .intro {
    color: #03081a;
    font-size: 0.93rem;
    //background-color: #fff;

    .introduce {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding: 0.1rem 0;
    }

    .introContent {
      display: -webkit-box;
      margin-left: 0.2rem;
      overflow: hidden;
      color: #03081a;
      text-overflow: ellipsis;

      /*! autoprefixer: off */
      -webkit-box-orient: vertical;

      /* autoprefixer: on */
      -webkit-line-clamp: 1;
    }

    img {
      height: 0.93rem;
      margin-right: 0.2rem;
    }
  }

  .more {
    display: flex;
    align-items: center;
    align-self: flex-end;
    justify-content: center;
    margin-top: 0.42rem;
    color: #989eb4;
    font-size: 0.86rem;

    img {
      width: 1.29rem;
      height: 1.29rem;
    }
  }
}

.HXYLT {
  height: 1.27rem;
  padding: 0 0.5rem;
  color: #fc4553;
  font-weight: 400;
  font-size: 0.64rem;
  font-family: PingFangSC-Regular, PingFang SC;
  line-height: 1.27rem;
  text-align: center;
  background: rgba(252, 69, 83, 0.1);
  border-radius: 0.18rem;
}
