.detailsMain {
  display: flex;
  flex: 1;
  flex-direction: column;
  box-sizing: border-box;
  margin-top: 0.86rem;
  padding: 0.86rem;
  overflow-x: hidden;
  background: #fff;
  border-radius: 12px;

  .headerText {
    box-sizing: border-box;
    height: 80px;
    padding: 0 30px;
    color: #333;
    font-weight: 500;
    font-size: 28px;
    line-height: 80px;
  }

  .main {
    .noMore {
      height: 77px;
      overflow: hidden;
      color: #ddd;
      font-weight: 400;
      font-size: 28px;
      font-family: PingFang SC;
      line-height: 33px;
      text-align: center;

      p {
        margin: 0;
        margin-top: 21px;
      }
    }

    .hospital {
      margin-top: 1rem;
      .hospitalTitle {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .hospitalName {
          color: #03081a;
          font-weight: 500;
          font-size: 1.07rem;
        }

        .spot {
          width: 0.57rem;
          height: 0.57rem;
          margin-right: 0.5rem;
          background: #3ad3c1;
          border-radius: 0.285rem;
        }
      }
      .expand {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100;
        padding: 0.4rem;
        color: #989eb4;
        font-size: 0.93rem;
        background-color: #fbfbfd;

        img {
          width: 1.29rem;
          margin-left: 0.1rem;
        }
      }
    }

    .schedule {
      display: flex;
      flex-direction: column;
      padding: 14px 0 0 0;
      color: #333;
      font-size: 1rem;
      background: #fbfbfd;

      .scheduleItem,
      .subScheduleItem {
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        height: auto;
        padding: 0 30px;
        // border-bottom: 1px solid #eee;
        .borderBottom {
          position: absolute;
          bottom: 0;
          left: 1rem;
          width: calc(~'100% - 2rem');
          height: 1px;
          background-color: #eee;
        }

        .date {
          display: flex;
          flex-direction: column;
          padding-bottom: 20px;
          color: #03081a;

          div:nth-child(2) {
            margin-top: 8px;

            span:nth-child(1) {
              color: #989eb4;
            }

            span:nth-child(2) {
              margin: 0 20px;
              color: #f47f1b;
            }
          }
        }

        .appoinmentBtn {
          display: flex;
          align-items: center;
          color: #999;
          font-size: 1rem;

          .btn {
            min-width: 3.72rem;
            min-height: 2rem;
            padding: 0 8px;
            color: #fff;
            font-size: 1rem;
            line-height: 2rem;
            text-align: center;
            background: linear-gradient(90deg, #b0b3bf 0%, #cdd2e4 100%);
            border: none;
            border-radius: 1rem;
          }

          .activeBtn {
            min-width: 3.72rem;
            min-height: 2rem;
            padding: 0 8px;
            color: #fff;
            font-size: 1rem;
            line-height: 2rem;
            text-align: center;
            background: linear-gradient(90deg, #3ad3c1 0%, #68e9db 100%);
            border: none;
            border-radius: 1rem;
          }

          .redBtn {
            min-width: 3.72rem;
            min-height: 2rem;
            padding: 0 8px;
            color: #fff;
            font-size: 1rem;
            line-height: 2rem;
            text-align: center;
            background: linear-gradient(90deg, #fc4553 0%, #ff7782 100%);
            border: none;
            border-radius: 1rem;
          }

          .activeBtnDown {
            min-width: 140px;
            min-height: 52px;
            padding: 8px 8px;
            color: #fc4553;
            font-size: 0.93rem;
            line-height: 52px;
            text-align: center;
            border: none;
            border-radius: 8px;
          }

          .arrowDown {
            width: 16px;
            height: 10px;
            margin-left: 20px;
          }
        }
      }
    }
  }
}
.docotor-item {
  width: 7rem;
  height: 9.33rem;
  background: #fbfbfd;
  border-radius: 0.5rem;
  img {
    width: 5rem;
    height: 5rem;
    border: 0.17rem solid #fcdda4;
    border-radius: 50%;
  }
}
.hospitalTitle {
  margin-bottom: 0.5rem;
}
.piTeamList {
  display: flex;
  align-items: center;
  width: 100%;
  height: 68px;
  margin: 32px 0 16px 0;
  padding: 16px 32px;
  color: #989eb4;
  font-weight: 400;
  font-size: 24px;
  font-family: PingFangSC-Regular, PingFang SC;
  background: #ebedf5;
  border-radius: 16px;
  img {
    width: 36px;
    height: 36px;
    margin-right: 8px;
  }
}
