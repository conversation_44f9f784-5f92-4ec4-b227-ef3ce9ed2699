/*
 * @Author: <PERSON>
 * @Date: 2022-03-18 16:34:32
 * @LastEditor: <PERSON>
 * @LastEditTime: 2025-01-15 14:21:57
 * @FilePath: /hyt-person/src/pages/DoctorDetailAggregationHX/Home/components/Appointment/index.tsx
 * Copyright (c) 2022 by <PERSON>, All Rights Reserved.
 * @Description:
 */

import React, { PureComponent } from 'react';
import { history, connect, Loading } from 'umi';
import { Dispatch, AnyAction } from 'redux';
import { StyleComponents } from '@/components';
import { getOrganCode } from '@/utils/parameter';
import { HxLocalStorage, HxSessionStorage } from '@/utils/storage';
import qs from 'query-string';
import { StorageEnum } from '@/utils/enum';
import { Toast, Modal } from 'antd-mobile';
import { sensorsRequest } from '@/utils/sensors';
import AppScheme from '@/utils/AppScheme';
import { ISourceOpenData, ISourceItemsRespVos, ISourceItemsDetailData } from '../../../../Appointment/data';
import { secondCountDown } from '../../../../Appointment/utils/countdown';
import styles from './index.less';
import { showCountDownStr } from './_utils';
import CountDown from './countDown';

const { EmptyView } = StyleComponents;

const arrowDownIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/arrow_down.png';
const arrowUpIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/arrow_up.png';
const piTeamIcon = require('../../../../../assets/<EMAIL>');

interface IProps {
  dispatch: Dispatch<AnyAction>;
  doctor: object;
  loading?: boolean;
  location: {
    query: {
      hospitalCode: string;
      sign: string;
      hospitalAreaCode: string;
      tabAreaCode: string;
      departmentCode: string;
      deptCode: string;
      doctorId: string;
      docName: string;
      hospitalName: string;
      deptCategoryCode: string;
      channelElderCode: string; // 老年专区channelCode
      sourceChannel: string;
    };
  };
}

interface IState {
  // showSubSource: string;
  showCountDown: string;
  countDownStr: string;
  visible: boolean;
  hospitalCode: string;
  sign: number;
  hospitalAreaCode: string;
  tabAreaCode: string;
  deptCode: string;
  doctorId: string;
  scheduleData: Array<any>;
  sourceInfo: ISourceOpenData;
  hospitalOpenSourceTime: string;
  tips: string;
  htmlTips: string;
  // docName: string;
  // hospitalName: string;
  listDocName: string;
  listHospitalName: string;
  // hideTime: number;
  // showTime: number;
  sysScheduleId: string;
  currentAvaliable: number;
  followStatus: number;
  showBox: boolean;
  chooseValue: any;
  chooseNthValueKey: string;
  chooseNthValue: any;
  chooseStartNoAndendNo: string;
  reportInterpretationImageVisible: boolean;
  bannerImage: string;
  doctorNotice: string;
  commentList: any;
  workOrganId: string;
  satisfaction: number;
  docInfo: any;
  sourceItemsDetailRespVos?: Array<any>;
  theTeamList: Array<any>;
  deptCategoryCode: string;
  channelElderCode: string;
}

const week = ['周天', '周一', '周二', '周三', '周四', '周五', '周六'];
const scheduleRange = ['上午', '下午', '全天', '夜间门诊'];
const sourceStatus: any = {
  1: '预约',
  2: '约满',
  3: '停诊',
  4: '待放',
};

let timer: any = null;
let timer1: any = null;
class AppointmentSchedule extends PureComponent<IProps, IState> {
  // visibilityChange = document.addEventListener('visibilitychange', () => {
  //   if (document.visibilityState === 'hidden') {
  //     clearInterval(timer);
  //   } else {
  //     const { sysScheduleId, currentAvaliable } = this.state;
  //     if (sysScheduleId !== '') {
  //       this.selSourceOpenTime(sysScheduleId, currentAvaliable);
  //     }
  //   }
  // });

  constructor(props: IProps) {
    super(props);
    this.state = {
      visible: false,
      countDownStr: '',
      showCountDown: '',
      hospitalCode: '',
      sign: 1,
      hospitalAreaCode: '',
      tabAreaCode: '',
      deptCode: '',
      doctorId: '',
      hospitalOpenSourceTime: '',
      tips: '',
      htmlTips: '',
      listDocName: '',
      scheduleData: [],
      sourceInfo: {},
      // hideTime: 0,
      // showTime: 0,
      sysScheduleId: '',
      currentAvaliable: 0,
      showBox: false,
      chooseValue: [],
      chooseNthValueKey: '',
      chooseNthValue: {},
      chooseStartNoAndendNo: '',
      deptCategoryCode: '',
      channelElderCode: '',
    };
  }

  componentDidMount() {
    sensorsRequest('Page_Appoint_DoctorHome', {
      organCode: getOrganCode(),
    });
    const {
      doctor,
      location: {
        query: {
          hospitalCode = '',
          sign = '1',
          hospitalAreaCode = '',
          tabAreaCode = '',
          deptCode = '',
          doctorId: queryDoctorId = '',
          docName,
          hospitalName: listHospitalName = '',
          deptCategoryCode = '',
          channelElderCode = '',
        },
      },
    } = this.props;
    const { doctorId } = this.props;
    const listDocName = docName || doctor?.doctorInfo?.doctorInfo?.doctorName;
    console.log('doctorId', doctorId);
    timer1 = setInterval(() => {
      this.setState({
        countDownStr: showCountDownStr(),
      });
    }, 1000);
    this.setState(
      {
        hospitalCode: hospitalCode || getOrganCode(),
        sign: Number(sign),
        hospitalAreaCode,
        tabAreaCode,
        deptCode,
        doctorId,
        listDocName,
        listHospitalName,
        deptCategoryCode,
        channelElderCode,
      },
      () => {
        if (doctorId) {
          this.fetchData();
        }
        // this.followStatus();
        // this.fetchTheTeamList();
      },
    );
  }

  componentWillUnmount() {
    clearInterval(timer);
    clearInterval(timer1);
    this.setState = () => {
      return false;
    };
  }

  fetchData = () => {
    const { hospitalCode, sign, tabAreaCode, hospitalAreaCode, deptCode, deptCategoryCode, doctorId } = this.state;
    this.props.dispatch({
      type: 'doctor/selHxDoctorDetails',
      payload: {
        appointmentType: sign,
        deptCode, // 科室编码
        deptCategoryCode, // 二级科室编码
        doctorId, // 医生ID
        hospitalAreaCode, // 选中医生的院区编码
        hospitalCode: 'HID0101', // 医院编码
        tabAreaCode, // 选中院区的院区编码
      },
      callback: (data: any) => {
        const { tips = '', htmlTips = '', sourceItems: scheduleData = [] } = data;
        this.setState({
          scheduleData,
          tips,
          htmlTips,
        });
      },
    });
  };

  fetchTheTeamList = () => {
    const { doctorId } = this.state;
    this.props.dispatch({
      type: 'appointment/queryTeamByServTypes',
      payload: {
        docId: doctorId,
        servTypes: [22],
      },
      callback: (res: any = {}) => {
        const { data = [] } = res;
        this.setState({
          theTeamList: data,
        });
      },
    });
  };

  // 查询号源开放情况
  selSourceOpenTime = (sysScheduleId: string, avaliable: number) => {
    const { sign, hospitalCode } = this.state;
    const t1 = Math.floor(new Date().getTime() / 1000);
    this.props.dispatch({
      type: 'appointment/selSourceOpenTime',
      payload: {
        appointmentType: sign,
        hospitalCode: 'HID0101',
        sysScheduleId,
      },
      callback: (data: ISourceOpenData = {}) => {
        this.setState(
          {
            sourceInfo: data,
          },
          () => {
            const { sourceInfo, channelElderCode } = this.state;
            const { hospitalOpenSourceTime = '', second = 0, sourceNoOpenHint } = sourceInfo;
            let cacheSecond: any = second;
            const { sourceChannel, teamName } = this.props.location.query;
            //获取就诊卡
            const patientCardInfo = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA);
            const data = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA);
            const params = { sourceChannel, isMdt: 1, channelElderCode, data: JSON.stringify(data) };
            if (sourceNoOpenHint === 1) {
              const param = 'color=white';
              AppScheme.changeColor(param);
              AppScheme.removeShare();
              const redirect = '/appointment/detail';
              //如果选卡，则直接去病情采集，否则去选卡页面
              if (patientCardInfo) {
                localStorage.removeItem('_MDTINFO'); //清除之前的病情数据
                history.push(`/appointment/mdtinfo?redirect=${redirect}&${qs.stringify(params)}`);
                return;
              }
              window.location.href = `${window.location.origin}/person/patientcard/home?isMdt=1&redirect=${redirect}${
                channelElderCode ? `&channelCode=${channelElderCode}` : ''
              }&channelElderCode=${channelElderCode}&sourceChannel=${sourceChannel}${
                teamName ? `&teamName=${teamName}` : ''
              }`;
            } else if (Number(second) > 0) {
              this.setState(
                {
                  visible: true,
                },
                () => {
                  if (avaliable === 0) {
                    return;
                  }
                  timer = setInterval(() => {
                    const t2 = Math.floor(new Date().getTime() / 1000);
                    const t_time = t2 - t1;
                    if (t_time <= second) {
                      this.setState({
                        showCountDown: secondCountDown((cacheSecond -= 1)),
                        hospitalOpenSourceTime,
                      });
                    } else {
                      clearInterval(timer);
                    }
                  }, 1000);
                },
              );
            } else {
              const redirect = '/appointment/detail';
              if (patientCardInfo) {
                localStorage.removeItem('_MDTINFO');
                history.push(`/appointment/mdtinfo?redirect=${redirect}&${qs.stringify(params)}`);
                return;
              }
              window.location.href = `${window.location.origin}/person/patientcard/home?isMdt=1&redirect=${redirect}${
                channelElderCode ? `&channelCode=${channelElderCode}` : ''
              }&channelElderCode=${channelElderCode}&sourceChannel=${sourceChannel}${
                teamName ? `&teamName=${teamName}` : ''
              }`;
            }
          },
        );
      },
    });
  };

  // 判断是否存在时段号源
  sourceClickItem = (value: ISourceItemsRespVos) => {
    this.setState(
      {
        chooseValue: value,
      },
      () => {
        const { isPrecise, status, sysScheduleId } = value;
        if (isPrecise === 1 && status === 1) {
          this.props.dispatch({
            type: 'appointment/getSelTimeSchedule',
            payload: {
              sysScheduleId,
              hospitalCode: getOrganCode(),
            },
            callback: (res: any) => {
              if (res.sourceItemsDetailRespVos) {
                const { sourceItemsDetailRespVos = [] } = res;
                this.setState({
                  sourceItemsDetailRespVos,
                  showBox: true,
                });
              }
            },
          });
        } else if (isPrecise === 0 && status === 1) {
          this.onClickItem(value);
        }
      },
    );
  };

  onClickItem = (value: ISourceItemsRespVos, subValue?: ISourceItemsDetailData) => {
    const { sign, hospitalCode, hospitalAreaCode, listDocName, deptCode } = this.state;
    const {
      admLocation = '',
      deptName = '',
      isPrecise,
      regFee,
      serviceFee,
      scheduleDate,
      scheduleRange,
      sysScheduleId = '',
      availableCount,
      sourceChannel,
      teamId,
    } = value;
    const { startTime = '', endTime = '', avaliable, sysTimeArrangeId = '', startNoAndendNo = '' } = subValue || {};
    const currentAvaliable = (isPrecise === 1 ? avaliable : availableCount) || 0;
    let data: any = {
      sign,
      hospitalCode,
      hospitalAreaCode,
      docName: listDocName,
      deptName,
      admLocation,
      isPrecise,
      regFee,
      serviceFee,
      scheduleRange,
      scheduleDate,
      sysScheduleId,
      mdtFlag: true,
      deptCode,
      sourceChannel,
      teamId,
    };
    if (subValue) {
      data = {
        ...data,
        startTime,
        endTime,
        sysTimeArrangeId,
        startNoAndendNo,
      };
    }
    this.setState(
      {
        sysScheduleId,
        currentAvaliable,
      },
      () => {
        HxLocalStorage.set(StorageEnum.APPOINTMENT_SOURCE_DETAIL, data);
        this.selSourceOpenTime(sysScheduleId, currentAvaliable);
      },
    );
  };

  onConfirm = () => {
    clearInterval(timer);
    const { showCountDown, channelElderCode } = this.state;
    if (showCountDown === '0秒') {
      const param = 'color=white';
      AppScheme.changeColor(param);
      AppScheme.removeShare();
      const redirect = '/appointment/detail';
      window.location.href = `${window.location.origin}/person/patientcard/home?isMdt=1&redirect=${redirect}${
        channelElderCode ? `&channelCode=${channelElderCode}` : ''
      }&channelElderCode=${channelElderCode}`;
    }
    this.setState({
      visible: false,
      sysScheduleId: '',
    });
  };

  // 医生简介
  goToIntroduction = (value: any) => {
    history.push({ pathname: '/appointment/doctorintroduction', query: { ...value } });
  };

  onCloseConfirm = () => {
    this.setState({
      tips: '',
      htmlTips: '',
    });
  };

  // 保存所选择的分时段数据
  saveChoose = (nthValue: any) => {
    this.setState({
      chooseNthValue: nthValue,
      chooseNthValueKey: nthValue.sysTimeArrangeId,
      chooseStartNoAndendNo: nthValue.startNoAndendNo,
    });
  };

  clickExpand = (hosSchedule) => {
    hosSchedule.isExpand = !hosSchedule.isExpand;
    const { scheduleData } = this.state;
    this.setState({
      scheduleData,
    });
  };

  render() {
    const {
      showCountDown,
      visible,
      scheduleData,
      hospitalOpenSourceTime,
      tips,
      htmlTips,
      listDocName,
      showBox,
      chooseValue,
      chooseNthValueKey,
      chooseNthValue,
      sourceItemsDetailRespVos = [],
      chooseStartNoAndendNo,
      doctorId = '0',
    } = this.state;
    const { regFee, serviceFee, scheduleDate, scheduleRange: curretnrRnge = 0, scheduleRangeOtherName } = chooseValue;
    const { piTeamList } = this.props;
    return (
      <div
        key={doctorId}
        className={piTeamList && scheduleData.length === 0 ? styles.detailsMain2 : styles.detailsMain}
      >
        {scheduleData?.length === 0 ? (
          <div>
            {piTeamList ? (
              <div className={styles.piTeamList}>
                <img src={piTeamIcon} alt="" />
                <span>医生暂无号源，您可以选择专家团队问诊。</span>
              </div>
            ) : (
              <EmptyView style={{ height: '22rem' }} isNew text="暂无内容" />
            )}
          </div>
        ) : (
          <div className={styles.main}>
            {scheduleData.map((hosSchedule) => {
              const { sourceItemsRespVos, areaName = '', areaCode, isExpand = false } = hosSchedule;
              const showExpand = sourceItemsRespVos.length > 4;
              return (
                <div key={areaCode} className={styles.hospital}>
                  <div className={styles.hospitalTitle}>
                    <span className={styles.spot} />
                    <span className={styles.hospitalName}>{areaName}</span>
                  </div>
                  {(isExpand ? sourceItemsRespVos : sourceItemsRespVos.slice(0, 4)).map((item: ISourceItemsRespVos) => {
                    const {
                      scheduleDate = '',
                      scheduleRange: curretnrRnge = 0,
                      serviceFee = 0,
                      regFee = 0,
                      status = 0,
                      scheduleRangeOtherName,
                      remainingNum = 0,
                    } = item;
                    // if (item.delaySeconds == 0) {
                    //   item.status = 1;
                    // }
                    return (
                      <div className={styles.schedule} key={item.sysScheduleId}>
                        <div className={styles.scheduleItem}>
                          <div className={styles.borderBottom} />
                          <div className={styles.date}>
                            <div>
                              {item.scheduleDate} {week[new Date(scheduleDate).getDay()]}{' '}
                              {curretnrRnge === 99 ? scheduleRangeOtherName : scheduleRange[curretnrRnge]}
                            </div>
                            <div>
                              <span>{item.deptName}</span>
                              <span>¥{(Number(regFee) + Number(serviceFee)).toFixed(2)}</span>
                            </div>
                          </div>
                          <div className={styles.appoinmentBtn}>
                            {/*
                            是否显示倒计时根基 delaySeconds 判断 大于0 显示倒计时
                          */}
                            {item.delaySeconds > 0 ? (
                              <div
                                className={`${styles.activeBtnDown}`}
                                style={{
                                  lineHeight: '18px',
                                }}
                              >
                                距离挂号还有 <br />
                                <CountDown
                                  delaySeconds={item.delaySeconds}
                                  key={item.sysScheduleId}
                                  getDocInfo={() => {
                                    // 直接修改当前按钮状态
                                    setTimeout(() => {
                                      const _flag1 = item.hospitalAreaCode;
                                      const _obj = { ...item, delaySeconds: 0, status: 1 };
                                      const _sourceItemsRespVos = sourceItemsRespVos.map((e) => {
                                        return { ...e, delaySeconds: 0, status: 1 };
                                      });
                                      const _scheduleData = scheduleData.map((e) => {
                                        return e.hospitalCode === _flag1
                                          ? { ...e, sourceItemsRespVos: _sourceItemsRespVos }
                                          : e;
                                      });
                                      console.log(_scheduleData);
                                      this.setState({
                                        scheduleData: [..._scheduleData],
                                      });
                                    }, 0);
                                    // this.fetchData();
                                  }}
                                />
                              </div>
                            ) : (
                              <div
                                className={`${styles.btn} ${item.status === 1 && styles.activeBtn} ${
                                  item.status === 3 && styles.redBtn
                                }`}
                                onClick={() => {
                                  item.status === 1 && (Number(item.delaySeconds) === 0 || !item.delaySeconds)
                                    ? this.sourceClickItem(item)
                                    : () => {};
                                }}
                              >
                                {sourceStatus[status]}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  {showExpand && (
                    <div className={styles.expand} onClick={() => this.clickExpand(hosSchedule)}>
                      <span> {isExpand ? '收起更多' : '展开更多'}</span>
                      <img src={isExpand ? arrowUpIcon : arrowDownIcon} alt="" />
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}
        <Modal
          visible={visible}
          title="提示"
          transparent
          maskClosable={false}
          footer={[
            {
              text: '确认',
              onPress: () => {
                this.onConfirm();
              },
            },
          ]}
        >
          {showCountDown !== '' && (
            <div className={styles.content}>
              <div>{`未到放号时间，每日【${hospitalOpenSourceTime}】准时开始放号源。`}</div>
              <div style={{ color: '#F47F1B' }}>{`倒计时${showCountDown}`}</div>
            </div>
          )}
        </Modal>
        {/* <Modal
          visible={!!tips || !!htmlTips}
          title="提示"
          transparent
          maskClosable={false}
          footer={[
            {
              text: '确认',
              onPress: () => {
                this.onCloseConfirm();
              },
            },
          ]}
        >
          <div className={styles.content} style={{ textAlign: 'left' }}>
            {tips.length && !htmlTips.length && (
              <div>
                {tips.split('\n').map((item) => {
                  return (
                    <div className="text" key={item}>
                      {item}
                    </div>
                  );
                })}
              </div>
            )}
            {htmlTips.length && (
              <div>
                <div className="text" dangerouslySetInnerHTML={{ __html: htmlTips }} />
              </div>
            )}
          </div>
        </Modal> */}
        {showBox && (
          <div className={styles.mask}>
            <div
              className={styles.rest}
              onClick={() => this.setState({ showBox: false, chooseNthValue: {}, chooseNthValueKey: '' })}
            />
            <div className={styles.head}>
              <div className={styles.name}>{listDocName}</div>
              <div className={styles.times}>
                {scheduleDate} {week[new Date(scheduleDate).getDay()]}{' '}
                {curretnrRnge === 99 ? scheduleRangeOtherName : scheduleRange[curretnrRnge]}
              </div>
              <div className={styles.price}>
                <span>¥</span>
                {(Number(regFee) + Number(serviceFee)).toFixed(2)}
              </div>
            </div>
            <div className={styles.box}>
              <ul>
                {sourceItemsDetailRespVos.map((item: any) => {
                  return (
                    <li
                      key={`${item.sysTimeArrangeId}${item.startNoAndendNo}`}
                      className={
                        item.avaliable > 0
                          ? item.sysTimeArrangeId === chooseNthValueKey &&
                            item.startNoAndendNo === chooseStartNoAndendNo
                            ? styles.nthSelected
                            : ''
                          : styles.vanish
                      }
                      onClick={
                        item.avaliable > 0
                          ? () => this.saveChoose(item)
                          : () => {
                              Toast.fail('该号源已被预约', 1);
                            }
                      }
                    >
                      <div className={styles.andendNoWrap}>
                        门诊号段：
                        <span>
                          {item.startNoAndendNo}号{}
                        </span>
                      </div>
                      <p>{`${item.startTime}-${item.endTime}`}</p>
                    </li>
                  );
                })}
              </ul>
            </div>
            <div
              className={chooseNthValue.avaliable ? styles.showStatus : styles.showNoStatus}
              onClick={chooseNthValue.avaliable ? () => this.onClickItem(chooseValue, chooseNthValue) : () => {}}
            >
              确定
            </div>
          </div>
        )}
      </div>
    );
  }
}

// export default AppointmentSchedule;

export default connect(({ loading }: { loading: Loading }) => ({
  detailLoading: loading.effects['doctor/queryHxDoctorInfo'],
  numberSourceLoading: loading.effects['doctor/selHxDoctorDetails'],
}))(AppointmentSchedule);
