/*
 * @Author: <PERSON>
 * @Date: 2022-03-22 17:17:54
 * @LastEditor: <PERSON>
 * @LastEditTime: 2022-03-23 14:49:39
 * @FilePath: /hyt-person/src/pages/DoctorDetailAggregationHX/Home/components/Appointment/_utils.ts
 * Copyright (c) 2022 by <PERSON>, All Rights Reserved.
 * @Description:
 */
const endTotalSeconds = (): number => {
  const now: Date = new Date();
  const hour = now.getHours();
  const minutes = now.getMinutes();
  const seconds = now.getSeconds();
  const total = 12 * 60 * 60;
  if (hour < 8) {
    return total - ((4 + hour) * 60 * 60 + minutes * 60 + seconds);
  }
  if (hour < 20) {
    return total - ((hour - 8) * 60 * 60 + minutes * 60 + seconds);
  }
  return total - ((23 - hour + 8) * 60 * 60 + minutes * 60 + seconds);
};
const addzearo = (num: any) => {
  if (num < 10) {
    return `0${num}`;
  }
  return num;
};
const endTime = (total: number): string => {
  const hours = Math.floor((total / 60 / 60) % 24); // 计算剩余的小时
  const minutes = Math.floor((total / 60) % 60); // 计算剩余的分钟
  const seconds = Math.floor(total % 60); // 计算剩余的秒数

  return `${addzearo(hours)}:${addzearo(minutes)}:${addzearo(seconds)}`;
};
const showCountDownStr = () => {
  return endTime(endTotalSeconds());
};
export { endTotalSeconds, endTime, showCountDownStr };
