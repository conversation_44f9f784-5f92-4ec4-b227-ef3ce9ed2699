import * as React from 'react';
import { useEffect, useState } from 'react';
import { DownOutline, UpOutline } from 'antd-mobile-icons';
import styles from './index.less';

interface IndexProps {
  content: string; // 文本内容
  maxLen?: number; // 文字最大显示长度
  contentRender: (text: string, handler: React.ReactNode) => React.ReactNode; // 自定义内容
  onExpand?: (expanded: boolean) => void; // 展开、收起后触发
}

/**
 * 自定义文字展开收起组件
 * @param props
 * @constructor
 */
const TextExpand = (props: IndexProps) => {
  const {
    content,
    maxLen = 36,
    contentRender = (text: string, handler: React.ReactNode) => {},
    onExpand = (expanded: boolean) => {},
  } = props;
  const [body, setBody] = useState<any>(null);
  const [expanded, setExpanded] = useState<boolean>(false);
  const [showBtn, setShowBtn] = useState<boolean>(false);
  /**
   * 展开、收起
   * @param status
   */
  const expandToggle = (status: boolean) => {
    const text = status ? content : getText(content);
    setExpanded(status);
    setBody(text);
    onExpand(status);
  };

  /**
   * 获取截断后的文字
   * @param text
   */
  const getText = (text: string): string => {
    return `${(text || '').slice(0, maxLen)}...`;
  };
  useEffect(() => {
    let contentBody = content || '';
    if (contentBody.length > maxLen) {
      contentBody = getText(contentBody);
      setShowBtn(true);
    }
    setBody(contentBody);
    return () => {
      setExpanded(false);
      setShowBtn(false);
    };
  }, [content]);

  /**
   * 获取展开收起按钮
   * @param status
   */
  const getExpandBtn = (): React.ReactNode =>
    showBtn ? (
      <div className={styles.btnbox}>
        <div className={styles.btn} onClick={() => expandToggle(!expanded)}>
          {expanded ? <UpOutline /> : <DownOutline />}
        </div>
      </div>
    ) : null;
  return <div className={styles.access}>{contentRender(body, getExpandBtn())}</div>;
};

export default TextExpand;
