import AppScheme from '@/utils/AppScheme';
import { isHytPerson } from '@/utils/platform';

/* 鸿蒙获取当前位置 */
const getLocationByApp = () => {
  return new Promise<any>((resolve) => {
    try {
      window.getLocationCallBack = function (lat, long) {
        console.log('经纬度=======', lat, long);
        if (isHytPerson()) {
          if (lat && long) {
            window.localStorage.setItem('longitude', long.toString());
            window.localStorage.setItem('latitude', lat.toString());
            resolve([lat, long]);
          } else {
            resolve([]);
          }
        }
        window.getLocationCallBack = null;
      };
      if (isHytPerson()) {
        AppScheme.getLocation();
      } else {
        // resolve([30.64,104.06]) // 华西位置
        resolve([]);
      }
    } catch (error) {
      resolve([]);
    }
  });
};

export { getLocationByApp };
