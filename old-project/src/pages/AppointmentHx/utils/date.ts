import moment from 'moment';
import _ from 'lodash';

const YMD = 'YYYY-MM-DD';
/**
 * 根据指定天数生成日期数据
 * @param days [number]生成多少天的日期数据
 * @param isBeginToday [boolean]是否从今天开始
 */

export const generateScheduleDate = (days: number, isBeginToday: boolean = false) => {
  const dates = [];
  // const formatWeek = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

  let initial = 1;
  if (isBeginToday) {
    initial = 0;
  } else {
    days += 1;
  }
  for (let i = initial; i < days; i += 1) {
    const date = moment().add(i, 'days').format(YMD);
    const displayDate = moment().add(i, 'days').format('MM.DD');
    const obj = {
      date,
      displayDate,
      displayWeek: moment().locale('zh-cn').add(i, 'days').format('ddd'),
    };
    dates.push(obj);
  }
  return dates;
};

/**
 * 获取当前时间是当月的第几周
 * @param year [any]当前时间的年份
 * @param month [any]当前时间的月份
 * @param day [any]当前时间的日期
 */
const getMonthWeek = (year: any, month: any, day: any) => {
  /**
   * year = d = 当前日期
   * month = 6 - w = 当前周的还有几天过完(不算今天)
   * year + month 的和在除以7 就是当天是当前月份的第几周
   */
  // eslint-disable-next-line radix
  const date = new Date(year, parseInt(month) - 1, day);
  const d = date.getDate();

  let w = date.getDay();
  if (w === 0) {
    w = 7;
  }
  const getWeek = Math.ceil((d + 6 - w) / 7);
  return getWeek;
};

/**
 * 获取本周,上一周,下一周一周的日期
 * @param days number]生成多少天的日期数据
 * @param isCurrentWeek [boolean]是否是当前周
 * @param isLastWeek [boolean]是否是上一周
 * @param isNextWeek [boolean]是否是下一周
 * @param startDate [any]当前周的开始时间
 * @param endDate [any]当前时间的结束时间
 */
export const getCurrentWeek = (
  days: number,
  isCurrentWeek?: boolean,
  isLastWeek?: boolean,
  isNextWeek?: boolean,
  startDate?: any,
  endDate?: any,
) => {
  const dates = [];
  const formatWeek = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];

  for (let i = 0; i < days; i += 1) {
    if (isCurrentWeek) {
      const CurretnDate = moment();
      const date = moment().weekday(i).format(YMD);
      const displayDate = moment().weekday(i).format('MM.DD');
      const topDisplayDate = moment().weekday(i).format('YYYY年MM月');
      const obj = {
        date,
        displayDate,
        topDisplayDate,
        displayWeek: formatWeek[i],
        weekNum: getMonthWeek(CurretnDate.format('YYYY'), CurretnDate.format('MM'), CurretnDate.format('DD')),
      };
      dates.push(obj);
    }
    if (isLastWeek) {
      const curretnStatr = moment(new Date(startDate)).subtract(1, 'days');
      const date = moment(new Date(startDate))
        .subtract(7 * 1 - i, 'days')
        .format(YMD);
      const displayDate = moment(new Date(startDate))
        .subtract(7 * 1 - i, 'days')
        .format('MM.DD');
      const topDisplayDate = curretnStatr.format('YYYY年MM月');
      const obj = {
        date,
        displayDate,
        topDisplayDate,
        displayWeek: formatWeek[i],
        weekNum: getMonthWeek(curretnStatr.format('YYYY'), curretnStatr.format('MM'), curretnStatr.format('DD')),
      };
      dates.push(obj);
    }
    if (isNextWeek) {
      const curretnEnd = moment(new Date(endDate)).add(i + 1, 'days');
      const date = curretnEnd.format(YMD);
      const displayDate = curretnEnd.format('MM.DD');
      const topDisplayDate = curretnEnd.format('YYYY年MM月');

      const obj = {
        date,
        displayDate,
        topDisplayDate,
        displayWeek: formatWeek[i],
        weekNum: getMonthWeek(curretnEnd.format('YYYY'), curretnEnd.format('MM'), curretnEnd.format('DD')),
      };
      dates.push(obj);
    }
  }
  return dates;
};

/**
 * 获取当前天数是本月第几周
 * @param date 日期
 */
export const getCurrentWeekIsMonth = (date: string) => {
  const momentDate = moment(date);
  const monthStartDay = momentDate.clone().startOf('month').day();
  const fixedDay = monthStartDay ? monthStartDay - 1 : 6;
  const currentWeekIsMonth = Math.ceil((momentDate.date() + fixedDay) / 7);
  return currentWeekIsMonth;
};

/**
 * 获取当前天所在本周日期 1-7
 * @param date 日期  YYYY-MM-DD
 * @param isGetStart boolean | undefined
 * @param isGetEnd boolean | undefined
 */
export const getCurrentWeekDay = (date: string, isGetStart?: boolean, isGetEnd?: boolean) => {
  const startWeekDay = moment(date).locale('zh-cn');
  const endWeekDay = moment(date).locale('zh-cn').weekday(6);
  if (isGetStart) return startWeekDay.format(YMD);
  if (isGetEnd) return endWeekDay.format(YMD);
  const weekDate = _.map(Array(7), (item, index) => {
    const dateMoment = startWeekDay.add(index ? 1 : 0, 'd');
    const isToday = moment(date).isSame(dateMoment, 'day');
    return {
      date: dateMoment.format(YMD),
      displayDate: dateMoment.format('MM.DD'),
      displayWeek: isToday ? '今天' : dateMoment.format('ddd'),
      topDisplayDate: endWeekDay.format('YYYY年MM月'),
      weekNum: getCurrentWeekIsMonth(date),
    };
  });
  return weekDate;
};

export const ellipsisTel = (tel: string) => (tel ? `${tel.slice(0, 3)}****${tel.slice(tel?.length - 4)}` : '--');
