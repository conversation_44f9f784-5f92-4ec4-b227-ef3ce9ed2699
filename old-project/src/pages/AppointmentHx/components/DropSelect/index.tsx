import React, { useEffect, useRef, useState } from 'react';
import { Dropdown } from 'antd-mobile-v5';
import { getDistance, getNewLocation } from '@/utils/tool';
import { isHytHarmonyOS, isHytPerson, isWechat } from '@/utils/platform';
import { IAreaRecordRespVos } from '../../data';
import { getLocationByApp } from '../../utils/tool';
import styles from './index.less';

const arrow = 'https://cdnhyt.cd120.com/person/assets/appoinmentHx/down_point.png';
const deptSelect = 'https://cdnhyt.cd120.com/person/assets/appoinmentHx/dept_select.png';
interface IProps {
  hospitalAreaCode?: string;
  dropList?: IAreaRecordRespVos[];
  onChange?: (code: string) => void;
}
const DropSelect: React.FC<IProps> = (props) => {
  const { hospitalAreaCode = 'ALL', dropList = [], onChange } = props;
  const [select, setSelect] = useState<string>(hospitalAreaCode || '');
  const [hospitalList, setHospitalList] = useState<IAreaRecordRespVos[]>(dropList || []);
  const getHospitalAreaName =
    dropList.find((item) => item.hospitalAreaCode === hospitalAreaCode)?.hospitalAreaName || '全部院区';
  const dropRef = useRef(null);

  const calcPosition = async () => {
    if (isWechat()) {
      await getNewLocation();
    } else if (isHytPerson()) {
      await getLocationByApp();
    }
    const longitude = window.localStorage.getItem('longitude') || '';
    const latitude = window.localStorage.getItem('latitude') || '';
    const _dropList = dropList.map((item) => {
      if (item.latitude && item.longitude && longitude && latitude) {
        const distance = getDistance(
          Number(item.latitude),
          Number(item.longitude),
          Number(latitude),
          Number(longitude),
        );

        return { ...item, distance };
      }
      return item;
    });
    setHospitalList(_dropList);
  };

  useEffect(() => {
    if (dropList.length && (isWechat() || isHytHarmonyOS())) {
      calcPosition();
    } else if (dropList.length) {
      setHospitalList(dropList);
    }
  }, [dropList]);
  return (
    <div className={styles.dropSelect}>
      <Dropdown arrow={<img src={arrow} alt="" style={{ width: '12px', height: '12px' }} />} ref={dropRef}>
        <Dropdown.Item key="sorter" title={getHospitalAreaName} className="dropSelectDepartment">
          {hospitalList.length > 0 ? (
            <div className={styles.dropDepartmentList}>
              {hospitalList.map((item) => {
                return (
                  <div
                    className={styles.item}
                    key={item?.hospitalAreaCode}
                    onClick={() => {
                      setSelect(item?.hospitalAreaCode as string);
                      dropRef?.current?.close();
                      onChange && onChange(item?.hospitalAreaCode);
                    }}
                  >
                    <div className={styles.info}>
                      <span className={styles.name}>{item?.hospitalAreaName}</span>
                      <div>
                        {item?.distance && `${item?.distance}km |`}{' '}
                        {item?.hospitalAreaCode === 'ALL' ? '查看所有院区医生排班情况' : item?.areaAddress}
                      </div>
                    </div>
                    {select === item?.hospitalAreaCode && <img src={deptSelect} alt="" />}
                  </div>
                );
              })}
            </div>
          ) : (
            ''
          )}
        </Dropdown.Item>
      </Dropdown>
    </div>
  );
};

export default DropSelect;
