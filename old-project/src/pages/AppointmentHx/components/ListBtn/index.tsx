import React, { PureComponent, Fragment } from 'react';
import { Button } from 'antd-mobile-v5';
import styles from './index.less';

interface IProps {
  appointStatus: number;
  isBack: number;
  isCancel: number;
  isToPay: boolean;
  takeMethod: string | number;
  appointAgain: (e: React.MouseEvent<HTMLButtonElement>) => void;
}

interface IState {}

class ListCopywrite extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  render() {
    const { appointStatus, isBack, isCancel, isToPay = false, takeMethod = '', appointAgain } = this.props;
    return (
      <div className={styles.actionsContainer}>
        {appointStatus === 2 && (
          <Fragment>
            {isCancel !== 0 && <Button shape="rounded">取消预约</Button>}
            {isToPay && (
              <Button color="primary" shape="rounded">
                {takeMethod === 2 ? '去自费' : '去支付'}
              </Button>
            )}
          </Fragment>
        )}
        {appointStatus === 7 && isBack !== 0 && <Button shape="rounded">退号</Button>}
        {appointStatus !== 2 && (
          <Button shape="rounded" onClick={(e) => appointAgain && appointAgain(e)}>
            再次预约
          </Button>
        )}
      </div>
    );
  }
}

export default ListCopywrite;
