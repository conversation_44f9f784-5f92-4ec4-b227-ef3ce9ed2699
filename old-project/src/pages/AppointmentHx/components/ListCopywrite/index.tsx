import React, { Fragment, PureComponent } from 'react';
import { getOrganCode } from '@/utils/parameter';
import { secondCountDown } from '../../utils/countdown';
import styles from './index.less';

interface IProps {
  appointStatus: number;
  appointAutoCancelMin: number;
  wxStop: boolean;
}

interface IState {
  appointAutoCancelMin: number;
  countdown: string;
}

const appointmentStatusList: any = {
  2: { color: '#3AD3C1', title: '待支付', text: '请在30分钟内完成支付' },
  5: { color: '#989EB4', title: '已取消' },
  6: { color: '#FF984B', title: '待退款' },
  7: { color: '#3AD3C1', title: '挂号成功'},
  8: { color: '#989EB4', title: '已退号' },
  stop: { color: '#FC4553', title: '停诊待退费' },
};

const hospitalSpecialConfig: any = {
  HXXM0101: {
    7: { text: '请在就诊当天前往医院签到就诊' },
  },
};

let timer: any = null;
class ListCopywrite extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    const { appointAutoCancelMin = 0 } = props;
    this.state = {
      appointAutoCancelMin,
      countdown: '',
    };
  }

  componentDidMount() {
    let { appointAutoCancelMin } = this.state;
    timer = setInterval(() => {
      if (appointAutoCancelMin === 0) {
        clearInterval(timer);
        // this.fetchData(1)
      } else {
        this.setState({
          countdown: secondCountDown((appointAutoCancelMin -= 1)),
        });
      }
    }, 1000);
  }

  componentWillUnmount() {
    clearInterval(timer);
  }

  render() {
    const { appointStatus, appointAutoCancelMin, wxStop } = this.props;
    const { countdown } = this.state;
    return (
      <div className={styles.container} style={{ color: appointmentStatusList[wxStop ? 'stop' : appointStatus].color }}>
        {appointStatus === 2 && countdown !== '00:00:00' && appointAutoCancelMin !== 0 && `剩余：${countdown}`}
        {appointStatus === 7 && !wxStop && (
          <Fragment>
            {hospitalSpecialConfig?.[getOrganCode()]?.[appointStatus]?.text ??
              appointmentStatusList[appointStatus].text}
          </Fragment>
        )}
        {/* {appointStatus === 7 && !wxStop && appointmentStatusList[appointStatus].text} */}
        {appointStatus === 7 && wxStop && appointmentStatusList.stop.text}
      </div>
    );
  }
}

export default ListCopywrite;
