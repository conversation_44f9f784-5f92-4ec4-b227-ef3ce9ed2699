.appointmentNotice {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  .title {
    font-size: 36px;
    font-weight: bold;
    text-align: left;
    line-height: 50px;
    color: #03081a;
    height: 106px;
    display: flex;
    align-items: center;
    padding: 0 32px;
  }
  .content {
    flex: 1;
    overflow-y: auto;
    padding: 0 32px 32px;
    .text {
      font-size: 28px;
    }
  }
  .footer {
    flex-shrink: 0;
    min-height: 232px;
    background: #ffffff;
    box-shadow: 0px -4px 8px 0px rgba(152, 158, 180, 0.1);
    padding-bottom: calc(16px + env(safe-area-inset-bottom));
    .btn {
      width: 100%;
      padding: 0 24px;
    }
    .radio {
      padding: 32px 36px;
    }
  }
}
