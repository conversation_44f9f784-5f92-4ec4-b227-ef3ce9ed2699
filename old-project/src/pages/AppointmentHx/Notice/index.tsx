import { getOrganCode } from '@/utils/parameter';
import React, { useEffect, useState } from 'react';
import { connect, Dispatch, Loading, history, IHxAppointmentState } from 'umi';
import { Radio, Button, Toast } from 'antd-mobile-v5';
import qs from 'query-string';
import styles from './index.less';

interface IProps {
  dispatch: Dispatch;
  loading: Loading;
}
const AppointmentNotice: React.FC<IProps> = (props) => {
  const [notice, setNotice] = useState('');
  const [isAgree, setIsAgree] = useState(false);
  const { dispatch, loading = false } = props;
  const fetchNotice = () => {
    dispatch({
      type: 'hxappointment/selHospitalConfigTypeAndNotice',
      payload: {
        hospitalCode: getOrganCode(),
        // deptCategoryCode,
      },
      callback(res) {
        const { appointNotice = '' } = res || {};
        setNotice(appointNotice);
      },
    });
  };
  const confirm = () => {
    // if (deptCategoryCode) {
    //   history.push({
    //     pathname: '/appointment/doctorlist',
    //     query: {
    //       hospitalCode: getOrganCode(),
    //       sign,
    //       hospitalAreaCode: '',
    //       departmentCode: deptCategoryCode,
    //       deptCategoryName,
    //     },
    //   });
    //   return;
    // }
    const params = { hospitalArea: getOrganCode() };
    history.push(`/hxappointment/department?${qs.stringify(params)}`);
  };
  useEffect(() => {
    fetchNotice();
  }, []);
  useEffect(() => {
    if (loading) {
      Toast.show({ icon: 'loading', duration: 0 });
    } else {
      Toast.clear();
    }
  }, [loading]);
  return (
    <div className={styles.appointmentNotice}>
      <div className={styles.title}>请仔细阅读以下内容：</div>
      <div className={styles.content}>
        <div className={styles.text} dangerouslySetInnerHTML={{ __html: notice }} />
      </div>
      <div className={styles.footer}>
        <div className={styles.radio}>
          <Radio
            checked={isAgree}
            onChange={(e) => {
              setIsAgree(e);
            }}
            style={{ '--font-size': '14px', color: '#03081A', fontWeight: 'bold' }}
          >
            已阅读并同意《预约挂号须知》
          </Radio>
        </div>
        <div className={styles.btn}>
          <Button size="large" color="primary" block shape="rounded" disabled={!isAgree} onClick={confirm}>
            确认
          </Button>
        </div>
      </div>
    </div>
  );
};

export default connect(({ loading, hxappointment }: { hxappointment: IHxAppointmentState; loading: Loading }) => ({
  loading: loading.models.hxappointment,
  hxappointment,
}))(AppointmentNotice);
