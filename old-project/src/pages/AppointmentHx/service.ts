import request from '@/utils/request';
import { getOrganCode } from '@/utils/parameter';

let node = '/cloud';
if (APP_ENV === 'prod') {
  const organ = getOrganCode() === null && 'HID0101';
  node =
    getOrganCode() === 'HID0101' || getOrganCode() === 'HYT' || getOrganCode() === 'COMMON' || organ
      ? `${API_HXYY}/cloud`
      : `${API_BASE}/cloud`;
}

const handleOrganCode = (data: any = {}) => {
  return data;
};

// 查询挂号类型与须知
export const selHospitalConfigTypeAndNotice = async (data: object): Promise<any> =>
  request(`${node}/appointment/hospitalConfig/selHospitalConfigTypeAndNotice`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

/**
 * 获取用户标示
 */
export const queryUserMark = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/api-xunfei/queryUserMark`, {
    method: 'POST',
    data,
  });

// 查询排班医院院区
export const selHospitalAreaRecord = async (data: object): Promise<any> =>
  request(`${node}/appointment/hospitalConfig/selHospitalAreaRecord`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

// 查询一级科室
export const selFirstDept = async (data: object): Promise<any> =>
  request(`${node}/appointment/dept/selFirstDeptV2`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

// 多条件查询医生列表
export const selDoctorListByMoreTerm = async (data: object): Promise<any> =>
  request(`${node}/appointment/doctorListModel/selDoctorListByMoreTermV2`, {
    method: 'POST',
    data,
  });

// 查询医生列表时间筛选条件
export const selDocListTimeRangeFilter = async (data: object): Promise<any> =>
  request(`${node}/appointment/appointmentModel/timeRange`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

/**
 * 就诊卡列表
 */
export const getCardList = async (data: object): Promise<any> => {
  return request('/cloud/cardservice/home/<USER>', { method: 'POST', data });
};

// 预约详情
export const selAppointmentDetails = async (data: object): Promise<any> =>
  request(`${node}/appointment/appointmentModel/selAppointmentDetails`, {
    method: 'POST',
    data,
  });

// 预约列表
export const selAppointmentRecord = async (data: object): Promise<any> =>
  request(`${node}/appointment/appointmentModel/selAppointmentRecord`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

// 查询历史挂号科室和医生
export const selHistoryDeptAndDoc = async (data: object): Promise<any> =>
  request(`${node}/appointment/appointmentModel/selHistoryDeptAndDoc`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

// 可预约日期
export const selAppointmentDate = async (data: object): Promise<any> =>
  request(`${node}/appointment/doctorListModel/selAppointmentDate`, {
    method: 'POST',
    data: handleOrganCode(data),
  });
// 查询周排班
export const selWeekSchedule = async (data: object): Promise<any> =>
  request(`${node}/appointment/doctorListModel/selWeekScheduleV2`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

//  查询号源开放情况
export const selSourceOpenTime = async (data: object): Promise<any> =>
  request(`${node}/appointment/appointmentModel/selSourceOpenTime`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

// 确认预约前的检查
export const checkAppointment = async (data: object): Promise<any> =>
  request(`${node}/appointment/appointmentModel/checkAppointment`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

// 确认预约
export const sureAppointment = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/appointment/appointmentModel/sureAppointment`, {
    method: 'POST',
    data: { ...handleOrganCode(data), showOriginData: true, skipError: true },
  });

// 取号详情
export const selToTakeNoDetails = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/appointment/takeANumberModel/selToTakeNoDetails`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

// 取号支付
export const toTakeNoToPay = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/call/appointment/takeANumberModel/toTakeNoToPay`, {
    method: 'POST',
    data: handleOrganCode(data),
  });

// 支付
export const appointmentToPay = async (data: object): Promise<any> =>
  request(`${node}/appointment/appointmentModel/appointmentToPay`, {
    method: 'POST',
    data: { ...handleOrganCode(data), showOriginData: true },
  });

// 取消或者退号
export const appointmentCancelOrBack = async (data: object): Promise<any> =>
  request(`${node}/appointment/appointmentModel/appointmentCancelOrBack`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });

/** 确认预约并去支付  */
export const sureAppointmentToPay = async (data: object): Promise<any> =>
  request(`${node}/appointment/appointmentModel/sureAppointmentToPay`, {
    method: 'POST',
    data: {
      ...handleOrganCode(data),
      showOriginData: true,
      skipError: true,
    },
  });
