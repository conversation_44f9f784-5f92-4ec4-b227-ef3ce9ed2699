import React, { PureComponent } from 'react';
import { history } from 'umi';
import { HxIcon } from '@/components';
import { HxLocalStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import styles from './index.less';
const search = 'https://cdnhyt.cd120.com/person/assets/appoinmentHx/input_search.png';

interface IProps {
  location: {
    query: {
      hospitalCode: string;
      sign: string;
      channelCode: string; // 老年专区 channelCode
      hospitalAreaCode: string; // 院区编码
    };
  };
}

interface IState {
  keyword: string;
  historyList: Array<any>;
}

interface IEvent {
  target: {
    value: string;
  };
  preventDefault: () => void;
}

class Search extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    const historyList = HxLocalStorage.get(StorageEnum.APPOINTMENT_SEARCH_HISTORY) || [];
    this.state = {
      keyword: '',
      historyList: historyList.slice(0, 20),
    };
  }

  onInputChange = (e: IEvent) => {
    const {
      target: { value },
    } = e;
    this.setState({
      keyword: value.trim(),
    });
  };

  // 清空输入框
  clear = () => {
    this.setState({
      keyword: '',
    });
  };

  search = (historyKeyword: string = '') => {
    const { keyword } = this.state;
    // 什么都没有输入，直接点搜索
    if (historyKeyword === '' && keyword === '') {
      return;
    }

    // 点击历史记录
    if (historyKeyword !== '') {
      this.setState({
        keyword: historyKeyword,
      });
    } else {
      // 点击搜索（根据输入的关键字）
      this.onSearch();
    }
  };

  onSearch = () => {
    const { keyword, historyList } = this.state;

    historyList.unshift(keyword);
    const historyList1 = [...new Set(historyList)]; // 使用Set去重

    HxLocalStorage.set(StorageEnum.APPOINTMENT_SEARCH_HISTORY, historyList1);
    this.setState(
      {
        historyList: historyList1.slice(0, 20),
      },
      () => {
        const {
          location: {
            query: { hospitalCode = '', sign = '1', channelCode = '', hospitalAreaCode = '' },
          },
        } = this.props;
        history.push({
          pathname: '/hxappointment/doctorlist',
          query: {
            hospitalCode,
            sign,
            keyword,
            channelCode,
            hospitalAreaCode,
          },
        });
      },
    );
  };

  // 删除历史记录
  delete = () => {
    HxLocalStorage.remove(StorageEnum.APPOINTMENT_SEARCH_HISTORY);
    this.setState({
      historyList: [],
    });
  };

  // 软键盘搜索事件
  onSubmit = (e: any) => {
    e.preventDefault();
    this.onSearch();
  };

  render() {
    const { keyword = '', historyList } = this.state;
    return (
      <div className={styles.container}>
        <div className={styles.top}>
          <div className={styles.inputView}>
            <form action="" onSubmit={this.onSubmit}>
              <img src={search} alt="" />
              <input
                placeholder="搜索科室、医生、擅长"
                value={keyword}
                type="search"
                onChange={(e: IEvent) => this.onInputChange(e)}
                // autoFocus
              />
              {keyword.length > 0 && <HxIcon className={styles.clear} onClick={() => this.clear()} iconName="clear" />}
            </form>
            <span onClick={() => this.search()} className={styles.search}>
              搜索
            </span>
          </div>
        </div>
        {historyList.length > 0 && (
          <div className={styles.historyView}>
            <div className={styles.historyTitle}>
              <span className={styles.titleText}>历史记录</span>
              <HxIcon className={styles.delete} onClick={() => this.delete()} iconName="icon-delete" />
            </div>
            <div className={styles.historyList}>
              {historyList.map((item) => (
                <div
                  key={`${Number(Math.random().toString().substr(3, 12) + Date.now()).toString(36)}`}
                  onClick={() => this.search(item)}
                  className={styles.historyItem}
                >
                  {item}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }
}

export default Search;
