.container {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .top {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    height: 104px;
    padding: 16px 32px;
    background: #fff;

    .inputView {
      position: relative;
      width: 100%;
      height: 72px;
      background: #f5f6fa;
      border-radius: 36px;
      display: flex;
      align-items: center;
      padding-left: 24px;
      justify-content: space-between;
      img {
        width: 40px;
        height: 40px;
        margin-right: 8px;
        flex-shrink: 0;
      }

      input {
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        font-size: 28px;
        background: #f5f6fa;
        border: none;
        color: #03081a;
        caret-color: #bbbeca;
        flex: 1;
      }
      .search {
        padding: 0 32px;
        padding-left: 34px;
        position: relative;
        font-size: 32px;
        font-weight: bold;
        text-align: center;
        color: #3ad3c1;
        &::before {
          content: '';
          width: 2px;
          height: 32px;
          background: #e0e2eb;
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      form {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 1;
        padding-right: 24px;
        .clear {
          width: 32px;
          height: 32px;
          flex-shrink: 0;
          fill: red;
        }
      }

      input::-webkit-input-placeholder,
      input::-moz-placeholder,
      input::-ms-input-placeholder {
        color: #aaa;
        font-size: 26px;
      }
    }
  }

  .historyView {
    padding: 20px 32px 10px;
    background: #fff;

    .historyTitle {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 50px;
      margin-bottom: 40px;

      .titleText {
        font-size: 32px;
        font-weight: bold;
        text-align: center;
        color: #03081a;
      }

      .delete {
        width: 50px;
        height: 50px;
      }
    }

    .historyList {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      justify-content: flex-start;

      .historyItem {
        margin: 0 24px 24px 0;
        padding: 12px 32px;
        white-space: nowrap;
        border-radius: 28px;
        background: #f5f6fa;
        border-radius: 30px;
        font-size: 26px;
        text-align: center;
        color: #03081a;
      }
    }
  }
  .showResult {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .loading {
      width: 80px;
      height: 80px;
    }
    .noresult {
      width: 153px;
      height: 112px;
    }

    .note {
      margin-top: 34px;
      color: #666;
      font-size: 24px;
    }
  }
}
