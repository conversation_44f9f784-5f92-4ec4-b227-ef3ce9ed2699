import { Reducer } from 'redux';
import { Effect } from 'umi';
import { createAction } from '@/utils/common';
import * as appointmentApi from './service';
import { IHistoryDeptItem, IHistoryDoctorItem } from './data';

export interface IHxAppointmentState {
  doctorList: any[];
  historyDeptList: IHistoryDeptItem[];
  historyDoctorList: IHistoryDoctorItem[];
}

export interface IHxAppointmentModel {
  namespace: 'hxappointment';
  state: IHxAppointmentState;
  effects: {
    selHospitalConfigTypeAndNotice: Effect;
    queryUserMark: Effect;
    querySelHistoryDeptAndDoc: Effect;
  };
  reducers: {
    updateState: Reducer<IHxAppointmentState>;
  };
}

const AppointmentModel: IHxAppointmentModel = {
  namespace: 'hxappointment',
  state: {
    doctorList: [],
    historyDoctorList: [],
    historyDeptList: [],
  },
  effects: {
    *selHospitalConfigTypeAndNotice({ payload, callback }, { call }) {
      const res = yield call(appointmentApi.selHospitalConfigTypeAndNotice, payload);
      res && callback(res);
    },
    *queryUserMark({ payload, callback }, { call }) {
      const res = yield call(appointmentApi.queryUserMark, payload);
      res && callback(res);
    },
    *querySelHistoryDeptAndDoc({ payload, callback }, { call, put }) {
      const res = yield call(appointmentApi.selHistoryDeptAndDoc, payload);
      if (res) {
        const { historyDeptList = [], historyDoctorList = [] } = res || {};
        yield put({
          type: 'updateState',
          payload: {
            historyDeptList,
            historyDoctorList,
          },
        });
      }
      res && callback(res);
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return { ...state, ...payload };
    },
  },
};

export default AppointmentModel;
