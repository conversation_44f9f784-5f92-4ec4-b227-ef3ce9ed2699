/** 查询排班医院院区 */
export interface IHxAreaRecordData {
  /** 查询排班医院院区对象 */
  selHospitalAreaRecordRespVos?: IAreaRecordRespVos[];
  /** 排班医院院区对象-有经纬度  */
  hospitalAreaRecordList?: IAreaRecordRespVos[];
  /** 科室列表页面提示语  */
  deptListTipsNew?: string;
}

/** 查询排班医院院区对象 */
export interface IAreaRecordRespVos {
  /** 院区编码 */
  hospitalAreaCode?: string;
  /** 院区名称 */
  hospitalAreaName?: string;
  /** 医院编码 */
  hospitalCode?: string;
  /** 地址  */
  areaAddress?: string;
  latitude?: string;
  longitude?: string;
}

/** 业务标签项实体 */
export interface IItemRespVoList {
  /** 业务ID  */
  businessId?: string;
  /** 业务状态  1 开启 2 关闭 3 禁用 */
  businessStatus?: number;
  /** 业务标签编码 */
  businessTagCode?: string;
  /** 业务标签名称 */
  businessTagValue?: string;
}

/** 多条件查询医生列表 */
export interface IHxDoctorListByMoreTermData {
  /** 可用号源数 */
  availableCount?: number;
  /** 业务标签项  */
  businessTagItemRespVoList?: IItemRespVoList[];
  /** 一级科室编码-科室门面-华西的挂号服务  */
  deptCategoryCode?: string;
  /** 一级科室名称--科室门面-华西的挂号服务  */
  deptCategoryName?: string;
  /** 二级科室编码-华西的亚专业   */
  deptCode?: string;
  /** 二级科室名称-华西的亚专业   */
  deptName?: string;
  /** 医生擅长   */
  docExperience?: string;
  /** 医生头像   */
  docHeadImage?: string;
  /** 医生名称   */
  docName?: string;
  /** 医生Id   */
  doctorId?: string;
  /** 在线医生Id   */
  zxmzDoctorId?: string;
  /** 院区编码   */
  hospitalAreaCode?: string;
  /** 医院编码   */
  hospitalCode?: string;
  /** 医院名称   */
  hospitalName?: string;
  /** 医生简介  */
  introduction?: string;
  /** 医生职称编码   */
  regTitelCode?: string;
  /** 医生职称名称   */
  regTitelName?: string;
  /** 号源时期  */
  scheduleDate?: string;
  /** 排班科室编码   */
  scheduleDeptCode?: string;
  /** 排班科室名称   */
  scheduleDeptName?: string;
  /** 状态 1 有号 2 约满 3 停诊   */
  status?: number;
  /** 职称排列顺序编号   */
  serialNumber?: number;
  /** 排班医生Code */
  docCode?: string;

  piType?: any;
  mdtFlag?: any;
  sourceChannel?: string;
  mdtTeamId?: string;
  teamId?: string;
}

/** 确认预约实体 */
export interface ISureAppointmentData extends IHxDoctorListByMoreTermData {
  /** 就诊日期 */
  admDate?: string;
  /** 就诊地点 */
  admLocation?: string;
  /** 就诊时段0 上午 1下午 2 全天 3夜间门诊 */
  admRange?: number;
  /** 就诊具体时间 */
  admTimeRange?: string;
  /** 预约状态-2 预约成功 5 已取消 6 已退号（等待退费确认） 7 已支付（取号） 8 已退费确认 , */
  appointStatus?: number;
  /** 预约时间 */
  appointTime?: string;
  /** 预约号 */
  appointmentNo?: string;
  /** 预约挂号提示语 */
  appointmentResult?: string;
  /** 订单号(订单业务标识) */
  bizSysSeq?: string;
  /** 渠道编码APP（大众端） WECHAT(微信端) */
  channelCode?: string;
  /** 渠道名称预约来源：APP（大众端） WECHAT(微信端) */
  channelName?: string;
  /** 下单时间 */
  orderCreateTime?: string;
  /** 订单状态 */
  orderStatus?: number;
  /** 病人Id */
  patientId?: string;
  /** 挂号费 */
  regFee?: number;
  /** 预约唯一ID(本系统产生) */
  sysAppointmentId?: string;
}

/** 预约详情实体 */
export interface IHXSelAppointmentDetailsData extends ISureAppointmentData {
  /** 预约状态提示多少分钟内支付-只有当预约状态为2的时候才有这个 */
  appointAutoCancelMin?: number;
  /** 预约挂号时间 */
  appointmentTime?: string;
  /** 1 预约挂号 2 当日挂号 */
  appointmentType?: number;
  /** 本系统生成的唯一交易订单号 */
  bizDealSeq?: string;
  /** 黑名单限制 */
  cancelBlackList?: number;
  /** 取消时间 */
  cancleTime?: string;
  /** 取消状态 */
  cancleType?: string;
  /** 病人卡ID 当日挂号去支付使用  */
  cardId?: string;
  /** 订单支付金额  */
  dealMoney?: number;
  /** 支付流水号 */
  dealSeq?: string;
  /** 医生头编码 */
  docCode?: string;
  /** 是否可退(0否 1 是) */
  isBack?: number;
  /** 是否可取消(0否 1 是) */
  isCancel?: number;
  /** 病人卡号 */
  patientCardNo?: string;
  /** 病人卡号 */
  patientIdCard?: string;
  /** 病人名字 */
  patientName?: string;
  /** 病人电话号码 */
  patientPhone?: string;
  /** 1 男 2 女 */
  patientSex?: number;
  /** 支付方式 */
  paymentSeq?: string;
  /** 支付时间 */
  paymentTime?: string;
  /** 退款金额 */
  refundMoney?: number;
  /** 退款状态-0 未发起退款申请 1 已退号(退款申请未知) 2 已退号(已申请成功待确认) 3 已退号(已退款成功待确认) 4 已退号 */
  refundStatus?: number;
  /** 退款时间 */
  refundTime?: string;
  /** 排班HISID 当日挂号特有 */
  scheduleHisId?: string;
  /** 平台服务费 */
  serviceFee?: number;
  /** 系统排班ID(系统产生) 当日挂号特有 */
  sysScheduleId?: string;
  /** 排班时段ID(本系统产生) 当日挂号特有 */
  sysTimeArrangeId?: string;

  isToPay: boolean;

  titleName: string;

  admRangeName: string;

  qrCode: string;
  patRegNo: string;
  appointmentId: string;
  hosId: string;
  hosAddress: string;
  commonProblem: { title?: string; content?: string }[];
  supportOrderTypeItems: any[];
  hospitalAreaName?: string;
  preConsultationStatus?: number;
  preConsultationUrl?: string;
}

/** 历史科室  */
export interface IHistoryDeptItem {
  deptCategoryCode: string;
  deptCategoryName: string;
  drainageInfo: string;
  hospitalCode: string;
}
/** 看过的科室  */
export interface IHistoryDoctorItem extends IHxDoctorListByMoreTermData {}

interface DeptDirectionList {
  deptDirectionCode: string;
  deptDirectionName: string;
}
/** 科室 */
export interface IDeptItem {
  hospitalCode: string;
  deptCategoryCode: string;
  deptCategoryName: string;
  orderNum: string;
  deptCategoryNameInitial: string;
  drainageInfo: string;
  deptDirectionList: DeptDirectionList[];
  hospitalAreaCode: string;
}

export interface IScheduleDateItem {
  scheduledDate: string;
  /** 状态 1 有号 2 约满 */
  status: number;
  week: string;
}

/** 号源开放信息实体 */
export interface ISourceOpenData {
  /** 医院放号时间文本 */
  hospitalOpenSourceTime?: string;
  /** 倒计时 */
  second?: string;
  /** 医院开放号源的标识-如果为1代表已到开放号源时间-为0代表未到放号时间 */
  sourceNoOpenHint?: number;
  /** 倒计时还剩-格式xx:xx:xx */
  surplusTime?: string;
  /** 倒计时还剩-格式xx时xx分xx秒 */
  surplusTimeChinese?: string;
}

export interface ISelWeekScheduleData {}
