import React, { useEffect, useState } from 'react';
import { PullToRefresh, Toast } from 'antd-mobile-v5';
import { Modal } from 'antd-mobile';
import { getOrganCode } from '@/utils/parameter';
import { HxEmpty } from '@/components';
import moment from 'moment';
import { Dispatch, history } from 'umi';
import { HxLocalStorage, HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import _ from 'lodash';
import qs from 'query-string';
import classnames from 'classnames';
import { selWeekSchedule } from '../service';
import styles from './index.less';

const noSchedule = 'https://cdnhyt.cd120.com/person/assets/appoinmentHx/no_schedule.png';
interface IProps {
  dispatch: Dispatch;
  location: {
    query: {
      deptCategoryCode?: string;
      hospitalAreaCode?: string;
      hospitalCode?: string;
      deptDirectionCode?: string;
      docCodeList?: string; // 科室主页跳转过来带的医生codelist
      deptCodeList?: string; // 科室主页跳转过来带的医生codelist
    };
  };
}

const HxWeeklySchedule: React.FC<IProps> = (props) => {
  const {
    location: {
      query: {
        deptCategoryCode = '',
        hospitalAreaCode = '',
        hospitalCode = 'HID0101',
        deptDirectionCode = '',
        docCodeList,
        deptCodeList,
      },
    },
  } = props;
  const [weekData, setWeekData] = useState<{ scheduledDate?: string; week?: string; scheduleData: any }[]>([]);
  const [tips, setTips] = useState('');
  const [chooseDoctorData, setChooseDoctorData] = useState<any>();
  const [scheduleInfoList, setScheduleInfoList] = useState<any>([]);
  const [loading, setLoading] = useState(false);

  //  type 0 | 1  0上午 1下午(全部copy华西逻辑)
  const initData = (type = 0, scheduledDate: string, scheduleInfoList: any = []) => {
    const filterData = _.filter(scheduleInfoList, (dataItem) => {
      const {
        scheduleDate,
        sourceItemsRespVos: { scheduleRange },
      } = dataItem;
      if (
        _.isEqual(scheduledDate, scheduleDate) &&
        type === 0 &&
        (scheduleRange === 0 || scheduleRange === 2 || scheduleRange === 99)
      ) {
        return dataItem;
      }
      if (
        _.isEqual(scheduledDate, scheduleDate) &&
        type === 1 &&
        (scheduleRange === 1 || scheduleRange === 2 || scheduleRange === 3 || scheduleRange === 99)
      ) {
        return dataItem;
      }
    });
    return filterData;
  };

  /**
   * 查询排班信息
   * @return {*}
   */

  const fetchData = async () => {
    /* 传卡id后台判断外省号源 */
    const { cardId = '' } = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA) || {};
    try {
      setLoading(true);
      Toast.show({ icon: 'loading', duration: 0 });
      const res = await selWeekSchedule({
        deptCategoryCode,
        hospitalAreaCode,
        deptDirectionCode,
        hospitalCode: getOrganCode(),
        cardId,
        docCodeList: docCodeList ? JSON.parse(docCodeList) : [],
        deptCodeList: deptCodeList ? JSON.parse(deptCodeList) : [],
      });
      const { scheduleDateList = [], scheduleInfoList = [] } = res || {};

      /* 处理周排班时间 */
      const _weekData = scheduleDateList.map((item) => {
        return {
          ...item,
          am: initData(0, item.scheduledDate, scheduleInfoList),
          pm: initData(1, item.scheduledDate, scheduleInfoList),
        };
      });
      setScheduleInfoList(scheduleInfoList);
      setWeekData(_weekData);
    } catch (error) {
      console.log('error:', error);
    } finally {
      Toast.clear();
      setLoading(false);
    }
  };

  /**
   * 跳转到确认预约页面
   * @return {*}
   */

  const appointmentNum = (value: any) => {
    const {
      docName = '',
      deptName = '',
      status = 4,
      sourceItemsRespVos = {},
      sign = 1,
      doctorId,
      deptCategoryCode,
      hospitalArea,
      hospitalCode,
    } = value;
    const {
      admLocation = '',
      isPrecise = 0,
      regFee,
      serviceFee,
      scheduleDate,
      scheduleRange,
      sysScheduleId,
      availableCount,
      mdtFlag,
      deptCode,
      teamId,
      sourceChannel,
    } = sourceItemsRespVos;
    const currentAvaliable = availableCount || 0;
    if (status === 1) {
      const data = {
        hospitalCode,
        hospitalAreaCode,
        docName,
        deptName,
        admLocation,
        isPrecise,
        regFee,
        serviceFee,
        scheduleDate,
        scheduleRange,
        sysScheduleId,
        mdtFlag,
        deptCode,
        teamId,
        sourceChannel,
        ...sourceItemsRespVos,
      };
      HxLocalStorage.set(StorageEnum.APPOINTMENT_SOURCE_DETAIL, data);
      /* mdt跳转 */
      if ((sourceChannel === 'unLineMdt' || sourceChannel === 'lineMdt') && hospitalCode === 'HID0101') {
        const patientCardInfo = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA);
        const redirect = '/appointment/detail';
        if (patientCardInfo && isPrecise !== 1) {
          localStorage.removeItem('_MDTINFO');
          const params = { sourceChannel, isMdt: 1, data: JSON.stringify(patientCardInfo) };
          history.push(`/appointment/mdtinfo?redirect=${redirect}&${qs.stringify(params)}`);
          return;
        }
        history.push({
          pathname: '/patientcard/home',
          query: {
            isMdt: 1,
            redirect,
            sourceChannel,
            teamName: sourceItemsRespVos.teamName,
          },
        });
        return;
      }
      if (isPrecise === 1) {
        history.push({
          pathname: '/doctor/index',
          query: {
            doctorId,
            deptCategoryCode,
            hospitalArea,
            hospitalCode,
            hospitalAreaCode,
          },
        });
        // history.push(`/hxappointment/dayparting?data=${JSON.stringify(value)}`);
      } else {
        /* 直接到确认预约界面 */
        history.push('/hxappointment/detail');
      }
    }
    if (status === 2) {
      Toast.show({ content: '该医生已无号源，请选择其他号源', duration: 1500 });
    }
    if (status === 3) {
      Toast.show({ content: '该医生已停诊，请选择其他号源', duration: 1500 });
    }
  };

  // 弹出该医生提示
  const onTips = (value: any) => {
    setChooseDoctorData(value);
    setTips(value.tips);
  };

  useEffect(() => {
    fetchData();
  }, []);
  return (
    <div className={styles.weklySchedule}>
      <header className={styles.header}>
        <span>日期</span>
        <span>上午</span>
        <span>下午</span>
      </header>
      <PullToRefresh
        onRefresh={async () => {
          await fetchData();
        }}
      >
        <main>
          {scheduleInfoList.length ? (
            weekData.map((item) => {
              return (
                <div className={styles.card} key={item.scheduledDate}>
                  <div className={styles.date}>
                    <span className={styles.time}>{moment(item.scheduledDate).format('MM-DD')}</span>
                    <span className={styles.text}>{item.week}</span>
                  </div>
                  <div className={classnames(styles.morning, item?.am.length === 0 && styles.noData)}>
                    {item?.am && item?.am.length > 0 ? (
                      (item?.am || []).map((val, index) => (
                        <div
                          key={index}
                          className={val?.status === 1 ? styles.active : ''}
                          onClick={() => (!val?.tips ? appointmentNum(val) : () => onTips(val))}
                        >
                          {val.docName}
                        </div>
                      ))
                    ) : (
                      <div>-</div>
                    )}
                  </div>
                  <div className={classnames(styles.afternoon, item?.pm.length === 0 && styles.noData)}>
                    {item?.pm && item?.pm.length > 0 ? (
                      (item?.pm || []).map((val, index) => (
                        <div
                          key={index}
                          className={val?.status === 1 ? styles.active : ''}
                          onClick={() => (!val?.tips ? appointmentNum(val) : () => onTips(val))}
                        >
                          {val.docName}
                        </div>
                      ))
                    ) : (
                      <div>-</div>
                    )}
                  </div>
                </div>
              );
            })
          ) : loading ? null : (
            <div className={styles.empty}>
              <HxEmpty canRefresh={false} isNewImg emptyImg={noSchedule} emptyMsg="暂无排班信息" />
            </div>
          )}
        </main>
      </PullToRefresh>

      <Modal
        visible={tips !== ''}
        title="提示"
        transparent
        maskClosable={false}
        footer={[
          {
            text: '确认',
            onPress: () => {
              appointmentNum(chooseDoctorData);
            },
          },
        ]}
      >
        <div className={styles.content} style={{ textAlign: 'left' }}>
          <div>
            {tips.split('\n').map((item) => {
              return (
                <div className="text" key={item}>
                  {item}
                </div>
              );
            })}
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default HxWeeklySchedule;
