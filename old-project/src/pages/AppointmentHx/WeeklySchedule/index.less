.weklySchedule {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f6fa;
  position: relative;
  .empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 70vh;
  }
  .header {
    width: 100%;
    height: 88px;
    background: #ffffff;
    position: sticky;
    left: 0;
    top: 0;
    display: flex;
    align-items: center;
    justify-content: space-around;
    padding: 0 24px;
    span {
      flex: 1;
      font-size: 28px;
      font-weight: 500;
      color: #03081a;
      line-height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  main {
    padding: 24px;

    .card {
      background: #ffffff;
      border-radius: 24px;
      min-height: 160px;
      width: 100%;
      display: flex;
      padding: 32px 0;
      &:not(:first-child) {
        margin-top: 24px;
      }
      > div {
        flex: 1;
        display: flex;
        flex-direction: column;
        // justify-content: center;
        align-items: center;
      }
      .morning,
      .afternoon {
        font-size: 28px;
        text-align: center;
        color: #989eb4;
        line-height: 40px;
        &.noData {
          align-self: center;
        }
        .active {
          color: #3ad3c1;
        }
        > div {
          &:not(:first-child) {
            margin-top: 32px;
          }
        }
      }
      .date {
        height: auto;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        .time {
          font-size: 36px;
          font-weight: bold;
          text-align: center;
          color: #03081a;
          line-height: 48px;
        }
        .text {
          font-size: 28px;
          text-align: center;
          color: #989eb4;
          line-height: 40px;
          margin-top: 8px;
        }
      }
    }
  }
}
