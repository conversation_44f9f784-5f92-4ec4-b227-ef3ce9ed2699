/*
 * @Author: <PERSON>
 * @Date: 2022-03-22 17:17:54
 * @LastEditor: <PERSON>
 * @LastEditTime: 2024-12-30 17:01:56
 * @FilePath: /hyt-person/src/pages/DoctorDetailAggregationHX/Home/components/Appointment/_utils.ts
 * Copyright (c) 2022 by <PERSON>, All Rights Reserved.
 * @Description:
 */
import crypto from 'crypto-js';

const endTotalSeconds = (): number => {
  const now: Date = new Date();
  const hour = now.getHours();
  const minutes = now.getMinutes();
  const seconds = now.getSeconds();
  const total = 12 * 60 * 60;
  if (hour < 8) {
    return total - ((4 + hour) * 60 * 60 + minutes * 60 + seconds);
  }
  if (hour < 20) {
    return total - ((hour - 8) * 60 * 60 + minutes * 60 + seconds);
  }
  return total - ((23 - hour + 8) * 60 * 60 + minutes * 60 + seconds);
};
const addzearo = (num: any) => {
  if (num < 10) {
    return `0${num}`;
  }
  return num;
};
const endTime = (total: number): string => {
  const hours = Math.floor((total / 60 / 60) % 24); // 计算剩余的小时
  const minutes = Math.floor((total / 60) % 60); // 计算剩余的分钟
  const seconds = Math.floor(total % 60); // 计算剩余的秒数

  return `${addzearo(hours)}:${addzearo(minutes)}:${addzearo(seconds)}`;
};
const showCountDownStr = () => {
  return endTime(endTotalSeconds());
};

// 加密字段
const encryptionStr = (bizSeq: string) => {
  const len = bizSeq.length;
  let sss = '';
  for (let i = 0; i < len; i++) {
    if (i % 2 === 0) {
      sss += bizSeq[i];
    }
  }
  const key = APP_ENV === 'prod' ? '87R6W73IO8C43P32' : '0CoJUm6Qyw8W8jud';
  const iv = APP_ENV === 'prod' ? crypto.enc.Utf8.parse('T7K702065Y9BZSZU') : crypto.enc.Utf8.parse('DYgjCEIMVrj2W9xN');
  return crypto.AES.encrypt(crypto.enc.Utf8.parse(sss), crypto.enc.Utf8.parse(key), {
    iv,
    mode: crypto.mode.CBC,
    padding: crypto.pad.Pkcs7,
  }).toString();
};

export { endTotalSeconds, endTime, showCountDownStr, encryptionStr };
