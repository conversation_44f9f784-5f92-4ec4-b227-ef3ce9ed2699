/*
 * @Author: <PERSON>
 * @Date: 2022-03-08 14:02:08
 * @LastEditor: <PERSON>
 * @LastEditTime: 2024-12-31 09:33:41
 * @FilePath: /hyt-person/src/pages/DoctorDetailAggregationHX/Home/components/Appointment/countDown.tsx
 * Copyright (c) 2022 by <PERSON>, All Rights Reserved.
 * @Description:
 */
import React, { useState, useEffect, FC } from 'react';
import { endTime } from './_utils';

interface PageProps {
  delaySeconds: number;
  getDocInfo: Function;
}
let timer: any = null;
const App: FC<PageProps> = ({ delaySeconds, getDocInfo }) => {
  const [str, setStr] = useState<string>('');
  useEffect(() => {
    if (delaySeconds === 0) {
      return;
    }
    timer = setInterval(() => {
      const res = endTime(delaySeconds);
      if (delaySeconds === 0) {
        // 倒计时结束
        clearInterval(timer);
        console.log(timer);
        getDocInfo();
      } else {
        setStr(res);
      }
      delaySeconds -= 1;
    }, 1000);
  }, []);
  return <div>{str}</div>;
};
export default App;
