.AppointmentDetail {
  width: 100%;
  height: 100vh;
  display: flex;
  background: #f5f6fa;
  flex-direction: column;
  .loadingWrap {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  main {
    padding: 24px 24px 0;
    padding-bottom: calc(24px + env(safe-area-inset-bottom));
    flex: 1;
    overflow-y: auto;
    .hint {
      background-color: #fff;
      border-radius: 24px;
      padding: 32px 24px;
      margin-top: 24px;
      .header {
        font-size: 32px;
        font-weight: bold;
        text-align: left;
        color: #03081a;
        line-height: 44px;
        display: flex;
        align-items: center;
        img {
          width: 36px;
          height: 36px;
          margin-right: 12px;
        }
      }
      .content {
        margin-top: 24px;
        font-size: 28px;
        color: #51586d;
        line-height: 44px;
      }
    }
  }
  .myPwoer {
    display: flex;
    text-align: center;
    height: 7vw;
    font-size: 3.7333vw;
    align-items: center;
    justify-content: center;
    margin-top: 24px;
    img {
      height: 3.7333vw;
      width: 3.7333vw;
      margin-right: 1vw;
    }
  }
  footer {
    width: 100%;
    padding: 16px 24px;
    background-color: #fff;
    padding-bottom: calc(16px + env(safe-area-inset-bottom));
    position: relative;
    .appointmentBtn {
      position: relative;
      z-index: 9;
    }
    @keyframes fade-in {
      0% {
        opacity: 0;
        top: 0;
      }
      70% {
        opacity: 0.7;
      }
      100% {
        opacity: 1;
        top: -2.3rem;
      }
    }
    .appointmentTimeDown {
      width: 100%;
      height: 2.4rem;
      background: #ffe9eb;
      position: absolute;
      left: 0;
      top: 0;
      opacity: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 26px;
      color: #fc4553;
      animation: fade-in 0.5s ease-in-out forwards;
    }
  }
}
