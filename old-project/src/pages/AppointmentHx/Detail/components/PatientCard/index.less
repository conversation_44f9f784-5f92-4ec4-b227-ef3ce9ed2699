.PatientCard {
  padding: 32px 24px;
  background: #ffffff;
  border-radius: 24px;
  min-height: 224px;
  margin-top: 24px;
  .header {
    font-size: 32px;
    font-weight: bold;
    color: #03081a;
    line-height: 48px;
  }
  .chooseCardContent {
    margin-top: 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    > img {
      width: 36px;
      height: 36px;
    }
    .normal-text-color {
      color: #989eb4;
    }
    .leftInfo {
      display: flex;
      align-items: center;
    }
    .cardInfo {
      height: 96px;
      margin-left: 24px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .bottom {
        font-size: 28px;
        font-weight: 400;

        .tel {
          margin-left: 22px;
        }
      }

      .top {
        display: flex;
        align-items: center;

        .name {
          font-size: 36px;
          font-weight: 600;
        }

        .tag {
          width: 72px;
          height: 36px;
          border-radius: 18px;
          margin-left: 16px;
          color: #989eb4;
          background-color: #ebedf5;
          font-size: 24px;
          font-weight: 400;
          text-align: center;
          line-height: 36px;

          &.isSelf {
            color: #fff;
            background-color: #3ad3c1;
          }
        }
      }
    }

    .img {
      width: 96px;
      height: 96px;
    }
  }
  .add {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    font-weight: bold;
    text-align: center;
    color: #3ad3c1;
    line-height: 40px;
    height: 80px;
    border: 2px dashed #3ad3c1;
    border-radius: 42px;
    margin-top: 32px;
  }
}
:global {
  .patientCardPopup {
    .patientCardPopupContent {
      height: 100%;
      display: flex;
      flex-direction: column;
      .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 24px 32px;
        span {
          font-size: 36px;
          font-weight: bold;
          color: #03081a;
        }
        img {
          width: 40px;
          height: 40px;
        }
      }
      .cardMain {
        flex: 1;
        overflow-y: auto;
        padding: 0 32px;
        .cardItem {
          width: 100%;
          height: 160px;
          display: flex;
          align-items: center;
          padding-left: 24px;
          box-sizing: border-box;
          margin-top: 32px;

          &.select {
            background: url('https://cdnhyt.cd120.com/person/assets/appoinmentHx/card_select_bg.png') no-repeat;
            background-size: cover;
            background-position: 0 0;
          }

          &.noSelect {
            background: url('https://cdnhyt.cd120.com/person/assets/appoinmentHx/card_bg.png') no-repeat;
            background-size: cover;
            background-position: 0 0;
          }
          &:not(:first-child) {
            margin-top: 32px;
          }
          .cardInfo {
            height: 96px;
            margin-left: 24px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .normal-text-color {
              color: #989eb4;
            }

            .bottom {
              font-size: 28px;
              font-weight: 400;

              .tel {
                margin-left: 22px;
              }
            }

            .top {
              display: flex;
              align-items: center;

              .name {
                font-size: 36px;
                font-weight: 600;
              }

              .tag {
                width: 72px;
                height: 36px;
                border-radius: 18px;
                margin-left: 16px;
                color: #989eb4;
                background-color: #ebedf5;
                font-size: 24px;
                font-weight: 400;
                text-align: center;
                line-height: 36px;

                &.isSelf {
                  color: #fff;
                  background-color: #3ad3c1;
                }
              }
            }
          }

          .img {
            width: 96px;
            height: 96px;
          }
        }
      }
      .footer {
        padding: 24px 40px;
        padding-bottom: calc(24px + env(safe-area-inset-bottom));
      }
    }
  }
}
