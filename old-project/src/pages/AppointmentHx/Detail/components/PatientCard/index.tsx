import React, { useEffect, useState } from 'react';
import { Popup, Button, Toast, Modal } from 'antd-mobile-v5';
import classnames from 'classnames';
import { HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import _ from 'lodash';
import { ICardItem } from '@/pages/PatientCard/data';
import HxModal from '@/components/HxModal';
import { history } from 'umi';
import styles from './index.less';
import { getCardList } from '../../../service';
import { BASE_IMG_URL } from '../../../utils/config';

const close = `${BASE_IMG_URL}/close_online.png`;
const man = `${BASE_IMG_URL}/man.png`;
const woman = `${BASE_IMG_URL}/woman.png`;
const arrow = `${BASE_IMG_URL}/card_arrow.png`;

interface IProps {
  onChange?: () => void;
  editCardStatus?: boolean;
}
const PatientCard: React.FC<IProps> = (props) => {
  const { onChange, editCardStatus } = props;
  const patientCardInfo: ICardItem = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA) || {};
  const [selectCard, setSelectCard] = useState<string>(patientCardInfo?.cardId || '');
  const [userCardList, setCardList] = useState<ICardItem[]>([]);
  const [visible, setVisible] = useState(false);
  const ellipsisTel = (tel: string) => (tel ? `${tel.slice(0, 3)}****${tel.slice(tel?.length - 4)}` : '--');
  const [canBindCount, setCanBindCount] = useState(0);

  /**
   * 获取卡列表
   * @return {*}
   */
  const fetchCardList = async () => {
    try {
      Toast.show({ icon: 'loading', duration: 0 });
      const data = await getCardList({});
      const { userCardList = [], canBindCount } = data || {};
      setCanBindCount(canBindCount);
      setCardList(userCardList);
    } catch (error) {
      console.log('error:', error);
    } finally {
      Toast.clear();
    }
  };

  /**
   * 弹出弹框
   * @return {*}
   */

  const visibleCard = async () => {
    //MDT不能切换就诊卡
    if (!editCardStatus) return;
    try {
      if (!userCardList.length) {
        await fetchCardList();
      }
      setVisible(true);
    } catch (error) {
      console.log('error:', error);
    }
  };

  /**
   * 切换就诊人
   * @return {*}
   */

  const handleCheckCard = (item: ICardItem) => {
    if (item?.cardId === selectCard) return;
    HxSessionStorage.set(StorageEnum.PATIENTCARD_DATA, item);
    setSelectCard(item?.cardId || '');
    setVisible(false);
    setTimeout(() => {
      onChange && onChange();
    }, 500);
  };

  /**
   * 添加就诊卡
   */
  const addPatientCard = () => {
    if (canBindCount === 0) {
      HxModal.show({
        title: '提示',
        content: '您的就诊卡已达上限，暂不能添加就诊卡',
        actions: [
          {
            text: '确定',
            onClick: () => {
              Modal.clear();
            },
          },
        ],
      });
      return;
    }
    history.push('/patientcard/add');
  };

  return (
    <div className={styles.PatientCard}>
      <div className={styles.header}>就诊人</div>

      {_.isEmpty(patientCardInfo) ? (
        <div className={styles.add} onClick={visibleCard}>
          添加就诊人
        </div>
      ) : (
        <div className={styles.chooseCardContent} onClick={visibleCard}>
          <div className={styles.leftInfo}>
            <img src={patientCardInfo?.gender === 1 ? man : woman} alt="" className={styles.img} />
            <div className={styles.cardInfo}>
              <div className={styles.top}>
                <span className={styles.name}>{patientCardInfo?.patientName}</span>
                <div className={classnames(styles.tag, patientCardInfo?.isSelf === 0 && styles.isSelf)}>
                  {patientCardInfo.isSelf === 0 ? '本人' : '其他'}
                </div>
              </div>
              <div className={styles.bottom}>
                <span className={classnames(styles.gender, styles['normal-text-color'])}>
                  {patientCardInfo?.gender === 1 ? '男' : '女'} {patientCardInfo?.age}岁
                </span>
                <span className={classnames(styles.tel, styles['normal-text-color'])}>
                  {ellipsisTel(patientCardInfo?.tel || '')}
                </span>
              </div>
            </div>
          </div>
          {editCardStatus && <img src={arrow} alt="" />}
        </div>
      )}

      {/* 就诊人弹框 */}
      <Popup
        visible={visible}
        bodyStyle={{
          borderTopLeftRadius: '12px',
          borderTopRightRadius: '12px',
          height: '80vh',
        }}
        className="patientCardPopup"
      >
        <div className="patientCardPopupContent">
          <div className="header">
            <span>选择就诊人</span>
            <img src={close} alt="" onClick={() => setVisible(false)} />
          </div>
          <div className="cardMain">
            {userCardList.map((item) => {
              return (
                <div
                  className={classnames('cardItem', selectCard === item.cardId ? 'select' : 'noSelect')}
                  key={item?.cardId}
                  onClick={() => handleCheckCard(item)}
                >
                  <img src={item?.gender === 1 ? man : woman} alt="" className="img" />
                  <div className="cardInfo">
                    <div className="top">
                      <span className="name">{item?.patientName}</span>
                      <div className={classnames('tag', 'isSelf')}>{item?.isSelf === 0 ? '本人' : '其他'}</div>
                    </div>
                    <div className="bottom">
                      <span className="gender normal-text-color">
                        {item?.gender === 1 ? '男' : '女'} {item?.age}岁
                      </span>
                      <span className="tel normal-text-color">{ellipsisTel(item?.tel || '')}</span>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
          <div className="footer">
            <Button
              size="large"
              shape="rounded"
              block
              color="primary"
              onClick={() => {
                try {
                  if (window.whetherAlipay) {
                    my.postMessage({ name: 'create-card-adult' });
                  } else {
                    addPatientCard();
                  }
                } catch (error) {
                  console.log('error:', error);
                }
              }}
            >
              添加就诊人
            </Button>
          </div>
        </div>
      </Popup>
    </div>
  );
};

export default PatientCard;
