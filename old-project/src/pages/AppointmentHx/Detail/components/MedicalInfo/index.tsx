import React from 'react';
import { history } from 'umi';
import { HxDoctorAvatar } from '@/components';
import { HxLocalStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import dayjs from 'dayjs';
import styles from './index.less';

const arrow = 'https://cdnhyt.cd120.com/person/assets/appoinmentHx/arrow.png';

const scheduleRangeList = ['上午', '下午', '全天', '夜间门诊'];
interface IProps {}
const MedicalInfo: React.FC<IProps> = (props) => {
  const appoinSourceDetail: any = HxLocalStorage.get(StorageEnum.APPOINTMENT_SOURCE_DETAIL);
  const {
    docName = '',
    deptName = '',
    docHeadImage = '',
    regTitelName = '',
    hospitalName = '',
    regFee,
    serviceFee,
    admLocation,
    dayDesc,
    scheduleDate,
    week,
    rangeName,
    scheduleRange,
    isPrecise,
    startTime,
    endTime,
    hospitalAreaName,
  } = appoinSourceDetail || {};
  return (
    <div className={styles.MedicalInfo}>
      <div className={styles.header}>就诊信息</div>
      <div className={styles.doctorInfo}>
        <HxDoctorAvatar src={docHeadImage} alt="" style={{ width: '48px', height: '48px', borderRadius: '50%' }} />
        <div className={styles.info}>
          <div className={styles.doctor}>
            <span>{docName}</span>
            <span>{regTitelName}</span>
          </div>
          <div className={styles.organName}>{hospitalName}</div>
        </div>
      </div>
      <div className={styles.userAppointment}>
        <div className={styles.title}>您的预约就诊时间</div>
        <div className={styles.timeInfo}>
          <div className={styles.time}>
            <span className={styles.text}>
              {dayjs(scheduleDate).format('MM月DD日')} {week} {rangeName || scheduleRangeList[scheduleRange]}{' '}
              {isPrecise === 0 ? '' : `${startTime} -  ${endTime}`}
            </span>
            <span className={styles.tag}>{dayDesc}</span>
          </div>
          <div
            className={styles.rightInfo}
            onClick={() => {
              history.go(-1);
            }}
          >
            <span>重新选择</span>
            <img src={arrow} alt="" />
          </div>
        </div>
        <div className={styles.row}>
          <span className={styles.label}>就诊科室</span>
          <span className={styles.value}>
            {hospitalAreaName} {deptName}
          </span>
        </div>
        <div className={styles.row}>
          <span className={styles.label}>挂号费用</span>
          <span className={styles.price}>{(Number(regFee) + Number(serviceFee)).toFixed(2)}元</span>
        </div>
        <div className={styles.row}>
          <span className={styles.label}>就诊地点</span>
          <span className={styles.value}>{admLocation}</span>
        </div>
      </div>
    </div>
  );
};

export default MedicalInfo;
