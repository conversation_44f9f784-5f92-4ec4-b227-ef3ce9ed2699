.MedicalInfo {
  padding: 32px 24px;
  background: #ffffff;
  border-radius: 24px;
  min-height: 614px;
  .header {
    font-size: 32px;
    font-weight: bold;
    color: #03081a;
    line-height: 48px;
  }
  .doctorInfo {
    display: flex;
    align-items: center;
    margin-top: 32px;
    padding-bottom: 32px;
    border-bottom: 2px solid #f6f8ff;
    .info {
      margin-left: 24px;
      .doctor {
        span {
          &:nth-child(1) {
            font-size: 32px;
            font-weight: bold;
            text-align: left;
            color: #03081a;
            line-height: 44px;
            margin-right: 16px;
          }
          &:nth-child(2) {
            font-size: 24px;
            color: #03081a;
            line-height: 36px;
          }
        }
      }
      .organName {
        font-size: 24px;
        text-align: left;
        color: #989eb4;
        line-height: 36px;
        margin-top: 14px;
      }
    }
  }
  .userAppointment {
    padding-top: 32px;
    .title {
      font-size: 28px;
      text-align: left;
      color: #989eb4;
      line-height: 36px;
    }
    .timeInfo {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 24px;
      .rightInfo {
        display: flex;
        align-items: center;
        span {
          font-size: 28px;
          text-align: left;
          color: #3ad3c1;
          line-height: 36px;
        }
        img {
          width: 32px;
          height: 32px;
        }
      }
      .time {
        display: flex;
        align-items: center;
        .text {
          font-size: 36px;
          font-weight: bold;
          text-align: left;
          color: #03081a;
          line-height: 44px;
        }
        .tag {
          font-size: 20px;
          text-align: left;
          color: #51586d;
          padding: 3px 8px;
          margin-left: 16px;
          background: #ebedf5;
          border-radius: 8px;
        }
      }
    }
    .row {
      margin-top: 32px;
      display: flex;
      // align-items: center;
      justify-content: space-between;
      column-gap: 40px;
      span {
        font-size: 28px;
        line-height: 36px;
        &:first-child {
          color: #989eb4;
        }
        &:nth-child(2) {
          flex: 1;
          text-align: right;
          color: #03081a;
        }
        &.price {
          color: #fc4553;
        }
      }
    }
  }
}
