import React, { useEffect, useState } from 'react';
import { connect, Dispatch, Loading, history } from 'umi';
import { Button, DotLoading, Toast, Modal as ModalV5 } from 'antd-mobile-v5';
import { Modal, InputItem, Toast as ToastV2 } from 'antd-mobile';
import { HxLocalStorage, HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import _ from 'lodash';
import { getOpenId, getOrganCode } from '@/utils/parameter';
import { hxGetImageCode } from '@/pages/Appointment/service';
import HxModal from '@/components/HxModal';
import { isWechat } from '@/utils/platform';
import Payment from '@/utils/Payment';
import { fetchUserOpenid } from '@/services/global';
import { MedicalInfo, PatientCard } from './components';
import CountDown from './CountDown';
import powerImg from '../../../assets/能量球-40px.png';
import powerImgBg from '../../../assets/弹窗.png';
import { checkAppointment, selSourceOpenTime as selSourceOpenTimeApi, sureAppointmentToPay } from '../service';
import { ISourceOpenData } from '../data';
import { encryptionStr } from './_utils';
import styles from './index.less';

const hint = 'https://cdnhyt.cd120.com/person/assets/appoinmentHx/hint_icon.png';

interface IProps {
  dispatch: Dispatch;
  loading: Boolean | undefined;
  location: {
    query: {
      channelElderCode?: string;
      sourceChannel?: string;
      teamName?: string;
      type?: string;
    };
  };
}
const Alert = Modal.alert;
const AppointmentDetail: React.FC<IProps> = (props) => {
  const {
    location: { query },
  } = props;
  const { channelElderCode, sourceChannel, teamName, isMdt, type } = query;
  const appoinSourceDetail = HxLocalStorage.get(StorageEnum.APPOINTMENT_SOURCE_DETAIL);
  const patientCard = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA);
  const { whetherAlipay }: any = window;
  const [myPowerModal, setMyPowerModal] = useState(false);
  const [visible, setVisible] = useState(false);
  const [imageCode, setImageCode] = useState('');
  const [imageData, setImageData] = useState('');
  const [sysScheduleId, setSysScheduleId] = useState('');
  const [bizSeq, setBizSeq] = useState('');
  const [authCode, setAuthCode] = useState(''); // 支付宝授权码
  const [hasImageCode, setHasImageCode] = useState(1);
  const [errorMsg, setErrorMsg] = useState('');
  const [checkInfo, setCheckInfo] = useState({});
  const [sourceInfo, setSourceInfo] = useState<ISourceOpenData>({});
  const [btnDisabled, setBtnDisabled] = useState(false); // 倒计时是否禁用按钮
  const [imageCodeLoading, setImageCodeLoading] = useState(false);
  const [editCardStatus, setEditCardStatus] = useState(true);

  const { sourceNoOpenHint, second = 0 } = sourceInfo || {};

  const onChangeText = (value: string) => {
    setImageCode(value);
  };
  const onBlur = () => {
    window.scroll(0, 0);
  };

  const onConfirm = () => {
    if (imageCode.length < 4) {
      Toast.show({ content: '请输入4位图形验证码', duration: 1500 });
      return;
    }
    sureAppointment();
  };

  // 获取图形验证码
  const getImageCode = async () => {
    try {
      setImageCodeLoading(true);
      const data = await hxGetImageCode({
        type: 'WEB',
        sysScheduleId: getOrganCode() === 'HID0101' ? sysScheduleId : '',
      });
      const { imageData = '', bizSeq = '' } = data;
      const imageBase = imageData.replace(/[\r\n]/g, '');
      setImageData(`data:image/png;base64,${imageBase}`);
      setBizSeq(bizSeq);
    } catch (error) {
      console.log('error:', error);
    } finally {
      setImageCodeLoading(false);
    }
  };

  /* 错误提示 */
  const alertErrorMsg = () => {
    Alert('', <div>{checkInfo?.errorMsg}</div>, [{ text: '确认', onPress: () => {} }]);
  };

  /** 流调表对话框 */
  const alertLdbInfo = () => {
    Alert('', <div>{checkInfo?.cpmMessage}</div>, [
      { text: '取消', onPress: () => {} },
      {
        text: '立即前往',
        onPress: () => {
          window.location.href = checkInfo.scaleUrl;
        },
      },
    ]);
  };

  const aliStatistics = () => {
    try {
      __bl.custom({
        title: '预约挂号确认按钮点击量',
        desc: '预约挂号确认按钮点击',
      });
    } catch (error) {
      console.log(error);
    }
  };

  // 检查号源
  const fetchData = async () => {
    console.log(99999999);
    const { hospitalCode, sysScheduleId = '' } = appoinSourceDetail;
    const { cardId } = patientCard || {};
    try {
      Toast.show({ icon: 'loading', duration: 0 });
      const data = await checkAppointment({
        appointmentType: 1,
        hospitalCode,
        cardId,
        sysScheduleId,
      });
      const { hasImageCode = 1, errorMsg = '', isSubmitFlowTable = '0', specialAppointmentTips = '' } = data;

      /* 特需提示 */
      if (specialAppointmentTips && getOrganCode() === 'HID0101') {
        ModalV5.alert({
          title: (
            <div style={{ justifyContent: 'center', fontSize: '18px', display: 'flex', alignItems: 'center' }}>
              <img src={hint} alt="" style={{ width: '18px', marginRight: '4px' }} />
              温馨提示
            </div>
          ),
          content: (
            <div dangerouslySetInnerHTML={{ __html: specialAppointmentTips }} style={{ padding: '0 10px 10px' }} />
          ),
          confirmText: '确定',
        });
      }

      setHasImageCode(hasImageCode);
      setCheckInfo(data);
      if (errorMsg && errorMsg !== '') {
        setErrorMsg(errorMsg);
      }
    } catch (error) {
      console.log('error:', error);
    } finally {
      Toast.clear();
    }
  };

  // 查询号源开放情况
  const selSourceOpenTime = async () => {
    const { hospitalCode, sysScheduleId } = appoinSourceDetail;
    try {
      const data = await selSourceOpenTimeApi({ appointmentType: 1, hospitalCode, sysScheduleId });
      setSysScheduleId(sysScheduleId);
      setSourceInfo(data);
      const { second = 0 } = data;
      setBtnDisabled(!!second);
    } catch (error) {
      console.log('error:', error);
    }
  };

  /**
   * 去支付
   * @return {*}
   */
  const handleAppointmentError = async (res: any) => {
    /**
     * 900001  无可用号源
     * 900008  xxxxx病房限制最小年龄14岁
     */
    const errCodeList = ['900001', '900008'];
    const errModalBtn = {
      '900001': '申请线上门诊',
      '900008': '儿童医学中心',
    };
    const { data = {}, msg = '', errCode = '', code } = res || {};
    if (errCode === '400000001') {
      const { sysAppointmentId = '', bizDealSeq = '' } = data || {};
      setVisible(false);
      HxModal.show({
        title: '就诊提醒',
        content: <div>{msg}</div>,
        closeOnMaskClick: true,
        actions: [
          {
            text: '取消',
            key: 'cancel',
            onClick: () => {
              ModalV5.clear();
            },
          },
          {
            text: '去处理',
            key: 'confirm',
            className: 'primary',
            onClick: () => {
              ModalV5.clear();
              history.push(`/hxappointment/order/detail?sysAppointmentId=${sysAppointmentId}&bizDealSeq=${bizDealSeq}`);
            },
          },
        ],
      });
      return;
    }
    /* 存在相同号源 */
    if (errCode === '404025' || errCode === '404024') {
      const { sysAppointmentId = '' } = data || {};
      setVisible(false);
      HxModal.show({
        title: '预约失败',
        content: <div>{msg}</div>,
        closeOnMaskClick: true,
        actions: [
          {
            text: '取消',
            key: 'cancel',
            onClick: () => {
              ModalV5.clear();
            },
          },
          {
            text: errCode === '404024' ? '去支付' : '查看',
            key: 'confirm',
            className: 'primary',
            onClick: () => {
              ModalV5.clear();
              history.push(`/hxappointment/order/detail?sysAppointmentId=${sysAppointmentId}`);
            },
          },
        ],
      });
      return;
    }
    if (code === '0' && errCodeList.includes(errCode)) {
      setVisible(false);
      HxModal.show({
        title: '预约失败',
        content: <div>{msg}</div>,
        closeOnMaskClick: true,
        actions: [
          {
            text: '取消',
            key: 'cancel',
            onClick: () => {
              ModalV5.clear();
            },
          },
          {
            text: errModalBtn[errCode],
            key: 'confirm',
            className: 'primary',
            onClick: () => {
              ModalV5.clear();
              window.location.href = data?.jumpUrl;
            },
          },
        ],
      });
    } else {
      ToastV2.fail(msg, 2);
    }
  };

  /**
   *  挂号预约-去支付
   * @return {*}
   */
  const sureAppointment = async () => {
    Toast.show({ icon: 'loading', duration: 0, content: '预约中' });
    const { cardId } = patientCard || {};
    const { hospitalCode, hospitalAreaCode, sysScheduleId, sysTimeArrangeId, startNoAndendNo } = appoinSourceDetail;

    // 先判断获取openid
    let openId = getOpenId();
    try {
      if (!openId && isWechat()) {
        const res = await fetchUserOpenid();
        res && res?.openID && (openId = res?.openID);
      }
    } catch (error) {
      console.log('error:', error);
      Toast.clear();
    }

    let payload: any = {
      appointmentType: 1,
      cardId,
      hospitalCode,
      hospitalAreaCode,
      sysScheduleId,
      sysTimeArrangeId,
      channelElderCode,
      admNo: startNoAndendNo,
      type: 'WEB',
      aliAuthCode: authCode,
      encrypt: encryptionStr(bizSeq),
      sourceChannel: sourceChannel === '1' ? 1 : null,
      teamName: teamName || null,
      openId,
    };
    if (isMdt === '1') {
      const _MDTINFO = localStorage.getItem('_MDTINFO') || '';
      const MDTINFO = JSON.parse(_MDTINFO);
      payload = {
        ...payload,
        mdtClinicalDataVO: {
          ...MDTINFO,
        },
      };
    }
    if (hasImageCode === 1) {
      payload.verifyCode = imageCode;
      payload.imageId = bizSeq;
    }
    console.log('确认预约请求参数', payload);
    try {
      const res = await sureAppointmentToPay(payload);
      const { code } = res;
      Toast.clear();
      if (code === '1') {
        setVisible(false); // 关闭弹框
        const { bizSysSeq = '', dealSeq = '', merchantSeq = '' } = res?.data || {};
        Payment.toPay({ bizSysSeq, dealSeq, merchantSeq });
        return;
      }
      /* 处理错误 */
      handleAppointmentError(res);
    } catch (error) {
      console.log('error:', error);
      Toast.clear();
    }
  };

  const confirm = () => {
    /* 确认预约首先交验是否选卡 */
    if (!patientCard || !patientCard?.cardId) {
      Toast.show({ content: '请选择就诊人', duration: 1500 });
      return;
    }
    // 阿里支付宝埋点
    const { whetherAlipay }: any = window;
    if (whetherAlipay) {
      aliStatistics();
    }
    if (checkInfo?.errorMsg) {
      alertErrorMsg();
      return;
    }
    if (checkInfo.isSubmitFlowTable === '1') {
      alertLdbInfo();
      return;
    }
    if (hasImageCode === 1) {
      setVisible(hasImageCode === 1);
      getImageCode();
    } else {
      sureAppointment();
    }
  };

  useEffect(() => {
    if (errorMsg) {
      alertErrorMsg();
    }
  }, [errorMsg]);

  useEffect(() => {
    if (window.whetherAlipay) {
      // 网页向小程序 postMessage 消息
      my.postMessage({ name: 'powerAuthCode' });
      // 接收来自小程序的消息。
      my.onMessage = (e) => {
        console.log('支付宝返回参数', e);
        if (e.name === 'powerAuthCode') {
          setAuthCode(e.authCode);
        }
      };
    }
    selSourceOpenTime();
    fetchData();
    return () => {
      ModalV5.clear();
    };
  }, []);
  useEffect(() => {
    console.log(999999, type);
    if (type === 'MDT') {
      setEditCardStatus(false);
    }
  }, [type]);
  return (
    <div className={styles.AppointmentDetail}>
      <main>
        {/* 预约信息 */}
        <MedicalInfo />
        <PatientCard onChange={fetchData} editCardStatus={editCardStatus} />
        {/* 注意事项 */}
        {checkInfo?.appointmentTips && (
          <div className={styles.hint}>
            <div className={styles.header}>
              <img src={hint} alt="" />
              注意事项
            </div>
            <div className={styles.content}>
              {checkInfo?.appointmentTips.split('\n').map((item: string) => {
                return (
                  <div className="text" key={item}>
                    {item}
                  </div>
                );
              })}
            </div>
          </div>
        )}
        {whetherAlipay && (
          <div
            className={styles.myPwoer}
            onClick={() => {
              setMyPowerModal(true);
            }}
          >
            {' '}
            <img src={powerImg} alt="" /> 预约挂号预计获得
            <div style={{ color: '#4fd510', padding: '0 8px' }}> 277g </div>{' '}
          </div>
        )}
      </main>
      <footer>
        {sourceNoOpenHint === 0 && btnDisabled && (
          <div className={styles.appointmentTimeDown}>
            距离挂号还有：
            <CountDown
              delaySeconds={Number(second)}
              getDocInfo={() => {
                // 直接修改当前按钮状态
                setBtnDisabled(false);
              }}
            />
          </div>
        )}
        <Button
          shape="rounded"
          color="primary"
          block
          size="large"
          className={styles.appointmentBtn}
          disabled={btnDisabled}
          onClick={confirm}
        >
          确认预约
        </Button>
      </footer>

      <Modal
        visible={visible}
        transparent
        maskClosable={false}
        title="图形验证"
        footer={[
          {
            text: '取消',
            onPress: () => {
              setVisible(false);
            },
          },
          {
            text: '确认',
            onPress: () => {
              onConfirm();
            },
          },
        ]}
      >
        <form action="">
          <div className={styles.modalContent} style={{ display: 'flex' }}>
            <InputItem
              type="number"
              className={styles.inputItem}
              placeholder="请输入图形验证码"
              onChange={onChangeText}
              style={{ width: '280px' }}
              onBlur={onBlur}
            />
            <div className={styles.littleBtn} onClick={getImageCode}>
              {imageCodeLoading ? (
                <div className={styles.loadingWrap}>
                  <DotLoading color="primary" />
                </div>
              ) : (
                <img src={imageData} style={{ height: '100%', width: '100%' }} alt="" />
              )}
            </div>
          </div>
        </form>
      </Modal>
      <Modal
        visible={myPowerModal}
        wrapClassName="alipowerModalWrap"
        className="alipowerModalWrap"
        transparent
        footer={[
          {
            text: '知道了',
            onPress: () => {
              setMyPowerModal(false);
            },
          },
        ]}
        title={
          <img
            src={powerImgBg}
            alt=""
            style={{
              width: '100%',
              height: '150px',
              marginTop: '-80px',
            }}
          />
        }
      >
        <div>
          <div style={{ fontSize: '20px', fontWeight: 700, marginBottom: '16px' }}>预约挂号、查报告得绿色能量</div>
          <div style={{ textAlign: 'left' }}>
            <p>• 完成预约挂号，得绿色能量 277g/ 笔，每月上限5笔（取消挂号失效）</p>
            <br />
            <p> • 完成报告查询，得绿色能量 2g/笔， 每月上限10次</p>
            <br />
            <p>• 得到的绿色能量可以前往【蚂蚁森林】 用来种树，改善我们的环境</p>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default AppointmentDetail;
