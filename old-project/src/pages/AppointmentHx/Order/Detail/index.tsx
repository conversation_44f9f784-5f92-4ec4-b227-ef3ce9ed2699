import React, { useEffect, useState, useMemo } from 'react';
import { connect, Dispatch, Loading, history } from 'umi';
import { Button, PickerView, Skeleton, Space, Toast, Modal as ModalV5, Radio } from 'antd-mobile-v5';
import { Modal, InputItem } from 'antd-mobile';
import _ from 'lodash';
import Payment from '@/utils/Payment';
import HxModal from '@/components/HxModal';
import HxConfirm, { useHxConfirmDialog } from '@/components/HxConfirm';
import { isHytHarmonyOS, isWechat } from '@/utils/platform';
import { saveEnergyStatus, saveReferralInfo } from '@/pages/Appointment/service';
import { fetchUserOpenid } from '@/services/global';
import { getOpenId, getOrganCode } from '@/utils/parameter';
import { BASE_IMG_URL } from '../../utils/config';
import InfoCard from '../components/InfoCard';
import OrderInfo from '../components/OrderInfo';
import { appointmentCancelOrBack, selAppointmentDetails, appointmentToPay as appointmentToPayApi } from '../../service';
import { IHXSelAppointmentDetailsData } from '../../data';
import { secondCountDown } from '../../utils/countdown';
import powerImg from '../../../../assets/能量球-40px.png';
import styles from './index.less';
import OrderSkeleton from '../components/OrderSkeleton';

const success = `${BASE_IMG_URL}/success.png`;
const dzf = `${BASE_IMG_URL}/dzf.png`;
const fail = `${BASE_IMG_URL}/fail.png`;

interface IProps {
  dispatch: Dispatch;
  loading: Boolean | undefined;
  location: {
    query: {
      sysAppointmentId: string;
      code?: string;
      bizDealSeq?: string;
    };
  };
}
const OrderDetail: React.FC<IProps> = (props) => {
  const {
    location: {
      query: { sysAppointmentId, code = '', bizDealSeq },
    },
    dispatch,
  } = props;

  const [orderInfo, setOrderInfo] = useState<Partial<IHXSelAppointmentDetailsData>>({});
  const [countdown, setCountdown] = useState('');
  const [visibleHint, setVisibleHint] = useState(false);
  const [checkIsSeeDoctor, setCheckIsSeeDoctor] = useState(2);
  /* 上次就诊医院名称 */
  const [otherHospitalName, setOtherHospitalName] = useState('');
  const [showPickerView, setShowPickerView] = useState(false);
  const [asyncValue, setAsyncValue] = useState<any>([]);
  const [sysOrderTypeId, setSysOrderTypeId] = useState('');

  const {
    appointStatus = 2,
    isBack,
    isCancel,
    remindTips = '',
    clinicalDataCollected = '',
    isToPay = false,
    wxInsuStop = '',
    commonProblem = [],
    supportOrderTypeItems = [],
    dealMoney = 0,
    hospitalCode,
  } = orderInfo || {};

  const { showConfirm, hideConfirm } = useHxConfirmDialog();
  const _supportOrderTypeItems = supportOrderTypeItems.map((item) => ({
    label: item.orderTypeName || '',
    value: item.sysOrderTypeId || '',
  }));

  const InsuStop = wxInsuStop && wxInsuStop === 1;

  const iconInfo: any = {
    2: { icon: dzf, title: '待支付', text: '' },
    5: { icon: fail, title: '已取消', text: '预约号源已取消' },
    6: { icon: dzf, title: '等待退款', text: '挂号费用已原路退回，请关注查询' },
    7: { icon: success, title: '挂号成功', text: '请在就诊当日准时前往' },
    8: { icon: fail, title: '已退号', text: '挂号费用已原路退回，请关注查询' },
    takeNumber: { icon: dzf, title: '待取号', text: '请在就诊前完成支付' },
    stop: { icon: dzf, title: '停诊待退费', text: '该订单已停诊,请点击“申请退号”进行退费' },
  };

  let timer: any = 0;

  /**
   * 查询预约详情
   * @return {*}
   */

  const fetchData = async () => {
    try {
      Toast.show({ icon: 'loading', duration: 0 });
      const res = await selAppointmentDetails({ sysAppointmentId, bizDealSeq });
      const { appointStatus, preConsultationStatus, cardId, preConsultationUrl } = res;
      Toast.clear();
      setOrderInfo(res);
      /* 支付宝小程序弹框 */
      if (res.energyNum && res.energyStatus === '0' && window.whetherAlipay) {
        // 支付宝能量弹框
        const alimsg = (
          <div className={styles.hxorderMyPwoer}>
            {' '}
            <img src={powerImg} alt="" /> 本次预约获得绿色能量
            <div style={{ color: '#4fd510', padding: '0 8px' }}> {res.energyNum}g </div>{' '}
          </div>
        );
        Toast.show({
          content: alimsg,
          duration: 2000,
        });
        await saveEnergyStatus({ sysAppointmentId, bizDealSeq });
      }
      let { appointAutoCancelMin } = res;
      const { ifOtherHospitalDiagnosis } = res;
      if (appointAutoCancelMin) {
        timer = setInterval(() => {
          if (appointAutoCancelMin === 0) {
            clearInterval(timer);
            fetchData();
            return;
          }
          setCountdown(secondCountDown((appointAutoCancelMin -= 1)));
        }, 1000);
      }
      /* 是否显示就诊提示弹框 */
      if (ifOtherHospitalDiagnosis === 0 && getOrganCode() === 'HID0101' && appointStatus === 7) {
        setVisibleHint(true);
      }

      if (appointStatus === 7 && preConsultationStatus === 1) {
        showConfirm({
          title: '温馨提示',
          content: '方便医生快速、精准获取您的病情状况，建议您在就诊前完成病史采集',
          onOk: () => {
            const openId = isHytHarmonyOS() ? `app${cardId}` : getOpenId();
            window.location.href = preConsultationUrl || '';
            // window.location.href = `https://ywz.wchscu.cn/api/v1/pre/redirect?visitId=${sysAppointmentId}&openid=${openId}`;
            hideConfirm();
          },
          okText: '立即完善',
          cancleText: '取消',
          onCancle: () => {
            hideConfirm();
          },
        });
      }
    } catch (error) {
      console.log('error:', error);
      Toast.clear();
    }
  };

  /**
   * 修改就诊提示单选状态
   * @return {*}
   */
  const changeCheckStatus = (val) => {
    setCheckIsSeeDoctor(val);
    if (checkIsSeeDoctor === 2) {
      setOtherHospitalName('');
    }
  };

  /**
   * 确认就诊提示
   * @return {*}
   */
  const onConfirmDoctorHint = async () => {
    /* 判断是否填写就诊机构 */
    if (checkIsSeeDoctor === 1 && !otherHospitalName) {
      Toast.show('请填写机构名称');
      return;
    }

    try {
      const res = await saveReferralInfo({
        ifOtherHospitalDiagnosis: checkIsSeeDoctor,
        otherHospitalName,
        sysAppointmentId,
      });
      if (res.code === '1') {
        Toast.show('提交成功！');
        setVisibleHint(false);
      }
    } catch (error) {
      console.log('error:', error);
    }
  };

  /* 渲染描述 */
  const renderDesc = () => {
    return appointStatus === 2
      ? countdown !== '00:00:00' && countdown && `剩余支付时间：${countdown}`
      : iconInfo[InsuStop ? 'stop' : appointStatus]?.hispitalConfig?.[getOrganCode()] ??
          iconInfo[InsuStop ? 'stop' : appointStatus].text;
  };

  // 选择支付方式
  const onChangePayTypes = (val: any) => {
    setAsyncValue(val);
    setSysOrderTypeId(val[0]);
  };

  /**
   * 支付
   * @param {string}
   * @return {*}
   */

  const appointmentToPay = async () => {
    // 先判断是否有openid
    let openId = getOpenId();
    if (!openId && isWechat()) {
      const res = await fetchUserOpenid();
      res && res?.openID && (openId = res?.openID);
    }
    const payload: { [key: string]: any } = {
      sysAppointmentId,
      hospitalCode,
      sysOrderTypeId,
      openId,
    };
    try {
      const res = await appointmentToPayApi(payload);
      if (res?.code === '1') {
        const { bizSysSeq = '', dealSeq = '', merchantSeq = '' } = res?.data || {};
        Payment.toPay({ bizSysSeq, dealSeq, merchantSeq });
      }
    } catch (error) {
      console.log('error:', error);
    } finally {
      Toast.clear();
    }
  };

  // 确认支付方式
  const getPayTypes = () => {
    appointmentToPay();
  };

  /**
   * 去支付
   * @return {*}
   */

  const goToPay = () => {
    if (supportOrderTypeItems.length > 1) {
      setShowPickerView(true);
      return;
    }
    appointmentToPay();
  };

  /**
   * 确认退号/取消预约
   * status 1-取消预约 2-申请退号
   * @return {*}
   */

  const onConfirm = async (status: number) => {
    try {
      Toast.show({ icon: 'loading', duration: 0 });
      const text = status === 1 ? '取消成功' : '退号成功';
      const { doctorId } = orderInfo || {};
      const res = await appointmentCancelOrBack({ types: status, doctorId, sysAppointmentId });
      Toast.clear();
      if (res?.code === '1') {
        Toast.show({
          content: text,
          duration: 2000,
          afterClose: () => {
            fetchData();
          },
        });
      }
    } catch (error) {
      console.log('error:', error);
      Toast.clear();
    } finally {
      ModalV5.clear();
    }
  };

  /**
   * 退号/取消预约
   * status 1-取消预约 2-申请退号
   * @return {*}
   */
  const cancelOrBack = (status: number) => {
    const { cancelBlackList } = orderInfo || {};
    const notice =
      cancelBlackList === 0
        ? status === 1
          ? '请确认是否取消该号源'
          : '请确认是否退号，退号需重新挂号'
        : `取消和退号每月超出${cancelBlackList}次将会加入黑名单，影响您正常预约挂号，确认是否${
            status === 1 ? '取消' : '退号'
          }`;
    HxModal.show({
      title: '温馨提示',
      content: <div>{notice}</div>,
      closeOnMaskClick: true,
      actions: [
        {
          text: '取消',
          key: 'cancel',
          onClick: () => {
            ModalV5.clear();
          },
        },
        {
          text: '确认',
          key: 'confirm',
          className: 'primary',
          onClick: () => {
            onConfirm(status);
          },
        },
      ],
    });
  };

  /**
   * 再次预约
   * @return {*}
   */
  const appointAgain = () => {
    const { doctorId, deptCode, hospitalAreaCode, deptCategoryCode } = orderInfo || {};
    history.push({
      pathname: '/doctor/index',
      query: {
        sign: 1,
        hospitalAreaCode,
        hospitalCode,
        deptCode,
        deptCategoryCode,
        doctorId,
      },
    });
  };

  useEffect(() => {
    fetchData();
    return () => {
      ModalV5.clear();
    };
  }, []);

  return _.isEmpty(orderInfo) ? (
    <OrderSkeleton />
  ) : (
    <div className={styles.recordOrderDetail}>
      <header>
        <img src={code === '' ? iconInfo[InsuStop ? 'stop' : appointStatus].icon : iconInfo[code].icon} alt="" />
        <div className={styles.appointmentStatus}>
          <div className={styles.statusDesc}>
            {code === '' ? iconInfo[InsuStop ? 'stop' : appointStatus].title : iconInfo[code].title}
          </div>
          <div className={styles.statusHint}>{renderDesc()}</div>
        </div>
      </header>
      <main>
        <InfoCard orderInfo={orderInfo} />
        {/* 订单信息 */}
        <OrderInfo orderInfo={orderInfo} sysAppointmentId={sysAppointmentId} />
      </main>
      <footer>
        <Space>
          {appointStatus === 2 && (isToPay || isCancel !== 0) && (
            <Space>
              {isCancel !== 0 && (
                <Button size="middle" shape="rounded" className={styles.appointmentBtn} onClick={() => cancelOrBack(1)}>
                  取消预约
                </Button>
              )}
              {isToPay && (
                <Button
                  shape="rounded"
                  color="primary"
                  className={styles.appointmentBtn}
                  size="middle"
                  onClick={goToPay}
                >
                  去支付
                </Button>
              )}
            </Space>
          )}
          {appointStatus === 7 && (
            <div className={styles.bottomBtn}>
              {isBack !== 0 && (
                <Button
                  className={styles.appointmentBtn}
                  shape="rounded"
                  color="primary"
                  size="middle"
                  onClick={() => cancelOrBack(2)}
                >
                  申请退号
                </Button>
              )}
            </div>
          )}
          {appointStatus !== 2 && (
            <Button shape="rounded" size="middle" onClick={appointAgain}>
              再次预约
            </Button>
          )}
        </Space>
      </footer>

      {showPickerView && (
        <div className={styles.mask}>
          <div className={styles.rest} />
          <div className={styles.head}>
            <div className={styles.head_top}>
              <div className={styles.cancel} onClick={() => setShowPickerView(false)}>
                取消
              </div>
              <div>请选择支付方式</div>
              <div
                className={styles.fontColor}
                onClick={() => {
                  getPayTypes();
                }}
              >
                确认
              </div>
            </div>
          </div>
          {dealMoney === 0 && _supportOrderTypeItems.findIndex((item) => item.value === 'wxProIns') > -1 ? (
            <div className={styles.box}>
              <PickerView
                columns={[_supportOrderTypeItems.filter((item) => item.value !== 'wxProIns')]}
                value={asyncValue}
                onChange={(val) => {
                  onChangePayTypes(val);
                }}
              />
            </div>
          ) : (
            <div className={styles.box}>
              <PickerView
                columns={[_supportOrderTypeItems]}
                value={asyncValue}
                onChange={(val) => {
                  onChangePayTypes(val);
                }}
              />
            </div>
          )}
        </div>
      )}

      <Modal
        visible={visibleHint}
        title="就诊提示"
        transparent
        maskClosable={false}
        className={styles.popupWrap}
        wrapClassName="hintModalWrap"
        footer={[
          {
            text: '取消',
            onPress: () => {
              setVisibleHint(false);
            },
          },
          {
            text: '确认',
            onPress: () => {
              onConfirmDoctorHint();
            },
          },
        ]}
      >
        <div className="doctorSuggest">
          <header className="title">您是否已在其他医疗机构就诊？</header>
          <div className="radioWrap">
            <Radio.Group value={checkIsSeeDoctor} onChange={changeCheckStatus}>
              <Radio
                value={1}
                style={{
                  '--icon-size': '20px',
                  '--font-size': '15px',
                  '--gap': '4px',
                }}
              >
                是
              </Radio>
              <Radio
                value={2}
                style={{
                  '--icon-size': '20px',
                  '--font-size': '15px',
                  '--gap': '4px',
                  marginLeft: '20px',
                }}
              >
                否
              </Radio>
            </Radio.Group>
          </div>
          {checkIsSeeDoctor === 1 && (
            <InputItem
              clear
              placeholder="请输入上次就诊机构名称"
              className="deptInput"
              value={otherHospitalName}
              onChange={(e) => {
                setOtherHospitalName(e);
              }}
            />
          )}
        </div>
      </Modal>
    </div>
  );
};

export default OrderDetail;
