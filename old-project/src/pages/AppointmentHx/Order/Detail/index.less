.recordOrderDetail {
  min-height: 100vh;
  width: 100%;
  background-color: #f5f6fa;
  padding-bottom: calc(144px + env(safe-area-inset-bottom));
  header {
    height: 250px;
    width: 100%;
    background: url('https://cdnhyt.cd120.com/person/assets/appoinmentHx/top_bg.png') no-repeat;
    background-size: 100% 100%;
    background-position: center;
    display: flex;
    padding: 40px 24px 0;
    img {
      width: 48px;
      height: 48px;
    }
    .appointmentStatus {
      flex: 1;
      margin-left: 16px;
      .statusDesc {
        font-size: 40px;
        font-weight: 500;
        text-align: left;
        color: #000000;
        line-height: 48px;
      }
      .statusHint {
        font-size: 28px;
        text-align: left;
        color: #989eb4;
        margin-top: 4px;
      }
    }
  }
  main {
    padding: 0 24px;
    flex: 1;
  }
  footer {
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    padding-right: 32px;
    .appointmentBtn {
      min-width: 176px;
      font-size: 28px;
      text-align: center;
      padding-left: 32px;
      padding-right: 32px;
    }
  }
  .mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 200;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    background-color: rgba(0, 0, 0, 0.45);

    .rest {
      flex: 1;
    }

    .head {
      padding: 0 30px;
      color: #333;
      text-align: center;
      background-color: #fff;
      border: none !important;

      .head_top {
        display: flex;
        justify-content: space-between;
        height: 95px;
        font-weight: bold;
        font-size: 32px;
        line-height: 95px;

        .fontColor {
          color: #3ad3c1;
        }
        .cancel {
          color: #03081a;
        }
      }

      .head_notice {
        color: rgb(227, 60, 60);
        font-size: 32px;
      }
    }

    .box {
      min-height: 110px;
      max-height: 50%;
      overflow-y: scroll;
      background-color: #fff;

      ul {
        display: flex;
        flex-wrap: wrap;
        height: 100%;
        margin-bottom: 0;
        padding: 0 25px;
        padding-top: 30px;
        list-style: none;
      }
    }
  }
}

.hxorderMyPwoer {
  display: flex;
  text-align: center;
  height: 7vw;
  font-size: 3.7333vw;
  align-items: center;
  justify-content: center;
  img {
    height: 3.7333vw;
    width: 3.7333vw;
    margin-right: 1vw;
  }
}
