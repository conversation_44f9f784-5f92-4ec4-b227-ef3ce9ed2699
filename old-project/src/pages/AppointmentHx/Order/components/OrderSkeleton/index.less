.recordOrderDetail {
  min-height: 100vh;
  width: 100%;
  background-color: #f5f6fa;
  padding-bottom: calc(144px + env(safe-area-inset-bottom));
  header {
    height: 250px;
    width: 100%;
    background: url('https://cdnhyt.cd120.com/person/assets/appoinmentHx/top_bg.png') no-repeat;
    background-size: 100% 100%;
    background-position: center;
    display: flex;
    padding: 40px 24px 0;
    .appointmentStatus {
      flex: 1;
      margin-left: 16px;
    }
    .descSkeleton {
      height: 36px;
      width: 392px;
      margin-top: 16px;
      display: block;
    }
    .titleSkeleton {
      height: 50px;
      width: 150px;
      display: block;
    }
  }
  main {
    padding: 0 24px;
    flex: 1;
    .orderDetailInfoCard {
      margin-top: -82px;
      min-height: 412px;
      background: #ffffff;
      border-radius: 24px;
      padding: 24px;
      .top {
        display: flex;
        align-items: center;
        padding-top: 6px;
      }
      .bottom {
        display: flex;
        align-items: center;
        margin-top: 82px;
        .c1 {
          margin-left: 24px;
          display: flex;
          flex-direction: column;
          row-gap: 14px;
          flex: 1;
        }
        .c2 {
          display: flex;
          align-items: center;
          column-gap: 16px;
        }
      }
      .center {
        display: flex;
        margin-top: 40px;
        .c1 {
          margin-left: 24px;
          display: flex;
          flex-direction: column;
          row-gap: 16px;
        }
        .c2 {
          margin-left: 30px;
          row-gap: 16px;
          display: flex;
          flex-direction: column;
        }
      }
    }
  }
}
