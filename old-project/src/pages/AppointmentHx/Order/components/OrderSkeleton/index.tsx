import { Skeleton } from 'antd-mobile-v5';
import React from 'react';
import styles from './index.less';

const OrderSkeleton: React.FC = () => {
  return (
    <div className={styles.recordOrderDetail}>
      <header>
        <Skeleton animated style={{ width: '24px', height: '24px' }} />
        <div className={styles.appointmentStatus}>
          <Skeleton animated className={styles.titleSkeleton} />
          <Skeleton animated className={styles.descSkeleton} />
        </div>
      </header>
      <main>
        <div className={styles.orderDetailInfoCard}>
          <div className={styles.top}>
            <Skeleton animated style={{ width: '18px', height: '18px' }} />
            <Skeleton animated style={{ width: '200px', height: '18px', marginLeft: '6px' }} />
          </div>
          <div className={styles.center}>
            <Skeleton animated style={{ width: '48px', height: '48px' }} />
            <div className={styles.c1}>
              <Skeleton animated style={{ width: '110px', height: '22px' }} />
              <Skeleton animated style={{ width: '100px', height: '17px' }} />
            </div>
            <div className={styles.c2}>
              <Skeleton animated style={{ width: '48px', height: '22px' }} />
              <Skeleton animated style={{ width: '96px', height: '17px' }} />
            </div>
          </div>
          <div className={styles.bottom}>
            <Skeleton animated style={{ width: '44px', height: '44px' }} />
            <div className={styles.c1}>
              <Skeleton animated style={{ width: '70px', height: '20px' }} />
              <Skeleton animated style={{ width: '150px', height: '17px' }} />
            </div>
            <div className={styles.c2}>
              <Skeleton animated style={{ width: '24px', height: '24px' }} />
              <Skeleton animated style={{ width: '18px', height: '18px' }} />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default OrderSkeleton;
