import React, { useEffect, useState } from 'react';
import { BASE_IMG_URL } from '@/pages/AppointmentHx/utils/config';
import classnames from 'classnames';
import { Modal } from 'antd-mobile-v5';
import QRCode from 'qrcode.react';
import logo_ from '@/assets/tencentPatientCard/logo_.png';
import { IHXSelAppointmentDetailsData } from '@/pages/AppointmentHx/data';
import { ellipsisTel } from '@/pages/AppointmentHx/utils/date';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import styles from './index.less';
import { getOrganCode } from '@/utils/parameter';
import { history } from 'umi';

dayjs.locale('zh-cn');
const close = `${BASE_IMG_URL}/close2.png`;

const arrow = `${BASE_IMG_URL}/arrow.png`;
const man = `${BASE_IMG_URL}/man.png`;
const woman = `${BASE_IMG_URL}/woman.png`;
const cardArrow = `${BASE_IMG_URL}/card_arrow.png`;
const qrcodeImg = `${BASE_IMG_URL}/qrcode.png`;

interface IProps {
  orderInfo?: Partial<IHXSelAppointmentDetailsData>;
}
const InfoCard: React.FC<IProps> = (props) => {
  const { orderInfo = {} } = props;
  const {
    hospitalName,
    hospitalAreaName,
    docName,
    scheduleDeptName,
    titleName,
    admDate,
    admRangeName,
    admTimeRange,
    appointmentNo,
    patientName,
    patientSex,
    patientPhone = '',
    qrCode = '',
    patRegNo,
    hosLogo,
    appointStatus,
    week,
    patientAge,
  } = orderInfo;
  const openQrcode = () => {
    if (!qrCode) return;
    Modal.show({
      content: (
        <div className={styles.cardQrcodeWrap}>
          <div className={styles.inner}>
            <QRCode
              value={qrCode}
              size={180}
              fgColor="#03161F"
              level="M"
              imageSettings={{
                src: `${logo_}`,
                height: 44,
                width: 44,
                excavate: true,
              }}
            />
            <div className={styles.name}>{patientName}</div>
            <div className={styles.pmi}>登记号：{patRegNo}</div>
          </div>
          <div className={styles.line} />
          <img
            src={close}
            className={styles.close}
            alt=""
            onClick={() => {
              Modal.clear();
            }}
          />
        </div>
      ),
      closeOnMaskClick: false,
      bodyClassName: 'cardQrCodeContent',
    });
  };

  /**
   * 跳转到医院首页
   * @param e
   */
  const goToHospital = () => {
    if (getOrganCode() === 'HID0101') {
      history.push('/hxHome');
      return;
    }
    history.push('/home');
  };
  return (
    <div className={styles.orderDetailInfoCard}>
      <div className={styles.topBar} onClick={goToHospital}>
        <img src={hosLogo} alt="" className={styles.logo} />
        <span>{hospitalName}</span>
        <span>（{hospitalAreaName}）</span>
        <img src={arrow} className={styles.arrow} alt="" />
      </div>
      <div className={styles.appointmentInfo} style={{ opacity: [5, 8].includes(appointStatus) ? 0.16 : 1 }}>
        <div className={styles.appointmentNum}>
          <div className={styles.num}>{appointmentNo}</div>
          <span>诊号</span>
        </div>
        <div className={styles.appointmentTime}>
          <div>
            {dayjs(admDate).format('MM月DD日')} {dayjs(admDate).format('dddd')}
          </div>
          <div>
            {admRangeName} {admTimeRange}
          </div>
        </div>
        <div className={styles.appointmentDoctor}>
          <div>
            <span className={styles.doctorName}>{docName}</span>
            <span className={styles.doctorTitle}>{titleName}</span>
          </div>
          <div className={styles.deptName}>{scheduleDeptName}</div>
        </div>
      </div>
      <div className={styles.patientInfo}>
        <div className={styles.leftInfo}>
          <img src={patientSex === 1 ? man : woman} alt="" className={styles.avatar} />
          <div className={styles.cardInfo}>
            <div className={styles.top}>
              <span className={styles.name}>{patientName}</span>
              {/* <div className={classnames(styles.tag, styles.isSelf)}>本人</div> */}
            </div>
            <div className={styles.bottom}>
              <span className={classnames(styles.gender, styles['normal-text-color'])}>
                {patientSex === 1 ? '男' : '女'} {patientAge}岁
              </span>
              <span className={classnames(styles.tel, styles['normal-text-color'])}>{ellipsisTel(patientPhone)}</span>
            </div>
          </div>
        </div>
        {qrCode && (
          <div className={styles.qrcode} onClick={openQrcode}>
            <img src={qrcodeImg} className={styles.qrcodeImg} alt="" />
            <img src={cardArrow} className={styles.cardArrow} alt="" />
          </div>
        )}
      </div>
    </div>
  );
};

export default InfoCard;
