.orderDetailInfoCard {
  margin-top: -82px;
  min-height: 412px;
  background: #ffffff;
  border-radius: 24px;
  .topBar {
    display: flex;
    align-items: center;
    padding-top: 32px;
    padding-left: 24px;
    .logo {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      flex-shrink: 0;
    }
    span {
      font-size: 28px;
      font-weight: 500;
      text-align: left;
      color: #03081a;
      line-height: 36px;
      margin-left: 12px;
    }
    .arrow {
      width: 24px;
      height: 24px;
      margin-left: 8px;
    }
  }
  .appointmentInfo {
    min-height: 174px;
    width: 100%;
    display: flex;
    align-items: center;
    border-bottom: 2px dashed #ebedf5;
    padding: 24px 24px;
    position: relative;
    opacity: 0.16;
    &::before {
      content: '';
      background-color: #f5f6fa;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      position: absolute;
      left: -20px;
      bottom: -20px;
    }
    &::after {
      content: '';
      background-color: #f5f6fa;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      position: absolute;
      right: -20px;
      bottom: -20px;
    }
    .appointmentNum {
      width: 96px;
      height: 96px;
      border: 4px solid #fe8d3c;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      .num {
        font-size: 36px;
        font-weight: bold;
        text-align: center;
        color: #ff6c00;
        line-height: 44px;
      }
      span {
        font-size: 20px;
        text-align: center;
        color: #ff8f43;
        line-height: 28px;
      }
    }
    .appointmentTime {
      margin-left: 24px;
      flex-shrink: 0;
      padding-right: 20px;
      div {
        &:first-child {
          font-size: 32px;
          font-weight: bold;
          text-align: center;
          color: #03081a;
          line-height: 44px;
        }
        &:last-child {
          font-size: 24px;
          text-align: center;
          color: #03081a;
          line-height: 34px;
          margin-top: 16px;
        }
      }
    }
    .appointmentDoctor {
      flex: 1;
      padding-left: 20px;
      position: relative;
      &::before {
        position: absolute;
        content: '';
        width: 2px;
        height: 88px;
        background: #f6f8ff;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
      .doctorName {
        font-size: 32px;
        font-weight: bold;
        text-align: left;
        color: #03081a;
        line-height: 44px;
      }
      .doctorTitle {
        font-size: 24px;
        text-align: left;
        color: #989eb4;
        line-height: 34px;
        margin-left: 16px;
      }
      .deptName {
        font-size: 24px;
        text-align: left;
        color: #03081a;
        line-height: 34px;
        margin-top: 16px;
      }
    }
  }
  .patientInfo {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 40px 24px 40px;
    .leftInfo {
      display: flex;
      align-items: center;
      .avatar {
        width: 88px;
        height: 88px;
        border-radius: 50%;
      }
      .cardInfo {
        margin-left: 24px;
        .bottom {
          margin-top: 14px;
          span {
            font-size: 24px;
            color: #989eb4;
            line-height: 34px;
          }
        }
        .top {
          display: flex;
          align-items: center;
          .name {
            font-size: 28px;
            color: #03081a;
            line-height: 40px;
          }
          .tag {
            width: 64px;
            height: 28px;
            border-radius: 18px;
            margin-left: 16px;
            color: #989eb4;
            background-color: #ebedf5;
            font-size: 20px;
            font-weight: 400;
            text-align: center;
            line-height: 28px;

            &.isSelf {
              color: #fff;
              background-color: #3ad3c1;
            }
          }
        }
      }
    }
    .qrcode {
      display: flex;
      align-items: center;
      .qrcodeImg {
        width: 48px;
        height: 48px;
      }
      .cardArrow {
        width: 36px;
        height: 36px;
        margin-left: 16px;
      }
    }
  }
}
:global {
  .cardQrCodeContent {
    padding: 0 !important;
    background-color: transparent;
  }
}
.cardQrcodeWrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  .inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #fff;
    width: 100%;
    padding-top: 44px;
    padding-bottom: 24px;
    border-radius: 12px;
  }
  > img {
    width: 56px;
    height: 56px;
    margin-top: -10px;
  }
  .name {
    text-align: center;
    margin-top: 40px;
    font-size: 32px;
    font-weight: bold;
    text-align: center;
    color: #03161f;
  }
  .pmi {
    font-size: 28px;
    font-weight: bold;
    text-align: center;
    color: #9aa2a6;
    margin-top: 24px;
  }
  .line {
    width: 4px;
    height: 50px;
    background: #ffffff;
  }
}
