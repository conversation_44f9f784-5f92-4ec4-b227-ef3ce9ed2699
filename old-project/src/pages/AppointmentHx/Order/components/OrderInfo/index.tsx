import React, { useEffect, useState } from 'react';
import { IHXSelAppointmentDetailsData } from '@/pages/AppointmentHx/data';
import { isHytHarmonyOS, isWechat } from '@/utils/platform';
import calendar from '@/assets/appoinment/<EMAIL>';
import AppScheme from '@/utils/AppScheme';
import { openWechatLocation } from '@/utils/tool';
import { Button } from 'antd-mobile-v5';
import qs from 'query-string';
import { getOpenId, getOrganCode } from '@/utils/parameter';
import { history } from 'umi';
import { BASE_IMG_URL } from '../../../utils/config';
import styles from './index.less';

const location = `${BASE_IMG_URL}/location.png`;
const hintIcon = `${BASE_IMG_URL}/hint_icon.png`;
const searchIcon = `${BASE_IMG_URL}/hint_question_icon.png`;
const zqtx = `${BASE_IMG_URL}/zqtx.png`;
const arrow = `${BASE_IMG_URL}/arrow.png`;

interface IProps {
  orderInfo: Partial<IHXSelAppointmentDetailsData>;
  sysAppointmentId?: string;
}
const orderInfo: React.FC<IProps> = (props) => {
  const { orderInfo = {}, sysAppointmentId = '' } = props;
  const {
    hospitalAreaName,
    scheduleDeptName,
    docName,
    admDate,
    admTimeRange,
    admRangeName,
    appointmentNo,
    dealMoney,
    admLocation,
    hosAddress,
    channelName,
    bizDealSeq,
    orderCreateTime,
    paymentTime,
    paymentSeq,
    dealSeq,
    orderStatus = '',
    cancleType,
    appointStatus,
    refundTime,
    refundMoney,
    latitude,
    longitude,
    remindTips = '',
    clinicalDataCollected = '',
    commonProblem = [],
    preConsultationStatus,
    preConsultationUrl,
  } = orderInfo;

  /**
   * 跳转病史采集页面
   * @param type 后台接口返回的 clinicalDataCollected 字段，1-已采集、2-跳前端病史采集页面、3-跳讯飞采集页面
   */
  const goMedicalHistory = () => {
    const {
      appointmentId = '',
      patientIdCard = '',
      hosId = '',
      hospitalName = '',
      doctorId = '',
      docName = '',
      deptCode = '',
      deptName = '',
      patientSex = 1,
      patientName = '',
      cardId,
    } = orderInfo || {};
    // const openId = isHytHarmonyOS() ? `app${cardId}` : getOpenId();
    window.location.href = preConsultationUrl || '';
    // window.location.href = `https://ywz.wchscu.cn/api/v1/pre/redirect?visitId=${sysAppointmentId}&openid=${openId}`;
    // switch (type) {
    //   case '1':
    //     history.push(`/appointment/medicalhistory?pageType=view&sysAppointmentId=${sysAppointmentId}`);
    //     break;
    //   case '2':
    //     history.push(`/appointment/medicalhistory?pageType=edit&sysAppointmentId=${sysAppointmentId}`);
    //     break;
    //   case '3':
    //     // window.iFlyGuide.init({
    //     //   channel: 'hxyy124',
    //     //   url: IFLY_INIT_URL,
    //     //   secKey: '9b7f6bc5dbe88b4f66c28d4b1169302a',
    //     //   userid: patientIdCard,
    //     //   terminaltag: 'web',
    //     //   entrance: '003',
    //     // });
    //     // window.iFlyGuide.onRegisteredWithCollectRecord({
    //     //   hosId,
    //     //   hosName: hospitalName,
    //     //   docId: doctorId,
    //     //   docName,
    //     //   regDeptCode: deptCode,
    //     //   regDeptName: deptName,
    //     //   // userAge: age,
    //     //   userGender: patientSex === 1 ? '男' : '女',
    //     //   userName: patientName,
    //     //   callback: `${window.location.href}`,
    //     //   hisRegisterNo: appointmentId,
    //     // });
    //     break;
    //   default:
    //     break;
    // }
  };

  /**
   * 诊前提醒跳转
   * @param {any} value
   * @return {*}
   */

  const turnTreatmentBeforeAdivce = (value: any) => {
    history.push({
      pathname: '/appointment/Treatmentadivce',
      state: {
        advice: value,
      },
    });
  };

  /**
   * 鸿蒙应用添加日程
   * @return {*}
   */
  const addSchedule = () => {
    try {
      const { admTimeRange = '', admDate, admRangeName = '', hospitalName, deptName = '' } = orderInfo || {};
      const timeArr = admTimeRange.split('-');
      const startTime = new Date(`${admDate} ${timeArr[0]}`).getTime();
      const endTime = new Date(`${admDate} ${timeArr[1]}`).getTime();
      const title = `${admDate} ${admRangeName !== '' && admRangeName} ${admTimeRange} ${hospitalName} ${deptName}就诊`;
      const queryData = {
        title,
        startTime,
        endTime,
      };
      AppScheme.hmCalendar(qs.stringify(queryData));
    } catch (error) {
      console.log('error:', error);
    }
  };

  /* 高德地图 */
  const goMap = () => {
    if (isWechat()) {
      openWechatLocation({ latitude, longitude, address: hosAddress });
    }
  };
  return (
    <div className={styles.appointmentOrderInfo}>
      <div className={styles.appointmentInfoContainer}>
        <div className={styles.title}>预约信息</div>
        <div className={styles.appointmentInfo}>
          <div className={styles.appointmentInfoItem}>
            <span>就诊院区</span>
            <span>{hospitalAreaName}</span>
          </div>
          <div className={styles.appointmentInfoItem}>
            <span>就诊科室</span>
            <span>{scheduleDeptName}</span>
          </div>
          <div className={styles.appointmentInfoItem}>
            <span>医生姓名</span>
            <span>{docName}</span>
          </div>
          <div className={styles.appointmentInfoItem}>
            <span>就诊时间</span>
            <span>{admDate}</span>
          </div>
          <div className={styles.appointmentInfoItem}>
            <span>就诊时段</span>
            <span>
              {admRangeName} {admTimeRange}
            </span>
          </div>
          <div className={styles.appointmentInfoItem}>
            <span>诊号</span>
            <span>{appointmentNo}</span>
          </div>
          <div className={styles.appointmentInfoItem}>
            <span>金额</span>
            <span>{dealMoney}元</span>
          </div>
          <div className={styles.appointmentInfoItem}>
            <span>就诊地点</span>
            <span>{admLocation}</span>
          </div>
          <div className={styles.appointmentInfoItem}>
            <span>医院地址</span>
            <span className={isWechat() && styles.location} onClick={goMap}>
              {isWechat() && <img src={location} alt="" />}
              {hosAddress}
            </span>
          </div>
          <div className={styles.appointmentInfoItem}>
            <span>预约渠道</span>
            <span>{channelName}</span>
          </div>
          {/* 鸿蒙添加日程 */}
          {isHytHarmonyOS() && (
            <div className={styles.schedule} onClick={addSchedule}>
              <img src={calendar} alt="" />
              <span>添加到日程</span>
            </div>
          )}
        </div>
      </div>
      {/* 病史采集 */}
      {appointStatus === 7 && preConsultationStatus !== 3 && ['HID0101', 'HYT'].includes(getOrganCode()) && (
        <div className={styles.medicalHistory}>
          <div className={styles.header}>病史采集</div>
          <div className={styles.medicalHistoryText}>
            {preConsultationStatus === 2
              ? '您已完成病史采集，请按时前往就诊，祝您早日康复！'
              : '为了方便医生快速、精准获取您的病情状况，您可以提前完善病情资料。'}
            <Button
              color="primary"
              fill="outline"
              shape="rounded"
              size="small"
              onClick={() => {
                goMedicalHistory();
              }}
              className={styles.medicalBtn}
            >
              {preConsultationStatus === 2 ? '查看' : '去完善'}
            </Button>
          </div>
        </div>
      )}
      {/* 诊前提醒 */}
      {remindTips && (
        <div className={styles.attendanceAdvice} onClick={() => turnTreatmentBeforeAdivce(remindTips)}>
          <div>
            <img src={zqtx} alt="" />
            <div>{remindTips}</div>
          </div>
          <img src={arrow} alt="" />
        </div>
      )}
      {/* 常见问题 */}
      {!!commonProblem.length && (
        <div className={styles.questionBox}>
          <div className={styles.questionTitle}>
            <img src={hintIcon} alt="" />
            <span>常见问题</span>
          </div>
          <div className={styles.questionList}>
            {commonProblem.map((item, index) => {
              return (
                <div className={styles.questionItem} key={index}>
                  <div className={styles.questionTop}>
                    <img src={searchIcon} alt="" />
                    <span>{item.title}</span>
                  </div>
                  <div className={styles.questionContent}>{item.content}</div>
                </div>
              );
            })}
          </div>
        </div>
      )}
      {bizDealSeq && (
        <div className={styles.orderInfoContainer}>
          <div className={styles.title}>订单信息</div>
          <div className={styles.orderInfo}>
            <div className={styles.orderInfoItem}>
              <span>订单编号</span>
              <span>{bizDealSeq}</span>
            </div>
            <div className={styles.orderInfoItem}>
              <span>下单时间</span>
              <span>{orderCreateTime}</span>
            </div>
            <div className={styles.orderInfoItem}>
              <span>支付时间</span>
              <span>{paymentTime}</span>
            </div>
            <div className={styles.orderInfoItem}>
              <span>支付方式</span>
              <span>{paymentSeq}</span>
            </div>
            <div className={styles.orderInfoItem}>
              <span>支付金额</span>
              <span>{dealMoney}元</span>
            </div>
            <div className={styles.orderInfoItem}>
              <span>支付流水号</span>
              <span>{dealSeq}</span>
            </div>
            {(appointStatus === 6 || appointStatus === 8) && (
              <>
                <div className={styles.orderInfoItem}>
                  <span>退款时间</span>
                  <span>{refundTime}</span>
                </div>
                <div className={styles.orderInfoItem}>
                  <span>退款金额</span>
                  <span>{refundMoney && `${refundMoney}元`}</span>
                </div>
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default orderInfo;
