.appointmentInfoContainer {
  margin-top: 24px;
  background: #ffffff;
  border-radius: 24px;
  overflow: hidden;
  padding-bottom: 24px;
  .title {
    display: flex;
    height: 88px;
    background: #ffffff;
    font-size: 36px;
    font-weight: bold;
    text-align: left;
    color: #03081a;
    padding-left: 24px;
    align-items: center;
  }
  .appointmentInfo {
    padding: 0 24px;
    .appointmentInfoItem {
      display: flex;
      // align-items: center;
      margin-top: 20px;
      justify-content: space-between;
      line-height: 40px;

      span {
        &:nth-child(1) {
          font-size: 28px;
          color: #989eb4;
        }
        &:nth-child(2) {
          font-size: 28px;
          color: #03081a;
          text-align: right;
          max-width: 80%;
          &.location {
            color: #3ad3c1 !important;
            img {
              width: 40px;
              height: 40px;
              margin-right: 8px;
              vertical-align: top;
            }
          }
        }
      }
    }
    .schedule {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      font-weight: 500;
      color: #3ad3c1;
      line-height: 44px;
      padding-top: 34px;
      padding-bottom: 10px;
      border-top: 2px solid #f6f8ff;
      margin-top: 24px;
      img {
        width: 40px;
        height: 40px;
        margin-right: 8px;
      }
    }
  }
}
.orderInfoContainer {
  margin-top: 24px;
  background: #ffffff;
  border-radius: 24px;
  overflow: hidden;
  padding-bottom: 24px;
  .title {
    display: flex;
    height: 88px;
    background: #ffffff;
    font-size: 36px;
    font-weight: bold;
    text-align: left;
    color: #03081a;
    padding-left: 24px;
    align-items: center;
  }
  .orderInfo {
    padding: 0 24px;
    .orderInfoItem {
      display: flex;
      align-items: center;
      margin-top: 20px;
      justify-content: space-between;

      span {
        &:nth-child(1) {
          font-size: 28px;
          color: #989eb4;
        }
        &:nth-child(2) {
          font-size: 28px;
          color: #03081a;
          text-align: right;
        }
      }
    }
  }
}
.appointmentOrderInfo {
  .attendanceAdvice {
    width: 100%;
    height: 108px;
    background: #ffffff;
    border-radius: 24px;
    margin-top: 24px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    > img {
      width: 28px;
      height: 28px;
      margin-left: 16px;
    }
    > div {
      font-size: 24px;
      color: #03081a;
      line-height: 40px;
      flex: 1;
      width: 0;
      display: flex;
      > div {
        flex: 1;
        width: 0;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis; /* 多出的文本显示为省略号 */
      }
      img {
        width: 76px;
        margin-right: 16px;
        height: 76px;
      }
    }
  }
  .questionBox {
    width: 100%;
    margin-top: 24px;
    background: #ffffff;
    border-radius: 24px;
    padding: 32px 24px 32px;
    .questionTitle {
      display: flex;
      align-items: center;
      img {
        width: 36px;
        height: 36px;
      }
      span {
        font-size: 32px;
        font-weight: bold;
        text-align: left;
        color: #03081a;
        line-height: 44px;
        margin-left: 16px;
      }
    }
    .questionList {
      .questionItem {
        margin-top: 32px;
        .questionTop {
          display: flex;
          align-items: center;
          img {
            width: 32px;
            height: 32px;
          }
          span {
            font-size: 28px;
            font-weight: bold;
            text-align: left;
            color: #03081a;
            margin-left: 16px;
          }
        }
        .questionContent {
          font-size: 28px;
          text-align: left;
          color: #51586d;
          line-height: 44px;
          margin-top: 8px;
        }
      }
    }
  }
  .medicalHistory {
    height: 208px;
    background: #ffffff;
    border-radius: 24px;
    margin-top: 24px;
    width: 100%;
    padding: 32px 24px 0;
    .header {
      font-size: 32px;
      font-weight: bold;
      text-align: left;
      color: #03081a;
      line-height: 40px;
    }
    .medicalHistoryText {
      font-size: 28px;
      text-align: justify;
      color: #51586d;
      line-height: 44px;
      display: flex;
      align-items: center;
      margin-top: 16px;
      .medicalBtn {
        margin-left: 24px;
        flex-shrink: 0;
      }
    }
  }
}
