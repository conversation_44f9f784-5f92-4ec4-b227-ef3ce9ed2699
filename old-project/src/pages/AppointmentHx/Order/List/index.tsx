import React, { useEffect, useState } from 'react';
import { Dispatch, history } from 'umi';
import { Modal, PullToRefresh, Toast } from 'antd-mobile-v5';
import { getOrganCode } from '@/utils/parameter';
import { HxEmpty } from '@/components';
import dayjs from 'dayjs';
import { BASE_IMG_URL } from '../../utils/config';
import { selAppointmentRecord } from '../../service';
import ListBtn from '../../components/ListBtn';
import ListCopywrite from '../../components/ListCopywrite';
import 'dayjs/locale/zh-cn';
import styles from './index.less';

dayjs.locale('zh-cn');
const arrow = `${BASE_IMG_URL}/arrow.png`;

interface IProps {
  dispatch: Dispatch;
}
const OrderList: React.FC<IProps> = (props) => {
  const statusList = ['全部', '待支付', '待就诊'];
  const appointmentStatusList: any = {
    2: { color: '#FC4553', title: '待支付', text: '请在30分钟内完成支付' },
    5: { color: '#989EB4', title: '已取消' },
    6: { color: '#FF984B', title: '待退款' },
    7: { color: '#3AD3C1', title: '挂号成功', text: '请在就诊当天前往医院就诊' },
    8: { color: '#989EB4', title: '已退号' },
    stop: { color: '#FC4553', title: '停诊待退费' },
  };
  const [status, setStatus] = useState(0);
  const [currentPageNum, setCurrentPageNum] = useState(1);
  const pageSize = 10;
  const [hasMore, setHasMore] = useState(true);
  const [loading, setLoading] = useState(false);
  const [orderList, setOrderList] = useState<any>([]);

  /**
   * 修改状态
   * @return {*}
   */
  const changeStatus = (index: number) => {
    setStatus(index);
  };

  const fetchData = async () => {
    try {
      Toast.show({ icon: 'loading', duration: 0 });
      setLoading(true);
      const res = await selAppointmentRecord({
        pageNum: currentPageNum,
        pageSize,
        query: {
          hospitalCode: getOrganCode(),
          status,
        },
      });
      const { content = [] } = res || {};
      setOrderList(content);
    } catch (error) {
      console.log('error:', error);
      setOrderList([]);
    } finally {
      Toast.clear();
      setLoading(false);
    }
  };

  const goToDetails = (sysAppointmentId: string) => {
    // 加个参数bizSysSeq
    history.push({
      pathname: '/hxappointment/order/detail',
      query: {
        sysAppointmentId,
      },
    });
  };

  /**
   * 跳转到医院首页
   * @param e
   */
  const goToHospital = (e: Event) => {
    e.stopPropagation();
    if (getOrganCode() === 'HID0101') {
      history.push('/hxHome');
      return;
    }
    history.push('/home');
  };

  /**
   * 再次预约
   * @return {*}
   */
  const appointAgain = (e: React.MouseEvent<HTMLButtonElement>, orderInfo) => {
    e.stopPropagation();
    const { doctorId, deptCode, hospitalAreaCode, deptCategoryCode, hospitalCode } = orderInfo || {};
    history.push({
      pathname: '/doctor/index',
      query: {
        sign: 1,
        hospitalAreaCode,
        hospitalCode,
        deptCode,
        deptCategoryCode,
        doctorId,
      },
    });
  };

  useEffect(() => {
    fetchData();
  }, [status]);

  return (
    <div className={styles.AppointmentOrderList}>
      <header className={styles.header}>
        {statusList.map((item, index) => {
          return (
            <div className={status === index ? styles.select : ''} onClick={() => changeStatus(index)} key={item}>
              {item}
            </div>
          );
        })}
      </header>
      <PullToRefresh
        onRefresh={async () => {
          await fetchData();
        }}
      >
        {orderList.length ? (
          <main>
            {orderList.map((item) => (
              <div
                className={styles.card}
                key={item.sysAppointmentId}
                onClick={() => goToDetails(item?.sysAppointmentId)}
              >
                <div className={styles.topBar}>
                  <div className={styles.organInfo} onClick={(e: Event) => goToHospital(e)}>
                    <img src={item?.hosLogo} alt="" className={styles.logo} />
                    <span>{item.hospitalName}</span>
                    <img src={arrow} className={styles.arrow} alt="" />
                  </div>
                  <span
                    className={styles.status}
                    style={{ color: appointmentStatusList[item?.wxStop ? 'stop' : item?.appointStatus].color }}
                  >
                    {appointmentStatusList[item?.wxStop ? 'stop' : item.appointStatus].title}
                  </span>
                </div>
                <div className={styles.content}>
                  <div className={styles.row}>
                    <span>就诊科室：</span>
                    <span>
                      {item?.hospitalAreaName} {item?.deptName}
                    </span>
                  </div>
                  <div className={styles.row}>
                    <span>就诊医生：</span>
                    <span>{item.docName}</span>
                  </div>
                  <div className={styles.row}>
                    <span>就诊时间：</span>
                    <span>
                      {dayjs(item?.admDate).format('MM月DD日')} {dayjs(item?.admDate).format('dddd')}{' '}
                      {item.admTimeRange}
                    </span>
                  </div>
                  <div className={styles.row}>
                    <span>就诊人：</span>
                    <span>{item.patientName}</span>
                  </div>
                </div>
                <div className={styles.footeractions}>
                  <ListCopywrite
                    appointStatus={item.appointStatus}
                    appointAutoCancelMin={item.appointAutoCancelMin}
                    wxStop={item.wxStop}
                  />
                  <ListBtn
                    appointStatus={item.appointStatus}
                    isBack={item.isBack}
                    isCancel={item.isCancel}
                    isToPay={item.isToPay}
                    takeMethod={item.takeMethod}
                    appointAgain={(e) => appointAgain(e, item)}
                  />
                </div>
              </div>
            ))}
          </main>
        ) : loading ? (
          ''
        ) : (
          <div className={styles.empty}>
            <HxEmpty isNewImg canRefresh={false} />
          </div>
        )}
      </PullToRefresh>
    </div>
  );
};

export default OrderList;
