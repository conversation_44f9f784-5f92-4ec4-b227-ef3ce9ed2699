.AppointmentOrderList {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f6fa;
  position: relative;
  .header {
    position: sticky;
    left: 0;
    top: 0;
    width: 100%;
    display: flex;
    align-items: center;
    height: 88px;
    background: #fff;
    z-index: 10;
    > div {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32px;
      text-align: center;
      color: #989eb4;
      height: 100%;
      line-height: 48px;
      &.select {
        color: #3ad3c1;
        font-weight: bold;
      }
    }
  }
  main {
    padding: 0 24px 24px;
    .card {
      margin-top: 24px;
      min-height: 388px;
      background: #ffffff;
      border-radius: 24px;
      padding: 0 24px 24px;
      .topBar {
        height: 92px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 32px 0 24px;
        .status {
          font-size: 28px;
          text-align: right;
          line-height: 36px;
        }
        .organInfo {
          display: flex;
          align-items: center;
          .logo {
            width: 32px;
            height: 32px;
            border-radius: 50%;
          }
          span {
            font-size: 28px;
            font-weight: 500;
            text-align: left;
            color: #03081a;
            line-height: 36px;
            margin-left: 12px;
          }
          .arrow {
            width: 24px;
            height: 24px;
            margin-left: 8px;
          }
        }
      }
      .content {
        min-height: 248px;
        background: #f5f6fa;
        border-radius: 16px;
        padding: 0 24px 24px;
        display: flex;
        flex-direction: column;
        .row {
          display: flex;
          line-height: 32px;
          font-size: 28px;
          margin-top: 24px;
          span {
            &:first-child {
              color: #989eb4;
              width: 145px;
            }
            &:last-child {
              color: #03081a;
            }
          }
        }
      }
      .footeractions {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 24px;
        min-height: 72px;
        .time {
          font-size: 28px;
          color: #3ad3c1;
          line-height: 40px;
        }
      }
    }
  }
  .empty {
    height: 70vh;
  }
}
