import React, { useEffect, useState } from 'react';
import { connect, Dispatch, Loading, history, IHxAppointmentState } from 'umi';
import { getOrganCode } from '@/utils/parameter';
import classnames from 'classnames';
import { SpinLoading, Toast } from 'antd-mobile-v5';
import { HxDoctorAvatar } from '@/components';
import { HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import DropSelect from '../components/DropSelect';
import { IAreaRecordRespVos, IDeptItem, IHxAreaRecordData } from '../data';
import { queryUserMark, selFirstDept, selHospitalAreaRecord } from '../service';
import IndexBar from './components/IndexBar';
import { BASE_IMG_URL } from '../utils/config';
import styles from './index.less';

const searchIcon = `${BASE_IMG_URL}/search.png`;
const question = `${BASE_IMG_URL}/question.png`;
const arrow = `${BASE_IMG_URL}/arrow.png`;
const doctorIcon = `${BASE_IMG_URL}/doctor_icon.png`;
const deptIcon = `${BASE_IMG_URL}/dept_icon.png`;
const empty = `${BASE_IMG_URL}/empty.png`;

interface IProps {
  dispatch: Dispatch;
  loading: Boolean | undefined;
  hxappointment: IHxAppointmentState;
}
const DepartmentHx: React.FC<IProps> = (props) => {
  const { dispatch, hxappointment: { historyDeptList = [], historyDoctorList = [] } = {}, loading } = props;
  const [hospitalAreaCode, setHospitalAreaCode] = useState('ALL');
  const [hospitalList, setHospitalList] = useState<IAreaRecordRespVos[]>([]);
  const [deptList, setDeptList] = useState<Partial<IDeptItem>[]>([]);
  const [deptCode, setDeptCode] = useState<string | number>(0);
  const [drainageInfo, setDrainageInfo] = useState<string>('');

  // 二级科室
  const secondDeptList = deptList.find((item) => item?.deptCategoryCode === deptCode)?.deptDirectionList || [];
  /**
   * 初始化SDK，跳转科大讯飞
   */
  const jumpTo = async () => {
    console.log('当前跳讯飞初始化url', IFLY_INIT_URL);
    try {
      Toast.show({ icon: 'loading', duration: 0 });
      const res = await queryUserMark({});
      const { userMark } = res;
      window.iFlyGuide.init({
        channel: 'hxyy124',
        url: IFLY_INIT_URL,
        secKey: '9b7f6bc5dbe88b4f66c28d4b1169302a',
        userid: userMark,
        entrance: '003',
        extend: '{"regType": "0"}',
        terminaltag: 'wechat-officialacc', // 讯飞那边的人说传这个
      });
      window.iFlyGuide.jumptoGuide();
    } catch (error) {
      console.log('error:', error);
    } finally {
      Toast.clear();
    }
  };

  /**
   * 获取院区
   * @return {*}
   */

  const fetchHospitalList = async () => {
    try {
      const data: IHxAreaRecordData = await selHospitalAreaRecord({ hospitalCode: getOrganCode(), appointmentType: 1 });
      const { deptListTipsNew = '', selHospitalAreaRecordRespVos = [], hospitalAreaRecordList = [] } = data;
      const _hospitalAreaRecordList = hospitalAreaRecordList;
      _hospitalAreaRecordList.unshift({
        hospitalAreaCode: 'ALL',
        hospitalAreaName: '全部院区',
        areaAddress: '全部院区',
        hospitalCode: 'HID0101',
        latitude: '',
        longitude: '',
      });
      setHospitalAreaCode('ALL');
      setHospitalList(_hospitalAreaRecordList);
    } catch (error) {
      console.log('error:', error);
    }
  };
  /**
   * 获取科室列表
   * @return {*}
   */
  const fetchDeptList = async () => {
    try {
      Toast.show({ icon: 'loading', duration: 0 });
      const res = await selFirstDept({
        hospitalCode: getOrganCode(),
        appointmentType: 1,
        hospitalArea: hospitalAreaCode,
      });
      setDeptList(res || []);
    } catch (error) {
      console.log('error:', error);
    } finally {
      Toast.clear();
    }
  };

  /**
   * 修改院区
   * @return {*}
   */
  const changeHospital = (code) => {
    if (code) {
      setDeptCode(0);
      dispatch({
        type: 'hxappointment/updateState',
        payload: {
          historyDoctorList: [],
          historyDeptList: [],
        },
      });
      setHospitalAreaCode(code);
    }
  };

  /**
   * 查看全部医生
   * @return {*}
   */
  const lookAllDoctor = () => {
    const deptCategoryObj = deptList.find((item) => item?.deptCategoryCode === deptCode);
    if (deptCategoryObj) {
      history.push({
        pathname: '/hxappointment/doctorlist',
        query: {
          hospitalCode: getOrganCode(),
          hospitalAreaCode,
          deptCategoryCode: deptCategoryObj.deptCategoryCode,
          deptCategoryName: deptCategoryObj.deptCategoryName,
          drainageInfo
        },
      });
    }
  };

  /**
   * 选择一级科室 看过的科室
   * @return {*}
   */
  const chooseFirstDepartment = (code: string, deptCategoryName: string) => {
    if (code === 'ETYXZX') {
      window.location.href = `${COMMON_DOMAIN}/person/childCenter?organCode=HID0101`;
    } else {
      history.push({
        pathname: '/hxappointment/doctorlist',
        query: {
          hospitalCode: getOrganCode(),
          hospitalAreaCode,
          deptCategoryCode: code,
          deptCategoryName,
        },
      });
    }
  };

  /**
   * 选择亚专业方向
   * @return {*}
   */
  const chooseSecondDepartment = (deptDirectionCode?: string, deptDirectionName?: string) => {
    const deptCategoryObj = deptList.find((item) => item?.deptCategoryCode === deptCode);

    history.push({
      pathname: '/hxappointment/doctorlist',
      query: {
        hospitalCode: getOrganCode(),
        hospitalAreaCode,
        deptCategoryCode: deptCategoryObj?.deptCategoryCode,
        deptCategoryName: deptCategoryObj?.deptCategoryName,
        deptDirectionCode,
        deptDirectionName,
        drainageInfo,
      },
    });
  };

  /**
   *  获取历史科室和医生
   * @return {*}
   */
  const fetchHistoryDeptAndDoc = () => {
    const patientCardInfo = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA);
    const { pmi, cardId } = patientCardInfo || {};
    try {
      dispatch({
        type: 'hxappointment/querySelHistoryDeptAndDoc',
        payload: {
          patientHisPAPMI: pmi,
          hospitalCode: getOrganCode(),
          hospitalArea: hospitalAreaCode === 'ALL' ? '' : hospitalAreaCode,
          cardId,
        },
      });
    } catch (error) {
      console.log('error:', error);
    }
  };

  // 去往搜索
  const goToSearch = () => {
    history.push({
      pathname: '/hxappointment/search',
      query: {
        hospitalCode: getOrganCode(),
        hospitalAreaCode,
      },
    });
  };

  /**
   * 渲染医生列表
   * @param {*} item
   * @return {*}
   */

  const renderDoctor = (item, index) => {
    const goToDetial = () => {
      const pathname = '/doctor/index';
      history.push({
        pathname,
        query: {
          doctorId: item.doctorId,
          hospitalCode: getOrganCode(),
          hospitalArea: hospitalAreaCode,
          deptCategoryCode: item.deptCategoryCode,
          deptCategoryName: item.deptCategoryName,
          deptDirectionCode: item.deptDirectionCode,
          deptDirectionName: item.deptDirectionName,
          deptCode: item.deptCode,
          deptName: item.deptName,
          tabAreaCode: hospitalAreaCode,
        },
      });
    };

    return (
      <div className={styles.doctorItem} key={item.doctorId + index} onClick={goToDetial}>
        <HxDoctorAvatar src={item?.docHeadImage || ''} alt="" className={styles.avatar} />
        <div className={styles.doctorInfo}>
          <div>
            <span className={styles.name}>{item.docName}</span>
            <span className={styles.title}>{item.regTitelName}</span>
          </div>
          <div className={styles.dept}>{item.deptCategoryName}</div>
          <div className={styles.tagList}>
            <div className={classnames(styles.tag, item.status !== 1 && styles.noNum)}>
              {item.status === 1 ? '有号' : '无号'}
            </div>
          </div>
        </div>
      </div>
    );
  };

  /**
   * 点击一级科室
   * 1、非华西院区的，选科室后直接跳医生列表
   * 2、华西院区的，如果没有方向的还是直接跳，有方向需要先选方向
   * @param {*} v
   * @return {*}
   */

  const clickFirstDepartment = (v) => {
    const { id = '' } = v;
    const item = deptList.find((item) => item?.deptCategoryCode === id) ?? { deptDirectionList: [] };
    /* 科室弹框提示-医生列表显示 */
    setDrainageInfo(item?.drainageInfo || '');
    /* 儿童医学中心跳转 */
    if (id === 'ETYXZX') {
      window.location.href = `${COMMON_DOMAIN}/person/childCenter?organCode=HID0101`;
      return;
    }
    /* 有亚专业方向需要选亚专业方向 */
    if (Array.isArray(item.deptDirectionList) && item?.deptDirectionList.length) {
      setDeptCode(id);
      return;
    }
    /* 其它情况直接跳医生列表 */
    history.push({
      pathname: '/hxappointment/doctorlist',
      query: {
        hospitalCode: getOrganCode(),
        hospitalAreaCode,
        deptCategoryCode: item?.deptCategoryCode,
        deptCategoryName: item?.deptCategoryName,
        drainageInfo: item?.drainageInfo,
      },
    });
  };

  /**
   * 判断是否有数据
   * @return {*}
   */
  const noDataState = () => {
    if (loading) return false;
    if (deptCode === 0) return historyDoctorList.length === 0;
    if (deptCode === 1) return historyDeptList.length === 0;
    return secondDeptList.length === 0;
  };

  useEffect(() => {
    fetchHospitalList();
    return () => {
      dispatch({
        type: 'hxappointment/updateState',
        payload: {
          historyDoctorList: [],
          historyDeptList: [],
        },
      });
    };
  }, []);

  useEffect(() => {
    if (hospitalAreaCode) {
      fetchDeptList();
      fetchHistoryDeptAndDoc();
    }
  }, [hospitalAreaCode]);

  /* 处理全局样式-全局的less不敢修改，只能在这里处理 */
  useEffect(() => {
    document.body.style.overflowY = 'hidden';
    document.documentElement.style.overflowY = 'hidden';
    return () => {
      document.body.style.overflowY = 'auto';
      document.documentElement.style.overflowY = 'auto';
    };
  }, []);

  return (
    <div className={styles.DepartmentHx} id="DepartmentHx">
      <header className={styles.header}>
        <DropSelect dropList={hospitalList} hospitalAreaCode={hospitalAreaCode} onChange={changeHospital} />
        <div className={styles.search} onClick={goToSearch}>
          <img src={searchIcon} alt="" />
          搜索医院、科室、医生
        </div>
      </header>
      <div className={styles.xunfei}>
        <div className={styles.content} onClick={jumpTo}>
          <div className={styles.left}>
            <img src={question} alt="" />
            <span>不知道挂什么科室？</span>
          </div>
          <div className={styles.right}>
            使用智能导诊
            <img src={arrow} alt="" />
          </div>
        </div>
      </div>
      <main>
        <div className={styles.leftSelect}>
          <IndexBar
            list={
              deptList.map((item) => ({
                name: item?.deptCategoryName || '', // 补充默认值
                id: item?.deptCategoryCode || '',
              })) as { name: string; id: string }[]
            }
            indexKey="name"
            onTop={() => {
              return (
                <>
                  <div
                    className={classnames(styles.item, deptCode === 0 && styles.select, styles.first)}
                    onClick={() => setDeptCode(0)}
                  >
                    <img src={doctorIcon} alt="" />
                    <span>看过的医生</span>
                  </div>
                  <div
                    className={classnames(styles.item, deptCode === 1 && styles.select)}
                    onClick={() => setDeptCode(1)}
                  >
                    <img src={deptIcon} alt="" />
                    <span>看过的科室</span>
                  </div>
                </>
              );
            }}
            onChange={(v: any) => {
              clickFirstDepartment(v);
            }}
            onRow={(v) => <div className={classnames(styles.item, deptCode === v.id && styles.select)}>{v.name}</div>}
          />
        </div>
        <div className={styles.rightDept}>
          <div className={styles.deptList}>
            <>
              {loading && (
                <div className={styles.loadingWrap}>
                  <SpinLoading color="primary" style={{ '--size': '32px' }} />
                </div>
              )}
              {/* 看过的医生 */}
              {deptCode === 0 && (
                <>
                  {!!historyDoctorList.length &&
                    historyDoctorList.map((item, index) => {
                      return renderDoctor(item, index);
                    })}
                </>
              )}
              {/* 看过的科室 */}
              {deptCode === 1 &&
                !!historyDeptList.length &&
                historyDeptList.map((item) => {
                  return (
                    <div
                      className={styles.item}
                      key={item.deptCategoryCode}
                      onClick={() => chooseFirstDepartment(item?.deptCategoryCode, item?.deptCategoryName)}
                    >
                      {item.deptCategoryName}
                    </div>
                  );
                })}
            </>
            {deptCode !== 1 && deptCode !== 0 && (
              <>
                {secondDeptList && secondDeptList.length && (
                  <div className={styles.item} onClick={lookAllDoctor}>
                    查看全部医生
                  </div>
                )}

                {secondDeptList.map((item) => {
                  return (
                    <div
                      className={styles.item}
                      key={item.deptDirectionCode}
                      onClick={() => chooseSecondDepartment(item?.deptDirectionCode, item?.deptDirectionName)}
                    >
                      {item.deptDirectionName}
                    </div>
                  );
                })}
              </>
            )}
          </div>
          {noDataState() && (
            <div className={styles.empty}>
              <img src={empty} alt="" />
              <span>暂无内容</span>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default connect(({ hxappointment, loading }: { hxappointment: IHxAppointmentState; loading: Loading }) => ({
  loading: loading.models.hxappointment,
  hxappointment,
}))(DepartmentHx);
