.DepartmentHx {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  .header {
    width: 100%;
    height: 136px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    flex-shrink: 0;
    .search {
      margin-left: 24px;
      flex-shrink: 0;
      height: 72px;
      background: #f5f6fa;
      border-radius: 36px;
      padding-left: 24px;
      display: flex;
      align-items: center;
      font-size: 28px;
      text-align: left;
      color: #bbbeca;
      flex: 1;
      img {
        width: 40px;
        height: 40px;
        margin-right: 8px;
      }
    }
  }
  .xunfei {
    width: 100%;
    padding: 0 24px;
    flex-shrink: 0;
    .content {
      width: 100%;
      height: 104px;
      background: rgba(58, 211, 193, 0.12);
      border-radius: 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 24px 0 16px;
      .right {
        font-size: 28px;
        font-weight: 500;
        color: #3ad3c1;
        display: flex;
        align-items: center;
        img {
          width: 36px;
          height: 36px;
        }
      }
      .left {
        display: flex;
        align-items: center;
        img {
          width: 60px;
          height: 60px;
        }
        span {
          font-size: 28px;
          font-weight: 500;
          color: #03081a;
          margin-left: 8px;
        }
      }
    }
  }
  main {
    flex: 1;
    overflow-y: auto;
    display: flex;
    margin-top: 24px;
    .leftSelect {
      width: 360px;
      background: #f5f6fa;
      height: 100%;
      flex-shrink: 0;
      overflow-y: auto;
      border-radius: 0 24px 24px 0;
      &::-webkit-scrollbar {
        display: none;
      }
      :global {
        .adm-index-bar {
          background-color: transparent;
          position: unset;
          .adm-index-bar-anchor-title {
            display: none;
          }
        }
      }
      .item {
        padding-left: 36px;
        padding: 12px 24px 12px 36px;
        display: flex;
        align-items: center;
        color: #03081a;
        min-height: 88px;
        font-size: 28px;
        padding-right: 24px;
        margin-top: 8px;
        &.first {
          margin-top: 0;
        }
        img {
          width: 36px;
          height: 36px;
          margin-right: 16px;
        }
        &.select {
          position: relative;
          background: #fff;
          font-weight: bold;
          &::before {
            position: absolute;
            content: '';
            width: 8px;
            height: 32px;
            background: #3ad3c1;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
          }
        }
      }
    }
    .rightDept {
      flex: 1;
      height: 100%;
      overflow-y: auto;
      &::-webkit-scrollbar {
        display: none;
      }
      .deptList {
        width: 100%;
        .loadingWrap {
          width: 100%;
          height: 400px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .doctorItem {
          padding: 32px 24px 32px 32px;
          min-height: 178px;
          display: flex;
          .avatar {
            width: 80px;
            height: 80px;
          }
          .doctorInfo {
            margin-left: 16px;
          }
          .name {
            font-size: 28px;
            font-weight: 500;
            color: #03081a;
            line-height: 40px;
          }
          .title {
            font-size: 24px;
            text-align: left;
            color: #03081a;
            line-height: 34px;
            margin-left: 16px;
          }
          .dept {
            font-size: 24px;
            text-align: left;
            color: #989eb4;
            line-height: 34px;
            margin-top: 4px;
          }
          .tagList {
            display: flex;
            align-items: center;
            margin-top: 8px;
            .tag {
              width: 56px;
              height: 28px;
              background: #eaf8f6;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 20px;
              text-align: left;
              color: #3ad3c1;
              &.noNum {
                background: #f6f8ff;
                color: #989eb4;
              }
            }
          }
        }
        .item {
          min-height: 88px;
          background: #ffffff;
          padding: 12px 52px 12px 32px;
          font-size: 28px;
          color: #03081a;
          display: flex;
          align-items: center;
          &:not(:first-child) {
            margin-top: 8px;
          }
        }
      }
    }
    .empty {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      min-height: 50vh;
      img {
        width: 128px;
        height: 128px;
        opacity: 0.6;
        object-fit: cover;
      }
      span {
        font-size: 24px;
        color: #989eb4;
        margin-top: 16px;
      }
    }
  }
}
