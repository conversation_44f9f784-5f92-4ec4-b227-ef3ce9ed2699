import React, { PureComponent } from 'react';
import pinyin from 'pyfl';
import styles from './index.less';

const ID_PREFIX = 'indexlist';

const LIST_TOP_ID = 'TopID';

const PIN_YIN_MAP = [
  'A',
  'B',
  'C',
  'D',
  'E',
  'F',
  'G',
  'H',
  'J',
  'K',
  'L',
  'M',
  'N',
  'P',
  'Q',
  'R',
  'S',
  'T',
  'W',
  'X',
  'Y',
  'Z',
];

interface IndexBarItem {
  name: string;
  id: string;
}

interface IndexBarProps {
  list: IndexBarItem[];
  indexKey: string;
  onTop: () => JSX.Element;
  onChange: (record: IndexBarItem) => void;
  onRow: (record: IndexBarItem, index: number) => JSX.Element;
  onBottom?: () => JSX.Element;
  caseSensitive?: boolean;
  guideClass?: string;
}

const preFixId = (id) => ID_PREFIX + id;
class Guide extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      interacting: false,
      activeIndex: 0,
    };
  }

  handleTouchMove(e) {
    if (!this.state.interacting) return;
    const { clientX, clientY } = e.touches[0];
    const target = document.elementFromPoint(clientX, clientY);
    if (!target) return;
    const index = target.dataset['index'];
    if (index) {
      this.handleScrollTo(Number(index));
    }
  }

  handleScrollTo(index) {
    const { indexList = [], scrollTo = () => {} } = this.props;
    this.setState({
      activeIndex: index,
    });
    scrollTo(indexList[index]);
  }

  render() {
    const { indexList = [], guideClass = '' } = this.props;
    const { interacting = false, activeIndex } = this.state;
    return (
      <div
        className={`${styles['index-list-guide']} ${guideClass}`}
        onMouseDown={() => {
          this.setState({
            interacting: true,
          });
        }}
        onMouseUp={() => {
          this.setState({
            interacting: false,
          });
        }}
        onTouchStart={() => {
          this.setState({
            interacting: true,
          });
        }}
        onTouchEnd={() => {
          this.setState({
            interacting: false,
          });
        }}
        onTouchMove={(e) => this.handleTouchMove(e)}
      >
        {/* <div
      className={styles["index-list-guide-item"]}
      onClick={() => {
        scrollTo(LIST_TOP_ID);
      }}
    >
      {topIcon}
    </div> */}
        <div className={styles['index-list-guide-dividing']} />
        {indexList.map((v, inx) => (
          <div
            className={styles['index-list-guide-item']}
            key={v}
            // onClick={() => {
            //   scrollTo(v);
            // }}
            data-index={inx}
            onMouseDown={() => {
              this.handleScrollTo(inx);
            }}
            onTouchStart={() => {
              this.handleScrollTo(inx);
            }}
            onMouseEnter={() => {
              this.handleScrollTo(inx);
            }}
          >
            {v}
            {activeIndex === inx && interacting && <div className={styles.bubble}>{v}</div>}
          </div>
        ))}
      </div>
    );
  }
}

const List = ({ children, listRef }) => (
  <div className={styles['h5-srcoll-hodler']} red={listRef}>
    {children}
  </div>
);

const doList = (list, key, caseSensitive) => {
  const listData = {};
  list.map((v) => {
    const firstChar = v[key].substr(0, 1);
    const indexKey = caseSensitive ? pinyin(firstChar)[0] : pinyin(firstChar)[0].toLocaleUpperCase();
    if (PIN_YIN_MAP.includes(indexKey)) {
      if (listData[indexKey]) {
        listData[indexKey].push(v);
      } else {
        listData[indexKey] = [v];
      }
    } else {
      listData['#'] = [v];
    }
  });
  const indexList = Object.keys(listData).sort();
  // 排序字幕组内部展示顺序
  Object.values(listData).map((v) => {
    v = v.sort((a, b) => ('' + a[key]).localeCompare(b[key]));
  });
  return {
    indexList,
    listData,
  };
};

const defaultLineFunc = (v, key) => <div className={styles['default-index-list-line']}>{v[key]}</div>;

const IndexList: React.FC<IndexBarProps> = ({
  list = [],
  indexKey = 'index',
  onChange,
  onRow = (record, index) => defaultLineFunc(record, indexKey),
  onTop,
  onBottom,
  caseSensitive = false,
  guideClass = '',
}) => {
  const { indexList, listData } = doList(list, indexKey, caseSensitive);

  const listRef = React.createRef();

  const ListView = List;

  const scrollTo = (id) => {
    const ID = preFixId(id);
    // web
    const anchorElement = document.getElementById(ID);
    anchorElement.parentNode.scrollTop = anchorElement.offsetTop - anchorElement.offsetHeight;
    if (anchorElement) {
      anchorElement.scrollIntoView({ block: 'start', behavior: 'smooth' });
    } else {
      console.error('找不到ID：' + ID);
    }
  };

  return (
    <div
      className={styles['index-list']}
      style={{
        position: 'relative',
      }}
    >
      <ListView listRef={listRef}>
        <div className={styles['index-list-main']}>
          <div id={preFixId(LIST_TOP_ID)} className={styles['index-list-top-item']}></div>
          {onTop && onTop()}
          {indexList.map((v) => {
            const indexedDataList = listData[v] || [];
            return (
              <div className="index-list-item" key={v}>
                <div className={styles['index-list-item-head']} id={preFixId(v)}>
                  {/* {v} */}
                </div>
                {indexedDataList.map((v, i) => (
                  <div
                    className={styles['index-list-item-line']}
                    key={i}
                    onClick={() => {
                      onChange && onChange(v);
                    }}
                  >
                    {onRow(v, i)}
                  </div>
                ))}
              </div>
            );
          })}
          {onBottom && onBottom()}
        </div>
      </ListView>
      <Guide indexList={indexList} scrollTo={scrollTo} guideClass={guideClass} />
    </div>
  );
};
export default IndexList;
