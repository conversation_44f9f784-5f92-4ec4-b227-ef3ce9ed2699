.index-list {
  // position: absolute;
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 50px;
  overflow-y: scroll;
  height: 100%;
}

@second-color: #989eb4;

.default-index-list-line {
  width: 100%;
  height: 44px;
  line-height: 44px;
  font-size: 14px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.index-list-guide {
  position: fixed;
  top: 50%;
  transform: translateY(-45%);
  right: 0;
  width: 72px;
  padding-right: 24px;
  text-align: center;
  color: @second-color;
  font-size: 24px;
  font-weight: 400;
  padding-bottom: 40px;
  padding-top: 40px;
  user-select: none;
  z-index: 9;
  user-select: none;
  .index-list-guide-item {
    width: 100%;
    height: 36px;
    line-height: 36px;
    position: relative;
    .bubble {
      position: absolute;
      top: 50%;
      right: 60px;
      width: 80px;
      height: 80px;
      color: #ffffff;
      font-size: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: center;
      background: #ccc;
      border-radius: 50%;
      transform: translateY(-50%);
      z-index: 9;
    }
  }
  .index-list-guide-dividing {
    width: 100%;
    // height: 14px;
  }
}

.h5-srcoll-hodler {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-y: scroll;
  &::-webkit-scrollbar {
    display: none;
  }
}

.index-list-main {
  position: relative;
  width: 100%;
  // padding: 16px;
  box-sizing: border-box;
  .index-list-top-item {
    position: absolute;
    top: 0;
  }
  .index-list-item {
    width: 100%;
    padding-bottom: 12px;
    .index-list-item-head {
      position: relative;
      width: 100%;
      height: 36px;
      line-height: 36px;
      text-align: left;
      font-size: 12px;
      color: @second-color;
      border-top: 1px solid rgba(0, 0, 0, 0.08);
    }
    .index-list-item-line {
      position: relative;
    }
  }
}
