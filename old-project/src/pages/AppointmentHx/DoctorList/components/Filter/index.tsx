import React, { useEffect, useState } from 'react';
import { connect, Dispatch, Loading, history, useLocation } from 'umi';
import { Popup, Switch, Button, Grid } from 'antd-mobile-v5';
import classnames from 'classnames';
import { ISelDocListTimeRangeFilterData } from '@/pages/Appointment/data';
import { BASE_IMG_URL } from '@/pages/AppointmentHx/utils/config';
import qs from 'query-string';
import styles from './index.less';

const calendar = `${BASE_IMG_URL}/calendar.png`;
const filter = `${BASE_IMG_URL}/filter.png`;
const close = `${BASE_IMG_URL}/close.png`;
const filterOn = `${BASE_IMG_URL}/filter_on.png`;

interface TitleItem {
  /**
   * 医生职称编码
   */
  code: string;
  /**
   * 医生职称名称
   */
  name: string;
}
interface IProps {
  /** 是否作为搜索结果页面 1 是  */
  searchType?: number;
  /**
   * 职称列表
   */
  titleList?: TitleItem[];

  /** 设置是否查询有号 */
  onHaveNo?: (val: boolean) => void;

  /** 时段列表  */
  timeRangeList?: ISelDocListTimeRangeFilterData[];

  onFilter: (timeRange?: number | null, title?: string) => void;
}
const DoctorListFilter: React.FC<IProps> = (props) => {
  const { titleList = [], searchType = 0, onHaveNo, timeRangeList = [], onFilter } = props;
  const [visibleFilter, setVisibleFilter] = useState(false);
  const [selectTitle, setSelectTitle] = useState('');
  const [timeRange, setTimeRange] = useState<number | null>(2);
  const [haveNo, setHaveNo] = useState(false); // 是否只查看有号
  const query = qs.parse(window.location.search);

  const changeNo = (value) => {
    setHaveNo(!haveNo);
    onHaveNo && onHaveNo(value);
  };

  const onWeekSchedule = () => {
    history.push({
      pathname: '/hxappointment/weeklyschedule',
      query,
    });
  };
  return (
    <div className={styles.doctorListFilter}>
      <div className={classnames(styles.leftFilter, !!searchType && styles.searchFilterStyle)}>
        <div className={styles.numberSwitch}>
          <span>仅显示有号</span>
          <Switch style={{ '--height': '22px', '--width': '36px' }} checked={haveNo} onChange={changeNo} />
        </div>
        <div className={styles.filter}>
          <span className={styles.line} />
          <div
            onClick={() => {
              setVisibleFilter(true);
            }}
          >
            <span className={styles.text}>筛选</span>
            <img src={selectTitle || timeRange !== 2 ? filterOn : filter} alt="" />
          </div>
        </div>
      </div>
      {!searchType && <img src={calendar} alt="" className={styles.calendar} onClick={onWeekSchedule} />}
      <Popup
        visible={visibleFilter}
        bodyStyle={{
          borderTopLeftRadius: '12px',
          borderTopRightRadius: '12px',
          height: '60vh',
        }}
        className="filterPopup"
      >
        <div className="popupContent">
          <div className="header">
            <span>筛选</span>
            <img
              src={close}
              alt=""
              onClick={() => {
                setVisibleFilter(false);
              }}
            />
          </div>
          <div className="filterContent">
            {!!timeRangeList?.length && (
              <div className="filterItem">
                <div className="title">时段</div>
                <div className="list">
                  <Grid columns={3} style={{ width: '100%', '--gap-horizontal': '16px', '--gap-vertical': '12px' }}>
                    {timeRangeList.map((item) => {
                      return (
                        <Grid.Item key={item.value}>
                          <div
                            className={classnames('item', timeRange === item.value && 'select')}
                            onClick={() => {
                              setTimeRange(item.value);
                            }}
                          >
                            {item.text}
                          </div>
                        </Grid.Item>
                      );
                    })}
                  </Grid>
                </div>
              </div>
            )}
            {titleList.length > 0 && (
              <div className="filterItem">
                <div className="title">职称</div>
                <div className="titleList">
                  {titleList.map((item) => {
                    return (
                      <div
                        key={item.code}
                        className={classnames('titleItem', selectTitle === item.code && 'select')}
                        onClick={() => {
                          setSelectTitle(item.code);
                        }}
                      >
                        {item.name}
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </div>
          <div className="footer">
            <Button shape="rounded" className="button">
              重置
            </Button>
            <Button
              shape="rounded"
              color="primary"
              className="button"
              onClick={() => {
                onFilter && onFilter(timeRange, selectTitle);
                setVisibleFilter(false);
              }}
            >
              确定
            </Button>
          </div>
        </div>
      </Popup>
    </div>
  );
};

export default DoctorListFilter;
