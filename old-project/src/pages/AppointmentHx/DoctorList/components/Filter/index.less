.doctorListFilter {
  width: 100%;
  margin-top: 2px;
  height: 88px;
  background: #ffffff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .calendar {
    width: 48px;
    height: 48px;
  }
  .leftFilter {
    display: flex;
    align-items: center;
    &.searchFilterStyle {
      justify-content: space-between;
      width: 100%;
    }
    .filter {
      display: flex;
      align-items: center;
      .line {
        width: 2px;
        height: 32px;
        background: #ebedf6;
        margin: 0 24px;
      }
      > div {
        display: flex;
        align-items: center;
      }
      .text {
        font-size: 28px;
        color: #03081a;
      }
      img {
        width: 20px;
        height: 20px;
        margin-left: 8px;
      }
    }
    .numberSwitch {
      display: flex;
      align-items: center;
      font-size: 28px;
      color: #03081a;
      span {
        margin-right: 16px;
      }
      :global {
        .adm-switch-checkbox {
          &::before {
            display: none;
          }
        }
      }
    }
  }
}
:global {
  .filterPopup {
    .popupContent {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
    .filterContent {
      flex: 1;
      overflow-y: auto;
      padding: 0 32px;
      .filterItem {
        .title {
          font-size: 32px;
          color: #03081a;
          line-height: 48px;
        }
        .titleList {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          margin-top: 32px;
          gap: 24px 32px;

          .titleItem {
            min-width: 206px;
            height: 72px;
            background: #f5f6fa;
            border-radius: 16px;
            border: 2px solid #f5f6fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 28px;
            color: #51586d;
            padding: 8px;
            &.select {
              background: rgba(58, 211, 193, 0.1);
              border: 2px solid #3ad3c1;
              color: #3ad3c1;
              font-weight: 500;
            }
          }
        }
        .list {
          display: flex;
          justify-content: space-between;
          width: 100%;
          margin-top: 32px;
          .item {
            width: 100%;
            height: 72px;
            padding: 8px;
            background: #f5f6fa;
            border-radius: 16px;
            border: 2px solid #f5f6fa;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            font-size: 28px;
            color: #51586d;
            overflow: hidden;
            white-space: no-wrap;
            text-overflow: ellipsis;
            &.select {
              background: rgba(58, 211, 193, 0.1);
              border: 2px solid #3ad3c1;
              color: #3ad3c1;
              font-weight: 500;
            }
          }
        }
        &:not(:first-child) {
          margin-top: 40px;
        }
      }
    }
    .footer {
      flex-shrink: 0;
      background: #ffffff;
      padding: 24px 32px;
      padding-bottom: calc(32px + env(safe-area-inset-bottom));
      display: flex;
      .adm-button {
        width: 320px !important;
        height: 88px;
        &.adm-button-default {
          background: #f5f6fa;
          border-color: #f5f6fa;
        }
        &:first-child {
          margin-right: 46px;
        }
      }
    }
    .header {
      padding: 40px 32px 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-shrink: 0;
      span {
        font-size: 36px;
        font-weight: bold;
        text-align: left;
        color: #03081a;
        line-height: 50px;
      }
      img {
        width: 36px;
        height: 36px;
      }
    }
  }
}
