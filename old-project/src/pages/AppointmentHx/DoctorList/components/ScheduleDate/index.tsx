import React, { memo, useState, useEffect } from 'react';
import { generateScheduleDate } from '../../../utils/date';
import styles from './index.less';
import classnames from 'classnames';
import { IScheduleDateItem } from '@/pages/AppointmentHx/data';
import moment from 'moment';

interface IProps {
  /**
   * 生成的天数
   */
  days?: number;
  /**
   * 是否从今天开始生成，默认false
   */
  isBeginToday?: boolean;
  /**
   * 固定文案，4个字符
   */
  fixedText?: string;
  /**
   * 日期改变事件
   */
  onDateChange: (value: string) => void;

  scheduleDateList: IScheduleDateItem[];
}

/**
 * 日期对象数据
 */
interface IScheduleDate {
  status: number;
  scheduledDate: string;
  week: string;
}

const ScheduleDate = memo((props: IProps) => {
  const { onDateChange, fixedText = '全部号源', scheduleDateList = [] } = props;

  const [curIndex, setCurIndex] = useState(-1);

  const _clearDate = async () => {
    setCurIndex(-1);
    onDateChange && onDateChange('');
  };

  const _changeDate = async (item: IScheduleDate, index: number) => {
    const { scheduledDate } = item;
    setCurIndex(index);
    onDateChange && onDateChange(scheduledDate);
  };

  /**
   * 格式化日期
   * @param {string} date
   * @return {*}
   */

  const formatDate = (date: string) => {
    try {
      return moment(date).format('MM-DD');
    } catch (error) {
      return date;
    }
  };


  const renderContent = (
    <div className={styles.scheduleDate}>
      <div className={classnames(styles.staticDate, `${curIndex === -1 ? styles.isSelected : ''}`)}>
        <p onClick={_clearDate}>{fixedText}</p>
      </div>
      <div className={styles.scrollDate}>
        {scheduleDateList.length > 0 &&
          scheduleDateList.map((item: IScheduleDate, index: number) => (
            <div
              onClick={() => _changeDate(item, index)}
              data-index={index}
              className={`${styles.item} ${curIndex === index ? styles.isSelected : ''}`}
              key={index}
            >
              <span>{item.week}</span>
              <span>{formatDate(item.scheduledDate)}</span>
              <span style={{ color: item.status === 1 ? '#3AD3C1' : '#BBBECA' }}>{item.status === 1 ? '有号' : '无号'}</span>
            </div>
          ))}
      </div>
    </div>
  );

  return renderContent;
});

export default ScheduleDate;
