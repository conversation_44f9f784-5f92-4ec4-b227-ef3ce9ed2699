.scheduleDate {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  height: 156px;
  background: #ffffff;
  padding-left: 24px;
  .staticDate {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 104px;
    height: 124px;
    background: #f8f9fc;
    border-radius: 8px;
    flex-shrink: 0;
    &.isSelected {
      background-color: #3ad3c1 !important;
      p {
        color: #fff;
      }
    }

    p {
      width: 64px;
      font-size: 28px;
      font-weight: 500;
      text-align: center;
      color: #51586d;
      margin: 0;
    }
  }

  .scrollDate {
    display: flex;
    flex-direction: row;
    margin-left: 104px;
    overflow-x: scroll;

    .item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      width: 112px;
      height: 124px;
      background: #f8f9fc;
      border-radius: 8px;
      flex-shrink: 0;
      margin-left: 16px;
      &.isSelected {
        background-color: #3ad3c1 !important;
        span {
          color: #fff !important;
        }
      }
      &:last-child {
        margin-right: 24px;
      }

      span:nth-child(1) {
        font-size: 24px;
        text-align: center;
        color: #51586d;
      }
      span:nth-child(2) {
        font-size: 28px;
        font-weight: 500;
        text-align: center;
        color: #51586d;
      }
      span:nth-child(3) {
        font-size: 20px;
        text-align: center;
        color: #bbbeca;
      }
    }
  }
}
