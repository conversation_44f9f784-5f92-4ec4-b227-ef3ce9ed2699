import { getOrganCode } from '@/utils/parameter';
import React, { PureComponent } from 'react';
import styles from './index.less';
import { IHxDoctorListByMoreTermData } from '@/pages/AppointmentHx/data';

const sc = 'https://cdnhyt.cd120.com/person/assets/appoinmentHx/sc.png';


interface IProps {
  /**
   * 医生列表数据
   */
  doctorListData: IHxDoctorListByMoreTermData;
}

interface IState {}

class DoctorListModule extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      // visible: false,
      // haveNo: false,
      // selectedDoctorCode: '',
      // selectedDoctorName: '全部职称',
    };
  }

  render() {
    const {
      doctorListData: {
        docName,
        regTitelName,
        scheduleDeptName = '',
        deptName = '',
        docExperience,
        businessTagItemRespVoList = [],
      },
    } = this.props;
    return (
      <div className={styles.doctorInfo}>
        <div className={styles.doctorTitle}>
          <span>{docName}</span>
          <span>{regTitelName}</span>
        </div>
        <div className={styles.doctorDepartment}>{scheduleDeptName}</div>
        {deptName !== '' && <div className={styles.doctorDepartmentYa}>亚专业：{deptName}</div>}
        <div className={styles.doctorAdept}><img src={sc} alt="" />{docExperience}</div>
        {businessTagItemRespVoList?.length < 0 && (
          <div className={styles.doctorService}>
            {businessTagItemRespVoList.map((item: any) => {
              return <div key={item.businessTagValue}>{item.businessTagValue}</div>;
            })}
          </div>
        )}
      </div>
    );
  }
}

export default DoctorListModule;
