import React, { PureComponent } from 'react';
// import { history } from 'umi';
import { HxIcon } from '@/components';
import { getOrganCode, HxParameter } from '@/utils/parameter';
import { HxLocalStorage } from '@/utils/storage';
import classNames from 'classnames';
import _ from 'lodash';
import doctorImg from '@/assets/appoinment/挂号-医生头像.png';
import { isTfsmy } from '@/utils/platform';
import { IHxDoctorListByMoreTermData } from '@/pages/AppointmentHx/data';
import { Modal } from 'antd-mobile-v5';
import { history } from 'umi';
import HxModal from '@/components/HxModal';
import DoctoeInfo from './doctoeInfo';
import styles from './index.less';

const youhao = 'https://cdnhyt.cd120.com/person/assets/appoinmentHx/youhao.png';
const wuhao = 'https://cdnhyt.cd120.com/person/assets/appoinmentHx/wuhao.png';

interface IProps {
  /**
   * 医生列表数据
   */
  doctorListData: IHxDoctorListByMoreTermData;
  /**
   * 医生列表数据
   */
  goToDoctorDetails: (value: IHxDoctorListByMoreTermData, selectedIndex?: number) => void;
}

interface IState {}

class DoctorCard extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {};
  }

  onClick = (e: any, doctorListData: IHxDoctorListByMoreTermData) => {
    // 阻止事件冒泡
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();
    const { goToDoctorDetails } = this.props;
    goToDoctorDetails && goToDoctorDetails(doctorListData);
  };

  // 点击每个医生模块上的在线门诊按钮
  turnZxmz = (e: any, docCode: string, doctorListData: IHxDoctorListByMoreTermData) => {
    // 阻止事件冒泡
    e.stopPropagation();
    e.nativeEvent.stopImmediatePropagation();

    // 2020.12月底需求 在线门诊变更为线上服务，跳转医生聚合主页 以下为老代码
    const { openId, organCode } = HxParameter;
    const token = HxLocalStorage.get('token');
    const { businessTagItemRespVoList = [], doctorId, scheduleDeptCode, zxmzDoctorId } = doctorListData;
    const servList = ['zxmz', 'hlzx', 'gdpb', 'bmmz', 'ypzx']; // 跳转线上门诊医生主页的服务
    const zxmzFlag = _.some(businessTagItemRespVoList, (item: any) => servList.includes(item.businessTagCode));
    if (!zxmzFlag) {
      HxModal.show({
        title: '温馨提示',
        content: <div>该医生暂未开通在线门诊，为您推荐同科室的其他医生</div>,
        closeOnMaskClick: true,
        actions: [
          {
            text: '点击前往',
            key: 'confirm',
            className: 'primary',
            onClick: () => {
              this.turnZxmz2(scheduleDeptCode);
              Modal.clear();
            },
          },
        ],
      });
    } else {
      // 跳在线门诊
      const { goToDoctorDetails } = this.props;
      goToDoctorDetails && goToDoctorDetails(doctorListData, 1, zxmzFlag);
    }
  };

  turnZxmz2 = (scheduleDeptCode?: string) => {
    const { openId } = HxParameter;
    const token = HxLocalStorage.get('token');
    // 跳在线门诊
    window.location.href = `${API_ZXMZ}/online/notice?selectIndex=-1&token=${token}&organCode=HID0101&openId=${openId}&selectKey=${scheduleDeptCode}`;
  };

  toPITeamHomePage = (doctorListData: any) => {
    const { doctorId } = doctorListData;
    if (doctorId) {
      const [teamWorkId, teamId] = doctorId.split('_');
      history.push({
        pathname: '/doctor/team/home',
        query: {
          teamId,
          teamWorkId,
          organCode: getOrganCode(),
        },
      });
    }
  };

  standbyImage = (e: any) => {
    const img = e.target;
    img.src = doctorImg;
    img.οnerrοr = null; // 控制不要一直跳动;
  };

  numIcon = (type = 0) => {
    return <img src={type === 1 ? youhao : wuhao} alt="" />;
  };

  render() {
    const { doctorListData } = this.props;
    const { docCode = '' } = doctorListData;
    const { organCode } = HxParameter;
    const { status = '', docHeadImage, piType, mdtFlag, doctorId, zxmzDoctorId } = doctorListData; // status 1 有号 2 约满  3 停诊

    return (
      <div className={styles.doctorWrap} onClick={(e) => this.onClick(e, doctorListData)}>
        <div className={styles.listItem}>
          <div className={styles.headImg}>
            {docHeadImage ? (
              <img src={docHeadImage} onError={(e) => this.standbyImage(e)} alt="" />
            ) : (
              <HxIcon iconName="doctor-avatar" className={styles.icon} />
            )}
          </div>
          <DoctoeInfo doctorListData={doctorListData} />
          {piType === 1 ? (
            <div className={styles.hasNum}>{this.numIcon(1)}</div>
          ) : doctorId ? (
            <div className={status === 1 ? classNames(styles.hasNum) : classNames(styles.hasNum, styles.hasNoNum)}>
              {status === 1 && this.numIcon(1)}
              {(status === 2 || status === 3 || status === '') && this.numIcon(0)}
              {/* {status === 3 && '停诊'} */}
            </div>
          ) : null}
        </div>
        <div className={styles.bottomButtonBox}>
          {piType === 1 ? (
            <div className={styles.wrapbox}>
              <button
                type="button"
                onClick={() => {
                  this.toPITeamHomePage(doctorListData);
                }}
                className={styles.yyButton}
              >
                申请问诊
              </button>
            </div>
          ) : (
            <div className={styles.wrapbox}>
              {organCode === 'HID0101' && !isTfsmy() && !mdtFlag && (
                <button
                  type="button"
                  onClick={(e) => {
                    this.turnZxmz(e, docCode, doctorListData);
                  }}
                  className={styles.zxButton}
                >
                  在线门诊
                </button>
              )}
              {status === 1 && (
                <button type="button" className={styles.yyButton} onClick={(e) => this.onClick(e, doctorListData)}>
                  {doctorId ? '预约挂号' : '查看详情'}
                </button>
              )}
            </div>
          )}
        </div>
      </div>
    );
  }
}

export default DoctorCard;
