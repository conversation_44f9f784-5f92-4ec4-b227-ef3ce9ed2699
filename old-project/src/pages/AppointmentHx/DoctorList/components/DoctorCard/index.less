@import '~@/styles/mixin.less';

.doctorWrap {
  position: relative;
  display: flex;
  flex-direction: column;
  margin-top: 24px;
  color: #03081a;
  font-size: 24px;
  background: #ffffff;
  border-radius: 28px;
  padding: 24px;
  .listItem {
    display: flex;
    box-sizing: border-box;
    height: auto;

    .headImg {
      flex-shrink: 0;
      width: 96px;
      height: 96px;
      img {
        width: 96px;
        height: 96px;
        border-radius: 50%;
      }
      .icon {
        width: 96px;
        height: 96px;
        border-radius: 50%;
      }
    }

    .doctorInfo {
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: space-between;
      box-sizing: border-box;
      color: rgba(102, 102, 102, 1);
      margin-left: 16px;
      width: 0;

      .doctorTitle {
        width: 420px;
        span:nth-child(2) {
          font-size: 28px;
          color: #03081a;
          line-height: 36px;
        }
        span:nth-child(1) {
          margin-right: 16px;
          font-weight: bold;
          font-size: 36px;
          color: #03081a;
          line-height: 44px;
        }
      }

      .doctorDepartment {
        overflow: hidden;
        white-space: wrap;
        text-overflow: ellipsis;
        font-size: 28px;
        color: #03081a;
        line-height: 36px;
        margin-top: 16px;
      }
      .doctorDepartmentYa {
        font-size: 28px;
        color: #03081a;
        line-height: 36px;
        margin-top: 8px;
      }

      .doctorAdept {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        font-size: 24px;
        color: #989eb4;
        line-height: 36px;
        margin-top: 16px;
        img {
          width: 54px;
          height: 24px;
          margin-right: 6px;
          margin-top: 6px;
          vertical-align: top;
        }
      }

      .doctorService {
        display: flex;

        div {
          width: 103px;
          height: 32px;
          margin-right: 12px;
          color: #f5aa09;
          font-size: 20px;
          line-height: 32px;
          text-align: center;
          border: 1px solid #f5aa09;
          border-radius: 16px;
        }
      }
    }

    .hasNum {
      position: absolute;
      top: 24px;
      right: 0;
      width: 88px;
      height: 46px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .bottomButtonBox {
    width: 100%;
    padding-top: 24px;
    .wrapbox {
      display: flex;
      float: right;
      height: 100%;
      button {
        width: 144px;
        height: 52px;
        padding: 0;
        font-weight: 300;
        font-size: 24px;
        line-height: 52px;
        text-align: center;
        border-radius: 30px;
        opacity: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: transparent;
        font-weight: 500;
      }
      .zxButton {
        color: #7facff;
        border: 2px solid #7facff;
      }
      .yyButton {
        color: #3ad3c1;
        border: 2px solid #3ad3c1;
      }
      button:nth-child(2n) {
        margin-left: 20px;
      }
    }
  }
}
