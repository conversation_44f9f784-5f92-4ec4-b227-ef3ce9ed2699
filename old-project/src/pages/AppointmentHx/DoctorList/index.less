.doctorListContainer {
  width: 100%;
  position: relative;
  background-color: #f5f6fa;
  min-height: 100vh;
  .header {
    min-height: 246px;
    position: sticky;
    position: -webkit-sticky;
    left: 0;
    top: 0;
    background-color: #fff;
    z-index: 1;
    .top {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      height: 104px;
      padding: 16px 32px;
      background: #fff;

      .inputView {
        position: relative;
        width: 100%;
        height: 72px;
        background: #f5f6fa;
        border-radius: 36px;
        display: flex;
        align-items: center;
        padding-left: 24px;
        justify-content: space-between;
        img {
          width: 40px;
          height: 40px;
          margin-right: 8px;
          flex-shrink: 0;
        }

        input {
          box-sizing: border-box;
          width: 100%;
          height: 100%;
          font-size: 28px;
          background: #f5f6fa;
          border: none;
          color: #03081a;
          caret-color: #bbbeca;
          flex: 1;
        }
        .search {
          padding: 0 32px;
          padding-left: 34px;
          position: relative;
          font-size: 32px;
          font-weight: bold;
          text-align: center;
          color: #3ad3c1;
          &::before {
            content: '';
            width: 2px;
            height: 32px;
            background: #e0e2eb;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
          }
        }
        form {
          display: flex;
          align-items: center;
          justify-content: space-between;
          flex: 1;
          padding-right: 24px;
          .clear {
            width: 32px;
            height: 32px;
            flex-shrink: 0;
            fill: red;
          }
        }

        input::-webkit-input-placeholder,
        input::-moz-placeholder,
        input::-ms-input-placeholder {
          color: #aaa;
          font-size: 26px;
        }
      }
    }
  }
  main {
    .list {
      padding: 0 24px;
      padding-bottom: 24px;
    }
    .empty {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 60vh;
    }
  }
}


.hintModalBox {
  background: #ffffff;
  border-radius: 24px;
  overflow: hidden;
  .modalBoxTitle {
    font-size: 36px;
    font-family: PingFang SC, PingFang SC-Semibold;
    font-weight: 600;
    color: #03081a;
  }

  .modalBoxCont {
    .modalBoxContRow1 {
      font-size: 28px;
      width: 100%;
      font-family: PingFang SC, PingFang SC-Regular;
      font-weight: 400;
      color: #51586d;
      margin-bottom: 40px;
      text-align: left;
    }

    .modalBoxContRow2 {
      width: 100%;

      .modalBoxContRow2Item {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24px 16px 24px 24px;
        background: #ffffff;
        border: 2px solid #f6f8ff;
        border-radius: 8px;
        box-sizing: border-box;
        margin-bottom: 24px;

        .modalBoxContRow2ItemL {
          display: flex;
          align-items: center;
          column-gap: 24px;

          .modalBoxContRow2ItemTxt {
            font-size: 32px;
            font-family: PingFang SC, PingFang SC-Medium;
            font-weight: 500;
            text-align: center;
            color: #03081a;
          }

          img {
            width: 48px;
            height: 48px;
            flex: none;
          }
        }

        .modalBoxContRow2ItemR {
          width: 36px;
          height: 36px;
          flex: none;
        }
      }
    }

    .modalBoxContRow3 {
      width: 366px;
      height: 80px;
      background: #3ad3c1;
      border-radius: 40px;
      font-size: 28px;
      font-family: PingFang SC, PingFang SC-Medium;
      font-weight: 500;
      text-align: center;
      color: #ffffff;
      line-height: 80px;
      margin: 48px auto 0 auto;
    }
  }
}
