import React, { useEffect, useState } from 'react';
import { connect, Dispatch, history, IAppointmentState } from 'umi';
import { HxEmpty, HxIcon } from '@/components';
import { Toast } from 'antd-mobile-v5';
import { Modal } from 'antd-mobile';
import moment from 'moment';
import { getChannelCode, getOrganCode } from '@/utils/parameter';
import { StorageEnum } from '@/utils/enum';
import { ISelDocListTimeRangeFilterData } from '@/pages/Appointment/data';

import jyjc from '@/assets/DoctorList/jyjc.png';
import fzky from '@/assets/DoctorList/fzky.png';
import addressSelectArea from '@/assets/addressSelectArea.png';
import { HxSessionStorage } from '@/utils/storage';
import Filter from './components/Filter';
import { selAppointmentDate, selDoctorListByMoreTerm } from '../service';
import { IHxDoctorListByMoreTermData, IScheduleDateItem } from '../data';
import ScheduleDate from './components/ScheduleDate';
import DoctorCard from './components/DoctorCard';
import styles from './index.less';
import { BASE_IMG_URL } from '../utils/config';

const emptyDoctor = `${BASE_IMG_URL}/empty_doctor.png`;
const searchIcon = `${BASE_IMG_URL}/input_search.png`;

const modalData = [
  {
    id: 1,
    icon: jyjc,
    title: '开检验检查',
    url: `/docList?type=kjczq&organCode=HID0101&channelCode=${getChannelCode()}`,
  },
  {
    id: 2,
    icon: fzky,
    title: '复诊开药',
    url: `/docList?type=kyzq&organCode=HID0101&channelCode=${getChannelCode()}`,
  },
];

interface IEvent {
  target: {
    value: string;
  };
  preventDefault: () => void;
}

interface IProps {
  dispatch: Dispatch;
  loading: Boolean | undefined;
  location: {
    query: {
      hospitalCode: string;
      hospitalAreaCode?: string;
      deptCategoryCode?: string;
      clinicCode?: string;
      keyword?: string;
      deptCategoryName?: string;
      channelElderCode?: string;
      deptCode?: string;
      /** 是否作为搜索结果页面  */
      searchType?: string;
      deptDirectionCode?: string;
      deptDirectionName?: string;
      /* 弹框内容 */
      drainageInfo?: string;
    };
  };
}
const AppointmentDoctorList: React.FC<IProps> = (props) => {
  const {
    location: {
      query: {
        hospitalCode = '',
        hospitalAreaCode: queryHospitalAreaCode = '',
        hospitalAreaCode = '',
        deptCategoryCode = '',
        clinicCode = '',
        keyword: defaultKeyword = '',
        deptCategoryName = '',
        channelElderCode = '',
        deptCode = '',
        searchType = '0',
        deptDirectionCode = '',
        deptDirectionName = '',
      },
    },
    dispatch,
  } = props;

  let { location: { query: { drainageInfo } = {} } = {} } = props;
  try {
    drainageInfo && (drainageInfo = JSON.parse(drainageInfo));
  } catch (error) {
    console.log('error:', error);
  }

  const [scheduleDate, setScheduleDate] = useState(''); // 排班日期
  const [scheduleDateList, setScheduleDateList] = useState<Partial<IScheduleDateItem>[]>([]); // 排班日期
  const [haveNo, setHaveNo] = useState(0); // 是否只看有号：0-否 1-是
  const [timeRange, setTimeRange] = useState<number>(2);
  const [timeRangeList, setTimeRangeList] = useState<ISelDocListTimeRangeFilterData[]>([]);
  const [regTitelCode, setRegTitelCode] = useState<string>('');
  const [doctorList, setDoctorList] = useState<IHxDoctorListByMoreTermData[]>([]);
  const [loading, setLoading] = useState(false);
  const [keyword, setKeyWord] = useState<string>(defaultKeyword);
  const [hintVisible, setHintVisible] = useState(false);
  const [titleListdata, setTitleListdata] = useState<any[]>([]);

  /** 职称列表 */
  const getTitleList = (list: IHxDoctorListByMoreTermData[]) => {
    const selRegTitelNameListRespVos = Array.from(
      new Map(
        list
          .filter((item) => item?.regTitelCode)
          .map((item) => [
            item.regTitelCode,
            {
              hisCTPCPLevelID: item.regTitelCode,
              hisName: item.regTitelName,
              serialNumber: item.serialNumber,
            },
          ]),
      ).values(),
    ).sort((a: any, b: any) => a.serialNumber - b.serialNumber);
    setTitleListdata(
      [{ hisCTPCPLevelID: '', hisName: '全部职称', category: 0, serialNumber: 0 }, ...selRegTitelNameListRespVos].map(
        (item) => ({
          ...item,
          code: item.hisCTPCPLevelID,
          name: item.hisName,
        }),
      ),
    );
  };

  /**
   * 查询医生列表
   * @return {*}
   */

  const fetchDoctorList = async () => {
    /* 传卡id后台判断外省号源 */
    const { cardId = '' } = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA) || {};
    try {
      Toast.show({ icon: 'loading', duration: 0 });
      setLoading(true);
      const res = await selDoctorListByMoreTerm({
        appointmentType: 1, // 标记查询有号源的科室列表入口(不传默认为预约挂号):1 预约挂号 2 当日挂号
        deptCategoryCode, // 一级科室编码
        haveNo: Number(haveNo), // 是否只看有号：0-否 1-是
        timeRange, // 上午下午时间段 只支持四川大学华西医院
        hospitalAreaCode, // 院区编码
        hospitalCode, // 医院编码
        keyWord: keyword, // 搜索关键字-若此字段不传或者传空字符串则代表全部
        keyWordEnumType: 0, // 搜索关键字类型 0 全部 1科室(既本业务系统的一级科室 2医生名称 3擅长 4亚专业(既本业务系统的二级科室-目前华西医院特有的称呼)
        regTitelCode, // 医生职称编码
        scheduleDate: scheduleDate ?? moment().format('YYYY-MM-DD'), // 排班日期-不传代表查明天及以后的
        deptCode,
        clinicCode,
        deptDirectionCode,
        cardId,
      });
      setDoctorList(res || []);
      /* 前端过滤获取医生职称列表 */
      if (titleListdata.length === 0) {
        getTitleList(res || []);
      }
    } catch (error) {
      console.log('error:', error);
    } finally {
      Toast.clear();
      setLoading(false);
    }
  };

  /**
   * 查询可预约日期列表
   * @return {*}
   */
  const fetchScheduleDateList = async () => {
    /* 传卡id后台判断外省号源 */
    const { cardId = '' } = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA) || {};
    try {
      const res = await selAppointmentDate({
        hospitalAreaCode,
        hospitalCode,
        deptDirectionCode,
        deptCategoryCode,
        keyWord: keyword,
        cardId,
      });
      if (res && res.length) {
        setScheduleDateList(res);
      }
    } catch (error) {
      console.log('error:', error);
    }
  };

  /**
   * 获取筛选时段
   * @return {*}
   */

  const fetchTimeRange = async () => {
    dispatch({
      type: 'appointment/selDocListTimeRangeFilter',
      payload: {},
      callback: (data: ISelDocListTimeRangeFilterData[]) => {
        setTimeRangeList(data);
      },
    });
  };

  const onDateChange = (value) => {
    setScheduleDate(value);
  };

  const search = () => {
    fetchScheduleDateList();
    fetchDoctorList();
  };
  const clear = () => {
    setKeyWord('');
  };
  // 软键盘搜索事件
  const onSubmit = (e: any) => {
    e.preventDefault();
    search();
  };

  const goUrl = (url: string) => {
    setHintVisible(false);
    window.location.href = url;
  };

  /**
   * 跳转到医生主页
   * @param data 医生列表数据
   * @param zxmzFlag 点击在线门诊
   * @return {*}
   */
  const goToDoctorDetails = (
    data: IHxDoctorListByMoreTermData,
    selectedIndex: number = 2,
    zxmzFlag: boolean = false,
  ) => {
    const { deptCode, doctorId, hospitalAreaCode, deptCategoryCode } = data;
    const tabAreaCode = queryHospitalAreaCode;
    const pathname = '/doctor/index';
    if (data.piType === 1) {
      if (doctorId) {
        const [teamWorkId, teamId] = doctorId.split('_');
        history.push({
          pathname: '/doctor/team/home',
          query: {
            teamId,
            teamWorkId,
            organCode: getOrganCode(),
          },
        });
      }
      return;
    }
    if (data.sourceChannel === 'unLineMdt' && hospitalCode === 'HID0101' && data.mdtTeamId) {
      history.push({
        pathname: '/doctor/hxhomemdtline',
        search: `organCode=HID0101&hospitalAreaCode=${data.hospitalAreaCode}&deptCode=${
          data.deptCode ? data.deptCode : ''
        }&doctorId=${data.doctorId ? data.doctorId : ''}&docName=${data.docName}&hospitalName=${
          data.hospitalName
        }&sourceChannel=${data.sourceChannel}&mdtTeamId=${data.mdtTeamId}&servType=24`,
      });
      return;
    }
    if (data.sourceChannel === 'lineMdt' && hospitalCode === 'HID0101' && data.teamId) {
      history.push({
        pathname: '/doctor/hxhomemdtline',
        search: `organCode=HID0101&price=&teamId=${data.teamId}&sourceChannel=${data.sourceChannel}&mdtTeamId=${data.mdtTeamId}&servType=24`,
      });
      return;
    }
    history.push({
      pathname,
      query: {
        sign: 1,
        tabAreaCode,
        hospitalAreaCode,
        hospitalCode,
        deptCode,
        deptCategoryCode,
        doctorId,
        channelElderCode,
        selectedIndex,
        deptDirectionCode,
        deptDirectionName,
        scheduleDate, // 排班日期
        zxmzFlag, // 医生主页在线门诊模块高亮标识
      },
    });
  };

  /* 筛选有号无号 */
  const onHaveNo = async (value) => {
    setHaveNo(value ? 1 : 0);
  };

  /* 筛选 */
  const onFilter = (timeRange, title) => {
    setRegTitelCode(title);
    setTimeRange(timeRange);
  };

  useEffect(() => {
    document.title = deptDirectionName || (deptCategoryName === '' ? '医生列表' : deptCategoryName);
    fetchTimeRange();
    fetchScheduleDateList();
    if (drainageInfo && drainageInfo.length) {
      setHintVisible(true);
    }
    return () => {
      Toast.clear();
      setHintVisible(false);
    };
  }, []);

  useEffect(() => {
    fetchDoctorList();
  }, [scheduleDate, haveNo, timeRange, regTitelCode]);

  return (
    <div className={styles.doctorListContainer}>
      <header className={styles.header}>
        {(defaultKeyword || searchType === '1') && (
          <div className={styles.top}>
            <div className={styles.inputView}>
              <form action="" onSubmit={onSubmit}>
                <img src={searchIcon} alt="" />
                <input
                  placeholder="搜索科室、医生、擅长"
                  value={keyword}
                  type="search"
                  onChange={(e: IEvent) => {
                    try {
                      setKeyWord(e.target?.value.trim());
                    } catch (error) {
                      console.log('error:', error);
                    }
                  }}
                  // autoFocus
                />
                {keyword.length > 0 && <HxIcon className={styles.clear} onClick={() => clear()} iconName="clear" />}
              </form>
              <span onClick={() => search()} className={styles.search}>
                搜索
              </span>
            </div>
          </div>
        )}
        <ScheduleDate days={7} onDateChange={(value) => onDateChange(value)} scheduleDateList={scheduleDateList} />
        <Filter
          searchType={defaultKeyword || searchType === '1' ? 1 : 0}
          titleList={titleListdata}
          timeRangeList={timeRangeList}
          onHaveNo={(value) => onHaveNo(value)}
          onFilter={onFilter}
        />
      </header>
      <main>
        <div className={styles.list}>
          {doctorList.length ? (
            doctorList.map((item) => (
              <DoctorCard
                key={`${Number(Math.random().toString().substr(3, 12) + Date.now()).toString(36)}`}
                doctorListData={item}
                goToDoctorDetails={goToDoctorDetails}
              />
            ))
          ) : loading ? (
            ''
          ) : (
            <div className={styles.empty}>
              <HxEmpty emptyMsg="暂无医生" canRefresh={false} isNewImg emptyImg={emptyDoctor} />
            </div>
          )}
        </div>
      </main>

      <Modal
        className={styles.hintModalBox}
        visible={hintVisible}
        transparent
        maskClosable={false}
        // onClose={() => this.onClose()}
        title={<div className={styles.modalBoxTitle}>温馨提示</div>}
        footer={[]}
      >
        <div className={styles.modalBoxCont}>
          <div className={styles.modalBoxContRow1}>为方便您的就医体验，若您有以下就医需求可选择线上问诊</div>
          <div className={styles.modalBoxContRow2}>
            {(drainageInfo || []).map((item: any, index: number) => (
              <div
                key={index}
                onClick={() => {
                  goUrl(item.openUrl);
                }}
                className={styles.modalBoxContRow2Item}
              >
                <div className={styles.modalBoxContRow2ItemL}>
                  <img src={item.icon} alt="" />
                  <div className={styles.modalBoxContRow2ItemTxt}>{item.title}</div>
                </div>
                <img className={styles.modalBoxContRow2ItemR} src={addressSelectArea} alt="" />
              </div>
            ))}
          </div>
          <div
            onClick={() => {
              setHintVisible(false);
            }}
            className={styles.modalBoxContRow3}
          >
            继续挂号
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default connect(({ appointment }: { appointment: IAppointmentState }) => ({
  appointment,
}))(AppointmentDoctorList);
