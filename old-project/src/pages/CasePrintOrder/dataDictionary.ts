/** 订单状态枚举 */
const ORDER_STATUS = [
  {
    label: '待审核',
    value: 1,
    color: '#FE8F3C',
  },
  {
    label: '待支付',
    value: 2,
    color: '#FC4553',
  },
  {
    label: '待发货',
    value: 3,
    color: '#FE8F3C',
  },
  {
    label: '已发货',
    value: 4,
    color: '#32B9AA',
  },
  {
    label: '审核未通过',
    value: 5,
    color: '#999999',
  },
  {
    label: '已取消',
    value: 6,
    color: '#999999',
  },
  {
    label: '已退款',
    value: 7,
    color: '#999999',
  },
  {
    label: '退款失败',
    value: 8,
    color: '#999999',
  },
  {
    label: '支付失败',
    value: 9,
    color: '#999999',
  },
];

/** 线下自取订单状态枚举 */
const OFFLINE_STATUS = [
  {
    label: '待审核',
    value: 1,
    color: '#FE8F3C',
  },
  {
    label: '待支付',
    value: 2,
    color: '#FC4553',
  },
  {
    label: '待复印',
    value: 3,
    color: '#FE8F3C',
  },
  {
    label: '待取件',
    value: 4,
    color: '#FE8F3C',
  },
  {
    label: '审核未通过',
    value: 5,
    color: '#999999',
  },
  {
    label: '已取消',
    value: 6,
    color: '#999999',
  },
  {
    label: '已取件',
    value: 10,
    color: '#32B9AA',
  },
];

/** 线下自取订单状态详情描述枚举 */
const OFFLINE_DESC_STATUS = [
  {
    label: '待复印',
    value: 3,
    signInStatus: 1,
    text: '到院后点击签到进行取件',
  },
  {
    label: '待复印',
    value: 3,
    signInStatus: 2,
    text: '您已经进行了取件签到',
  },
  {
    label: '待取件',
    value: 4,
    signInStatus: 1,
    text: '到院后点击签到进行取件',
  },
  {
    label: '待取件',
    value: 4,
    signInStatus: 2,
    text: '您已经进行了取件签到',
  },
  {
    label: '已取消',
    value: 6,
    text: '订单超时未支付，已取消',
  },
  {
    label: '已取件',
    value: 10,
    text: '您已完成病案复印取件，请妥善保管相关资料。',
  },
];

/* 节假日枚举 */
const HOLIDAY_SHIELDING = [
  /* 元旦节 */
  '2025-01-01',
  /* 春节 */
  '2025-01-28',
  '2025-01-29',
  '2025-01-30',
  '2025-01-31',
  '2025-02-01',
  '2025-02-02',
  '2025-02-03',
  '2025-02-04',
  /* 清明节 */
  '2025-04-04',
  '2025-04-05',
  '2025-04-06',
  /* 劳动节 */
  '2025-05-01',
  '2025-05-02',
  '2025-05-03',
  '2025-05-04',
  '2025-05-05',
  /* 端午节 */
  '2025-05-31',
  '2025-06-01',
  '2025-06-02',
  /* 中秋节 */
  /* 国庆节 */
  '2025-10-01',
  '2025-10-02',
  '2025-10-03',
  '2025-10-04',
  '2025-10-05',
  '2025-10-06',
  '2025-10-07',
  '2025-10-08',
];

export { ORDER_STATUS, OFFLINE_STATUS, OFFLINE_DESC_STATUS, HOLIDAY_SHIELDING };
