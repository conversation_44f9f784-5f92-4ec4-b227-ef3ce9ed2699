import { Response, Request } from 'express';
import Mock from 'mockjs';
import { commonSuccessResponse } from '../../../mock/util';
import { CasePrintOrderItemType } from './data.d';

const endTimeStampSecond = new Date().getTime() + 3 * 60 * 1000;

const casePrintOrderData = Mock.mock({
  'list|10-20': [
    {
      applyDateTime: '@date',
      'applyStatus|1-9': 1,
      caseNo: '@id',
      deptName: '神经外科',
      inDateTime: '@date',
      organName: '四川大学华西第四医院',
      patName: '@cname',
      recordId: '@id',
      endTimeStampSecond,
      docName: '@cname',
      unPassReason: '审核不通过的原因哦～',
    },
  ],
});

// const reasonList = ['司法诉讼（全套病历）', '特殊门诊', '医保报销', '看病复诊'];

const copyItemsData = Mock.mock({
  'list|1-4': [
    {
      copier: '@integer(1,100)',
      purposeCode: 'sss',
      // purposeName: "@pick(['司法诉讼（全套病历）','特殊门诊','医保报销','看病复诊'])",
      totalPages: '@integer(1,100)',
      totalPrice: '@float(30,200)',
      unitPrice: '@float(1,20)',
    },
  ],
});

const detailOtherInfo = {
  allPrice: 100,
  cardNo: 'JZK4214158833221',
  recAddress: '四川省成都市高新区金融城A座8单元F08',
  recPhone: '15624356789',
  receiver: '李常超',
  payPopupDesc:
    '<div style="text-align: left;color: rgba(0,0,0,0.85)"><div>1、该订单仅支持复印费用支付，快递邮寄费用本期只支持快递到付方式</div><div>2、病案复印不支持线上退费，一经支付不做退费处理</div></div>',
  expressDesc: '当前仅支持顺丰到付，邮费以到付为准',
  order: {
    paidTime: '2020-12-23 09:54:53',
    orderDateTime: '2020-12-22 09:54:53',
    dealSeq: 'DD324765645342',
    payChannelName: '支付宝',
    paidTransNo: 'LS35474532425',
    allPrice: 100,
  },
  items: copyItemsData.list,
  trackingNum: 'SF352765322',
};

const casePrintOrderList = casePrintOrderData.list;

export default {
  /** 查询病案邮寄的订单列表 */
  'POST /cloud/hosplatcustomer/caseExpress/queryCasePrintOrderList': (req: Request, res: Response) => {
    const { applyStatus } = req.body;
    const orderList = casePrintOrderList.filter((item: CasePrintOrderItemType) => {
      if (applyStatus === 99) return true;
      return item.applyStatus === applyStatus;
    });
    setTimeout(() => {
      res.status(200).send({
        ...commonSuccessResponse,
        data: {
          orderList,
        },
      });
    }, 500);
  },
  /** 查询病案邮寄的订单详情 */
  'POST /cloud/hosplatcustomer/caseExpress/queryCasePrintOrderDetails': (req: Request, res: Response) => {
    const { recordId } = req.body;
    const recordItem = casePrintOrderList.find((item: CasePrintOrderItemType) => item.recordId === recordId);
    setTimeout(() => {
      res.status(200).send({
        ...commonSuccessResponse,
        data: {
          ...recordItem,
          ...detailOtherInfo,
        },
      });
    }, 500);
  },
};
