import React, { useState, useEffect, useRef } from 'react';
import classnames from 'classnames';
import { Modal } from 'antd-mobile';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { history, connect, Dispatch, Loading } from 'umi';
import { StyleComponents, HxIndicator } from '@/components';
import { countdownData } from '@/utils/tool';
import { ORDER_STATUS, OFFLINE_STATUS } from '../dataDictionary';
import styles from './index.less';
import { CasePrintOrderState, CasePrintOrderItemType } from '../data';

const no_data_icon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/medical-record-order/no_data_icon.png';
const { EmptyView } = StyleComponents;
const wayCodeList = [
  {
    label: '线上邮寄',
    value: 2,
  },
  {
    label: '线下自取',
    value: 1,
  },
];
const statusList = [
  {
    label: '全部',
    value: 99,
    showRedIcon: false,
  },
  {
    label: '待审核',
    value: 1,
    showRedIcon: false,
  },
  {
    label: '待支付',
    value: 2,
    showRedIcon: false,
  },
  {
    label: '待发货',
    value: 3,
    showRedIcon: false,
  },
  {
    label: '已发货',
    value: 4,
    showRedIcon: false,
  },
];
const offlineStatusList = [
  {
    label: '全部',
    value: 99,
    showRedIcon: false,
  },
  {
    label: '待审核',
    value: 1,
    showRedIcon: false,
  },
  {
    label: '待支付',
    value: 2,
    showRedIcon: false,
  },
  {
    label: '待取件',
    value: 3,
    showRedIcon: false,
  },
  {
    label: '已取件',
    value: 10,
    showRedIcon: false,
  },
];

const fieldList = [
  {
    label: '就诊医院：',
    key: 'organName',
  },
  {
    label: '主治医生：',
    key: 'docName',
  },
  {
    label: <span>科&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;室：</span>,
    key: 'deptName',
  },
  {
    label: '住院时间：',
    key: 'inDateTime',
  },
  {
    label: '申请时间：',
    key: 'applyDateTime',
  },
];

interface MedicalRecordOrderListProps {
  casePrintOrder: CasePrintOrderState;
  dispatch: Dispatch;
  loading: boolean | undefined;
  location: {
    query: {
      /** 默认状态 */
      orderStatus: number;
      wayCode: number;
    };
  };
}

const MedicalRecordOrderList: React.FC<MedicalRecordOrderListProps> = (props) => {
  // props
  const {
    dispatch,
    casePrintOrder: { casePrintOrderList, currentOrderStatus, currentOfflineOrderStatus, currentWayCodeStatus },
    loading,
    location: {
      query: { orderStatus, wayCode },
    },
  } = props;

  // state
  const [orderList, setOrderList] = useState<CasePrintOrderItemType[]>([]);

  // ref
  const timer = useRef<any>(null);

  const fetchData = (applyStatus: number, wayCode?: number) => {
    console.log(applyStatus);
    dispatch({
      type: 'casePrintOrder/queryCasePrintOrderList',
      payload: {
        applyStatus,
        wayCode: wayCode || currentWayCodeStatus,
      },
    });
  };

  useEffect(() => {
    const paramWayCode = Number(wayCode);
    fetchData(
      Number(orderStatus) || (paramWayCode === 1 ? currentOfflineOrderStatus : currentOrderStatus),
      paramWayCode,
    );

    // 页面卸载清除定时器
    return () => {
      clearTimeout(timer.current);
    };
  }, []);

  /** 切换病案邮寄方式 */
  const updateCurrentWayCodeStatus = (wayCode: number) => {
    dispatch({
      type: 'casePrintOrder/updateState',
      payload: {
        currentWayCodeStatus: wayCode,
      },
    });
  };

  const updateCurrentOrderStatus = (newOrderState: number) => {
    dispatch({
      type: 'casePrintOrder/updateState',
      payload: {
        currentOrderStatus: newOrderState,
      },
    });
  };

  const updateCurrentOfflineOrderStatus = (newOrderState: number) => {
    dispatch({
      type: 'casePrintOrder/updateState',
      payload: {
        currentOfflineOrderStatus: newOrderState,
      },
    });
  };

  /** 默认取件类型 */
  useEffect(() => {
    if (wayCode) {
      updateCurrentWayCodeStatus(Number(wayCode));
    }
  }, [wayCode]);

  /** 默认订单状态 */
  useEffect(() => {
    if (orderStatus) {
      let initOrderStatus = Number(orderStatus);
      if (statusList.findIndex((item) => item.value === initOrderStatus) === -1) {
        initOrderStatus = 99;
      }
      console.log(initOrderStatus);
      if (wayCode && Number(wayCode) === 1) {
        updateCurrentOfflineOrderStatus(initOrderStatus);
        return;
      }
      updateCurrentOrderStatus(initOrderStatus);
    }
  }, [orderStatus]);

  /**
   * 更新定时器
   * @param endTimeStampSecond 结束时间戳
   */
  const getCountdown = (endTimeStampSecond: number) => {
    const { day, hour, minute, second } = countdownData(endTimeStampSecond);
    let text = '';
    if (day > 0) {
      text = `${day}天`;
    } else if (hour > 0) {
      text = `${hour}小时`;
    } else if (minute > 0) {
      text = `${minute}分钟`;
    } else if (second > 0) {
      text = `${second}秒`;
    }
    return text;
  };

  /** 更新倒计时文案 */
  const updateOrderList = () => {
    const newOrderList = casePrintOrderList.map((item) => ({
      ...item,
      countdownText: getCountdown(item.endTimeStampSecond || 0),
    }));
    setOrderList(newOrderList);
    // 如果有订单的待支付时间到了，则重新请求接口
    if (casePrintOrderList.some((item) => item.applyStatus === 2 && item.endTimeStampSecond <= new Date().getTime())) {
      fetchData(currentWayCodeStatus === 2 ? currentOrderStatus : currentOfflineOrderStatus);
    }
    timer.current = setTimeout(updateOrderList, 1000);
  };

  useEffect(() => {
    if (!casePrintOrderList.length) return;

    clearTimeout(timer.current);
    if (casePrintOrderList.every((item) => item.applyStatus !== 2)) {
      // 如果不存在待支付订单，直接赋值
      setOrderList(casePrintOrderList);
    } else {
      // 存在待支付的订单，添加倒计时文案字段
      updateOrderList();
    }
  }, [casePrintOrderList]);

  const onClickRecord = (item: CasePrintOrderItemType) => {
    const { applyStatus, unPassReason = '', recordId, wayCode } = item;
    switch (applyStatus) {
      case 1: // 待审核
        Modal.alert(
          <div>
            <ExclamationCircleFilled className={styles.warningIcon} />
            温馨提示
          </div>,
          <div style={{ textAlign: 'left', color: 'rgba(0,0,0,0.85)' }}>您的病案复印申请还在审核中，请耐心等待。</div>,
          [{ text: '我知道了', onPress: () => {} }],
        );
        break;

      case 5: // 审核未通过
        Modal.alert(
          <div>
            <ExclamationCircleFilled className={styles.warningIcon} />
            温馨提示
          </div>,
          <div style={{ textAlign: 'left', color: 'rgba(0,0,0,0.85)' }}>
            {`您的申请因【${unPassReason}】未通过审核`}
          </div>,
          [
            { text: '关闭', onPress: () => {} },
            {
              text: '去修改资料',
              onPress: () => {
                // history.push('/common/uploadidcard?to=BLDD_UPDATE');
                history.push({
                  pathname: '/common/uploadidcard',
                  query: {
                    to: 'BLDD_UPDATE',
                    recordId,
                    wayCode,
                  },
                });
              },
            },
          ],
        );
        break;

      default:
        // 进入订单详情页面
        history.push({
          pathname: '/caseprintorder/detail',
          query: {
            recordId,
          },
        });
        break;
    }
  };

  return (
    <div className={styles.container}>
      {loading && <HxIndicator />}
      <div className={classnames(styles.tabs, styles.wayCodeTabs)}>
        {wayCodeList.map((item) => (
          <div
            key={item.value}
            className={styles.tab}
            onClick={() => {
              updateCurrentWayCodeStatus(item.value);
              fetchData(item.value === 2 ? currentOrderStatus : currentOfflineOrderStatus, item.value);
            }}
          >
            <div
              style={{ color: currentWayCodeStatus === item.value ? '#32B9AA' : '#333333' }}
              className={styles.tabTitle}
            >
              {item.label}
            </div>
            {item.value === currentWayCodeStatus && <div className={styles.selectedIcon} />}
          </div>
        ))}
      </div>
      {currentWayCodeStatus === 2 ? (
        <div className={styles.tabs}>
          {statusList.map((item) => (
            <div
              key={item.value}
              className={styles.tab}
              onClick={() => {
                updateCurrentOrderStatus(item.value);
                fetchData(item.value);
              }}
            >
              {item.showRedIcon && <div className={styles.redIcon} />}
              <div
                style={{ color: currentOrderStatus === item.value ? '#32B9AA' : '#333333' }}
                className={styles.tabTitle}
              >
                {item.label}
              </div>
              {item.value === currentOrderStatus && <div className={styles.selectedIcon} />}
            </div>
          ))}
        </div>
      ) : (
        <div className={styles.tabs}>
          {offlineStatusList.map((item) => (
            <div
              key={item.value}
              className={styles.tab}
              onClick={() => {
                updateCurrentOfflineOrderStatus(item.value);
                fetchData(item.value);
              }}
            >
              {item.showRedIcon && <div className={styles.redIcon} />}
              <div
                style={{ color: currentOfflineOrderStatus === item.value ? '#32B9AA' : '#333333' }}
                className={styles.tabTitle}
              >
                {item.label}
              </div>
              {item.value === currentOfflineOrderStatus && <div className={styles.selectedIcon} />}
            </div>
          ))}
        </div>
      )}
      {!!casePrintOrderList.length && (
        <div className={styles.records}>
          {orderList.map((item: any) => {
            const statusItem =
              currentWayCodeStatus === 2
                ? ORDER_STATUS.find((statusItem) => statusItem.value === item.applyStatus)
                : OFFLINE_STATUS.find((statusItem) => statusItem.value === item.applyStatus);
            return (
              <div
                key={item.recordId}
                className={styles.record}
                onClick={() => {
                  onClickRecord(item);
                }}
              >
                <div className={styles.recordTitle}>
                  <div className={styles.left}>
                    <img
                      src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/case-mailing/medical_record_icon.png"
                      alt=""
                    />
                    <div className={styles.number}>病案号：{item.caseNo}</div>
                  </div>
                  <div className={styles.recordStatus}>
                    <span style={{ backgroundColor: statusItem?.color }} className={styles.circle} />
                    <span style={{ color: statusItem?.color }} className={styles.text}>
                      {statusItem?.label}
                    </span>
                  </div>
                </div>
                <div className={styles.recordContent}>
                  {fieldList.map((fieldItem) => (
                    <div key={fieldItem.key} className={styles.fieldItem}>
                      <span className={styles.label}>{fieldItem.label}</span>
                      <span className={styles.value}>{item[fieldItem.key]}</span>
                    </div>
                  ))}
                </div>
                {item.applyStatus === 2 && (
                  <div className={styles.countdown}>
                    <span>剩余支付时间：{item.countdownText}</span>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
      {casePrintOrderList.length === 0 && !loading && (
        <EmptyView isNew newImgUrl={no_data_icon} text="暂无病历订单记录" />
      )}
    </div>
  );
};

export default connect(({ casePrintOrder, loading }: { casePrintOrder: CasePrintOrderState; loading: Loading }) => ({
  casePrintOrder,
  loading: loading.effects['casePrintOrder/queryCasePrintOrderList'],
}))(MedicalRecordOrderList);
