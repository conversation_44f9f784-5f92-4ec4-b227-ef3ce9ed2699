/** 病案复印订单数据类型 */
export interface CasePrintOrderItemType {
  /** 申请日期 */
  applyDateTime: string;

  /** 病案邮寄线上邮寄订单状态:全部=99，1=待审批，2=待支付，3=待发货(待复印)，4=已发货(已复印)，5=审批未通过，6=已取消 */
  /** 病案邮寄线下自取订单状态:全部=99，1=待审批，2=待支付，3=待复印，4=待取件(已复印)，5=审批未通过，6=已取消 */
  applyStatus: number;

  /** 病案号 */
  caseNo: string;

  /** 科室名称 */
  deptName: string;

  /** 入院日期 */
  inDateTime: string;

  /** 机构名称 */
  organName: string;

  /** 患者姓名 */
  patName: string;

  /** 医生姓名 */
  docName: string;

  /** 这是申请记录ID,不是住院记录ID */
  recordId: string;

  /** 结束支付时间戳 */
  endTimeStampSecond: number;

  /** 审核不通过原因 */
  unPassReason: string;

  /** 倒计时文案（前端定义） */
  countdownText: string;

  /** 病案邮寄类型 */
  wayCode: number;
}

/** 复印相关信息数据类型 */
interface CopyInfoType {
  /** 复印份数 */
  copier: number;

  /** 复印目的编码 */
  purposeCode: string;

  /** 复印目的名称 */
  purposeName: string;

  /** 总页数 */
  totalPages: number;

  /** 总价 */
  totalPrice: number;

  /** 单价 */
  unitPrice: number;
}

/** 订单数据类型 */
interface OrderType {
  /** 订单总价 */
  allPrice: number;

  /** 业务系统订单号 */
  bizDealSeq: string;

  /** 业务系统编号 */
  bizSysSeq: string;

  /** 取消时间 */
  cancelTime: string;

  /** 支付系统订单号 */
  dealSeq: string;

  /** 商户号 */
  merchantCode: string;

  /** 支付时间 */
  paidTime: string;

  /** 支付流水号 */
  paidTransNo: string;

  /** 支付渠道 */
  payChannel: string;

  /** 支付渠道名称 */
  payChannelName: string;

  /** 退款时间 */
  refundTime: string;

  /** 退款流水号 */
  refundTransNo: string;

  /** 订单状态：1=待支付，2=已支付，3=已取消，4=退款中，5=已退款 */
  status: number;

  /** 下单时间 */
  orderDateTime: string;

  /** 商户号 */
  merchantSeq: string;
}

/** 病案复印订单详情数据类型 */
interface CasePrintOrderDetailType extends CasePrintOrderItemType {
  /** 总价 */
  allPrice: number;

  /** 就诊卡号 */
  cardNo: string;

  /** 配送方式 */
  expressDesc: string;

  /** 支付二次弹窗（html格式的字符串） */
  payPopupDesc: string;

  /** 收货地址 */
  recAddress: string;

  /** 收货人联系方式 */
  recPhone: string;

  /** 收货人 */
  receiver: string;

  /** 复印相关信息 */
  items: CopyInfoType[];

  /** 订单信息 */
  order: OrderType;

  /** 运单编号 */
  trackingNum: string;

  /** 线下自取描述 */
  offlineTakeDesc: string;

  /** 线下自取时间 */
  offlineTakeDate: string;

  /** 是否可以点击签到按钮 */
  signEnable: boolean;

  /** 签到状态1=未签到，2=已签到 */
  signIn: number;
}
export interface CasePrintOrderState {
  /** 病案复印订单列表 */
  casePrintOrderList: CasePrintOrderItemType[];

  /** 当前选中的订单状态 */
  currentOrderStatus: number;

  /** 当前选中的线下自取订单状态 */
  currentOfflineOrderStatus: number;

  /** 当前选中的病案邮寄类型 */
  currentWayCodeStatus: number;

  /** 病案复印订单详情 */
  casePrintOrderDetail: CasePrintOrderDetailType;
}
