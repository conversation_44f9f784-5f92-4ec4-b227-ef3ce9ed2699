import { Effect, Reducer } from 'umi';
import { CasePrintOrderState } from './data';
import { queryCasePrintOrderList, queryCasePrintOrderDetails, confirmTakeDate, confirmSignIn } from './service';

interface ModelType {
  namespace: string;

  state: CasePrintOrderState;

  effects: {
    /** 查询病案邮寄的订单列表 */
    queryCasePrintOrderList: Effect;
    /** 查询病案邮寄的订单详情 */
    queryCasePrintOrderDetails: Effect;
    /** 确认病案邮寄线下取件时间 */
    confirmTakeDate: Effect;
    /** 确认签到 */
    confirmSignIn: Effect;
  };

  reducers: {
    updateState: Reducer;
  };
}

const Model: ModelType = {
  // 病历（复印）订单模块
  namespace: 'casePrintOrder',

  state: {
    casePrintOrderList: [],
    currentOrderStatus: 1, // 默认待审核
    currentOfflineOrderStatus: 1, // 线下自取默认待审核
    currentWayCodeStatus: 2, // 默认选择线上邮寄
    casePrintOrderDetail: {},
  },

  effects: {
    *queryCasePrintOrderList({ payload }, { call, put }) {
      const data = yield call(queryCasePrintOrderList, payload);
      const { orderList = [] } = data || {};
      yield put({
        type: 'updateState',
        payload: {
          casePrintOrderList: orderList,
        },
      });
    },
    *queryCasePrintOrderDetails({ payload }, { call, put }) {
      const data = yield call(queryCasePrintOrderDetails, payload);
      yield put({
        type: 'updateState',
        payload: {
          casePrintOrderDetail: data || {},
        },
      });
    },
    *confirmTakeDate({ payload, callback }, { call }) {
      const data = yield call(confirmTakeDate, payload);
      callback && callback(data);
    },
    *confirmSignIn({ payload, callback }, { call }) {
      const data = yield call(confirmSignIn, payload);
      callback && callback(data);
    },
  },

  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};

export default Model;
