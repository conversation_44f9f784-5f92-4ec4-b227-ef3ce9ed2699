import React, { useEffect, useState, useRef, ReactElement } from 'react';
import {
  ClockCircleOutlined,
  ClockCircleFilled,
  CheckCircleFilled,
  CloseCircleFilled,
  EnvironmentOutlined,
  ExclamationCircleFilled,
  RightOutlined,
} from '@ant-design/icons';
import { Toast, Button, Modal, Calendar } from 'antd-mobile';
import { connect, Dispatch, Loading } from 'umi';
import { countdownData, copyText } from '@/utils/tool';
import { doPay } from '@/utils/common';
import { HxIndicator } from '@/components';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import styles from './index.less';
import { CasePrintOrderState } from '../data.d';
import { OFFLINE_STATUS, ORDER_STATUS, OFFLINE_DESC_STATUS, HOLIDAY_SHIELDING } from '../dataDictionary';
import { festivalList } from '../service';

const medical_record_icon =
  'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/case-mailing/medical_record_icon.png';

dayjs.extend(isSameOrBefore);
const dayZero = dayjs(dayjs().format('YYYY-MM-DD'));
const MinDate = dayZero.add(1, 'day');
const MaxDate = dayZero.add(1, 'day').add(2, 'month');
const extra: any = {};

let i = 1;

while (dayjs(dayZero.add(i, 'day')).isSameOrBefore(MaxDate)) {
  if (dayZero.add(i, 'day').day() === 6 || dayZero.add(i, 'day').day() === 0) {
    extra[+dayZero.add(i, 'day').valueOf()] = { disable: true };
  }
  i += 1;
}

/* 排除节假日 */
// const holidayShielding = () => {
//   HOLIDAY_SHIELDING.forEach((i) => {
//     extra[+new Date(i)] = { disable: true };
//   });
// };
// holidayShielding();

Object.keys(extra).forEach((key) => {
  const info = extra[key];
  const date = new Date(key);
  if (!Number.isNaN(+date) && !extra[+date]) {
    extra[+date] = info;
  }
});

interface MedicalRecordOrderDetailProps {
  casePrintOrder: CasePrintOrderState;
  dispatch: Dispatch;
  location: {
    query: {
      /** 记申请录ID */
      recordId: string;
    };
  };
  loading: boolean | undefined;
}

const MedicalRecordOrderDetail: React.FC<MedicalRecordOrderDetailProps> = (props) => {
  // props
  const {
    dispatch,
    location: {
      query: { recordId },
    },
    casePrintOrder: { casePrintOrderDetail },
    loading,
  } = props;
  const {
    applyStatus,
    endTimeStampSecond,
    receiver,
    recPhone = '',
    recAddress,
    order: {
      paidTime,
      dealSeq = '',
      payChannelName,
      paidTransNo,
      orderDateTime,
      bizSysSeq = '',
      merchantSeq = '',
      allPrice,
    } = {},
    trackingNum,
    caseNo,
    patName,
    cardNo,
    items = [],
    expressDesc,
    payPopupDesc,
    wayCode,
    offlineTakeDesc,
    offlineTakeDate,
    signEnable,
    signIn,
  } = casePrintOrderDetail;
  const orderStatusItem =
    wayCode === 2
      ? ORDER_STATUS.find((item) => item.value === applyStatus)
      : OFFLINE_STATUS.find((item) => item.value === applyStatus);
  const offlineDescStatusItem = OFFLINE_DESC_STATUS.find((item) => {
    if (applyStatus === 3 || applyStatus === 4) {
      return item.value === applyStatus && item.signInStatus === signIn;
    }
    return item.value === applyStatus;
  });

  // state
  const [countdownText, setCountdownText] = useState('');
  const [calendarVisible, setCalendarVisible] = useState(false);
  /** 配合本地修改ui，不重新请求接口 */
  const [pickupTime, setPickupTime] = useState('');
  const [canSignEnable, setCanSignEnable] = useState(false);
  const [signInStatus, setSignInStatus] = useState(1);
  const [holidayList, setHolidayList] = useState([]);

  // ref
  const timer = useRef<any>(null);

  const fetchData = () => {
    dispatch({
      type: 'casePrintOrder/queryCasePrintOrderDetails',
      payload: { recordId },
    });
  };

  /**
   * 节假日排除
   * @return {*}
   */

  const fetchHoliday = async () => {
    try {
      const res = await festivalList({ festivalYears: [new Date().getFullYear(), new Date().getFullYear() + 1] });
      if (res && res.length) {
        setHolidayList(res || []);
        res.forEach((i) => {
          extra[+new Date(i.festivalDate)] = { disable: true };
        });
      }
    } catch (error) {
      console.log('error:', error);
    }
  };

  useEffect(() => {
    fetchData();
    fetchHoliday();
    // 页面卸载清除定时器
    return () => {
      clearTimeout(timer.current);
    };
  }, []);

  useEffect(() => {
    offlineTakeDate && setPickupTime(offlineTakeDate);
    signIn && setSignInStatus(signIn);
    signEnable && setCanSignEnable(signEnable);
  }, [casePrintOrderDetail]);

  /** 更新倒计时文案 */
  const updateCountdownText = () => {
    const { day, hour, minute } = countdownData(endTimeStampSecond || 0);
    setCountdownText(`${day}天${hour}小时${minute}分钟`);
    if (day === 0 && hour === 0 && minute === 0) {
      // 待支付时间到，重新请求接口更新订单状态
      fetchData();
    }
    timer.current = setTimeout(updateCountdownText, 1000);
  };

  useEffect(() => {
    // 清除上一个定时器
    clearTimeout(timer.current);

    if (endTimeStampSecond && applyStatus === 2) {
      updateCountdownText();
    }
  }, [endTimeStampSecond, applyStatus]);

  const onPay = () => {
    Modal.alert(
      <div>
        <ExclamationCircleFilled className={styles.warningIcon} />
        温馨提示
      </div>,
      <div dangerouslySetInnerHTML={{ __html: payPopupDesc }} />,
      [
        { text: '取消', onPress: () => console.log('cancel'), style: { color: '#999999' } },
        {
          text: '继续支付',
          onPress: () => {
            doPay(dealSeq, bizSysSeq, merchantSeq);
          },
        },
      ],
    );
  };

  const shouldConfirmOfflineTakeDate = () => {
    if (wayCode === 1) {
      if (applyStatus === 2 && !pickupTime) {
        Toast.info('请选择取件时间');
        return;
      }
      if (applyStatus === 2 && pickupTime === offlineTakeDate) {
        onPay();
        return;
      }
      if (applyStatus === 2 && pickupTime) {
        dispatch({
          type: 'casePrintOrder/confirmTakeDate',
          payload: {
            recordId,
            offlineTakeDate: pickupTime,
          },
          callback: (result: any) => {
            if (result?.code === '1') {
              onPay();
            }
          },
        });
      }
    } else if (wayCode === 2) {
      onPay();
    }
  };

  const getDateExtra = (date: any) => {
    // 添加假期逻辑
    const formatDay = dayjs(date).format('YYYY-MM-DD');
    if (holidayList.some((item) => item?.festivalDate === formatDay)) {
      return {
        disable: true,
      };
    }
    return extra[+date];
  };

  /** 确认选择取件时间 */
  const confirmSelectPickupTime = (date: any) => {
    setPickupTime(dayjs(date).format('YYYY-MM-DD'));
    setCalendarVisible(false);
  };

  /** 确认签到 */
  const confirmSignIn = () => {
    dispatch({
      type: 'casePrintOrder/confirmSignIn',
      payload: {
        recordId,
      },
      callback: (result: any) => {
        if (result?.code === '1') {
          setCanSignEnable(false);
          setSignInStatus(2);
        }
      },
    });
  };

  /** 点击签到按钮 */
  const handleCLickSignIn = () => {
    /** 待复印和待取件只有signEnable为true才允许签到，签到成功后signEnable => canSignEnable本地修改为false */
    if (!signEnable || !canSignEnable) {
      return;
    }
    Modal.alert(
      <div>
        <ExclamationCircleFilled className={styles.warningIcon} />
        温馨提示
      </div>,
      '请确认您是否已到达医院取件处，如已到达请点击确认进行取件签到。',
      [
        { text: '取消' },
        {
          text: '确认',
          onPress: () => {
            confirmSignIn();
          },
        },
      ],
    );
  };

  /** applyStatus状态对应icon */
  const applyStatusIcon = () => {
    let IconDom: ReactElement = <ClockCircleOutlined className={styles.clockIcon} />;
    if (wayCode === 1) {
      switch (applyStatus) {
        case 2:
        case 3:
        case 4:
          IconDom = <ClockCircleFilled className={styles.clockIcon} />;
          break;
        case 6:
          IconDom = <CloseCircleFilled className={styles.clockIcon} />;
          break;
        case 10:
          IconDom = <CheckCircleFilled className={styles.clockIcon} />;
          break;
        default:
          break;
      }
    }
    return IconDom;
  };

  return (
    <div className={styles.container}>
      {loading && <HxIndicator />}
      {/* 订单状态 */}
      <div className={styles.toBePaid}>
        <div className={styles.title}>
          {applyStatusIcon()}
          <span className={styles.text}>{orderStatusItem?.label}</span>
        </div>
        {/* 待支付显倒计时 */}
        {applyStatus === 2 && (
          <div>
            <span>剩余：</span>
            <span>{countdownText}</span>
          </div>
        )}
        {/* 线上邮寄待发货和已发货显示订单复制 */}
        {(applyStatus === 3 || applyStatus === 4) && wayCode === 2 && (
          <div>
            <span>运单编号：{trackingNum} </span>
            <span
              className={styles.copy}
              onClick={() => {
                copyText(trackingNum);
              }}
            >
              复制
            </span>
          </div>
        )}
        {/* 线下自取显示 */}
        {wayCode === 1 && (
          <div className={styles.signInBox}>
            <span>{offlineDescStatusItem?.text}</span>
            {(applyStatus === 3 || applyStatus === 4) && (
              <div
                onClick={handleCLickSignIn}
                className={styles.signInButton}
                style={{ color: canSignEnable ? '#3AD3C1' : 'rgba(58, 211, 193, 0.3)' }}
              >
                {signInStatus === 2 ? '已签到' : '签到'}
              </div>
            )}
          </div>
        )}
      </div>
      {/* 地址 */}
      {wayCode === 2 && (
        <div className={styles.address}>
          <div className={styles.userInfo}>
            <EnvironmentOutlined className={styles.addressIcon} />
            <div>{receiver}</div>
            <div className={styles.phone}>{recPhone.split('').fill('*', 3, 7)}</div>
          </div>
          <div className={styles.addressDetail}>{recAddress}</div>
        </div>
      )}
      {/* 取件时间 */}
      {wayCode === 1 &&
        (applyStatus === 2 || applyStatus === 3 || applyStatus === 4 || applyStatus === 6 || applyStatus === 10) && (
          <div className={styles.info}>
            <div className={styles.pickupTimeBox}>
              <div className={styles.pickupTimeTitle}>
                <ClockCircleOutlined className={styles.clockIcon} />
                <span className={styles.label}>取件时间{applyStatus !== 2 && <span>：&nbsp;{pickupTime}</span>}</span>
              </div>
              {applyStatus === 2 && (
                <div className={styles.pickupTimeTitle} onClick={() => setCalendarVisible(true)}>
                  {pickupTime ? (
                    <span className={styles.label}>{pickupTime}</span>
                  ) : (
                    <span className={styles.labelDec}>请选择</span>
                  )}
                  <RightOutlined className={styles.clockIcon} />
                </div>
              )}
            </div>
            {applyStatus !== 10 && applyStatus !== 6 && pickupTime && (
              <div className={styles.offlineDesc}>{offlineTakeDesc}</div>
            )}
          </div>
        )}
      {/* 病案号 */}
      <div className={styles.recordInfo}>
        <img src={medical_record_icon} alt="" />
        <div className={styles.number}>病案号：{caseNo}</div>
      </div>
      {/* 就诊卡信息 */}
      <div className={styles.info}>
        <div className={styles.infoItem}>
          <span className={styles.labe}>就诊卡姓名</span>
          <span className={styles.value}>{patName}</span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.labe}>就诊卡号</span>
          <span className={styles.value}>{cardNo}</span>
        </div>
      </div>
      {/* 复印信息 */}
      {items.map((item) => (
        <div key={item.purposeCode}>
          <div className={styles.info}>
            <div className={styles.infoItem}>
              <span className={styles.labe}>复印目的</span>
              <span className={styles.value}>{item.purposeName}</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.labe}>复印份数</span>
              <span>{item.copier} 份</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.labe}>页码总数</span>
              <span>{item.totalPages} 页</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.labe}>单页金额</span>
              <span>¥ {item.unitPrice}</span>
            </div>
            <div className={styles.total}>
              <span>小计：</span>
              <span className={styles.totalPrice}>￥{item.totalPrice}</span>
            </div>
          </div>
        </div>
      ))}
      {/* 配送方式 */}
      <div className={styles.info}>
        <div className={styles.infoItem}>
          {/* <span className={styles.labe}>配送方式</span> */}
          <span>{expressDesc}</span>
        </div>
      </div>
      {/* 病例复印费总额 */}
      <div className={styles.info}>
        <div className={styles.infoItem}>
          <span className={styles.labe}>病例复印费总额</span>
          <div>
            <span className={styles.priceUnit}>￥</span>
            <span className={styles.price}>{allPrice}</span>
          </div>
        </div>
      </div>
      {/* 订单信息 */}
      <div className={styles.info}>
        <div className={styles.infoItem}>
          <span className={styles.labe}>订单编号：{dealSeq}</span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.labe}>下单时间：{orderDateTime}</span>
        </div>
        {/* 待发货和待收货显示以下订单信息 */}
        {(applyStatus === 3 || applyStatus === 4) && (
          <div>
            <div className={styles.infoItem}>
              <span className={styles.labe}>支付时间：{paidTime}</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.labe}>支付渠道：{payChannelName}</span>
            </div>
            <div className={styles.infoItem}>
              <span className={styles.labe}>支付流水：{paidTransNo}</span>
            </div>
          </div>
        )}
      </div>
      {/* 结算按钮 */}
      {applyStatus === 2 && (
        <div className={styles.bottom}>
          <div>
            <span>合计：</span>
            <span className={styles.priceUnit}>￥</span>
            <span className={styles.price}>{allPrice}</span>
          </div>
          <Button
            className={styles.button}
            type="primary"
            size="small"
            style={{ color: '#ffffff' }}
            onClick={shouldConfirmOfflineTakeDate}
          >
            去支付
          </Button>
        </div>
      )}
      <Calendar
        type="one"
        title="Select Date"
        minDate={MinDate.toDate()}
        maxDate={MaxDate.toDate()}
        getDateExtra={getDateExtra}
        visible={calendarVisible}
        onCancel={() => setCalendarVisible(false)}
        onConfirm={confirmSelectPickupTime}
      />
    </div>
  );
};

export default connect(({ casePrintOrder, loading }: { casePrintOrder: CasePrintOrderState; loading: Loading }) => ({
  casePrintOrder,
  loading: loading.effects['casePrintOrder/queryCasePrintOrderDetails'],
}))(MedicalRecordOrderDetail);
