.container {
  color: #333;
  background-color: #f2f2f2;
  .toBePaid {
    padding: 24px;
    color: #fff;
    background-color: #32b9aa;

    .copy {
      padding: 6px 12px;
      border: 1px solid #f2f2f2;
      border-radius: 26px;
    }

    .title {
      padding-bottom: 16px;
      font-size: 36px;

      .clockIcon {
        margin-right: 20px;
      }

      .text {
        font-size: 500;
      }
    }
  }

  .address {
    margin-bottom: 24px;
    padding: 24px;
    background-color: #fff;

    .userInfo {
      display: flex;
      align-items: center;
      color: #333;
      font-weight: 500;
      font-size: 36px;

      .addressIcon {
        margin-right: 20px;
      }
    }

    .phone {
      margin-left: 80px;
    }

    .addressDetail {
      color: #999;
    }
  }

  .recordInfo {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    padding: 24px;
    background-color: #fff;

    img {
      width: 80px;
      height: 80px;
      margin-right: 20px;
    }

    .number {
      color: #03081a;
      font-weight: 500;
    }

    .pageNumTotal {
      color: #333;
      font-size: 24px;

      .pageNum {
        color: #666;
      }
    }
  }

  .info {
    margin-bottom: 24px;
    padding: 8px 24px;
    background-color: #fff;

    .infoItem {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 16px 0;

      .value {
        max-width: 364px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .total {
      padding-bottom: 24px;
      text-align: right;
      .totalPrice {
        color: #333;
        font-weight: 500;
      }
    }
  }
  .bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 24px 60px 24px;
    background-color: #fff;

    .button {
      width: 272px;
      height: 90px;
      border-radius: 90px;
    }
  }
  .priceUnit {
    color: #fc4553;
    font-weight: 600;
  }

  .price {
    color: #fc4553;
    font-weight: 600;
    font-size: 36px;
  }
}

.warningIcon {
  margin-right: 8px;
  color: #f0944f;
  font-size: 36px;
}

.pickupTimeBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  background-color: #fff;

  .pickupTimeTitle {
    display: flex;
    align-items: center;
    justify-content: center;

    .clockIcon {
      color: #03081a;
      font-weight: 600;
      font-size: 36px;
    }

    & span.label {
      color: #03081a;
      font-weight: 500;
      font-size: 32px;
    }

    & span.labelDec {
      color: #b0b3bf;
      font-weight: 500;
      font-size: 32px;
    }
  }
}

.offlineDesc {
  padding-bottom: 10px;
  color: #fc4553;
}

.signInBox {
  position: relative;

  .signInButton {
    position: absolute;
    right: 20px;
    bottom: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 156px;
    height: 68px;
    margin-top: -34px;
    color: #3ad3c1;
    font-size: 32px;
    background: #fff;
    border-radius: 68px;
  }
}

:global{
  .date {
    div {
      padding-left: 0!important;
    }
  }
}
