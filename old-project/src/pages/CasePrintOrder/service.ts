import request from '@/utils/request';

/** 查询病案邮寄的订单列表 */
export const queryCasePrintOrderList = async (data: {
  /** 病案邮寄订单状态:全部=99，1=待审批，2=待支付，3=待复印，4=已复印，5=审批未通过，6=已取消 */
  applyStatus: number;
}) =>
  request('/cloud/hosplatcustomer/caseExpress/queryCasePrintOrderList', {
    method: 'POST',
    data,
    prefix: API_HXYY_NEW,
  });

/** 查询病案邮寄的订单详情 */
export const queryCasePrintOrderDetails = async (data: {
  /** 订单列表中的申请记录ID */
  recordId: string;
}) =>
  request('/cloud/hosplatcustomer/caseExpress/queryCasePrintOrderDetails', {
    method: 'POST',
    data,
    prefix: API_HXYY_NEW,
  });

/** 确认病案邮寄线下取件时间 */
export const confirmTakeDate = async (data: {
  /** 订单列表中的申请记录ID */
  recordId: string;
  /** 取件日期 */
  offlineTakeDate: string;
}) =>
  request('/cloud/hosplatcustomer/caseExpress/confirmTakeDate', {
    method: 'POST',
    data: {
      ...data,
      showLoading: true,
      showOriginData: true,
    },
    prefix: API_HXYY_NEW,
  });

/** 确认签到 */
export const confirmSignIn = async (data: {
  /** 订单列表中的申请记录ID */
  recordId: string;
}) =>
  request('/cloud/hosplatcustomer/caseExpress/signIn', {
    method: 'POST',
    data: {
      ...data,
      showLoading: true,
      showOriginData: true,
    },
    prefix: API_HXYY_NEW,
  });

/* 获取节假日列表 */
export const festivalList = async (data: any) =>
  request('/cloud/medicalService/festival/list', {
    method: 'POST',
    data: {
      ...data,
    },
    prefix: API_HXYY_NEW,
  });
