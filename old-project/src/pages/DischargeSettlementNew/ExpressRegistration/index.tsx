import React, { useState, useEffect, FC } from 'react';
import { Button } from 'antd-mobile-v5';
import { Toast } from 'antd-mobile';
import { history } from 'umi';
import styles from './index.less';
import { StorageEnum } from '@/utils/enum';
import { getOrganCode } from '@/utils/parameter';
import { HxSessionStorage } from '@/utils/storage';
import { saveBankCardInfo } from '../service';
import '../index';

interface PageProps {
  location: {
    query: {
      admId: string;
    };
  };
}
const ExpressRegistration: FC<PageProps> = (props) => {
  const {
    location: {
      query: { admId },
    },
  } = props;
  const [formData, setFormData] = useState({
    bankcardholder: '',
    bankcardnumber: '',
  });
  const { pmi } = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA) || {};

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    const { bankcardholder, bankcardnumber } = formData;
    if (!bankcardholder || !bankcardnumber) {
      Toast.info('请填写完信息！', 2);
      return;
    }
    const payload = {
      hospitalCode: getOrganCode(),
      bankcardnumber,
      patientID: pmi,
      bankcardholder,
      admId,
    };
    const res: any = await saveBankCardInfo(payload);
    if (res?.code === '1') {
      Toast.success('快捷登记成功即将进入结算页面…', 1);
      setTimeout(() => {
        history.push(`/dischargeSettlementNew/home?admId=${admId}`);
      }, 2000);
    }

    console.log('登记响应---', res);
  };

  return (
    <div className={styles.mainContent}>
      <div className={styles.card}>
        <div className={styles.subtitle}>请填写以下信息进行快捷登记</div>
        <form>
          <div className={styles.formItem}>
            <label className={styles.label}>
              <span className={styles.required}>*</span>
              1、姓名
            </label>
            <input
              type="text"
              className={styles.inputField}
              value={formData.bankcardholder}
              placeholder="请输入姓名"
              onChange={(e) => setFormData({ ...formData, bankcardholder: e.target.value })}
            />
          </div>

          <div className={styles.formItem}>
            <label className={styles.label}>
              <span className={styles.required}>*</span>
              2、银行卡号
            </label>
            <input
              type="text"
              className={styles.inputField}
              value={formData.bankcardnumber}
              placeholder="请输入银行卡号"
              onChange={(e) => setFormData({ ...formData, bankcardnumber: e.target.value })}
            />
          </div>
          <div className={styles.action}>
            <Button block className={styles.action_btn} onClick={handleSubmit}>
              确认登记
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};
export default ExpressRegistration;
