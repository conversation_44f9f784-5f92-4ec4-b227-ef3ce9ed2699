// index.less
@primary-color: #1890ff;
@error-color: #ff4d4f;
@text-color: rgba(0, 0, 0, 0.85);
@border-color: #d9d9d9;

.mainContent {
  // max-width: 500px;
  padding: 24px;
  background: #f5f6fa;
  font-family: PingFang SC, PingFang SC-Semibold;

  .card {
    padding: 24px;
    background-color: #fff;
    border-radius: 16px;
  }
  .subtitle {
    margin-bottom: 32px;
    font-size: 32px;
    font-weight: bolder;
  }

  .formItem {
    margin-bottom: 32px;
    .label {
      display: block;
      margin-bottom: 24px;
      color: @text-color;
      font-size: 28px;
    }
  }

  .required {
    color: @error-color;
    margin-right: 4px;
  }

  .inputField {
    width: 100%;
    padding: 32px 24px;
    border: none;
    font-size: 28px;
    transition: border-color 0.3s;
    background: #f5f6fa;
    border-radius: 16px;
    // &:focus {
    //   outline: none;
    //   border-color: @primary-color;
    //   box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    // }
  }

  .action {
    position: fixed;
    bottom: 0;
    width: 100%;
    background: #fff;
    padding: 36px 32px;
    left: 0;
    right: 0;
    .action_btn {
      font-size: 36px;
      height: 96px;
      line-height: 96px;
      background: #3ad3c1;
      border-radius: 52px;
      color: #fff;
      padding: 0px !important;
      border: none;
    }
  }
}
