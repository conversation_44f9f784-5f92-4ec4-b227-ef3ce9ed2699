@import '~@/styles/mixin.less';

.listContent {
  font-family: PingFang SC, PingFang SC-Semibold;
  background: #f5f6fa;
  min-height: 100vh;
  padding: 24px;
  .listView {
    height: 100%;
    width: 100%;
    box-sizing: border-box;
  }
  .item {
    box-sizing: border-box;
    // margin: 24px 24px 0;
    margin-bottom: 24px;
    background: #fff;
    border-radius: 24px;
    padding: 28px 24px 32px;

    .recordTitle {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;

      .name {
        width: 500px;
        display: flex;
        align-items: center;

        img {
          width: 48px;
          height: 48px;
          margin-right: 20px;
        }

        .checkName {
          width: calc(100% - 68px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 32px;
          font-weight: 600;
          color: #03081a;
          line-height: 48px;
        }
      }
      .status {
        position: relative;
        font-size: 24px;
        &::before {
          position: absolute;
          top: 14px;
          left: -22px;
          content: '';
          width: 12px;
          height: 12px;
          border-radius: 50%;
        }
      }
      .pending {
        color: #fc4553;
        &::before {
          background-color: #fc4553;
        }
      }
      .paid {
        color: #bbbeca;
        &::before {
          background-color: #bbbeca;
        }
      }

      .orderStatusText {
        display: flex;
        align-items: center;
        flex-shrink: 0;

        .circle {
          display: inline-block;
          width: 10px;
          height: 10px;
          margin-right: 10px;
          border-radius: 50%;
        }

        .text {
          font-size: 24px;
        }
      }
    }

    .userInfoCell {
      width: 100%;
      margin-top: 34px;
      padding: 24px;
      background: #f5f6fa;
      border-radius: 16px;
      .cell {
        &:not(:first-child) {
          margin-top: 24px;
        }
        .tips {
          font-size: 28px;
          color: #ff984b;
          span {
            padding: 0px 8px;
            height: 32px;
            line-height: 32px;
            background: linear-gradient(270deg, #ffc26e, #fe8f3c 100%);
            border-radius: 8px;
            text-align: center;
            color: #fff;
            margin-right: 14px;
          }
        }
        .blueBg {
          color: #568df2;
          span {
            background: linear-gradient(270deg, #b4cfff, #568df2 100%);
          }
        }
        .printed {
          color: #3ad3c1;
          span {
            background: linear-gradient(270deg, #baf2ec 100%, #3ad3c1 100%);
          }
        }
      }

      .cellTitle {
        font-size: 28px;
        // color: #989eb4;
        line-height: 40px;
      }

      .cellContent {
        font-size: 28px;
        color: #03081a;
        line-height: 40px;
      }
    }

    .buttonWrap {
      display: flex;
      justify-content: flex-end;
      margin-top: 32px;

      :global {
        .am-button,
        .cancelButton {
          height: 64px !important;
          width: 156px;
          // border: 2px solid #3ad3c1;
          background: #3ad3c1;
          border-radius: 32px;
          color: #fff;
          box-sizing: border-box;
          line-height: 1 !important;
          display: flex;
          justify-content: center;
          align-items: center;
          position: static;
          font-size: 28px;
        }
      }
      .cancelButton {
        background: #fff;
        color: #03081a;
        border: 2px solid #d5daec;
        margin-right: 24px;
        font-size: 28px;
      }
    }
  }
  .empty {
    margin-top: 38%;
  }
  .tabBar {
    width: 100%;
    height: 92px;
    background: #ffffff;
    flex-shrink: 0;
    :global {
      .adm-tabs-header {
        border: none;
        height: 100%;
        .adm-tabs-tab-list {
          height: 100%;
        }
      }
    }
  }

  .listViewFooter {
    display: flex;
    align-items: center;
    justify-content: center;
    // loading样式
    // 无更多样式
    .noMore {
      padding: 32px 0;
      color: #9c9c9c;
      font-weight: 300;
      text-align: center;
    }
  }
  :global {
    .am-list-body::before,
    .am-list-body::after {
      display: none !important;
    }
  }
}
