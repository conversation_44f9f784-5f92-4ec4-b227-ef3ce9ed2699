import React, { FC, useEffect, useState } from 'react';
import { PullToRefresh, ListView, Button, Toast, ActivityIndicator } from 'antd-mobile';
import { connect, Dispatch, Loading, ConnectProps, useLocation, history } from 'umi';
import { HxEmpty, HxIndicator } from '@/components';
import { Modal } from 'antd-mobile-v5';
import styles from './index.less';
import moment from 'moment';
import { StorageEnum } from '@/utils/enum';
import { getOrganCode } from '@/utils/parameter';
import { HxSessionStorage } from '@/utils/storage';
import HxModal from '@/components/HxModal';
import femaleImg from '@/assets/HospitalizationSettlement/female.png';
import maleImg from '@/assets/HospitalizationSettlement/man.png';
import { getBeHospitalized } from '../service';

const { alert } = Modal;

interface IProps extends ConnectProps {
  dispatch: Dispatch;
}

const ListPage: FC<IProps> = ({ dispatch }) => {
  const { query }: any = useLocation();
  const [recordList, setRecordList] = useState<any>([]);
  const [loading, setLoading] = useState<boolean>(true);

  const { pmi, cardId, gender, patientName, age } = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA) || {};

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    const payload = {
      organPmi: pmi,
      // organPmi: '145709',
      hospitalCode: getOrganCode(),
    };
    const res: any = (await getBeHospitalized(payload)) || [];
    setRecordList(res?.orderList || []);
    setLoading(false);
  };
  const clickItem = (item: any) => {
    //已缴费
    if (item?.chgStat === '1') {
      history.push(`/dischargeSettlementNew/settlementDetail?admId=${item?.admId}`);
      return;
    }
  };
  const goPay = (event, item: any = {}) => {
    const { admId, chgStat, orderDetail } = item;
    console.log('fastBankNo===', item);
    // 阻止事件继续向上传播
    event.stopPropagation();
    // 未登记银行卡
    if (!orderDetail?.fastBankNo) {
      HxModal.show({
        title: '温馨提示',
        content: <div>您未登记银行卡号，暂无法办理出院结算，请您先进行登记！</div>,
        closeOnMaskClick: true,
        actions: [
          {
            text: '取消',
            key: 'cancel',
            onClick: () => {
              Modal.clear();
            },
          },
          {
            text: '去登记',
            key: 'confirm',
            className: 'primary',
            onClick: () => {
              Modal.clear();
              history.push(`/dischargeSettlementNew/expressRegistration?admId=${admId}`);
            },
          },
        ],
      });
      return;
    }

    //已缴费
    if (chgStat === '1') {
      history.push(`/dischargeSettlementNew/settlementDetail?admId=${admId}`);
      return;
    } else if (chgStat === '0') {
      //未缴费
      history.push(`/dischargeSettlementNew/home?admId=${admId}`);
    }
  };
  return (
    <div className={styles.listContent}>
      {loading ? (
        <HxIndicator />
      ) : recordList.length ? (
        <>
          {recordList.map((item, index) => {
            return (
              <div className={styles.item} key={index} onClick={() => clickItem(item)}>
                <div className={styles.recordTitle}>
                  <div className={styles.name}>
                    <img src={gender === 1 ? maleImg : femaleImg} alt="" />
                    <span className={styles.checkName}>
                      {patientName} &nbsp; {age}岁 &nbsp; {gender === 1 ? '男' : '女'}
                    </span>
                  </div>
                  <div className={`${styles.status} ${item.chgStat === '0' ? styles.pending : styles.paid}`}>
                    {item.chgStat === '0' ? '未结算' : '已结算'}
                  </div>
                </div>
                <div className={styles.userInfoCell}>
                  <div className={styles.cell}>
                    <span className={styles.cellTitle}>住院科室：</span>
                    <span className={styles.cellContent}>{item.admDep}</span>
                  </div>
                  <div className={styles.cell}>
                    <span className={styles.cellTitle}>入院时间：</span>
                    <span className={styles.cellContent}>{item.admDate}</span>
                  </div>
                </div>
                {item.chgStat === '0' && (
                  <div className={styles.buttonWrap}>
                    <Button size="small" type="primary" className={styles.button} onClick={(e) => goPay(e, item)}>
                      去结算
                    </Button>
                  </div>
                )}
              </div>
            );
          })}
        </>
      ) : (
        <div className={styles.empty}>
          <HxEmpty emptyMsg="暂无内容" emptyIcon="norecord" isNewImg canRefresh={false} />
        </div>
      )}
    </div>
  );
};

export default connect(({ filmPrinting }: { filmPrinting: any }) => ({
  filmPrinting,
}))(ListPage);
