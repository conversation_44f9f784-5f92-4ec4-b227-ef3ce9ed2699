import React, { useState, useEffect, FC } from 'react';
import { Checkbox, Toast, Button } from 'antd-mobile-v5';
import { history } from 'umi';
import styles from './index.less';

interface PageProps {}
const App: FC<PageProps> = () => {
  const [checked, setchecked] = useState(false);
  const goCardList = () => {
    if (checked) {
      history.push({
        pathname: '/patientcard/home',
        query: { redirect: '/dischargeSettlementNew/hospitalizationRecord' },
      });
    } else {
      Toast.show({
        content: '请阅读 四川大学华西医院自助结算须知》',
      });
    }
  };
  return (
    <div className={styles.main}>
      <div className={styles.title}>四川大学华西医院自助结算须知</div>
      <div className={styles.content}>
        <div>1．如需使用医保结算，请先进行医保登记，并等待医保审核通过</div>
        <div>2．结算后住院预交金余款，将会退款到您登记的银行卡</div>
        <div>3．您可以在自助机上登记或修改退款信息，但只限患者本人开户的银行卡</div>
        <div>4．退款登记姓名须与退款银行卡持卡人一致，否则无法退款</div>
        <div>5．结算过程中请按提示操作，不要重复点击。扫码支付后请耐心等待，不要取消交易。</div>
      </div>

      <div className={styles.action}>
        <Checkbox
          onChange={(val) => {
            setchecked(val);
          }}
        >
          已阅读 《四川大学华西医院自助结算须知》
        </Checkbox>
        <Button block className={styles.action_btn} onClick={goCardList}>
          确定
        </Button>
      </div>
    </div>
  );
};
export default App;
