import React, { useEffect, useState, useRef, ReactElement } from 'react';
import classnames from 'classnames';
import { Toast, Button } from 'antd-mobile';
import { Modal } from 'antd-mobile-v5';
import HxModal from '@/components/HxModal';
import { connect, Dispatch } from 'umi';
import { doPay } from '@/utils/common';
import { HxIndicator, HxIcon } from '@/components';
import { getOrganCode, getToken } from '@/utils/parameter';
import styles from './index.less';
import noticeImg from '@/assets/icon_modal.png';
import clockIcon from '@/assets/addressSelected.png';
import femaleImg from '@/assets/HospitalizationSettlement/female.png';
import maleImg from '@/assets/HospitalizationSettlement/male.png';
import rightArrow from '@/assets/ElectronReport/rightArrow.png';
import codeImg from '@/assets/HospitalizationSettlement/code.png';
import { getBeHospitalized } from '../service';
import QRCode from 'qrcode.react';
import { HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import logo_ from '@/assets/tencentPatientCard/logo_.png';

const close = `https://cdnhyt.cd120.com/person/assets/appoinmentHx/close2.png`;

interface FilmChioceProps {
  // filmPrinting: IFilmPrintingModelState;
  dispatch: Dispatch;
  location: {
    query: {
      admId: string;
    };
  };
  loading: boolean | undefined;
}

const SettlementDetail: React.FC<FilmChioceProps> = (props) => {
  // props
  const {
    location: {
      query: { admId },
    },
    loading,
  } = props;
  const [detailData, setDetailData] = useState<any>({});
  const { pmi = '', pmiNo, credNo, gender, age, cardNo, patientName } =
    HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA) || {};
  const getDetail = async (admId) => {
    const payload = {
      organPmi: pmi,
      admId, //详情传
      // organPmi: '145709',
      hospitalCode: getOrganCode(),
      token: getToken(),
    };
    const res: any = (await getBeHospitalized(payload)) || [];
    const data = res?.orderList || [];
    if (data?.length) {
      const amount = convertToChineseCurrency(Number(data[0].orderDetail?.sumAmt));
      data[0].amount = amount;
      console.log(111111, amount, data);
      setDetailData(data[0]);
    }
  };
  //数字转换大写
  const convertToChineseCurrency = (num) => {
    if (isNaN(num)) return '输入的不是数字';
    // 处理小数部分，确保只有两位
    num = parseFloat(num).toFixed(2);
    const digits = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const units = ['', '拾', '佰', '仟'];
    const bigUnits = ['', '万', '亿'];
    // 分割整数和小数部分
    const [integerPart, decimalPart] = num.split('.');
    // 处理整数部分
    let integerStr = '';
    let zeroFlag = false; // 标记是否有连续的零

    // 从右到左每4位一组处理
    for (let i = 0; i < Math.ceil(integerPart.length / 4); i++) {
      const start = Math.max(0, integerPart.length - (i + 1) * 4);
      const end = integerPart.length - i * 4;
      const chunk = integerPart.slice(start, end);

      let chunkStr = '';
      for (let j = 0; j < chunk.length; j++) {
        const digit = parseInt(chunk[j]);
        const unit = units[chunk.length - 1 - j];

        if (digit === 0) {
          zeroFlag = true;
        } else {
          if (zeroFlag) {
            chunkStr += '零';
            zeroFlag = false;
          }
          chunkStr += digits[digit] + unit;
        }
      }

      if (chunkStr !== '') {
        chunkStr += bigUnits[i];
        integerStr = chunkStr + integerStr;
      }
    }
    if (integerStr === '') {
      integerStr = '零';
    }
    // 处理小数部分
    let decimalStr = '';
    if (decimalPart) {
      const jiao = parseInt(decimalPart[0]);
      const fen = parseInt(decimalPart[1]);

      if (jiao > 0) {
        decimalStr += digits[jiao] + '角';
      }
      if (fen > 0) {
        decimalStr += digits[fen] + '分';
      }
    }
    // 组合结果
    let result = integerStr + '元';
    if (decimalStr) {
      result += decimalStr;
    } else {
      result += '整';
    }

    return result;
  };
  //放大就诊卡
  const enlargeCode = () => {
    console.log('cardNo===', cardNo);
    Modal.show({
      content: (
        <div className={styles.cardQrcodeWrap}>
          <div className={styles.inner}>
            <QRCode
              value={cardNo}
              size={180}
              fgColor="#03161F"
              level="M"
              imageSettings={{
                src: `${logo_}`,
                height: 44,
                width: 44,
                excavate: true,
              }}
            />
            <div className={styles.name}>{patientName}</div>
            <div className={styles.pmi}>登记号：{pmiNo}</div>
          </div>
          <div className={styles.line} />
          <img
            src={close}
            className={styles.close}
            alt=""
            onClick={() => {
              Modal.clear();
            }}
          />
        </div>
      ),
      closeOnMaskClick: false,
      bodyClassName: 'cardQrCodeContent',
    });
  };
  useEffect(() => {
    if (admId) {
      getDetail(admId);
    }
  }, [admId]);
  const list = {
    payAmount: '50.00', // 支付金额
    payTime: '2024-05-10 10:30:00', // 支付时间
    bankTradeNo: '*****************', // 银行交易流水号
    payMethod: 1, // 支付方式（1 可能代表微信支付等，需结合实际字典）
    examName: '胸部X光检查', // 检查名称
    radiographCount: 2, // 胶片数量
    radiographPrice: 25.0, // 单张胶片价格
    radiographType: '数字化X光片', // 胶片类型
    orderStatus: 0, // 订单状态（0 待缴费，1 已缴费，2 已取消）
    radiographTotalPrice: 50.0, // 胶片总价
    createDate: '2024-05-10 09:30:00', // 订单创建时间
    dealSeq: 'ORD202405100001', // 交易流水号
    patientName: '张三', // 患者姓名
    idCard: '123456789012345678', // 身份证号/就诊卡号
    expirationTime: '2024-05-10 11:30:00', // 支付过期时间
    printStatus: 0, // 打印状态（0 未出，1 已出，2 已打印）
    bizSysSeq: 'BIZ202405100001', // 业务系统流水号
    merchantSeq: 'MER202405100001', // 商户流水号
  };

  const { admDep, hosName, amount, orderDetail = {}, patName, selfPay } = detailData;
  const { insuFundAmt, cashAmt, fastBankNo, insuAccAmt } = orderDetail;
  console.log('render--', detailData, selfPay);
  return (
    <div className={classnames(styles.container, styles.sjContainer)}>
      {loading && <HxIndicator />}

      <div className={styles.header}>
        <div className={styles.orderStatus}>
          <img src={clockIcon} alt="" />
          结算成功
          <div className={styles.time}>您已完成出院结算</div>
        </div>
      </div>
      <div className={styles.main}>
        <div className={styles.patientInfo}>
          <div className={styles.avater}>
            <img src={femaleImg} />
            <div className={styles.name}>
              <span> {patName}</span>
              <span>
                {gender === 1 ? '男' : '女'} {age}岁
                {credNo && `${credNo.slice(0, 4)}****${credNo.slice(credNo.length - 4)}`}
              </span>
            </div>
          </div>

          <div className={styles.right} onClick={enlargeCode}>
            <img src={codeImg} />
            <img src={rightArrow} className={styles.arrow} />
          </div>
        </div>
        {/* 订单信息 */}
        <div className={styles.info}>
          <div className={styles.title}>住院结算信息</div>
          <div className={styles.infoItem}>
            <div className={styles.label}>就诊院区</div>
            <div>{hosName}</div>
          </div>
          <div className={styles.infoItem}>
            <div className={styles.label}>住院科室</div>
            <div>{admDep}</div>
          </div>
          <div className={styles.infoItem}>
            <div className={styles.label}>登记银行卡号</div>
            <div>{fastBankNo}</div>
          </div>
          <div className={styles.infoItem}>
            <div className={styles.label}>金额合计</div>
            <div> {orderDetail?.sumAmt}</div>
          </div>
          <div className={styles.infoItem}>
            <div className={styles.label}>合计大写</div>
            <div> {amount}</div>
          </div>
          {/* 已支付 */}
          {
            <div className={styles.payAmountInfo}>
              {insuFundAmt > 0 && (
                <div className={styles.infoItem}>
                  <div className={styles.label}>医保基金支付</div>
                  <div>{insuFundAmt}</div>
                </div>
              )}

              <div className={styles.infoItem}>
                <div className={styles.label}>自费支付</div>
                <div> {selfPay}</div>
              </div>
              {Number(insuAccAmt) > 0 && (
                <div className={styles.infoItem}>
                  <div className={styles.label}>医保个账支付</div>
                  <div> {insuAccAmt}</div>
                </div>
              )}
              {/* 根据状态显示 */}
              {/* {depsitBalance && (
                <div className={styles.infoItem}>
                  <div className={styles.label}>预交金退款</div>
                  <div style={{ color: 'red' }}> {depsitBalance}</div>
                </div>
              )} */}
            </div>
          }
        </div>
        <div className={styles.info}>
          <div className={styles.tips}>
            <img alt="" src={noticeImg} />
            温馨提示
          </div>
          <div className={styles.tipsText}>
            <p>1、电子票据请到华医通【电子发票>住院分类>电子发票】查看。</p>
            <p> 2、医保结算清单请到华医通【电子发票>住院分类>其他票据】查看。</p>
            <p> 3、出院证请到华医通【电子发票>住院分类>出院证】查看。</p>
          </div>
        </div>
      </div>
    </div>
  );
};
export default connect(({ filmPrinting }: { filmPrinting: any }) => ({
  filmPrinting,
}))(SettlementDetail);
