.container {
  color: #03081a;
  background-color: #f5f6fa;
  font-family: PingFang SC, PingFang SC-Medium;
  min-height: 100vh;
  padding-bottom: 128px;
  .header {
    background: url('../../../assets/HospitalizationSettlement/header.png') no-repeat center;
    background-size: 100% 100%;
    padding: 44px 24px 24px;
    height: 250px;
    .orderStatus {
      font-size: 40px;
      font-weight: bolder;
      img {
        width: 48px;
        height: 48px;
        margin-right: 16px;
        vertical-align: sub;
      }
      .time {
        font-size: 28px;
        color: #989eb4;
        margin-left: 60px;
        font-weight: normal;
      }
    }
  }
  .main {
    padding: 24px;
    position: relative;
    top: -80px;
  }
  .filmName {
    display: flex;
    padding: 24px;
    background: #ffffff;
    border-radius: 24px;
    margin-bottom: 24px;
    margin-top: 40px;
    align-items: center;
    font-size: 36px;
    font-weight: bolder;
    color: #03081a;
    img {
      width: 80px;
      height: 80px;
      margin-right: 24px;
    }
  }
  .toBePaid {
    padding: 50px 24px 0;
    color: #fff;
    background-color: #32b9aa;
    height: 202px;
    background: url('../../../assets/record_order_bg.png') no-repeat;
    background-size: cover;
    background-position: center center;

    .copy {
      padding: 6px 20px;
      border: 1px solid #f2f2f2;
      border-radius: 26px;
    }

    .title {
      padding-bottom: 16px;
      font-size: 36px;

      .clockIcon {
        margin-right: 20px;
      }

      .text {
        font-size: 500;
      }
    }
  }

  .info,
  .printInfo,
  .patientInfo {
    margin-bottom: 24px;
    padding: 32px 24px;
    background-color: #fff;
    border-radius: 24px;
    .tips {
      font-size: 32px;
      font-weight: bolder;
      color: #03081a;
      padding-top: 16px;
      img {
        vertical-align: sub;
        margin-right: 16px;
      }
    }
    .tipsText {
      font-size: 28px;
      color: #51586d;
      margin-top: 30px;
      padding-bottom: 16px;
    }
    .payAmountInfo {
      padding-top: 40px;
      margin-top: 40px;
      border-top: 2px dashed #ebedf5;
    }
    .infoItem {
      display: flex;
      align-items: center;
      justify-content: space-between;
      // padding: 16px 0;
      font-size: 28px;
      line-height: 60px;
      color: #03081a;
      &.infoSelfAddress {
        align-items: flex-start;
        line-height: 40px;
      }

      .label {
        flex-shrink: 0;
        color: #989eb4;
      }

      .infoText {
        padding-left: 20px;
        text-align: right;
      }

      .value {
        max-width: 364px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        color: #03081a;
      }
    }

    .total {
      padding-bottom: 24px;
      text-align: right;

      .totalPrice {
        color: #333;
        font-weight: 500;
      }
    }
  }
  .patientInfo {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .avater {
      display: flex;
      img {
        width: 88px;
        height: 88px;
        border-radius: 50%;
      }
      .name {
        margin-left: 24px;
        span {
          display: block;
          font-size: 28px;
          &:last-child {
            color: #989eb4;
            font-size: 24px;
          }
        }
      }
    }
    .right {
      width: 50%;
      text-align: right;
      img {
        width: 48px;
        height: 48px;
      }
      .arrow {
        width: 36px;
        height: 36px;
        opacity: 0.85;
      }
    }
  }
  .info {
    .title {
      font-size: 32px;
      font-weight: bolder;
      color: #03081a;
      margin-bottom: 32px;
    }
  }
  .bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 24px 16px 24px;
    background-color: #fff;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    box-shadow: 0px 0px 8px 0px rgba(235, 237, 245, 1);
    padding-bottom: calc(16px + constant(safe-area-inset-bottom));
    padding-bottom: calc(16px + env(safe-area-inset-bottom));

    .button,
    .cancelBtn {
      height: 72px !important;
      width: 176px;
      background: #3ad3c1;
      box-shadow: 0px 0px 8px 0px rgba(235, 237, 245, 1);
      border-radius: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .cancelBtn {
      background: #fff;
      border: 2px solid #dee0ea;
      color: #03081a;
      margin-left: 80px;
    }
    .sumPrice {
      background: #3ad3c1;
      border-radius: 52px;
      font-size: 32px;
      font-weight: bold;
      color: #ffffff;
      width: 100%;
    }

    .priceUnit {
      color: #fc4553;
      font-weight: 600;
      font-size: 36px;
    }

    .price {
      color: #fc4553;
      font-weight: 600;
      font-size: 50px;
    }
  }

  .priceUnit {
    color: #fc4553;
    font-weight: 600;
  }

  .price {
    color: #fc4553;
    font-weight: 600;
    font-size: 36px;
  }
}

.warningIcon {
  margin-right: 8px;
  color: #f0944f;
  font-size: 36px;
}

.pickupTimeBox {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 0;
  background-color: #fff;

  .pickupTimeTitle {
    display: flex;
    align-items: center;
    justify-content: center;

    .clockIcon {
      color: #03081a;
      font-weight: 600;
      font-size: 36px;
      margin-right: 8px;
    }

    & div.label {
      color: #03081a;
      font-weight: 500;
      font-size: 36px;
    }

    & span.labelDec {
      color: #b0b3bf;
      font-weight: 500;
      font-size: 36px;
    }
  }
}

.pickupHint {
  font-size: 28px;
  font-weight: 400;
  color: #fc4553;
  line-height: 44px;
}

.pickupCode {
  width: 100%;
  height: 96px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 24px 0;
  padding: 0 24px;
  background-color: #fff;

  .label {
    font-size: 36px;
    font-weight: 600;
    color: #03081a;
    line-height: 50px;
  }

  .code {
    font-size: 36px;
    font-weight: 600;
    color: #3ad3c1;
    line-height: 50px;
  }
}

.offlineDesc {
  padding-bottom: 10px;
  color: #fc4553;
}

.signInBox {
  position: relative;

  .signInButton {
    position: absolute;
    right: 20px;
    bottom: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 156px;
    height: 68px;
    margin-top: -34px;
    color: #3ad3c1;
    font-size: 32px;
    background: #fff;
    border-radius: 68px;
  }
}

.sjContainer {
  :global {
    .am-calendar .single-month .row .cell .date-wrapper .disable {
      color: #b0b3bf;
      background-color: #fff !important;
    }

    .am-calendar .week-panel .cell-grey {
      color: #989eb4;
    }

    .am-calendar {
      .content {
        height: 1043px;
        bottom: 0;
        top: unset;

        .week-panel {
          padding-top: 24px;
          padding-bottom: 24px;
          border-bottom: none;
        }

        .confirm-panel {
          display: none;
        }

        header {
          width: 100%;
          height: 96px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 32px;

          span {
            font-size: 32px;
            line-height: 48px;
          }
        }
      }
    }

    .date {
      div {
        padding-left: 0 !important;
      }
    }
  }
}
.cardQrcodeWrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  .inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #fff;
    width: 100%;
    padding-top: 44px;
    padding-bottom: 24px;
    border-radius: 12px;
  }
  > img {
    width: 56px;
    height: 56px;
    margin-top: -10px;
  }
  .name {
    text-align: center;
    margin-top: 40px;
    font-size: 32px;
    font-weight: bold;
    text-align: center;
    color: #03161f;
  }
  .pmi {
    font-size: 28px;
    font-weight: bold;
    text-align: center;
    color: #9aa2a6;
    margin-top: 24px;
  }
  .line {
    width: 4px;
    height: 50px;
    background: #ffffff;
  }
}
