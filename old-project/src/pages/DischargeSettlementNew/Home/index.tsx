import React, { useState, useEffect } from 'react';
import { useHistory, useLocation } from 'umi';
import { Button } from 'antd-mobile-v5';
import { Toast } from 'antd-mobile';
import { connect } from 'dva';
import { Dispatch, AnyAction } from 'redux';
import { HxLocalStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import { getOrganCode, getToken, getOpenId } from '@/utils/parameter';
import addressSelectArea from '@/assets/矩形备份@2x.png';
import styles from './index.less';
import { settlement, getChgUrl } from '../service';
import { HxSessionStorage } from '@/utils/storage';
import femaleImg from '@/assets/HospitalizationSettlement/female.png';
import maleImg from '@/assets/HospitalizationSettlement/man.png';
import rightArrow from '@/assets/ElectronReport/rightArrow.png';
import completedIcon from '@/assets/select.png';
import radioIcon from '@/assets/radio1.png';
import tipsIcon from '@/assets/record_hint.png';
import underwayImg from '@/assets/HospitalizationSettlement/underway.png';
import failedImg from '@/assets/HospitalizationSettlement/failed.png';
import { getBeHospitalized } from '../service';
import HxModal from '@/components/HxModal';
import { Modal } from 'antd-mobile-v5';

interface IProps {
  loading?: boolean;
  dispatch: Dispatch<AnyAction>;
  location: any;
}

const progressList = [
  { title: '快捷登记', icon: completedIcon, status: 'finished' },
  { title: '护理结算', icon: underwayImg },
  { title: '医保登记', icon: completedIcon },
  { title: '医保审核', icon: completedIcon },
  { title: '自助结算', icon: completedIcon },
];
const Home: React.FC<IProps> = ({ loading, dispatch }) => {
  const history = useHistory();
  const location: any = useLocation();
  const { admId = '' } = location?.query;
  const { credNo, pmiNo, pmi, cardId, gender, patientName, age } =
    HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA) || {};
  const [progressData, setProgressData] = useState<any>(progressList);
  const [hospitalizationInfo, setHospitalizationInfo] = useState<any>({});
  const [lastStep, setLastStep] = useState<boolean>(false); //1护理结算
  const [payWayType, setPayWayType] = useState<number>(0); //医保，1现金

  const { admDep, patNo, admDate, orderId, orderDetail = {} } = hospitalizationInfo;
  const {
    insuAuditStat,
    insuRegStat,
    depsitBalance = 0,
    depsitAmt,
    sumAmt,
    fastBankNo,
    nurChgStat,
    onChgType,
    onChgRea,
  } = orderDetail;
  const transDepsitBalance: any =
    Number(depsitBalance) < 0 ? Math.abs(parseFloat(depsitBalance))?.toFixed(2) : Number(depsitBalance); //把负数转成正数

  useEffect(() => {
    getDetail(admId);
  }, [admId]);

  const judgeStatus = (value, index) => {
    if (value === '0') {
      progressData[index].status = 'wait';
      //护理结算
      if (index === 1) {
        progressData[index].status = 'current';
      }
    } else if (value === '1') {
      progressData[index].status = 'finished';
      if (progressData[index + 1].status !== 'finished' && progressData[index + 1].status !== 'failed') {
        progressData[index + 1].status = 'current';
      }
    } else if (value === '2') {
      progressData[index].status = 'failed';
    }
    return;
  };

  const getDetail = async (admId) => {
    const payload = {
      admId,
      // organPmi: '145709',
      organPmi: pmi,
      hospitalCode: getOrganCode(),
      token: getToken(),
    };
    const res: any = (await getBeHospitalized(payload)) || [];
    const data = res?.orderList || [];
    if (data?.length) {
      // data[0].orderDetail.depsitBalance = '29.889'; //测试
      // data[0].orderDetail.insuRegStat = '0'; //测试
      // data[0].orderDetail.nurChgStat = '1'; //测试
      // data[0].orderDetail.insuAuditStat = '0'; //测试
      setHospitalizationInfo(data[0]);
      const { nurChgStat, insuRegStat, insuAuditStat } = data[0]?.orderDetail;
      judgeStatus(insuAuditStat, 3); //医保审核
      judgeStatus(insuRegStat, 2); //医保登记
      judgeStatus(nurChgStat, 1); //护理结算
      setPayWayType(insuAuditStat === '1' ? 0 : 1); //默认医保支付
      //直接自助结算
      if (nurChgStat === '1' && insuRegStat === '1' && insuAuditStat === '1') {
        progressData[4].status = 'current';
        setLastStep(true);
        console.log('设置----');
      } else {
        progressData[4].status = 'wait';
      }
      console.log('nodeList===', progressData);
      setProgressData([...progressData]);
    }
  };

  const goToPay = async () => {
    const res: any = await settlement({
      organPmiNo: pmiNo,
    });
    if (res?.code === '1') {
      //去详情
      history.push(`/dischargeSettlementNew/settlementDetail?admId=${admId}`);
    }
  };
  //跳转收银台
  const healthInsPay = async () => {
    console.log('COMMON_回调域名--', COMMON_DOMAIN);
    const payload = {
      hospitalCode: getOrganCode(),
      patientID: pmi,
      admId,
      orderID: orderId,

      resultUrl: `${COMMON_DOMAIN}/person/dischargeSettlementNew/settlementDetail?admId=${admId}`, //详情地址
    };
    const res: any = await getChgUrl(payload);
    console.log('获取收银台地址--', res);
    if (res?.code === '1') {
      const url = res.data?.insuPayUrl;
      const separator = url.includes('?') ? '&' : '?';
      window.location.href = `${url}${separator}openid=${getOpenId()}`;
    }
  };
  //去结算
  const nextStep = () => {
    const list = [...progressData];
    if (onChgType === '00') {
      HxModal.show({
        title: '温馨提示',
        content: <div>{onChgRea}</div>,
        closeOnMaskClick: true,
        actions: [
          {
            text: '知道了',
            key: 'confirm',
            className: 'primary',
            onClick: () => {
              Modal.clear();
            },
          },
        ],
      });
      return;
    }
    //确认结算，医保审核通过
    if (lastStep) {
      //医保支付，insuAuditStat医保审核通过
      if (payWayType === 0 && insuAuditStat === '1' && onChgType === '01') {
        healthInsPay();
      } else if (payWayType === 1 && onChgType === '10') {
        //现金
        goToPay();
      }
      return;
    }
    //第四步，护理结算完成直接现金支付
    else if (lastStep && nurChgStat === '1') {
      goToPay();
      return;
    }
    const currentIndex = list.findIndex((item) => item.status === 'current' || item.status === 'failed');
    if (currentIndex !== -1 && currentIndex !== list?.length - 1) {
      list[currentIndex].status = list[currentIndex].status === 'failed' ? 'failed' : 'finished';
      list[currentIndex + 1].status = 'current';
    }
    //自助结算
    if (currentIndex + 1 === 4) {
      setLastStep(true);
    }
    //护理完成，可直接跳到自助结算
    if (nurChgStat === '1') {
      list[2].status = 'wait';
      list[3].status = 'wait';
      list[4].status = 'current';
      setLastStep(true);
    }
    setProgressData(list);
  };
  //去充值
  const handleSettlement = async () => {
    const data = {
      patientName,
      credNo,
    };
    history.push(
      `/hospitalizationprepayment/recharge?data=${JSON.stringify(
        data,
      )}&startDate=${admDate}&inHospNo=${admId}&firstDeposit=${transDepsitBalance}&patientAge=${age}&cardId=${cardId}&patientSex=${gender}&from=DischargeSettlement&channelType=4`,
    );
  };

  const changeCard = () => {
    history.push('/patientcard/home?redirect=/dischargeSettlementNew/home&guidance=1');
  };
  const changeWay = (type: number) => {
    setPayWayType(type);
  };
  console.log('render====', transDepsitBalance, depsitBalance, progressData, Number(depsitBalance) > 0);
  const payTypeList = ['医保结算', '现金结算'];
  return (
    <div className={styles.container}>
      <div className={styles.headerHint}>
        {credNo ? (
          <div className={styles.info} onClick={changeCard}>
            <img src={gender === 1 ? maleImg : femaleImg} alt="" />

            <div style={{ marginLeft: '16px' }}>
              <div>
                <span className={styles.patientName}>{patientName}</span>
                <span className={styles.gender}>{gender === 1 ? '男' : '女'}</span>
                <span className={styles.age}>{age}岁</span>
              </div>
              <div>就诊卡：{`${credNo.slice(0, 4)}****${credNo.slice(credNo.length - 4)}`}</div>
            </div>
            <img src={addressSelectArea} alt="" className={styles.action1} />
          </div>
        ) : null}
      </div>
      <div className={styles.flowProgress}>
        <div className={styles.progressContainer}>
          {progressData.map((item, index) => {
            return (
              <div className={`${styles.progressStep}  ${styles.completed}`} key={index}>
                <div className={`${styles.circleIcon} `}>
                  {item.status === 'current' ? (
                    <img src={underwayImg} />
                  ) : item.status === 'finished' ? (
                    <img src={completedIcon} />
                  ) : item.status === 'failed' ? (
                    <img src={failedImg} />
                  ) : (
                    <div className={styles.grayCircle}></div>
                  )}
                </div>
                <p>{item.title}</p>
              </div>
            );
          })}
        </div>
      </div>
      <div className={styles.content}>
        {(!lastStep || (depsitBalance > 0 && lastStep)) && (
          <div className={styles.notice}>
            <div className={styles.title}>
              <img src={tipsIcon} />
              温馨提示
            </div>
            <div>
              {onChgRea}
              {/* {nurChgStat === '0'
                ? '护理结算正在进行中，请耐心等待'
                : insuRegStat === '0'
                ? '您尚未进行医保登记，只能进行【现金】结算!如您想使用医保结算，请先进行【医保登记】'
                : insuAuditStat === '0'
                ? '医保正在审核上传中，只能进行【现金】结算，请10分钟后再试，如还未通过请到结算窗口咨询。或绑定医保办公众号实时查询审核结果。'
                : insuAuditStat === '2'
                ? '医保审核未通过，只能进行【现金】结算，请到结算窗口咨询。文案根据产品反馈。'
                : depsitBalance > 0 && lastStep
                ? `您的预交金有结余，将申请退款${depsitBalance}元。请确认登记姓名与持卡人一致，否则无法退款。`
                : ''} */}
            </div>
          </div>
        )}

        <div className={styles.sumMoney}>
          <div style={{ width: '100%' }}>
            <div className={styles.sumMoney_item}>
              <span className={styles.label}>入院时间：</span>
              <span className={styles.content}> {admDate}</span>
            </div>
            <div className={styles.sumMoney_item}>
              <span className={styles.label}>入院科室：</span>
              <span className={styles.content}> {admDep}</span>
            </div>
            <div className={styles.sumMoney_item}>
              <span className={styles.label}>登记银行卡号：</span>
              <span className={styles.content}> {fastBankNo}</span>
            </div>
            <div className={styles.sumMoney_item}>
              <span className={styles.label}> 总费用（¥）：</span>
              <span className={styles.content}> {sumAmt}</span>
            </div>
            <div className={styles.sumMoney_item}>
              <span className={styles.label}> 住院预交金（¥）：</span>
              <span className={styles.content}> {depsitAmt}</span>
            </div>
            <div className={styles.sumMoney_item}>
              <span className={styles.label}> 预交金余额（¥）：</span>
              <span className={styles.content} style={{ color: '#FC4553' }}>
                {depsitBalance}
              </span>
            </div>
          </div>
        </div>
        {insuAuditStat === '1' && !['00', '01', '10'].includes(onChgType) && (
          <div className={styles.payWay}>
            <div className={styles.title}>请选择结算方式</div>
            <div className={styles.radioGroup}>
              {payTypeList.map((item, index) => {
                return (
                  <div className={styles.item} key={index}>
                    <img src={payWayType === index ? completedIcon : radioIcon} onClick={() => changeWay(index)} />
                    {item}
                  </div>
                );
              })}

              {/* <div className={styles.item}>
                <img src={payWayType === 2 ? completedIcon : radioIcon} onClick={()=>changeWay(2)}/>
                现金结算
              </div> */}
            </div>
          </div>
        )}

        {/* 余额小于0显示,payWayType为0医保 */}
        {depsitBalance < 0 && nurChgStat === '1' && payWayType !== 0 ? (
          <div className={styles.toPay}>
            <div className={styles.row} onClick={handleSettlement}>
              <div className={styles.title}>充值住院预缴金</div>
              <div className={styles.recharge}>
                去充值
                <img src={rightArrow} />
              </div>
            </div>

            <div className={styles.tips}>
              温馨提示：您的预交金无结余，须补交{transDepsitBalance}元，方可办理出院结算。
            </div>
          </div>
        ) : null}
      </div>
      {/* 护士结算完成就显示 */}
      {nurChgStat === '1' && (
        <div className={styles.action}>
          <Button className={styles.button} onClick={nextStep} disabled={depsitBalance < 0 && payWayType !== 0}>
            {lastStep ? '确认结算' : '去结算'} {Number(depsitBalance) > 0}
          </Button>
        </div>
      )}
    </div>
  );
};

export default connect(({ loading }: { loading: any }) => ({
  // loading: loading.effects['hospitalizationPrepayment/getInpDetail'],
}))(Home);
