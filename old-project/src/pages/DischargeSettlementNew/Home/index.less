.container {
  background: #f5f6fa;
  font-family: PingFang SC, PingFang SC-Medium;
  padding-bottom: 300px;

  .headerHint {
    box-sizing: border-box;
    width: 750px;
    height: 152px;
    padding: 32px 24px;
    background: #3ad3c1;
    .info {
      position: relative;
      display: flex;
      align-items: center;
      height: 88px;
      color: #fff;
      font-size: 30px;
      font-weight: normal;
      .patientName {
        height: 50px;
        color: #fff;
        font-size: 32px;
        font-weight: 600;
        font-family: PingFangSC-Semibold, PingFang SC;
        line-height: 50px;
      }
      .avatar {
        width: 88px;
        height: 88px;
        border: 2.2px solid #ffffff;
        border-radius: 50%;
      }
      .action1 {
        position: absolute;
        right: 0;
        width: 48px;
        height: 48px;
      }
      .gender,
      .age {
        margin-left: 30px;
      }
    }
    .patientInfo {
      display: flex;
      flex-direction: row;
      align-items: center;
    }
    .button {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      width: 190px;
      height: 52px;
      background: #fff;
      border-radius: 26px;
    }
  }
  .flowProgress {
    background: #ffffff;
    padding: 24px;
    font-size: 24px;
    // padding-top: 64px;
  }
  .progressContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    margin-bottom: 10px;
    position: relative;
  }
  .progressStep {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    .circleIcon {
      z-index: 999;
      margin-bottom: 6px;
      img {
        width: 32px;
        height: 32px;
      }
    }
    .grayCircle {
      width: 32px;
      height: 32px;
      background: #ebedf5;
      border-radius: 50%;
    }
    &::after {
      content: '';
      position: absolute;
      height: 2px;
      background: #3ad3c1;
      width: 110%;
      top: 20px;
      left: -30%;
      transform: translateX(-50%);
      z-index: 199;
    }
    &:first-child::after {
      display: none;
    }
  }
  // .completed::before {
  //   border-color: #00b38a;
  //   background-color: #00b38a;
  // }
  .completed::after {
    background: #3ad3c1;
  }
  .failed::after {
    background-color: #ebedf5;
  }
  .noRecord {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 702px;
    height: 804px;
    margin-top: 24px;
    margin-left: 24px;
    // background: #fff;
    border-radius: 16px;
    .img {
      width: 240px;
      height: 240px;
    }

    .text {
      height: 44px;
      margin-top: 24px;
      color: #03081a;
      font-weight: 600;
      font-size: 32px;
      font-family: PingFangSC-Semibold, PingFang SC;
      line-height: 44px;
    }
  }
  .content {
    .notice {
      margin: 24px;
      font-size: 28px;
      color: #03081a;
      background: #fff;
      border-radius: 24px;
      padding: 24px;
      .title {
        font-size: 32px;
        font-weight: bolder;
        color: #fc4553;
        margin-left: 12px;
        margin-bottom: 16px;
        img {
          width: 36px;
          height: 36px;
          margin-right: 12px;
          vertical-align: sub;
        }
      }
    }
    .payWay {
      border-radius: 24px;
      padding: 32px 24px;
      background: #fff;
      margin: 0px 24px;
      .title {
        font-size: 32px;
        font-weight: bolder;
        text-align: left;
        margin-bottom: 32px;
      }
      .radioGroup {
        display: flex;
        .item {
          font-size: 32px;
          &:first-child {
            margin-right: 120px;
          }
          img {
            width: 48px;
            height: 48px;
            margin-right: 16px;
            vertical-align: middle;
          }
        }
      }
    }
    .sumMoney {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 24px;
      padding: 24px 24px 0 24px;
      background: #fff;
      border-radius: 24px;
      .sumMoney_item {
        width: 100%;
        margin-bottom: 20px;
        font-weight: 400;
        font-size: 28px;
        line-height: 40px;
        .label {
          // width: 276px;
          color: #989eb4;
          width: 240px;
          display: inline-block;
        }
        .content {
          color: #03081a;
        }
      }
      .pageRemark {
        width: 100%;
        margin-top: 64px;
        color: #e03636;
        font-weight: 400;
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        line-height: 44px;
      }
    }
  }
}
.nodataImg {
  width: 160px;
  height: 160px;
  margin-bottom: 24px;
}
.action {
  position: fixed;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  background: #fff;
  padding: 16px 32px;
  left: 0;
  right: 0;
  box-shadow: 0px -2px 8px 0px rgba(3, 8, 26, 0.06);
  .button {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 702px;
    height: 96px;
    color: #fff;
    font-size: 36px;
    border-radius: 48px;
    background: #3ad3c1;
    padding: 0px !important;
  }
}
.toPay {
  margin: 24px;
  padding: 32px 24px;
  color: #03081a;
  font-size: 32px;
  background: #fff;
  border-radius: 24px;
  .row {
    display: flex;
    justify-content: space-between;
    padding-bottom: 24px;
    .title {
      font-weight: bolder;
    }
    .recharge {
      color: #fc4553;
      img {
        width: 36px;
        height: 36px;
        opacity: 0.65;
        left: 6px;
        position: relative;
        top: -4px;
      }
    }
  }
}
.tips {
  padding-top: 24px;
  color: #ff984b;
  font-size: 28px;
  line-height: 40px;
  border-top: 2px solid #f6f8ff;
}
