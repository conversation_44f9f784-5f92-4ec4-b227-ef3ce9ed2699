import request from '@/utils/request';

// 查询项目信息
export const queryProjectInfo = async (data: object): Promise<any> =>
  request(`${API_CHRONIC}/chronicdisease/doctor/findprojectdetail`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });

// 查询服务包详情
export const queryServiceDetail = async (data: object): Promise<any> =>
  request(`${API_CHRONIC}/chronicdisease/purchasepkg/querypkgdetail`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });

// 我的服务包订单列表 慢病服务包列表
export const queryMyServiceList = async (data: object): Promise<any> =>
  request(`${API_CHRONIC}/chronicdisease/purchasepkg/findpatientpkglist`, {
    method: 'POST',
    data,
  });

// 服务包订单列表
export const queryServiceOrderList = async (params: object): Promise<any> =>
  request(`${API_CHRONIC}/chronicdisease/purchasepkg/listpkgorder`, {
    method: 'GET',
    params,
  });
// 服务包订单详情
export const queryServiceOrderDetail = async (data: object): Promise<any> =>
  request(`${API_CHRONIC}/chronicdisease/purchasepkg/orderdetail`, {
    method: 'POST',
    data,
  });

// 我的服务包详情和慢病复诊列表
export const queryChronicAppointList = async (data: object): Promise<any> =>
  // request(`${API_DOCTOR}/doctor/chronicdisease/querypkginfo`, {
  //   method: 'POST',
  //   data,
  request(`${API_CHRONIC}/chronicdisease/purchasepkg/querypkgdetailinfo`, {
    method: 'POST',
    data,
  });
// 服务包订单列表页面点击立即支付之后判断服务包是否已下架
export const isSole = async (data: object): Promise<any> =>
  request(`${API_CHRONIC}/chronicdisease/purchasepkg/beforepay`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });

// 服务包订单列表页面取消订单按钮
export const cancelOrder = async (data: object): Promise<any> =>
  request(`${API_CHRONIC}/chronicdisease/purchasepkg/cancelorder`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });
// 购买服务包
export const buyServicePackage = async (data: object): Promise<any> =>
  request(`${API_CHRONIC}/chronicdisease/purchasepkg/buypkg`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
      skipError: true,
    },
  });
// 删除缓存
export const clearcache = async (data: object): Promise<any> =>
  request(`${API_CHRONIC}/chronicdisease/purchasepkg/clearcache`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });

// 原生接口查看老服务包列表
export const queryOrderForUserId = async (data: object): Promise<any> =>
  request(`${API_DOCTOR}/servicePkg/queryOrderForUserId`, {
    method: 'POST',
    data,
  });

// // 原生接口查看老服务包列表
// export const queryOrderForUserId = async (data: object): Promise<any> =>
//   request('http://************:20000/servicePkg/queryOrderForUserId', {
//     method: 'POST',
//     data,
//   });

// 我的服务包详情页面发起慢病复诊 ---检查患者在同一服务包是否有正在咨询记录
export const checkinquiry = async (data: object): Promise<any> =>
  request(`${API_DOCTOR}/netInquiry/chronicdisease/checkinquiry`, {
    method: 'POST',
    data,
  });

// 我的服务包详情页面发起慢病复诊 ---查询患者服务包的在线复诊记录
export const queryzxfzrecord = async (data: object): Promise<any> =>
  request(`${API_DOCTOR}/netInquiry/chronicdisease/queryzxfzrecord`, {
    method: 'POST',
    data,
  });

// 我的服务包详情页面发起慢病复诊 ---发起在线复诊
export const toinquiry = async (data: object): Promise<any> =>
  request(`${API_CHRONIC}/netInquiry/chronicdisease/toinquiry`, {
    method: 'POST',
    data,
  });
// 选择就诊人
export const selectPatient = async (data: object): Promise<any> =>
  request(`${API_DOCTOR}/doctor/netinquiry/query/cardList`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });

// 购买服务包前的判断
export const preBuypkg = async (data: object): Promise<any> =>
  request(`${API_CHRONIC}/chronicdisease/purchasepkg/preBuypkg`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });

/**
 * 就诊卡列表
 */
export const cardList = async (data: object): Promise<any> => {
  return request('/cloud/cardservice/home/<USER>', { method: 'POST', data });
};

/**
 * 慢病3.5期--从学透通获取已买服务包的服务内容数据
 */
export const getservicecontent = async (data: object): Promise<any> =>
  request(`${API_CHRONIC}/chronicdisease/thridparty/getservicecontent`, {
    method: 'POST',
    data,
  });

/** 慢病3.5期 -- 从学透通获取代办事项和工具地址 */
export const getpendingitem = async (data: object): Promise<any> =>
  request(`${API_CHRONIC}/chronicdisease/thridparty/getpendingitem`, {
    method: 'POST',
    data,
  });

/** 慢病3.5期 -- 判断用户授权是否超期 */
export const isAuthorization = async (data: object): Promise<any> =>
  request(`${API_CHRONIC}/usercenter/auth/isoutauthtimeforbusiness`, {
    method: 'POST',
    data: {
      showOriginData: true,
      skipError: true,
      ...data,
    },
  });

/** 慢病3.5期 -- 用户授权  */
export const userAuthorization = async (data: object): Promise<any> =>
  request(`${API_CHRONIC}/usercenter/auth/authorize`, {
    method: 'POST',
    data,
  });

/** 6.3.4 -- 需求迭代 慢病推荐医生列表  */
export const recommendedList = async (data: object): Promise<any> =>
  request(`${API_DOCTOR}/netInquiry/patient/chronicDoctors`, {
    method: 'POST',
    data,
  });
// 判断是否可购买慢病服务包接口
export const purchasepkgCanSell = async (params: object): Promise<any> =>
  request(`${API_CHRONIC}/chronicdisease/purchasepkg/canSell`, {
    method: 'GET',
    params: {
      showOriginData: true,
      skipError: true,
      ...params,
    },
  });
// 慢病服务包配置
export const purchasepkgPageConfig = async (params: object): Promise<any> =>
  request(`${API_CHRONIC}/chronicdisease/purchasepkg/pageConfig`, {
    method: 'GET',
    params: {
      showOriginData: true,
      ...params,
    },
  });
