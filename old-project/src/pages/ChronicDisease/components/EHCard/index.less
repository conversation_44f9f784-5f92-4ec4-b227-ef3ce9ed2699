.itemCard {
  position: relative;
  box-sizing: border-box;
  width: 690px;
  margin-top: 20px;
  padding: 30px 40px 26px 40px;
  background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/patient-card/bg_%E5%8D%87%E7%BA%A7%E5%90%8E%E7%9A%84%E5%B0%B1%E8%AF%8A%E5%8D%A1_%E5%B0%8F.png')
    center center;
  background-size: cover;

  .nameType {
    display: flex;
    flex-direction: row;
    align-items: baseline;
    color: #222;
    font-weight: 600;

    .patientName {
      font-size: 34px;
    }

    .contact {
      font-size: 24px;
    }
  }

  .audit {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-top: 10px;

    .isAudit {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      margin-right: 16px;
      padding: 2px 12px;
      color: #fab319;
      font-size: 22px;
      background-color: #fff;
      border: 1px solid #fab319;
      border-radius: 16px;
    }

    .isIns {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      margin-right: 16px;
      padding: 2px 12px;
      color: #19b5fa;
      font-size: 22px;
      background-color: #fff;
      border: 1px solid #19b5fa;
      border-radius: 16px;
    }
  }

  .cardCode {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-top: 27px;

    .contentOne {
      display: flex;
      flex-direction: column;

      .cardType {
        color: #666;
        font-size: 24px;
      }
      .cardNo {
        margin-top: 6px;
        color: #333;
        font-weight: 500;
        font-size: 36px;
      }
    }

    .img {
      width: 80px;
      height: 80px;
      opacity: 0.16;
    }
  }

  .default {
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 93px;
  }
}
