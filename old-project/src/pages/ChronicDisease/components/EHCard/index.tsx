import React from 'react';
import { HxIcon } from '@/components';
import { defaultimg as Default } from '@/utils/image';
import styles from './index.less';

interface IProps {
  /**
   * 就诊卡参数
   */
  item: any;
  /**
   * 点击事件
   */
  onClick?: React.MouseEventHandler<Element>;
}

const EHCard = (props: IProps) => {
  const { item, onClick } = props;
  const { credNo, cardTypeName, relation, listDefault, realName, patientName, cardInsChannel = [] } = item;
  return (
    <div className={styles.itemCard} onClick={onClick}>
      <div className={styles.nameType}>
        <div className={styles.patientName}>{patientName}</div>
        <div className={styles.contact}>{relation === 0 ? '（本人）' : '（其他）'}</div>
      </div>
      <div className={styles.audit}>
        {realName && <div className={styles.isAudit}>已实名</div>}
        {cardInsChannel.length > 0 &&
          cardInsChannel.map((i: any, j: any) => (
            <div key={j.toString()} className={styles.isIns}>
              {i.name}
            </div>
          ))}
      </div>
      <div className={styles.cardCode}>
        <div className={styles.contentOne}>
          <div className={styles.cardType}>{cardTypeName}</div>
          <div className={styles.cardNo}>{`${credNo.slice(0, 4)}****${credNo.slice(credNo.length - 4)}`}</div>
        </div>
        <HxIcon iconName="patientcard-list-qrcode" className={styles.img} />
      </div>
      {listDefault && <img src={Default} className={styles.default} alt="" />}
    </div>
  );
};
export default EHCard;
