import React from 'react';
import { HxIcon } from '@/components';
import { defaultimg as Default, barDefault } from '@/utils/image';
import styles from './index.less';

interface IProps {
  /**
   * 就诊卡参数
   */
  item: any;
  /**
   * 点击事件
   */
  onClick?: React.MouseEventHandler<Element>;
}

const ItemCard = (props: IProps) => {
  const { item, onClick } = props;
  const {
    bankName = '',
    bankCardIcon = '',
    bankCardBottom = '',
    cardNo,
    cardTypeName,
    isSelf,
    listDefault,
    realName,
    patientName,
    cardInsChannel = [], // [{code: , name: ,}, {}]
  } = item;
  return (
    <div className={styles.itemCard} onClick={onClick}>
      <div className={styles.nameType}>
        <div className={styles.patientName}>{patientName}</div>
        <div className={styles.contact}>{isSelf === 0 ? '（本人）' : '（其他）'}</div>
      </div>
      <div className={styles.audit}>
        {realName && <div className={styles.isAudit}>已实名</div>}
        {cardInsChannel.length > 0 &&
          cardInsChannel.map((i: any, j: any) => (
            <div key={j.toString()} className={styles.isIns}>
              {i.name}
            </div>
          ))}
      </div>
      <div className={styles.cardCode}>
        <div className={styles.contentOne}>
          <div className={styles.cardType}>
            {bankCardIcon && (
              <img alt="" src={bankCardIcon} className={styles.barDefault} style={{ marginRight: '6px' }} />
            )}
            <div className={styles.cardTypeDesc}>
              <span style={{ marginRight: '3px' }}>{bankName}</span>
              <span>{cardTypeName}</span>
            </div>
          </div>
          <div className={styles.cardNo}>{`${cardNo.slice(0, 4)}****${cardNo.slice(cardNo.length - 4)}`}</div>
        </div>
        <HxIcon iconName="patientcard-list-qrcode" className={styles.img} />
      </div>
      {listDefault && <img src={Default} className={styles.default} alt="" />}
      <img
        alt=""
        src={bankName !== '' && bankName !== null && bankName !== undefined ? bankCardBottom : barDefault}
        className={styles.bar}
      />
    </div>
  );
};
export default ItemCard;
