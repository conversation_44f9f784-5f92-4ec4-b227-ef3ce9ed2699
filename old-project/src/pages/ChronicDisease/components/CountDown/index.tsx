import React, { useEffect, useState } from 'react';

interface IProps {
  sys_second: number;
}

const CountDown = (props: IProps) => {
  let { sys_second } = props;
  const [hour, setHour] = useState(0);
  const [minute, setMinute] = useState(0);
  const [second, setSecond] = useState(0);
  useEffect(() => {
    const timerId = setInterval(() => {
      // 防止倒计时出现负数
      if (sys_second > 1000) {
        sys_second -= 1000;
        const hour = Math.floor((sys_second / 1000 / 3600) % 24);
        const minute = Math.floor((sys_second / 1000 / 60) % 60);
        const second = Math.floor((sys_second / 1000) % 60);
        setHour(hour < 10 ? `0${hour}` : hour);
        setMinute(minute < 10 ? `0${minute}` : minute);
        setSecond(second < 10 ? `0${second}` : second);
      } else {
        clearInterval(timerId);
      }
    }, 1000);
    return () => {
      // 返回一个回调函数用来清除定时器
      clearInterval(timerId);
    };
  }, []);

  return (
    <div>
      倒计时：{hour}:{minute}:{second}
    </div>
  );
};

export default CountDown;
