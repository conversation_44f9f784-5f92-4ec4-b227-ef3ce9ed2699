.comment {
  padding: 18px 30px 18px 25px;
  background: #fff;
  border-bottom: 0.5px solid #eee;
  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      display: flex;
      .name {
        margin-right: 24px;
        color: #666;
        font-size: 26px;
      }
      .right {
        display: flex;
        padding-top: 10px;
        img {
          width: 20px;
          height: 20px;
          margin-right: 6px;
        }
      }
    }
    .time {
      color: #999;
      font-size: 24px;
    }
  }
  .desc {
    margin-top: 16px;
    color: #333;
    font-size: 26px;
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
  }
  .descOne {
    display: -webkit-box;
    width: 100%;
    margin-top: 16px;
    overflow: hidden;
    color: #333;
    font-size: 26px;
    text-overflow: ellipsis;
    word-break: break-all; /* 追加这一行代码 */

    /*! autoprefixer: off */
    -webkit-box-orient: vertical;

    /* autoprefixer: on */
    -webkit-line-clamp: 2;
  }
}

.openApp {
  position: relative;

  .image {
    width: 100vw;
    height: 100vh;
  }

  .buttons {
    position: absolute;
    bottom: 0;
    width: 100vw;
    padding: 90px;
    a {
      margin-bottom: 50px;
      border-radius: 20px;
    }
    .open {
      background-color: @bg-color-gray !important;
    }
    .download {
      background-color: @bg-color-gray !important;
    }
  }

  // 取消button的border样式
  :global(.am-button)::before {
    border: none !important;
    content: '' !important;
  }
}
