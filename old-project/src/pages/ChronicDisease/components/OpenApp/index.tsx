import React from 'react';
import { Button } from 'antd-mobile';

import styles from './index.less';

const OpenApp = ({ appPageUrl, backgroundImageUrl }: any) => {
  const isAndroid = navigator.userAgent.match(/android/i);
  const isIos = navigator.userAgent.match(/(iPhone|iPod|iPad);?/i);

  /** 跳转app指定页面 */
  const onOpenApp = () => {
    window.location.href = appPageUrl;
  };

  /** 跳转到下载app页面 */
  const onDownload = () => {
    if (isAndroid) {
      window.location.href = 'https://huayitong-10042696.file.myqcloud.com/huayitong-latest.apk';
    } else if (isIos) {
      window.location.href = 'https://itunes.apple.com/cn/app/%E5%8D%8E%E5%8C%BB%E9%80%9A/id1037725396?mt=8';
    }
  };

  return (
    <div className={styles.openApp}>
      <img className={styles.image} src={backgroundImageUrl} alt="" />
      <div className={styles.buttons}>
        <Button className={styles.open} type="primary" onClick={onOpenApp}>
          打开APP，立即体验
        </Button>
        <Button className={styles.download} type="primary" onClick={onDownload}>
          没有APP？点击下载
        </Button>
      </div>
    </div>
  );
};

export default OpenApp;
