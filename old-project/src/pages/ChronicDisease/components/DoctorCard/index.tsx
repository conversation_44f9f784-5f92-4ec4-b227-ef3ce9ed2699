import React, { useEffect, useState } from 'react';
import { HxIcon } from '@/components';
import { connect, IChronicDiseaseModelState, Dispatch, useLocation, history } from 'umi';
import styles from './index.less';

const avater = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/default-doctor-logo.png';
const goodAt = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/shanchang.png';

interface IProps {
  dispatch: Dispatch;
  chronicDisease: IChronicDiseaseModelState;
}

const DoctorItem = (props: any) => {
  const { item = {}, location = {} } = props;
  const {
    doctorName = '',
    titelName = '',
    organName = '',
    deptName = '',
    headPortraits = '',
    profession = '',
    usable = 0,
    satisfaction = '',
    servTimes = 0,
    organLevel = '',
  } = item;

  const toDetail = (item: any) => {
    history.push({
      pathname: '/doctor/hxhome',
      query: {
        ...location?.query,
        doctorId: item?.doctorId,
        deptName: item?.deptName,
        organId: item?.organId,
        servCode: item?.servCode,
        middleType: 1,
      },
    });
  };

  return (
    <div className={styles.doctorItem}>
      <div className={styles.item} onClick={() => toDetail(item)}>
        <img src={headPortraits || avater} alt="" className={styles.poster} />
        <div className={styles.doctorInfo}>
          <div className={styles.basicInfo}>
            <div className={styles.doctorName}>{doctorName}</div>
            <div className={styles.titleNmae}>{titelName}</div>
            {organLevel && <div className={styles.rank}>{organLevel}</div>}
            {usable === 0 ? <div className={styles.usableNone}>无号</div> : ''}
          </div>
          <div className={styles.workInfo}>
            <span className={styles.organName}>{organName}</span>
            <span>{deptName}</span>
          </div>
          <div className={styles.introduce}>
            <img src={goodAt} alt="" className={styles.goodAt} />
            <div className={styles.introContent}>{profession || '-'}</div>
          </div>
          <div className={styles.commont}>
            好评率<span> {satisfaction}% </span> / 问诊量
            <span> {servTimes}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

const DoctorCard = (props: IProps) => {
  const { chronicDisease = {}, dispatch } = props;

  const { recommendedListData = [] } = chronicDisease;

  const location: any = useLocation();

  const [totalNum, setTotalNum] = useState(0);

  useEffect(() => {
    location?.query?.menuCode &&
      dispatch({
        type: 'chronicDisease/queryRecommendedList',
        payload: { pageSize: 10, pageNum: 1, chronicType: location?.query?.menuCode || '' },
        callback: (data: any) => {
          if (data?.total) {
            setTotalNum(data?.total);
          } else {
            setTotalNum(0);
          }
        },
      });
  }, []);

  const renderBow = (item) => {
    return (
      <div className={styles.listItem} key={item?.doctorId}>
        <DoctorItem item={item} location={location} />
      </div>
    );
  };

  const goMore = () => {
    history.push({
      pathname: '/chronicdisease/recommendedlist',
      query: location?.query,
    });
  };
  return (
    <>
      {recommendedListData?.length ? (
        <div className={styles.list_wrap}>
          <div className={styles.list_container}>
            <div className={styles.title}>
              <h3>推荐医生</h3>
              {totalNum > 10 && (
                <div className={styles.more} onClick={goMore}>
                  查看更多 <HxIcon iconName="arrow-right" className={styles.more_icon} />
                </div>
              )}
            </div>
            {recommendedListData?.map((item) => {
              return renderBow(item);
            })}
          </div>
        </div>
      ) : (
        ''
      )}
    </>
  );
};
export default connect(({ chronicDisease }: { chronicDisease: IChronicDiseaseModelState }) => ({
  chronicDisease,
}))(DoctorCard);
