.list_wrap {
  width: 100%;
  overflow-x: hidden;
  margin-top: 24px;
  padding: 0 24px 40px;

  .more {
    display: flex;
    align-items: center;
    color: #989eb4;

    .more_icon {
      color: #989eb4;
      font-size: 21px;
    }
  }

  .list_container {
    background-color: #fff;
    padding-bottom: 10px;
    border-radius: 16px;
  }

  .title {
    background: #fff;
    border-radius: 16px 16px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 32px 24px 8px;

    h3 {
      font-size: 36px;
      font-weight: bold;
      color: #03081a;
      line-height: 50px;
      padding: 0;
      margin: 0;
    }
  }
}

.doctorItem {
  position: relative;
  padding: 24px 24px 0 24px;
  background-color: #ffffff;
  position: relative;

  .item {
    display: flex;
    border-bottom: 2px solid rgba(235, 237, 245, 0.3);
  }

  .poster {
    width: 128px;
    height: 128px;
    margin-right: 24px;
    border-radius: 50%;
  }

  .tag_jisu {
    position: absolute;
    width: 92px;
    right: 0;
    top: 16px;
  }

  .doctorInfo {
    .basicInfo {
      display: flex;
      align-items: center;

      .doctorName {
        font-size: 36px;
        color: #03081a;
        font-weight: 600;
        margin-right: 16px;
      }

      .titleNmae {
        font-size: 28px;
        color: #03081a;
        margin-right: 24px;
      }

      .rank {
        background-color: #568df2;
        padding: 0 12px;
        color: #ffffff;
        border-radius: 8px;
        font-size: 18px;
        height: 32px;
        line-height: 32px;
        text-align: center;
      }

      .usableNone {
        font-size: 20px;
        color: #989eb4;
        padding: 10px 8px;
        background: #eff0f6;
        border-radius: 12px;
      }
    }

    .workInfo {
      font-size: 28px;
      color: #03081a;
      margin: 12px 0;

      .organName {
        margin-right: 16px;
      }
    }

    .introduce {
      font-size: 24px;
      width: 502px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      /*! autoprefixer: off */
      -webkit-box-orient: vertical;
      /* autoprefixer: on */
      -webkit-line-clamp: 2;
      text-indent: 55px;
      position: relative;

      .goodAt {
        width: 50px;
        height: 24px;
        position: absolute;
        left: 0;
        top: 5px;
      }

      .introContent {
        font-size: 24px;
        color: #989eb4;
      }
    }

    .commont {
      font-size: 24px;
      color: #989eb4;
      margin: 18px 0 34px 0;

      span {
        font-size: 32px;
        color: #3ad3c1;
        font-weight: bold;
      }
    }
  }
}

.fastLabel {
  width: 92px;
  height: 44px;
  position: absolute;
  top: 0;
  right: 0;
}
