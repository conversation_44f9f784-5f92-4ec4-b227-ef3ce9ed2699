import React from 'react';
import { HxIcon } from '@/components';
import styles from './index.less';

interface IProps {
  /** 标题 */
  title?: string;
  /** 是否展示查看更多按钮 */
  isShow?: boolean;
  /**
   * 点击事件，点击查看更多
   */
  toSeeMore?: React.MouseEventHandler<Element>;
}

const Title = (props: IProps) => {
  const { title, isShow, toSeeMore } = props;
  return (
    <div className={styles.titleItem}>
      <div className={styles.title}>
        <div className={styles.line} />
        <div className={styles.text}>{title}</div>
      </div>
      {isShow && (
        <div className={styles.more} onClick={toSeeMore}>
          <span>更多</span> <HxIcon iconName="arrow-right" />
        </div>
      )}
    </div>
  );
};
export default Title;
