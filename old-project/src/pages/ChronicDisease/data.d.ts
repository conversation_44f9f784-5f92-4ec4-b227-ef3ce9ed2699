import { Dispatch, IChronicDiseaseModelState } from 'umi';

export interface IProps {
  dispatch: Dispatch;
  chronicDisease: IChronicDiseaseModelState;
}

// 项目详情的服务包列表
export interface IServiceItem {
  /** 版本id */
  projectVersionId?: string | undefined;
  /** 服务包创建时间 */
  createTime?: string;
  /** 状态 */
  effectStatus?: number;
  /** 服务包id */
  packageId?: string;
  /** 项目简介 */
  projectIntroduction?: string;
  /** 服务包名称 */
  packageName?: string;
  /** 服务包价格 */
  packagePrice?: number;
  /** 服务对象 */
  serviceObject?: string;
  /** 项目名称 */
  projectName?: string;
  /** 医嘱项id */
  adviceItemId?: string;
  /** 服务包简介 */
  packageIntroduce?: string;
  /** 服务包列表 */
  pkgPackageList?: [];
  /** 医疗组id */
  pkgGroupId?: string;
  /** 团队名称 */
  groupName?: string;
}

// 服务包详情
export interface IServiceDetail {
  /** 服务包名称 */
  pkgName?: string;
  /** 服务内容 */
  content?: string;
  /** 医生简介 */
  doctorIntroduce?: string;
  /** 知情同意书 */
  informBookContent?: string;
  /** 服务包介绍 */
  packageIntroduce?: string;
  /** 医嘱项id */
  adviceItemId?: string;
  /** 有效期 */
  effectiveTime?: string;
  /** 服务包名称 */
  packageName?: string;
  /** 服务包价格 */
  adviceItemPrice?: string;
  /** 服务介绍 */
  serviceIntroduce?: string;
  /** 团队简介 */
  teamIntroduce?: string;
  /** 服务包 */
  pkgAndAdviceRespVO?: IpkgAndAdviceRespVO;
  /** 是否取消关联 */
  isRelation?: boolean;
}

export interface IpkgAndAdviceRespVO {
  /** 有效期 */
  effectiveTime?: string;
  /** 服务包名称 */
  packageName?: string;
  /** 服务包名称 */
  adviceItemPrice?: string;
  adviceItemId?: string;
}

export interface Icontent {
  content: string;
}

export interface ITeamInfo {
  teamInfo: string;
  doctorInfo: string;
}
export interface IServiceDesc {
  serviceDesc: string;
}

// 我的服务包列表页面
export interface ImyServiceList {
  /** 服务包历史id */
  packageHistoryId?: string | number;
  /** 服务包ID */
  packageId?: string | number;
  /** 服务包名称 */
  packageName?: string;
  /** 服务包价格 */
  packagePrice?: string | number;
  /** 有效期 */
  periodOfValidity?: string;
  /** 服务包开始服务时间 */
  serviceStartTime?: string;
  /** 服务包状态 */
  serviceStatusName?: string;
  /** 服务包购买记录id */
  buyRecordId?: string | number | undefined;
  /** 有效期单位 */
  periodOfValidityUnit?: string;
  /** 判断服务包是线上还是线下，1--线上--走3.5期新详情，2--线下--走老详情 */
  packageType?: number;
  orderAuthStatus?: number;
  orderAuthStatusName?: string;
}

// 服务包订单列表数据
export interface IserviceOrderList {
  /** 服务包订单id */
  id?: string | number;
  /** 下单日期 */
  orderDate?: string;
  /** 订单状态，1 待审核 2 待支付 3 已取消 4 已通过 5 已退款 */
  orderStatus?: string | number;
  /** 患者名称 */
  patientName?: string;
  /** 服务包名称 */
  pkgName?: string;
  /** 价格 */
  price?: string | number;
  /** 业务系统编号 */
  bizSysSeq?: string;
  /** 是否可购买 */
  canBuy?: boolean;
  /** 交易编号 */
  dealSeq?: string;
  /** 是否和使用中的服务包在同一项目当中 */
  inSameProject?: boolean;
  /** 商户编号 */
  merchantSeq?: string;
  /** 医嘱项id */
  adviceItemId?: string;
}

// 服务包订单详情数据
export interface IserviceOrderInfo {
  /** 医嘱项id */
  adviceItemId?: string | number | undefined;
  /** 业务系统编号 */
  bizSysSeq?: string | number | undefined;
  /** 年龄 */
  age?: number;
  /** 就诊卡卡号 */
  cardNo?: string;
  /** 审核人姓名 */
  checkPerson?: string;
  /** 审核时间 */
  checkTime?: string;
  /** 交易编号 */
  dealSeq?: string;
  /** 性别 */
  gender?: string | number | undefined;
  /** 商户编号 */
  merchantSeq?: string;
  /** 业务系统订单号 */
  orderSeq?: string;
  /** 订单状态，1 待审核 2 待支付 3 已取消 4 已通过 5 已退款 */
  orderStatus?: number;
  /** 下订单时间 */
  orderTime?: string;
  /** 患者姓名 */
  patientName?: string;
  /** 支付时间 */
  payTime?: string;
  /** 支付方式 */
  paymentSeq?: string;
  /** 手机号 */
  phone?: string;
  /** 服务包名称 */
  pkgName?: string;
  /** 金额 */
  price?: string | number;
  /** 退款时间 */
  refundTime?: string;
  /** 拒绝理由 */
  rejectReason?: string;
  /** 倒计时时间戳 */
  timeStamp?: string | number;
  /** 取消订单时间 */
  updateTime?: string;
  /** 服务包购买id */
  purchaseId?: string;
  /** 患者id */
  patientId?: string;
  /** 团队名称 */
  groupName?: string;
  /** 项目组id */
  pkgGroupId?: string;
}

// 我的服务包订单详情
export interface ImyServiceInfo {
  /** json字符串，前端解析 */
  content?: string;
  /** 服务结束时间 */
  endTime?: string;
  /** 服务剩余天数 */
  leftDay?: string | number;
  /** 患者姓名 */
  patientName?: string;
  /** 服务包名称 */
  pkgName?: string;
  /** 服务状态 */
  serviceStatus?: string | number;
  /** 服务开始时间 */
  startTime?: string;
  /** 服务包状态字段 */
  serviceStatusName?: string;
  /** 慢病复诊列表 */
  dateList?: [];
  /** 已使用的次数 */
  usedInquiryTimes?: number;
  /** 总次数 */
  inquiryTimes?: number;
  /** 在线复诊总次数团队 */
  teamInquiryTimes?: number;
  /** 已使用次数团队 */
  teamUsedInquiryTimes?: number;
  /** 服务包历史id */
  packageHistoryId?: string;
  /** 随访跳转到mobile项目的计划id */
  planInstanceId?: string;
  /** 性别状态  */
  gender?: number;
  /** 性别名称 */
  genderName?: string;
  /** 年龄 */
  age?: string;
  /** 连续服务天数 */
  continueServiceDay?: string;
  /** 患者id */
  patientId?: string;
  /** 三期服务包id */
  packageId?: string;
  /** 一期服务包id */
  servicepkgId?: string;
  /** 服务包版本 */
  packageVersion?: number;
  pmi?: string;
  /** 服务包购买id */
  pkgPurchaseId?: string;
}

// 购买服务包
export interface IBuypkg {
  /** 业务系统编号 */
  bizSysSeq?: string;
  /** 是否可购买 */
  canBuy?: boolean;
  /** 交易编号 */
  dealSeq?: string;
  /** 是否和使用中的服务包在同一项目当中 */
  inSameProject?: boolean;
  /** 商户编号 */
  merchantSeq?: string;
  /** 用公众号，生活号支付的时候需要 */
  openId?: string;
  /** 提示文案 */
  tip?: string;
}

// 老服务包订单列表
export interface IOldServiceList {
  /** 服务加成费 */
  addtionPrice?: number;
  /** 年齡 */
  age?: number;
  /** 审核时间 */
  auditTime?: string;
  /** 证件号 */
  cardNo?: string;
  /** 证件类型Code */
  cardType?: string;
  /** 证件类型名称 */
  cardTypeName?: string;
  /** 创建时间 */
  createTime?: string;
  /** 服务时长 */
  duration?: number;
  /** 服务时长类型 */
  durationUnit?: number;
  /** 服务到期时间 */
  expriedTime?: string;
  /** 是否已评论 0:否,1:是 */
  hasComment?: number;
  /** 对已退款的订单进行历史服务包订单状态记录-2审核不通过退款(包括超时检测的)-7服务中的进行终止并退款-5以结束的进行终止并退款 */
  historyStatus?: number;
  /** 服务包配图 */
  imageUrl?: string;
  /** 服务包详情介绍 */
  introduction?: string;
  /** 患者姓名 */
  patientId?: string;
  /** 患者姓名 */
  patientName?: string;
  /** 退款原因 */
  refundRemark?: string;
  /** 审核未通过原因 */
  repulseRemark?: string;
  /** 在团队中的角色识别码 */
  roleCode?: string;
  /** 服务包简介 */
  serviceDescription?: string;
  /** 服务包ID */
  serviceId?: string | number;
  /** 服务包名称 */
  serviceName?: string;
  /** 服务包订单id */
  serviceOrderId?: string | number;
  /** 服务包价格 */
  servicePrice?: number;
  /** 服务包订单状态–4服务中-5以结束-7已终止 */
  serviceStatus?: number;
  /** 服务包类型 1:个人,2:团队 */
  serviceType?: number;
  /** 服务人ID 医生的doctorId或者团队id */
  servicerId?: string | number;
  /** 服务人名称 */
  servicerName?: string;
  /** 总费用 */
  totalPrice?: number;
}

export interface IdoctorItem {
  /** 医生姓名 */
  doctorName?: string;
  /** 医生职称 */
  titleName?: string;
  /** 医生简介 */
  introduction?: string;
  /** 医生角色 */
  role?: string;
  /** 医生编号 */
  doctorInfoId?: string;
  /** 医生头像 */
  portrait?: string;
}

export interface ICardItem {
  /** 姓名 */
  patientName?: string;
  /** 年龄 */
  age?: number;
  /** 银医卡底部横线 */
  bankCardBottom?: string;
  /** 银医卡图标 */
  bankCardIcon?: string;
  /** 银医卡名称 */
  bankName?: string;
  /** 就诊卡ID */
  cardId?: string;
  /** 就诊卡号 */
  cardNo?: string;
  /** 就诊卡类型 */
  cardTypeCode?: string;
  /** 就诊卡类型描述 */
  cardTypeDesc?: string;
  /** 证件号 */
  credNo?: string;
  /** 性别 */
  gender?: number;
  /** 是否本人 */
  isSelf?: number;
  /** 机构编码 */
  organCode?: string;
  pmi?: string;
  pmiNo?: string;
  organPmi?: string;
  /** 电子健康卡卡号 */
  qrCode?: string;
  /** 是否实名 */
  realName?: boolean;
  /** 就诊卡状态 */
  status?: number;
  /** 是否支持电子健康卡 */
  openEhCard?: boolean;
}

// 学透通获取代办事项和工具地址
export interface ITodoDataType {
  /** 待办数据列表 */
  items?: ITodoItemType[];
  /** 工具地址集合  */
  toolInfoList?: ITodoType[];
  /** 患者id */
  patientId?: string;
  /** 待办数 */
  pendingNum?: number;
  /** 患者pmi */
  pmi?: string;
}
export interface ITodoItemType {
  /** 事项操作按钮标题 */
  btnTitle?: string;
  /** 事项内容 */
  content?: string;
  /** 事项图片地址 */
  imgUrl?: string;
  /** 是否已读 1-已读，0-未读 */
  isRead?: string;
  /** 记录时间 */
  recDate?: string;
  /** 事项标题 */
  title?: string;
  /** 事项详情地址 */
  url?: string;
}
export interface ITodoType {
  channelCode?: string;
  channelName?: string;
  createTime?: number;
  id?: string;
  /** 医学小工具type类型 11-慢病档案，12-居家记录，13-医学小工具，14-查看就诊全景 */
  type?: number;
  typeName?: string;
  updateTime?: number;
  /** 跳转学透通的地址 */
  url?: string;
}

// 患者已购买服务包详情服务内容数据类型
export interface IMyServiceSeConType {
  /** 服务内容数据列表 */
  itemsList?: IConListType[];
  /** 服务包版本 */
  packageVersion?: number;
  /** 患者pmi */
  pmi?: string;
  /** 一期服务包id */
  servicepkgId?: string;
}

export interface IConListType {
  /** 项目总服务次数 */
  cnt?: number;
  /** 服务内容名称 */
  itemName?: string;
  /** 服务内容类型 */
  type?: string;
  /** 已使用的内容服务次数 */
  usedCnt?: number;
}

export interface IServiceConType {
  /** 是否限次数 0-不限次数，1-有限次数 */
  isLimit?: number;
  /** 服务说明  */
  serviceDesc?: string;
  /** 项目总次数  */
  times?: number;
  /** 具体根据dictionary的SERVICE_CONTENT_TYPES确定  */
  type?: number;
  /** 弹框提示文案 */
  promptText?: string;
}
