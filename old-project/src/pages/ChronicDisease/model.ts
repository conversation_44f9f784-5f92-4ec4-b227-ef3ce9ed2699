import { Reducer } from 'redux';
import { Effect } from 'umi';
import { createAction } from '@/utils/common';
import {
  queryProjectInfo,
  queryServiceDetail,
  queryMyServiceList,
  queryServiceOrderList,
  queryServiceOrderDetail,
  queryChronicAppointList,
  cancelOrder,
  buyServicePackage,
  clearcache,
  queryOrderForUserId,
  isSole,
  checkinquiry,
  queryzxfzrecord,
  toinquiry,
  selectPatient,
  preBuypkg,
  cardList,
  getservicecontent,
  getpendingitem,
  isAuthorization,
  userAuthorization,
  recommendedList,
  purchasepkgCanSell,
  purchasepkgPageConfig,
} from './service';
import {
  IServiceItem,
  IServiceDetail,
  ImyServiceList,
  IserviceOrderList,
  ImyServiceInfo,
  IserviceOrderInfo,
  IBuypkg,
  IOldServiceList,
  ITodoDataType,
  IMyServiceSeConType,
} from './data.d';
import { sensorsRequest } from '@/utils/sensors';

export interface IChronicDiseaseModelState {
  projectDetail: IServiceItem;
  serviceDetail: IServiceDetail;
  myServiceList: ImyServiceList[];
  serviceOrderList: IserviceOrderList[];
  serviceOrderInfo: IserviceOrderInfo;
  myServiceInfo: ImyServiceInfo;
  // appointList: {}; // 慢病复诊数据还没有定义
  isOnline: {}; // 服务包是否下架还没有定义
  bugpkg: IBuypkg;
  cancel: {};
  oldServiceList: IOldServiceList[];
  CheckInquiryRespVO: {}; // 没有定义数据
  receptionTimes: {};
  toinquer: {};
  patient: [];
  prebugpkg: {}; // 购买服务包前的判断还没有定义数据
  // myoldServiceList: {}; // 我的服务包 老服务包列表还未定义
  myServiceSeCon: IMyServiceSeConType;
  todoListData: ITodoDataType;
  authCode: {
    code?: string;
  };
  buttonflag: number;
  tabs: boolean;
  receptionTimesObj: {};
  recommendedListData: any;
}

export interface IChronicDiseaseModel {
  namespace: 'chronicDisease';
  state: IChronicDiseaseModelState;
  effects: {
    queryProjectInfo: Effect;
    queryServiceDetail: Effect;
    queryMyServiceList: Effect;
    queryServiceOrderList: Effect;
    queryServiceOrderDetail: Effect;
    queryChronicAppointList: Effect;
    isSole: Effect;
    cancelOrder: Effect;
    buyServicePackage: Effect;
    clearcache: Effect;
    queryOrderForUserId: Effect;
    switchoverTabs: Effect;
    checkinquiry: Effect;
    queryzxfzrecord: Effect;
    selectPatient: Effect;
    toinquiry: Effect;
    tabs: Effect;
    preBuypkg: Effect;
    cardList: Effect;
    getservicecontent: Effect;
    getpendingitem: Effect;
    isAuthorization: Effect;
    userAuthorization: Effect;
    queryRecommendedList: Effect;
    purchasepkgCanSell: Effect;
    purchasepkgPageConfig: Effect;
  };
  reducers: {
    updateState: Reducer<IChronicDiseaseModelState>;
    save: Reducer<any>;
  };
}

const ChronicDisease: IChronicDiseaseModel = {
  namespace: 'chronicDisease',

  state: {
    projectDetail: {},
    serviceDetail: {},
    myServiceList: [],
    myServiceInfo: {},
    serviceOrderList: [],
    serviceOrderInfo: {},
    isOnline: {},
    oldServiceList: [],
    cancel: {},
    bugpkg: {},
    CheckInquiryRespVO: {},
    receptionTimes: {},
    toinquer: {},
    patient: [],
    prebugpkg: {},
    myServiceSeCon: {},
    todoListData: {},
    authCode: {},
    buttonflag: 1,
    tabs: true,
    receptionTimesObj: {},
    recommendedListData: [],
  },

  effects: {
    // 项目详情
    *queryProjectInfo({ payload, callback }, { put }) {
      const res = yield queryProjectInfo({ ...payload }) || [];
      yield put(
        createAction('updateState')({
          projectDetail: res?.data,
        }),
      );
      callback && callback(res);
    },
    // 服务包详情
    *queryServiceDetail({ payload, callback }, { put }) {
      const res = yield queryServiceDetail({ ...payload }) || [];
      yield put(
        createAction('updateState')({
          serviceDetail: res?.data,
        }),
      );
      callback && callback(res);
    },
    // 我的服务包订单列表 慢病服务包
    *queryMyServiceList({ payload }, { put }) {
      const data = yield queryMyServiceList({ ...payload }) || [];
      yield put(
        createAction('updateState')({
          myServiceList: data,
        }),
      );
    },
    // 服务包订单列表
    *queryServiceOrderList({ payload }, { put }) {
      const data = yield queryServiceOrderList({ ...payload }) || {};
      const { pkgOrderRespVOList = [] } = data || {};
      yield put(
        createAction('updateState')({
          serviceOrderList: pkgOrderRespVOList,
        }),
      );
    },
    // 服务包订单详情
    *queryServiceOrderDetail({ payload }, { put }) {
      const data = yield queryServiceOrderDetail({ ...payload }) || [];
      yield put(
        createAction('updateState')({
          serviceOrderInfo: data,
        }),
      );
    },
    // 查询慢病复诊列表数据
    *queryChronicAppointList({ payload, callback }, { put }) {
      const data = yield queryChronicAppointList({ ...payload }) || [];
      yield put(
        createAction('updateState')({
          myServiceInfo: data,
        }),
      );
      callback && callback(data);
    },
    // 查询慢病复诊列表数据
    *queryRecommendedList({ payload, callback }, { put }) {
      const res = yield recommendedList({ ...payload }) || [];
      const { data = {} } = res || {};
      yield put(
        createAction('updateState')({
          recommendedListData: data?.list,
        }),
      );
      callback && callback(data);
    },
    // 服务包订单立即购买查询服务包是否已下架
    *isSole({ payload, callback }, { put }) {
      const res = yield isSole({ ...payload }) || [];
      yield put(
        createAction('updateState')({
          isOnline: res?.data,
        }),
      );
      callback && callback(res);
    },
    // 服务包订单取消订单
    *cancelOrder({ payload, callback }, { call, put }) {
      const res = yield call(cancelOrder, payload);
      yield put(
        createAction('updateState')({
          cancel: res?.data,
        }),
      );
      callback && callback(res);
    },
    // 购买服务包
    *buyServicePackage({ payload, callback }, { call, put }) {
      const res = yield call(buyServicePackage, payload);
      yield put(
        createAction('updateState')({
          bugpkg: res?.data,
        }),
      );
      callback && callback(res);
    },
    // 删除缓存
    *clearcache({ payload, callback }, { put }) {
      const res = yield clearcache({ ...payload }) || [];
      yield put(
        createAction('updateState')({
          delete: res?.data,
        }),
      );
      callback && callback(res);
    },
    // 原生啊老服务包列表
    *queryOrderForUserId({ payload }, { put }) {
      const data = yield queryOrderForUserId({ ...payload }) || [];
      yield put(
        createAction('updateState')({
          oldServiceList: data,
        }),
      );
    },

    // 我的服务包详情页面发起慢病复诊 ---检查患者在同一服务包是否有正在咨询记录
    *checkinquiry({ payload, callback }, { put }) {
      const data = yield checkinquiry({ ...payload }) || {};
      yield put(
        createAction('updateState')({
          CheckInquiryRespVO: data,
        }),
      );
      callback && callback(data);
    },
    // 我的服务包详情页面发起慢病复诊 ---查询患者服务包的在线复诊记录
    *queryzxfzrecord({ payload }, { put }) {
      const data = yield queryzxfzrecord({ ...payload }) || [];
      yield put(
        createAction('updateState')({
          receptionTimesObj: data,
        }),
      );
    },
    // 我的服务包详情页面发起慢病复诊 ---发起在线复诊
    *toinquiry({ payload }, { put }) {
      const data = yield toinquiry({ ...payload }) || [];
      yield put(
        createAction('updateState')({
          toinquer: data,
        }),
      );
    },
    // 选择就诊人
    *selectPatient({ payload, callback }, { put }) {
      const res = yield selectPatient({ ...payload }) || [];
      yield put(
        createAction('updateState')({
          patient: res?.data,
        }),
      );
      callback && callback(res);
    },
    // 购买服务包前的判断
    *preBuypkg({ payload, callback }, { call, put }) {
      const res = yield call(preBuypkg, payload);
      yield put(
        createAction('updateState')({
          prebugpkg: res?.data,
        }),
      );
      callback && callback(res);
    },
    // 选择就诊卡
    *cardList({ payload, callback }, { call, put }) {
      sensorsRequest('VIDEO_PAGE_CARD', payload);
      const res = yield call(cardList, payload);
      yield put(createAction('updateState')({ wxpramsurl: window.location.href }));
      res && callback(res);
    },
    *switchoverTabs({ payload }, { put }) {
      yield put({ type: 'updateState', payload: { buttonflag: payload } });
    },
    *tabs({ payload }, { put }) {
      yield put({ type: 'updateState', payload: { tabs: payload } });
    },
    *getservicecontent({ payload }, { put }) {
      const data = yield getservicecontent({ ...payload }) || [];
      // console.log('283=====>', data);
      yield put(
        createAction('updateState')({
          myServiceSeCon: data,
        }),
      );
    },
    *getpendingitem({ payload }, { put }) {
      const data = yield getpendingitem({ ...payload }) || {};
      yield put(
        createAction('updateState')({
          todoListData: data,
        }),
      );
    },
    *isAuthorization({ payload, callback }, { put }) {
      const res = yield isAuthorization({ ...payload }) || {};
      yield put(
        createAction('updateState')({
          authDate: res?.data,
        }),
      );
      callback && callback(res);
    },
    *userAuthorization({ payload, callback }, { put }) {
      const data = yield userAuthorization({ ...payload }) || {};
      yield put(
        createAction('updateState')({
          authCode: data,
        }),
      );
      callback && callback(data);
    },
    *purchasepkgCanSell({ payload, callback }, { put }) {
      const data = yield purchasepkgCanSell({ ...payload }) || {};
      callback && callback(data);
    },
    *purchasepkgPageConfig({ payload, callback }, { put }) {
      const data = yield purchasepkgPageConfig({ ...payload }) || {};
      callback && callback(data);
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
    save(state, { payload }) {
      return { ...state, ...payload };
    },
  },
};

export default ChronicDisease;
