// import React from 'react';
// import qs from 'query-string';
// import { HxRedirect, getModuleHomeUrl } from '@/utils/interceptor';
// import { ModuleEnum } from '@/utils/enum';

// // 是否需要选卡
// const needChooseCard = true;

// // 是否需要访问口令
// const needAccessToken = false;
// // 组装参数
// const query: any = qs.parse(window.location.href.split('?')[1]) || {};
// // const { type = '' } = query; // 我的服务包和服务包订单区分，分别是 OWN--->我的服务包，ORDER------->服务包订单
// query.needAccessToken = needAccessToken;
// // const a = type[0];
// // console.log('type======', type);
// // console.log('query======', query);

// // 重定向地址
// const redirectUrl = '/chronicdisease/projectdetail';
// // const defaultParamsOne = {
// //   pathname: needChooseCard ? getModuleHomeUrl(ModuleEnum.MODULE_PATIENT_CARD) : redirectUrl,
// //   search: needChooseCard ? `redirect=/chronicdisease/myservice&${qs.stringify(query)}` : `${qs.stringify(query)}`,
// // };
// const defaultParams = {
//   pathname: needChooseCard ? getModuleHomeUrl(ModuleEnum.MODULE_PATIENT_CARD) : redirectUrl,
//   search: needChooseCard ? `redirect=/chronicdisease/service/order&${qs.stringify(query)}` : `${qs.stringify(query)}`,
// };

// // const defaultParams = type === 'OWN' ? defaultParamsOne : type === 'ORDER' ? defaultParamsTwo : '';

// export default () => {
//   return <HxRedirect params={defaultParams} />;
// };
