.container {
  min-height: 100vh;
  background: @bg-color-gray;
  .projectInfo {
    padding: 30px;
    background-color: #fff;
    .projectName {
      color: #333;
      font-weight: 600;
      font-size: 30px;
    }
    .basicInfo {
      margin-top: 20px;
      padding: 0.1px 30px 30px 30px;
      background: #f9f9f9;
      .intro {
        margin-top: 30px;
        color: #32b9aa;
        font-weight: 600;
        .title {
          padding-left: 14px;
        }
      }
      .projectText {
        margin-top: 20px;
        color: #333;
        font-size: 26px;
      }
    }
  }
  .serviceList {
    margin-top: 20px;
    background: #fff;
    .serviceListName {
      padding: 30px 30px 40px 30px;
      color: #333;
      font-weight: 600;
      font-size: 28px;
    }
    .seerviceItem {
      background: #fff;
      .item {
        display: flex;
        justify-content: flex-start;
        padding: 20px 30px;
        background-color: #fff;
        border-bottom: 1px solid #eee;
        .servicePic {
          width: 90px;
          height: 90px;
        }
        .serviceInfo {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 580px;
          margin-top: -8px; // 文字和图片对齐
          margin-left: 20px;
          .serviceName {
            color: #333;
            font-size: 28px;
          }
          .serviceCon {
            width: 520px;
            overflow: hidden;
            color: #999;
            font-size: 26px;
            white-space: nowrap;
            text-overflow: ellipsis;
          }
          .servicePrice {
            margin-top: 8px;
            color: #f47f1b;
            font-size: 26px;
          }
          .arrow {
            color: #999;
          }
        }
      }
      .noMore {
        height: 90px;
        color: #ddd;
        font-size: 24px;
        line-height: 90px;
        text-align: center;
      }
    }
  }
  :global(.am-list-body::before) {
    background-color: transparent !important;
  }
  .contentImg {
    width: 100%;
    height: 100%;
  }
  :global(.am-list-view-scrollview) {
    min-height: 100vh;
    overflow: hidden;
  }
}
