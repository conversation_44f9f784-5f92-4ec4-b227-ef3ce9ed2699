// 脱敏治疗
export const DESENSITIZA_LIST = [
  // {
  //   code: 1,
  //   title: '尘螨皮下',
  //   price: '4996',
  // },
  // {
  //   code: 2,
  //   title: '尘螨舌下',
  //   price: '3073',
  // },
  // {
  //   code: 3,
  //   title: '花粉A1',
  //   price: '3384',
  // },
  // {
  //   code: 4,
  //   title: '花粉A2',
  //   price: '5224',
  // },
  // {
  //   code: 5,
  //   title: '霉菌B1',
  //   price: '3930',
  // },
  // {
  //   code: 6,
  //   title: '霉菌B2',
  //   price: '5770',
  // },
  // {
  //   code: 7,
  //   title: '花粉联合尘螨或霉菌（C1套餐）',
  //   price: '3930',
  // },
  // {
  //   code: 8,
  //   title: '花粉联合尘螨或霉菌皮下C2套餐',
  //   price: '5770',
  // },
  // {
  //   code: 9,
  //   title: '多重过敏原（D1套餐）',
  //   price: '4476',
  // },
  // {
  //   code: 10,
  //   title: '多重过敏原（D2套餐）',
  //   price: '6316',
  // },
];
// 消化道早癌
export const DIGESTION_LIST = [
  // {
  //   code: 1,
  //   title: '消化道早癌A包',
  //   price: '1824',
  // },
  // {
  //   code: 2,
  //   title: '消化道早癌B包',
  //   price: '2624',
  // },
];

// 起搏器 pacemaker
export const PACEMAKER_LIST = [
  // {
  //   code: 1,
  //   title: '普通起搏器A包',
  //   price: '961',
  // },
  // {
  //   code: 2,
  //   title: '普通起搏器B包',
  //   price: '2129',
  // },
  // {
  //   code: 3,
  //   title: 'ICDA包',
  //   price: '2031',
  // },
  // {
  //   code: 4,
  //   title: 'CRT/CRT-D A包',
  //   price: '2668',
  // },
  // {
  //   code: 5,
  //   title: 'CRT/CRT-D B包',
  //   price: '3700',
  // },
];

// 冠心病 heart
export const HEART_LIST = [
  // {
  //   code: 1,
  //   title: '心脏内科冠心病A包',
  //   price: '3354',
  // },
  // {
  //   code: 2,
  //   title: '心脏内科冠心病B包',
  //   price: '1560',
  // },
];

// 心律失常 arrhythmology
export const ARRHYTHMOLOGY_LIST = [
  // {
  //   code: 1,
  //   title: '心内科心律失常A包',
  //   price: '1082',
  // },
  // {
  //   code: 2,
  //   title: '心内科心律失常B包',
  //   price: '2066',
  // },
];
