import Mock from 'mockjs';
import { commonSuccessResponse } from '../../../mock/util';

// const { Random } = Mock;

const fakeCardList = (req: any, res: any) => {
  const response = Mock.mock({
    ...commonSuccessResponse,
    data: {
      createTime: '@datetime()',
      projectName: '服务项目的名称服务项目的名称服务项目的名称服务项目的名称',
      projectVersionId: '212323',
      projectIntroduction: '通过系统的患者管理，提高患者依从性，简化就诊流程并提升患者诊疗效果及满意度。',
      serviceObject:
        '为了促进对患者管理中医患双方的有效互动，优化个体化治疗方案、保证医疗服务的连续性，提高患者依从性和满意度、优化患者的健康管理，并且便于科研数据收集。',
      'pkgPackageList|10': [
        {
          'serviveId|1-5': 5,
          serviceName: '@ctitle',
          serviceCon: '@cparagraph',
          price: '@integer(100,2000)',
        },
      ],
    },
  });
  // 模拟请求延迟
  setTimeout(() => {
    return res.status(200).send(response);
  }, 10);
};

export default {
  'POST chronicdisease/doctor/findprojectdetail': fakeCardList,
};
