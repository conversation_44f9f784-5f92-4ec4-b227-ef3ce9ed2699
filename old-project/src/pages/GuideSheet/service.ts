import request from '@/utils/request';
import { HxParameter } from '@/utils/parameter';

let node = '/cloud';
if (APP_ENV === 'prod') {
  const { organCode } = HxParameter;
  node = organCode === 'HID0101' || organCode === 'HYT' ? `${API_HXYY}/cloud` : `${API_BASE}/cloud`;
}

// 查询导诊单列表
export const getGuideList = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/medical/op/list/guideDetail`, {
    method: 'POST',
    data,
  });

// 查询导诊单详情
export const getGuideDetail = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/medical/op/query/guideDetail`, {
    method: 'POST',
    data,
  });

// 支付成功后回调，通过orderId查询数据
export const getInfoByOrderId = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/medical/op/list/listGuidePaySuccess`, {
    method: 'POST',
    data,
  });
