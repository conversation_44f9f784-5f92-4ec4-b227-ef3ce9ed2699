import { Reducer } from 'redux';
import { Effect } from 'umi';
import { createAction } from '@/utils/common';
import * as guideSheetApi from './service';
import { IGuideListItem, IGuideSheetContent, IGetInfoByOrderId } from './data.d';

export interface IGuideSheetState {
  /** 导诊单列表 */
  guideList: IGuideListItem;
  /** 导诊单详情 */
  guideDetail: IGuideSheetContent;
  /** 通过orderId查询信息 */
  getInfoByOrderId: IGetInfoByOrderId;
}

export interface IGuideSheetModel {
  namespace: 'guideSheet';
  state: IGuideSheetState;
  effects: {
    getGuideList: Effect;
    getGuideDetail: Effect;
    getInfoByOrderId: Effect;
  };
  reducers: {
    updateState: Reducer<IGuideSheetState>;
  };
}

const guideSheetModel: IGuideSheetModel = {
  namespace: 'guideSheet',
  state: {
    guideList: {},
    guideDetail: {},
    getInfoByOrderId: {},
  },
  effects: {
    // 查询导诊单列表
    *getGuideList({ payload, callback }, { call, put }) {
      const res = yield call(guideSheetApi.getGuideList, payload);
      yield put(createAction('updateState')({ guideList: res }));
      res && callback(res);
    },
    // 查询导诊单详情
    *getGuideDetail({ payload, callback }, { call, put }) {
      const res = yield call(guideSheetApi.getGuideDetail, payload);
      yield put(createAction('updateState')({ guideDetail: res }));
      res && callback(res);
    },
    // 支付成功后回调，通过orderId查询数据
    *getInfoByOrderId({ payload, callback }, { call, put }) {
      const res = yield call(guideSheetApi.getInfoByOrderId, payload);
      yield put(createAction('updateState')({ getInfoByOrderId: res }));
      res && callback(res);
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return { ...state, ...payload };
    },
  },
};

export default guideSheetModel;
