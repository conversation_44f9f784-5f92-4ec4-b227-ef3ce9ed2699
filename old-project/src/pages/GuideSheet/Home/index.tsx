import React, { PureComponent } from 'react';
import { HxIcon, HxEmpty, HxIndicator, HxListView } from '@/components';
import { history, connect, Loading, IGuideSheetState } from 'umi';
import { Dispatch, AnyAction } from 'redux';
import { ConstantEnum } from '@/utils/enum';
import _ from 'lodash';
import { IGuideSheetContent } from '../data.d';
import styles from './index.less';
import PatientCard from '@/components/PatientCard';
import { getCardList } from '@/pages/AppointmentHx/service';
import TZPhysicalReport from '@/pages/TZPhysicalReport';
import { Toast } from 'antd-mobile-v5';

export interface IProps {
  dispatch: Dispatch<AnyAction>;
  loading: boolean;
  location: {
    query: {
      data: string;
      bizDealSeq: string;
    };
  };
}

interface IState {
  cardId: string;
  pAPMI: string;
  /** 是否加载中 */
  isLoading: boolean;
  /** 页码 */
  pageNum: number;
  /** 条数 */
  pageSize: number;
  /** 总页数 */
  // totalPages: number;
  /* 总条数 */
  total: number;
  /** 是否有更多数据 */
  hasMore: boolean;
  /** 待缴费订单列表列表 */
  guideSheetList: IGuideSheetContent[];
  patientInfo: any;
}

class GuideSheet extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      cardId: '',
      pAPMI: '',
      isLoading: true,
      hasMore: true,
      pageNum: ConstantEnum.PAGENUM,
      pageSize: ConstantEnum.PAGESIZE,
      // totalPages: 0,
      guideSheetList: [],
      total: 0,
      patientInfo: {},
    };
  }

  componentDidMount() {
    const {
      location: {
        query: { data = '', bizDealSeq = '' },
      },
    } = this.props;
    console.log('componentDidMount----', data, bizDealSeq);
    if (bizDealSeq === '') {
      if (data) {
        const { cardId = '', pmi = '', papmi = '' } = JSON.parse(data) || {};
        this.setState(
          {
            cardId,
            pAPMI: pmi || papmi,
          },
          () => {
            this.queryCardData(cardId);
          },
        );
      } else {
        this.setState(
          {
            patientInfo: { a: 1 },
          },
          () => {
            // this.handleDataFromChild();
          },
        );
      }
    } else {
      // 支付回调, 通过orderId查询相关信息
      this.props.dispatch({
        type: 'guideSheet/getInfoByOrderId',
        payload: {
          orderId: bizDealSeq,
        },
        callback: (res: any) => {
          const { cardId = '', pAPMI = '' } = res;
          this.setState(
            {
              cardId,
              pAPMI,
            },
            () => {
              this.getGuideList(1, cardId, pAPMI);
            },
          );
        },
      });
    }
  }

  queryCardData = async (cardId) => {
    const data = await getCardList({});
    const { userCardList = [] } = data || {};
    const patientInfo = userCardList.find((item) => item.cardId === cardId); //路由传来的cardId
    if (patientInfo?.cardId) {
      this.setState(
        {
          patientInfo,
          guideSheetList: [], // 清空旧数据
        },
        () => {
          this.getGuideList(1, cardId, patientInfo?.pmi);
        },
      );
    } else {
      Toast.show('暂无此就诊卡');
      console.log('loading---');
      this.setState(
        {
          patientInfo: null,
          guideSheetList: [], // 清空旧数据
        },
        () => {
          // this.handleDataFromChild(null);
        },
      );
    }
  };
  /** 查询导诊单列表 */
  getGuideList = (pageNum = 1, cardId: string, pAPMI: string) => {
    const { pageSize } = this.state;
    this.props.dispatch({
      type: 'guideSheet/getGuideList',
      payload: {
        cardId,
        pAPMI,
        pageNum,
        pageSize,
      },
      callback: (res: any) => {
        if (res.content) {
          const { content = [], totalPages = 0, total = 0 } = res;
          const { guideSheetList: oldPendingOrderList = [] } = this.state;
          this.setState({
            isLoading: false,
            pageNum,
            // totalPages,
            guideSheetList: [...oldPendingOrderList, ...content],
          });
          if ([...oldPendingOrderList, ...content].length === total) {
            this.setState({ hasMore: false });
          }
        }
      },
    });
  };

  /** 加载后一页 */
  loadMore = () => {
    const { isLoading, hasMore, pageNum, cardId, pAPMI } = this.state;
    if (isLoading || !hasMore) {
      return;
    }
    this.setState({ isLoading: false });
    setTimeout(() => {
      this.getGuideList(pageNum + 1, cardId, pAPMI);
    }, 200);
  };
  handleDataFromChild = (data: any = {}) => {
    const { cardId } = this.state;
    console.log('子组件回调----', data, cardId);
    const { pmi = '', papmi = '' } = data;
    this.setState(
      {
        cardId: data?.cardId,
        pAPMI: pmi || papmi,
        guideSheetList: [], // 清空旧数据
      },
      () => {
        this.getGuideList(1, data?.cardId, pmi);
      },
    );
  };
  /** 上拉至底触发 */
  onEndReached = () => {
    const { isLoading } = this.state;
    !isLoading && this.loadMore();
  };

  /** 跳转至详情页 */
  gotToGuidanceDetails = (item: IGuideSheetContent) => {
    // console.log('item===', item);
    const { guideInfoId = '' } = item;
    const { cardId = '' } = this.state;
    const {
      location: {
        query: { data = '{}' },
      },
    } = this.props;
    const { pmiNo = '' } = JSON.parse(data) || {};
    history.push({
      pathname: '/guidesheet/detail',
      query: {
        guideInfoId,
        cardId,
        pmiNo,
      },
    });
  };

  render() {
    const { loading } = this.props;
    // const { content = [] } = guideList;
    const { guideSheetList = [], pageSize, isLoading, hasMore, patientInfo = {} } = this.state;

    const ListItem = (rowData: IGuideSheetContent) => {
      const { admTime = '', hospitalName = '', patientName = '' } = rowData;

      const v = [
        {
          title: '就诊医院',
          value: hospitalName,
        },
        {
          title: '患者',
          value: patientName,
        },
      ];

      return (
        <div className={styles.guidanceListMain} onClick={() => this.gotToGuidanceDetails(rowData)}>
          <div className={styles.header}>
            <span>{admTime || ''}</span>
            <span>
              详情
              <HxIcon iconName="arrow-right" className={styles.img} />
            </span>
          </div>
          {_.map(v, (items: any) => {
            return (
              <div className={styles.info} key={items.title}>
                <div className={styles.infoTitle}>
                  <div className={styles.infoTitleValue}>
                    {_.map(items.title.split(''), (ite: string) => {
                      return <span key={ite}>{ite}</span>;
                    })}
                  </div>
                  <span>：</span>
                </div>
                <span className={styles.infoValue}>{items.value}</span>
              </div>
            );
          })}
          {/* <div className={styles.info}>
            医院：<span>{hospitalName}</span>
          </div>
          <div className={styles.info}>
            患者：<span>{patientName}</span>
          </div> */}
        </div>
      );
    };
    console.log('this.render', patientInfo, patientInfo !== null && Object.keys(patientInfo).length !== 0);
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          {patientInfo !== null && Object.keys(patientInfo).length !== 0 && (
            <PatientCard onDataReady={this.handleDataFromChild} patientInfo={patientInfo} />
          )}
        </div>
        {loading && isLoading ? (
          <HxIndicator />
        ) : guideSheetList.length > 0 ? (
          <HxListView
            dataSource={guideSheetList}
            renderRow={ListItem}
            initialListSize={pageSize}
            pageSize={pageSize}
            onEndReached={() => this.onEndReached()}
            onEndReachedThreshold={20}
            isRenderFooter
            hasMore={hasMore}
          />
        ) : (
          <div className={styles.hxEmpty}>
            <HxEmpty emptyMsg="暂无导诊单" emptyIcon="norecord" canRefresh={false} />
          </div>
        )}
      </div>
    );
  }
}

export default connect(({ loading, guideSheet }: { guideSheet: IGuideSheetState; loading: Loading }) => ({
  loading: loading.models.guideSheet,
  guideSheet,
}))(GuideSheet);
