@import '~@/styles/mixin.less';

.container {
  box-sizing: border-box;
  height: 100%;
  padding: 21px 30px;
  font-family: PingFang SC;
  background: #f5f6f7;
  .header {
    margin-bottom: 24px;
  }
  .guidanceListMain {
    box-sizing: border-box;
    margin-bottom: 20px;
    padding: 0 30px 20px 30px;
    background: #fff;
    border: 1px solid #e5e5e5;
    border-radius: 16px;

    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 81px;
      margin-bottom: 22px;
      color: #666;
      font-size: 26px;
      line-height: 81px;
      .hxOnepxB();

      span:nth-child(2) {
        display: flex;
        align-items: center;

        .img {
          width: 25px;
          height: 35px;
          margin-left: 5px;
        }
      }
    }

    .info {
      display: flex;
      margin-bottom: 10px;

      .infoTitle {
        display: flex;
        span {
          font-size: 28px;
          color: #989eb4;
        }

        .infoTitleValue {
          width: 120px;
          display: flex;
          justify-content: space-between;
          font-size: 28px;
        }
      }

      .infoValue {
        font-size: 28px;
        color: #03081a;
      }
    }

    .info:last-child {
      margin-bottom: 0;
    }
  }

  // 长列表滚动条高度
  :global(.am-list-view-scrollview) {
    height: calc(100vh - 42px);
  }

  // 取消白色背景色
  :global(.am-list-body) {
    background-color: #f5f6f7;
  }
}
.hxEmpty {
  margin-top: 30%;
}
