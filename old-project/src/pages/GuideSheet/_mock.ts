import Mock from 'mockjs';
import { commonSuccessResponse } from '../../../mock/util';

const { Random } = Mock;

// 导诊单列表
const getGuideList = (req: any, res: any) => {
  const response = Mock.mock({
    ...commonSuccessResponse,
    data: {
      asc: true,
      'content|3-5': [
        {
          'admId|155555-999999999': 131564,
          admTime: () => Random.datetime(),
          'age|15-35': 17,
          alipayInsurance: 0,
          'cardNo|202194616936697856-203194616936697856': 202094616936697856,
          guideType: '门诊',
          'guideInfoId|202194616936697856-203194616936697856': 202094616936697856,
          'gender|+1': ['男', '女'],
          hospitalCode: 'HID0101',
          hospitalName: '华西医院',
          outPatientId: '98789789789',
          patientName: 'ZZF',
          'registerNo|202194616936697856-203194616936697856': 202094616936697856,
        },
      ],
      orderBy: '',
      pageNum: 0,
      pageSize: 15,
      total: 1,
      totalPages: 1,
    },
  });
  // 模拟请求延迟
  setTimeout(() => {
    return res.status(200).send(response);
  }, 500);
};

// 导诊单详情
const getGuideDetail = (req: any, res: any) => {
  const response = Mock.mock({
    ...commonSuccessResponse,
    data: {
      'admId|155555-999999999': 131564,
      admTime: () => Random.datetime(),
      'age|15-35': 17,
      cardNo: '000100008474893',
      guideData: `1.两只老虎。
      2.一只没有尾巴，一直没有耳朵。
      3.真奇怪。`,
      guideType: '门诊',
      'guideInfoId|202194616936697856-203194616936697856': 202094616936697856,
      'gender|+1': ['男', '女'],
      hospitalCode: 'HID0101',
      hospitalName: '华西医院',
      outPatientId: '98789789789',
      patientName: 'ZZF',
      'registerNo|202194616936697856-203194616936697856': 202094616936697856,
    },
  });
  // 模拟请求延迟
  setTimeout(() => {
    return res.status(200).send(response);
  }, 500);
};

export default {
  'POST /cloud/hosplatcustomer/medical/op/list/guideDetail': getGuideList,
  'POST /cloud/hosplatcustomer/medical/op/query/guideDetail': getGuideDetail,
};
