.itemCard {
  position: relative;
  box-sizing: border-box;
  padding: 20px 30px 20px 30px;
  background: url('http://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/patient-card/card_ls_bg.png') center center;
  background-size: cover;
  border-radius: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .card {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
  }
  .img {
    width: 160px;
    height: 160px;
  }
  .nameType {
    display: flex;
    flex-direction: row;
    align-items: baseline;
    color: #222;
    font-weight: 600;

    .patientName {
      font-size: 28px;
    }

    .contact {
      font-size: 24px;
    }
  }

  .cardCode {
    margin-top: 12px;
    .cardType {
      color: #989eb4;
      font-size: 24px;
    }
    .contentOne {
      display: flex;
      flex-direction: column;

      .cardType {
        color: #666;
        font-size: 24px;
      }
      .cardNo {
        color: #333;
        font-weight: 500;
        font-size: 28px;
      }
    }
  }

  .default {
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 93px;
  }
}

:global {
  .guideCardQrCodeContent {
    padding: 0 !important;
    background-color: transparent;
  }
}
.guideCardQrcodeWrap {
  display: flex;
  flex-direction: column;
  align-items: center;
  .inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: #fff;
    width: 100%;
    padding-top: 44px;
    padding-bottom: 24px;
    border-radius: 12px;
  }
  > img {
    width: 56px;
    height: 56px;
    margin-top: -10px;
  }
  .name {
    text-align: center;
    margin-top: 40px;
    font-size: 32px;
    font-weight: bold;
    text-align: center;
    color: #03161f;
  }
  .pmi {
    font-size: 28px;
    font-weight: bold;
    text-align: center;
    color: #9aa2a6;
    margin-top: 24px;
  }
  .line {
    width: 4px;
    height: 50px;
    background: #ffffff;
  }
}
