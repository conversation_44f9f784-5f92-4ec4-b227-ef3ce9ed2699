import React, { useEffect } from 'react';
import QRCode from 'qrcode.react';
import logo_ from '@/assets/tencentPatientCard/logo_.png';
import { Modal } from 'antd-mobile-v5';
import styles from './index.less';

const close = 'https://cdnhyt.cd120.com/person/assets/appoinmentHx/close2.png';

interface IProps {
  /**
   * 就诊卡参数
   */
  item: any;
  /**
   * 点击事件
   */
  onClick?: React.MouseEventHandler<Element>;
}

const EHCard = (props: IProps) => {
  const { item, onClick } = props;
  const { credNo, cardTypeName, realName, patientName, qrCode = '' } = item;

  const openQrcode = () => {
    if (!qrCode) return;
    Modal.show({
      content: (
        <div className={styles.guideCardQrcodeWrap}>
          <div className={styles.inner}>
            <QRCode
              value={qrCode}
              size={180}
              fgColor="#03161F"
              level="M"
              imageSettings={{
                src: `${logo_}`,
                height: 44,
                width: 44,
                excavate: true,
              }}
            />
            <div className={styles.name}>{patientName}</div>
          </div>
          <div className={styles.line} />
          <img
            src={close}
            className={styles.close}
            alt=""
            onClick={() => {
              Modal.clear();
            }}
          />
        </div>
      ),
      closeOnMaskClick: false,
      bodyClassName: 'guideCardQrCodeContent',
    });
  };
  useEffect(() => {
    return () => {
      Modal.clear();
    };
  }, []);

  return (
    <div className={styles.itemCard}>
      <div className={styles.card}>
        <div className={styles.nameType}>
          <div className={styles.patientName}>{patientName}</div>
        </div>
        <div className={styles.cardCode}>
          <div className={styles.cardType}>{cardTypeName}</div>
          <div className={styles.contentOne}>
            <div className={styles.cardNo}>{`${credNo.slice(0, 4)}****${credNo.slice(credNo.length - 4)}`}</div>
          </div>
        </div>
      </div>
      {qrCode && (
        <div className={styles.img} onClick={openQrcode}>
          <QRCode
            value={qrCode}
            size={80}
            fgColor="#03161F"
            level="M"
            imageSettings={{
              src: `${logo_}`,
              height: 22,
              width: 22,
              excavate: true,
            }}
          />
        </div>
      )}
    </div>
  );
};
export default EHCard;
