/**
 * 查询导诊单列表接口
 */
export interface IGuideListItem {
  asc?: boolean;
  content?: IGuideSheetContent[];
  orderBy?: string;
  pageNum?: number;
  pageSize?: number;
  total?: number;
  totalPages?: number;
}

/**
 * 公用接口
 * 查询导诊单列表 => content字段
 * 查询导诊单详情接口
 */
export interface IGuideSheetContent {
  /** 开单id/处方id/就诊id */
  admId?: string;
  /** 开单时间 */
  admTime?: string;
  /** 年龄 */
  age?: string;
  /** 患者卡号 */
  cardNo?: string;
  /** 性别 */
  gender?: string;
  /** 导诊信息  */
  guideData?: string;
  /** 导诊单系统id */
  guideInfoId?: string;
  /** 导诊单字段 默认（门诊） */
  guideType?: string;
  /** 机构编码 */
  hospitalCode?: string;
  /** 机构名称 */
  hospitalName?: string;
  /** 门诊缴费订单号  */
  outPatientId?: string;
  /** 患者姓名 */
  patientName?: string;
  /** 患者登记号 */
  registerNo?: string;
  /** 天府医院是否存在华西外检 */
  jumpFlag?: false;
  /** 登记号是否展示二维码  1展示 0不展示，默认不展示二维码(展示条形码) */
  displayQrCode?: number | string;
  /** 需要展示二维码的值，为空不展示 */
  qrCode?: number | string;
  /** 卡类型 */
  cardTypeCode?: string;
  showAppointmentBtn?: boolean;
}

export interface IGetInfoByOrderId {
  cardId?: string;
  organCode?: string;
  pAPMI?: string;
}
