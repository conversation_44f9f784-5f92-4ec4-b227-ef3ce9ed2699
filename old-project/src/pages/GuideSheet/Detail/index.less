.container {
  height: 100%;
  padding: 24px 24px 0 24px;
  font-family: PingFang SC;
  background: #f5f6f7;

  // 导诊单-详情
  .card {
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 24px;
    margin-bottom: 24px;
    padding: 16px 16px 0;

    .cardTop {
      display: flex;
      justify-content: space-between;
      padding: 24px 0 24px 0;
      margin: 0 24px;
      color: #3ad3c1;
      font-weight: bold;
      font-size: 28px;
      border-bottom: 1px solid #ebedf5;
    }

    .cardBottom {
      display: flex;
      justify-content: space-between;
      padding: 24px 0 16px 0;
      margin: 0 24px;
      color: #989eb4;
      font-size: 28px;
    }
    .barCode {
      display: flex;
      justify-content: center;
      width: 100%;
      height: 130px;
      svg {
        width: 626px !important;
      }
    }
    .qrCode {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 100%;
      min-height: 130px;
      padding: 20px 0;
      position: relative;
      .EHlogo {
        position: absolute;
        width: 80px;
        height: 82px;
        // top: 100px
      }
    }
  }

  .notice {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    padding: 30px;
    color: #333;
    font-size: 28px;
    background: #fff;
    border-radius: 16px;
    span {
      margin-bottom: 10px;
    }
  }

  .buttonFixedBox {
    height: 128px;

    .buttonWarpBox {
      background: #fff;
      height: 128px;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0px -4px 8px 0px rgba(152, 158, 180, 0.1);
    }

    .buttonInfo {
      width: 702px;
      height: 96px;
      background: #3ad3c1;
      border-radius: 52px;
      display: flex;
      align-items: center;
      justify-content: center;

      span {
        font-size: 36px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
      }

      span:nth-child(2) {
        font-size: 24px;
      }
    }
  }

  .bannerBox {
    padding: 0 0 24px 0;
  }
}
