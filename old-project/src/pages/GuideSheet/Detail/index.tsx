import React, { PureComponent } from 'react';
import { HxBarcode } from '@/components';
import { Dispatch, AnyAction } from 'redux';
// import { Modal } from 'antd-mobile';
import { encryptPmiNo } from '@/utils/common';
import { connect, Loading, IGuideSheetState } from 'umi';
import { HxLocalStorage } from '@/utils/storage';
import { getOrganCode, HxParameter } from '@/utils/parameter';
import { isJhsh } from '@/utils/platform';
import { EHlogo } from '@/utils/image';
import QRCode from 'qrcode.react';
import qs from 'query-string';
import _ from 'lodash';
import { ICardItem } from '@/pages/PatientCard/data';
import { IGuideSheetContent } from '../data.d';
import styles from './index.less';
import EHCard from '../components/EHCard';

export interface IProps {
  dispatch: Dispatch<AnyAction>;
  guideDetail: IGuideSheetContent;
  search: any;
  location: {
    search: any;
    query: {
      cardId: string;
      guideInfoId: string;
      pmiNo: string;
      purePlayKey: string;
      purePlayParam: string;
    };
  };
}

interface IState {
  reportInterpretationImageVisible: boolean;
  bannerImage: string;
  guideDetail: IGuideSheetContent;
  cardInfo: Partial<ICardItem>;
}

class GuideDetail extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      reportInterpretationImageVisible: false,
      bannerImage: '',
      guideDetail: {},
      cardInfo: {},
    };
  }

  componentDidMount() {
    this.fetchConfigData();
    const {
      location: {
        query: { cardId = '', guideInfoId = '', purePlayKey = '', purePlayParam = '' },
        search,
      },
    } = this.props;
    const c = qs.parse(search);
    console.log(7777, c);
    if (purePlayKey && purePlayParam) {
      console.log(456, JSON.parse(purePlayParam));
      const { cardId: pureCardId = '', guideInfoId: pureGuideInfoId = '' } = JSON.parse(purePlayParam);
      this.props.dispatch({
        type: 'guideSheet/getGuideDetail',
        payload: {
          cardId: pureCardId,
          guideInfoId: pureGuideInfoId,
        },
        callback: (res: any) => {
          this.setState({
            guideDetail: res,
          });
        },
      });
      return;
    }
    this.props.dispatch({
      type: 'guideSheet/getGuideDetail',
      payload: {
        cardId,
        guideInfoId,
      },
      callback: (res: any) => {
        this.setState({
          guideDetail: res,
        });
      },
    });
    getOrganCode() === 'HID0101' && this.fetchCardInfo();
  }

  fetchCardInfo = () => {
    const {
      location: {
        query: { cardId = '', purePlayKey = '', purePlayParam = '' },
      },
    } = this.props;
    /* 处理单一业务cardId */
    let pureCardId = '';
    if (purePlayKey && purePlayParam) {
      const { cardId: CardId = '' } = JSON.parse(purePlayParam);
      pureCardId = CardId;
    }
    this.props.dispatch({
      type: 'patientCard/cardInfo',
      payload: {
        cardId: pureCardId || cardId,
      },
      callback: (res) => {
        this.setState({
          cardInfo: res || {},
        });
      },
    });
  };

  /** 获取配置数据 */
  fetchConfigData = () => {
    this.props.dispatch({
      type: 'reportclip/configPictureOrTextDisplay',
      payload: {
        organCode: 'HID0101',
        position: 'DZD',
        serverCode: '',
      },
      callback: (res: any) => {
        if (res.length > 0) {
          const { display, picUrl } = res[0];
          this.setState({
            reportInterpretationImageVisible: display,
            bannerImage: picUrl,
          });
        }
      },
    });
  };

  // 预约检查导流
  goAppointmentcheck = () => {
    const {
      location: {
        query: { pmiNo = '' },
      },
    } = this.props;
    const enPmiNo = encryptPmiNo(pmiNo); // 加密pmiNo跳转检查预约
    window.location.href = `https://hytweb.cd120.com/yijiyuyue/#/?patRegNo=${enPmiNo}`;
  };

  /** 渲染引流banner模块 */
  renderBanner = () => {
    // state
    const { organCode } = HxParameter;
    const { reportInterpretationImageVisible, bannerImage } = this.state;

    const showBanner = reportInterpretationImageVisible && bannerImage;
    if (showBanner && (organCode === 'HID0101' || organCode === 'HYT' || organCode === 'HXTF0101')) {
      return (
        <img
          style={{ height: '58px', width: '100%', background: '#f5f6f7' }}
          src={bannerImage}
          onClick={this.onGuide}
          alt=""
        />
      );
    }
    return null;
  };

  /** 点击报告解读图片的回调函数 */
  onGuide = () => {
    const { openId } = HxParameter;
    const token = HxLocalStorage.get('token');
    // 跳在线门诊
    window.location.href = `${API_ZXMZ}/online/notice?token=${token}&organCode=HID0101&openId=${openId}`;
  };

  render() {
    const { guideDetail = {}, cardInfo = {} } = this.state;
    const { organCode } = HxParameter;
    const {
      patientName = '',
      gender = '',
      guideData = '',
      registerNo = '',
      jumpFlag = false,
      displayQrCode = '',
      qrCode = '',
      cardTypeCode = '',
      showAppointmentBtn = false,
    } = guideDetail;
    const guideDatas = guideData.replace(/(\n)/g, '<br/>');

    return (
      <div className={styles.container}>
        <div className={styles.card}>
          {organCode === 'HID0101' ? (
            cardInfo?.cardId && <EHCard item={cardInfo} />
          ) : (
            <div className={styles.cardTop}>
              <div>
                {patientName || ''}&nbsp;
                {gender || ''}&nbsp;
                {registerNo || ''}
              </div>
              <div>
                <span>门诊</span>
              </div>
            </div>
          )}
          {displayQrCode !== 1 && displayQrCode !== 2 && (
            <div className={styles.cardBottom}>
              <span>登记号条形码：{registerNo}</span>
            </div>
          )}
          {registerNo && displayQrCode !== 1 && displayQrCode !== 2 && (
            <div className={styles.barCode}>
              <HxBarcode
                brValue={registerNo}
                brFormat="CODE128"
                brWidth={3.4}
                brHight={50}
                brMargin={0}
                brDisplayValue={false}
              />
            </div>
          )}
          {qrCode && [1, 2].includes(Number(displayQrCode)) && (
            <div className={styles.qrCode}>
              {displayQrCode === 1 && <QRCode value={registerNo} size={180} fgColor="#000000" level="M" />}
              {displayQrCode === 2 && (
                <QRCode
                  value={qrCode}
                  size={180}
                  fgColor="#000000"
                  level="M"
                  imageSettings={
                    cardTypeCode === 'EHCard'
                      ? {
                          src: `${EHlogo}`,
                          height: 44,
                          width: 44,
                          excavate: true,
                        }
                      : {}
                  }
                />
              )}
              {/* {displayQrCode === 2 && <img alt="" className={styles.EHlogo} src={EHlogo} />} */}
            </div>
          )}
        </div>
        {(organCode === 'HID0101' || organCode === 'HYT' || (organCode === 'HXTF0101' && jumpFlag)) && !isJhsh() && (
          <div className={styles.bannerBox}>{this.renderBanner()}</div>
        )}
        {guideData && (
          <div className={styles.notice}>
            {/* eslint-disable-next-line react/no-danger */}
            <span dangerouslySetInnerHTML={{ __html: guideDatas }} />
          </div>
        )}
        {(organCode === 'HID0101' || organCode === 'HYT' || (organCode === 'HXTF0101' && jumpFlag)) &&
          !isJhsh() &&
          showAppointmentBtn && (
            <div className={styles.buttonFixedBox}>
              <div className={styles.buttonWarpBox}>
                {showAppointmentBtn && (
                  <div className={styles.buttonInfo} onClick={this.goAppointmentcheck}>
                    <span>去预约检查</span>
                    <span>{organCode === 'HXTF0101' ? '(华西本部)' : ''}</span>
                  </div>
                )}
                {/* {showHistoryCollectionBtn && organCode === 'HID0101' ? (
                <div className={styles.buttonInfo} onClick={this.goHistoryCollection}>
                  <span>完成诊前病史采集</span>
                </div>
              ) : (
                <div className={styles.buttonInfo} onClick={this.goAppointmentcheck}>
                  <span>去预约检查</span>
                  <span>{organCode === 'HXTF0101' ? '(华西本部)' : ''}</span>
                </div>
              )} */}
              </div>
            </div>
          )}
      </div>
    );
  }
}

export default connect(({ loading, guideSheet }: { guideSheet: IGuideSheetState; loading: Loading }) => ({
  loading: loading.models.patientCard,
  guideDetail: guideSheet.guideDetail,
}))(GuideDetail);
