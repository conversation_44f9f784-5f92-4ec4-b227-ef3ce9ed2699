import React, { FC, useEffect, useState } from 'react';
import { Toast } from 'antd-mobile';
import { InfiniteScroll } from 'antd-mobile-v5';
import { connect, Dispatch, CommendLetterModelState } from 'umi';
import { HxIcon, HxEmpty } from '@/components';
import { CommendLetterType } from '../data.d';
import styles from './index.less';
import DoctorItem from '../components/DoctorItem/index';
import { getOpenId, getOrganCode } from '@/utils/parameter';
import { HxLocalStorage } from '@/utils/storage';

interface IProps {
  dispatch: Dispatch;
  commendletter: CommendLetterModelState;
  location: {
    query: {
      data: string;
    };
  };
}
const MyLetter: FC<IProps> = (props) => {
  const [hasMore, setHasMore] = useState(true);
  const [domHeight, setDomHeight] = useState(520);
  const {
    dispatch,
    commendletter,
    location: { query = {} },
  } = props;
  const { commendLetterList = [] } = commendletter;
  useEffect(() => {
    setDomHeight(document.body.clientHeight);
    // console.log('openid999999', getOpenId());
    fetchData();
  }, []);

  const fetchData = () => {
    dispatch({
      type: 'commendletter/praiseList',
      payload: {
        userId: HxLocalStorage.get('commendLetterOpenid'),
      },
      callback(res) {},
    });
  };

  const loadMore = async () => {
    //触底加载
    // const res = await mockRequest()
    // setData(val => [...val, ...res])
    // setHasMore(res.length > 0)
  };

  return (
    <div className={styles.container} style={{ minHeight: domHeight }}>
      {commendLetterList.length > 0 ? (
        commendLetterList.map((item: any, index: number) => <DoctorItem doctorData={item} />)
      ) : (
        <HxEmpty />
      )}
      {/* <InfiniteScroll loadMore={loadMore} hasMore={hasMore} /> */}
    </div>
  );
};

export default connect(({ commendletter }: { commendletter: CommendLetterModelState }) => ({
  commendletter,
}))(MyLetter);
