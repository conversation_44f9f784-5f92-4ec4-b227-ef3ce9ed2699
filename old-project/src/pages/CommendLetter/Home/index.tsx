import React, { FC, useEffect, useState } from 'react';
import { Toast } from 'antd-mobile';
import { SearchBar, InfiniteScroll } from 'antd-mobile-v5';
import { connect, Dispatch, CommendLetterModelState } from 'umi';
import { HxIcon, HxEmpty } from '@/components';
import { CommendLetterType } from '../data.d';
import styles from './index.less';
import DoctorItem from '../components/DoctorItem/index';
import Footer from '../components/Footer/index';
import { HxLocalStorage } from '@/utils/storage';
import { isWechat, isAlipay } from '@/utils/platform';
import { getAppId, getOrganCode } from '@/utils/parameter';

interface IProps {
  dispatch: Dispatch;
  commendletter: CommendLetterModelState;
  userCenter: any;
  global: any;
  location: {
    query: {
      code: string;
    };
  };
}

const CommendLetterHome: FC<IProps> = (props) => {
  const [hasMore, setHasMore] = useState(true);
  const [domHeight, setDomHeight] = useState(520);
  const {
    dispatch,
    commendletter,
    global,
    userCenter,
    location: {
      query: {
        code = '', // 微信授权code
      },
    },
  } = props;
  const { commendLetterList = [] } = commendletter;
  console.log('this.props-commendletter', props);
  const loadMore = async () => {
    //触底加载
    // const res = await mockRequest()
    // setData(val => [...val, ...res])
    // setHasMore(res.length > 0)
  };
  const handelSearch = (val: any) => {
    fetchData(val);
  };
  const getOpenid = () => {
    dispatch({
      type: 'userCenter/wxWebAuth',
      payload: {
        code,
        loginDeviceNum: '236511321231441',
        loginDeviceType: 'WX_H5',
      },
      callback: (data: any = {}) => {
        HxLocalStorage.set('commendLetterOpenid', data.openid), console.log('commend---openid', data);
      },
    });
  };
  useEffect(() => {
    setDomHeight(document.body.clientHeight);
    fetchData('');
    let redirectUrlLetter = `${window.location.origin}/person/commendletter/home`;
    let oid = HxLocalStorage.get('commendLetterOpenid');
    if (isWechat()) {
      let ocStr = getOrganCode();
      dispatch({
        type: 'global/getWxAppId',
        payload: {
          organCode: ocStr,
        },
        callback: (res) => {
          console.log('getWxAppId', res, getAppId());
          const appId = getAppId();
          if (code) {
            if (oid) {
              return;
            } else {
              getOpenid();
            }
          } else {
            window.location.href = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${encodeURIComponent(
              redirectUrlLetter,
            )}&response_type=code&scope=snsapi_base&state=123#wechat_redirect`;
          }
        },
      });
    }
  }, []);

  const fetchData = (data: any) => {
    dispatch({
      type: 'commendletter/praiseList',
      payload: {
        searchParam: data,
      },
      callback(res) {
        // console.log(res, 'commendLetterList', commendLetterList);
      },
    });
  };

  return (
    <div className={styles.containerbox} style={{ minHeight: domHeight }}>
      <div className={styles.container}>
        <SearchBar
          onChange={handelSearch}
          placeholder="搜索科室或医生"
          style={{ '--background': '#ffffff', '--height': '42px' }}
        />

        {commendLetterList.map((item: any, index: number) => (
          <DoctorItem doctorData={item} />
        ))}
        {/* <InfiniteScroll loadMore={loadMore} hasMore={hasMore} /> */}
      </div>
      <Footer />
    </div>
  );
};

export default connect(
  ({
    global,
    userCenter,
    commendletter,
  }: {
    global: any;
    userCenter: any;
    commendletter: CommendLetterModelState;
  }) => ({
    commendletter,
    userCenter,
    global,
  }),
)(CommendLetterHome);
