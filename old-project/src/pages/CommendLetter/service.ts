import request from '@/utils/request';

/**
 * 查询医生表扬信
 * @param data
 */
export const praiseList = async (data: object): Promise<object> =>
  request(`${API_BASE}/cloud/doctorcenter/doctor/praise/list`, {
    method: 'POST',
    data: {
      ...data,
    },
  });

/**
 * 新增医生表扬信
 * @param data
 */
export const praiseSave = async (data: object): Promise<object> =>
  request(`${API_BASE}/cloud/doctorcenter/doctor/praise/save`, {
    method: 'POST',
    data: {
      skipError: true,
      showOriginData: true,
      ...data,
    },
  });
