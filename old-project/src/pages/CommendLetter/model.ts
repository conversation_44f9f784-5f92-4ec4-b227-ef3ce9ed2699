import { Reducer } from 'redux';
import { Effect } from 'umi';
import { createAction } from '@/utils/common';
import { praiseList, praiseSave } from './service';
import { CommendLetterType } from './data.d';

export interface CommendLetterModelState {
  commendLetterList: CommendLetterType[];
}

export interface ICommendLetterModel {
  namespace: string;
  state: CommendLetterModelState;
  effects: {
    praiseList: Effect;
    praiseSave: Effect;
  };
  reducers: {
    updateState: Reducer<any>;
  };
}

const CommendLetterModel: ICommendLetterModel = {
  namespace: 'commendletter',

  state: {
    commendLetterList: [],
  },

  effects: {
    *praiseList({ payload, callback }, { put }) {
      const res = yield praiseList({ ...payload }) || {};
      yield put(
        createAction('updateState')({
          commendLetterList: res,
        }),
      );
      callback && callback(res);
    },
    *praiseSave({ payload, callback }) {
      const res = yield praiseSave({ ...payload }) || {};
      console.log(res, 99999);
      callback && callback(res);
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};
export default CommendLetterModel;
