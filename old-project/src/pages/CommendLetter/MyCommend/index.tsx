import React, { FC, useEffect, useState } from 'react';
import { Toast } from 'antd-mobile';
import { Form, Input, Button, TextArea, Picker } from 'antd-mobile-v5';
import { connect, Dispatch, CommendLetterModelState, history } from 'umi';
import { HxIcon, HxEmpty } from '@/components';
import { CommendLetterType } from '../data.d';
import styles from './index.less';
import DoctorItem from '../components/DoctorItem/index';
import { FormInstance } from 'antd-mobile-v5/es/components/form';
import { isWechat } from '@/utils/platform';
import { getOpenId, getOrganCode } from '@/utils/parameter';
import deptData from '../data';
import { HxLocalStorage } from '@/utils/storage';

interface IProps {
  dispatch: Dispatch;
  commendLetterList: CommendLetterModelState;
  location: {
    query: {
      data: string;
    };
  };
}
const MyCommend: FC<IProps> = (props) => {
  const [pickerVisible, setPickerVisible] = useState(false); //科室picker显隐
  const [pickerValue, setPickerValue] = useState<(string | null)[]>(['']);
  const formRef = React.createRef<FormInstance>();
  const {
    dispatch,
    commendLetterList = [],
    location: { query = {} },
  } = props;

  useEffect(() => {}, []);

  const onFinish = async (val) => {
    try {
      const val = await formRef.current?.validateFields();
      val.deptName = pickerValue[0];
      if (val.deptName) {
        // console.log(val, 'val', getOpenId());
        dispatch({
          type: 'commendletter/praiseSave',
          payload: {
            ...val,
            userId: HxLocalStorage.get('commendLetterOpenid'),
          },
          callback(res) {
            console.log(res);
            const { code = '', msg = '' } = res;
            code === '1' ? Toast.success('提交成功') : Toast.success('提交失败', msg);
            history.push('/commendletter/home');
          },
        });
      } else {
        Toast.info('请选择科室');
      }
    } catch (error) {
      console.log(error, 'error');
      Toast.info('提交验证失败');
    }
  };

  return (
    <div className={styles.container}>
      <Form requiredMarkStyle="text-required" ref={formRef} name="form" layout="vertical" className={styles.form}>
        <Form.Item
          messageVariables={{ another: 'good' }}
          onClick={() => {
            setPickerVisible(true);
          }}
          name="deptName"
          label="科室"
        >
          <Picker
            columns={[deptData]}
            visible={pickerVisible}
            onClose={() => {
              setPickerVisible(false);
            }}
            onConfirm={(val, extend) => {
              setPickerValue(val);
              console.log('onConfirm', val, extend.items);
            }}
          >
            {(value) => (value.length > 0 ? value[0]?.label : '请选择科室')}
          </Picker>
        </Form.Item>
        <Form.Item
          rules={[{ required: true }, { message: '请输入您想表扬人的姓名' }]}
          name="doctorName"
          label="医生/护士/老师"
        >
          <Input placeholder="请输入您想表扬人的姓名" />
        </Form.Item>
        <Form.Item rules={[{ required: true }, { message: '请输入患者姓名' }]} name="patientName" label="患者姓名">
          <Input placeholder="请输入患者姓名" />
        </Form.Item>
        <Form.Item
          rules={[{ required: true }, { message: '请输入表扬内容' }]}
          name="praiseContent"
          label="表扬内容（100字以内）"
        >
          <TextArea placeholder="请输入表扬内容" maxLength={100} rows={6} />
        </Form.Item>
        <Form.Item>
          <div className={styles.atention}>注：表扬信内容，会在审核后公示</div>
        </Form.Item>
      </Form>
      <div className={styles.Btnbox}>
        <Button
          onClick={onFinish}
          className={styles.subBtn}
          style={{ '--background-color': '#32bc6f', '--border-color': '#32bc6f' }}
          block
          color="primary"
          size="large"
        >
          提交
        </Button>
      </div>
    </div>
  );
};

export default connect(({ commendLetterList }: { commendLetterList: CommendLetterModelState }) => ({
  commendLetterList,
}))(MyCommend);
