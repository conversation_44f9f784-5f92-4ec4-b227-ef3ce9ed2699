import React, { useEffect, useState } from 'react';
import { connect, Dispatch, Loading, history } from 'umi';
import { Button } from 'antd-mobile-v5';
import styles from './index.less';

const Footer: React.FC<any> = () => {
  const [totalPrice, setTotalPrice] = useState(0);

  useEffect(() => {}, []);

  const handelGoMyLetter = () => {
    history.push('/commendletter/myletter');
  };

  const handleGoCommend = () => {
    history.push('/commendletter/mycommend');
  };

  return (
    <div className={styles.footerbox}>
      <div className={styles.footer}>
        <Button
          onClick={handelGoMyLetter}
          style={{ '--border-color': '#32bc6f', '--text-color': '#32bc6f' }}
          className={styles.btn1}
          color="primary"
          fill="outline"
        >
          我的表扬信
        </Button>
        <Button
          onClick={handleGoCommend}
          style={{ '--background-color': '#32bc6f', '--border-color': '#32bc6f' }}
          className={styles.btn2}
          color="primary"
          fill="solid"
        >
          我要表扬
        </Button>
      </div>
    </div>
  );
};

export default connect(({ commendletter }: { commendletter: any }) => ({
  commendletter,
}))(Footer);
