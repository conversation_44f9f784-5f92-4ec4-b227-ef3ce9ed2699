import React, { useEffect, useState } from 'react';
import { connect, Dispatch, Loading, history } from 'umi';
import { Button } from 'antd-mobile';
import classnames from 'classnames';
import styles from './index.less';
import doc from './doc.png';
interface IProps {
  doctorData: any;
}
const DoctorItem: React.FC<IProps> = (props) => {
  const { doctorData = {} } = props;

  const [totalPrice, setTotalPrice] = useState(0);

  useEffect(() => {}, []);

  const handleGo = () => {
    //华西表扬信是跳转到公众号诊疗服务主页
  };

  return (
    <div onClick={handleGo} className={styles.docItem}>
      <div className={styles.docItemL}>
        <img src={doc} alt="" />
      </div>
      <div className={styles.docItemR}>
        <div className={styles.docItemTitle}>
          <span>{doctorData.doctorName}</span>
          <span>{doctorData.deptName}</span>
        </div>
        <div className={styles.docItemDesc}>{doctorData.praiseContent}</div>
        <div className={styles.docItemDte}>{doctorData.praiseDate}</div>
      </div>
    </div>
  );
};

export default connect(({ commendletter }: { commendletter: any }) => ({
  commendletter,
}))(DoctorItem);
