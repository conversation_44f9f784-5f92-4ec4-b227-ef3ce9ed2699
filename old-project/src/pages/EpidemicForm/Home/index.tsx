import React, { FC } from 'react';
import { connect, Dispatch, ConnectProps, useLocation, IAddressModelState, history } from 'umi';
import { Button } from 'antd-mobile';
import styles from './index.less';

interface IProps extends ConnectProps {
  dispatch: Dispatch;
}

const Home: FC<IProps> = () => {
  const { search = '' }: any = useLocation();

  const goForm = () => {
    if (search.indexOf('gate=true') > -1) {
      // 跳转闸机流程专属流调问题页面
      history.push(`/gate/gateLDquestion${search}`);
    } else {
      history.push(`/epidemic/detail${search}`);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.note}>
        <h3>尊敬的用户您好：</h3>
        &nbsp;&nbsp;&nbsp;&nbsp;在您到院做诊疗前，请您根据自己的实际情况完成下列信息量表。鉴于病毒疫情的不断变化，给整个疫情的评估与防控带来了巨大挑战。基于此，本量表根据国家卫健委最新版《新型冠状病毒感染的肺炎诊疗方案》并结合已有相关数据库设计，并经过相关专家审定测试后制定，希望能为大家的筛查与防治提供及时准确的建议。让我们一起努力，战胜疫情！
      </div>
      <div className={styles.red}>
        &nbsp;&nbsp;&nbsp;&nbsp;所有信息的准确填写，将有助于医生在接诊时做出更为准确的判断，请务必认真回答。
      </div>
      <div className={styles.btn} onClick={() => goForm()}>
        <Button type="primary" className={styles.button} style={{ color: '#ffffff', backgroundColor: '#3AD3C1' }}>
          进入问卷调查
        </Button>
      </div>
    </div>
  );
};

export default connect(({ address }: { address: IAddressModelState }) => ({
  address,
}))(Home);
