import React, { FC, useEffect, useState } from 'react';
import { Dispatch, ConnectProps, useLocation, history, connect, REGISTER_FROM } from 'umi';
import { HxIcon } from '@/components';
import { Toast, Modal } from 'antd-mobile';
import AppScheme from '@/utils/AppScheme';
import { ModuleEnum, StorageEnum } from '@/utils/enum';
import { ExclamationCircleFilled } from '@ant-design/icons';
import queryString from 'query-string';
import { getChannelCode, getOpenId, getOrganCode, getToken } from '@/utils/parameter';
import styles from './index.less';
import { HxSessionStorage } from '@/utils/storage';
import HxIndicatorPlus from '@/components/HxIndicatorPlus';
import { isWechat } from '@/utils/platform';

interface IProps extends ConnectProps {
  dispatch: Dispatch;
}

const Detail: FC<IProps> = ({ dispatch }) => {
  // submitCode 后台推送需要 医嘱：oeoitem   处方：drug
  const {
    query: {
      patientId = '',
      from = '',
      submitCode = 'oeoitem',
      skip = '',
      bizSysSeq = '',
      dealSeq = '',
      merchantSeq = '',
      scan = '',
      wechatFrom = '',
    } = {},
  }: any = useLocation();

  const [data, setData] = useState<any>({});
  const [quesList, setQuesList] = useState<Array<any>>([]);
  const [ansList, setAnsList] = useState<Array<any>>([]);
  const [loadingMask, setLoadingMask] = useState(false);

  const fetchData = () => {
    dispatch({
      type: 'kgNaire/queryNaireDetail',
      payload: {
        patientId,
        queryType: 1,
      },
      callback: (data: any = {}) => {
        const { questions = [] } = data;
        setData(data);
        setQuesList(questions);
      },
    });
  };

  useEffect(() => {
    fetchData();
  }, []);

  const onChange = (value: any, index: number, questionId: string) => {
    const newAnsList = [...ansList];
    newAnsList[index] = { questionId, answerStr: value };
    setAnsList(newAnsList);
  };

  const goWhere = () => {
    const ac = Object.values({ bizSysSeq, dealSeq, merchantSeq }).every(Boolean);
    if (from === 'app') {
      // 不使用isHytPerson()是因为 可能是从app->mobile->person
      AppScheme.closeWeb();
    } else if (from === 'convenience') {
      // 判断 bizSysSeq, dealSeq, merchantSeq 是否存在，存在即直接跳收银台
      if (ac) {
        const addInfo = queryString.stringify({ bizSysSeq, dealSeq, merchantSeq });
        Toast.info('跳转中...', 1);
        if (getChannelCode() === 'PATIENT_WECHAT') {
          window.location.href = `${API_BASE}/cloud/paygateway/getCashier?bizSysSeq=${bizSysSeq}&dealSeq=${dealSeq}&merchantSeq=${merchantSeq}&accessToken=${getToken()}&openid=${getOpenId()}`;
        } else {
          const urlPay = 'hxgyappscheme://inner.cd120.info/action/pay';
          window.location.href = `${urlPay}?${addInfo}`;
        }
        return;
      }
      if (skip) {
        history.push(`/${ModuleEnum.MODULE_CONVENIENCE_CLINIC}/order/list`);
        return;
      }
      history.push({ pathname: '/outpatientpayment/home' });
    } else if (from === 'newconvenience') {
      if (scan === REGISTER_FROM.HsQuickOrder) {
        const cardInfo = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA);
        dispatch({
          type: 'patientCard/applyNucleinMedical',
          payload: {
            cardId: cardInfo.cardId,
            channelCode: 'WECHAT_SCANHS',
            hospitalCode: cardInfo.organCode,
          },
          callback: (res: any = {}) => {
            const { code } = res;
            if (code === '1') {
              history.push({ pathname: `/${ModuleEnum.MODULE_OUTPATIENT_PAYMENT}/home` });
            }
            setLoadingMask(false);
          },
        });
      } else if (wechatFrom === REGISTER_FROM.WechatHospitalPrePayment) {
        // 推送消息住院预缴金
        const data = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA);
        if (isWechat()) {
          const params = {
            data: JSON.stringify(data),
            channelCode: getChannelCode(),
            organCode: getOrganCode(),
            from: REGISTER_FROM.WechatHospitalPrePayment,
          };
          history.push(`/${ModuleEnum.MODULE_HOSPITALIZATION_PREPAYMENT}/home?${queryString.stringify(params)}`);
        } else {
          const urlPay = 'hxgyappscheme://inner.cd120.info/action/inHospitalPreCash';
          window.location.href = `${urlPay}?patientName=${encodeURIComponent(
            data.patientName,
          )}&cardId=${encodeURIComponent(data.cardId)}&organCode=${encodeURIComponent(getOrganCode())}`;
        }
        setLoadingMask(false);
      } else {
        history.go(-2);
      }
    } else {
      history.go(-2);
    }
  };

  const goSubmit = () => {
    const { naireId = '' } = data;
    if (scan === REGISTER_FROM.HsQuickOrder) {
      setLoadingMask(true);
    }
    dispatch({
      type: from === 'newconvenience' ? 'kgNaire/submitNaireV1' : 'kgNaire/submitNaire',
      payload: {
        patientId,
        naireDetail: {
          naireId,
          answers: ansList,
          submitCode,
        },
      },
      callback: (data: any = {}) => {
        if (scan === REGISTER_FROM.HsQuickOrder && data.code !== '1') {
          setLoadingMask(false);
        }
        /* 自助开单流调新接口 单独处理 */
        if (from === 'newconvenience') {
          const { msg = '' } = data;
          if (data.code === '1') {
            Toast.info('提交成功', 1, () => {
              goWhere();
            });
          } else if (data?.errCode === 'C1003') {
            Modal.alert(
              <div>
                <ExclamationCircleFilled style={{ color: '#fe8f3c' }} /> 温馨提示
              </div>,
              <div style={{ textAlign: 'left' }}>{msg}</div>,
              [{ text: '确定', onPress: () => goWhere() }],
            );
          } else {
            /* 流调异常 */
            goWhere();
          }
        } else {
          const {
            flag = '0',
            resultMsg = '请做好防护并在我院行核酸检测后，方可检查或就诊。核酸检测申请流程：进入四川大学华西医院微信公众号—诊疗服务—门诊服务—自助开单—新冠检测或进入华医通APP—四川大学华西医院—门诊服务—自助开单—新冠检测。',
          } = data;
          if (flag === '1') {
            Modal.alert('提示', <div style={{ textAlign: 'left' }}>{resultMsg}</div>, [
              { text: '确定', onPress: () => goWhere() },
            ]);
          } else {
            Toast.info('提交成功', 1, () => {
              goWhere();
            });
          }
        }
      },
    });
  };

  const onSubmit = () => {
    const canSubmit =
      ansList.filter((i) => {
        return Object.prototype.toString.call(i) === '[object Object]';
      }).length === quesList.length;

    if (!canSubmit) {
      Toast.info('请填写完整', 1);
      return;
    }
    goSubmit();
  };

  const RadioItem = ({ checked = true, text = '', onClick = () => {} }) => {
    return (
      <div className={styles.radio} onClick={onClick}>
        <HxIcon className={styles.image} iconName={checked ? 'checkbox-selected' : 'checkbox-normal'} />
        <span className={styles.text}>{text}</span>
      </div>
    );
  };

  return (
    <div className={styles.container}>
      {quesList.map((item: any, index: number) => {
        const { questionOrder = '', questionDesc = '', questionId = '', questionType = 1, items = [] } = item;
        const ansData = items.map((i: any = {}) => {
          return {
            label: i?.itemDesc,
            value: i?.itemId,
          };
        });
        return (
          questionType === 1 && (
            <div className={styles.item} key={index.toString()}>
              <div className={styles.title}>
                第{questionOrder}题：{questionDesc}
              </div>
              {ansData.map((k: any, i: any) => (
                <RadioItem
                  key={i.toString()}
                  checked={ansList[index]?.answerStr === k.value}
                  text={k.label}
                  onClick={() => onChange(k.value, index, questionId)}
                />
              ))}
            </div>
          )
        );
      })}
      {quesList?.length === 0 ? (
        <div style={{ textAlign: 'center' }}>暂无问卷</div>
      ) : (
        <div className={styles.btnBox}>
          <div className={styles.btn} onClick={onSubmit}>
            提交
          </div>
        </div>
      )}
      {loadingMask && <HxIndicatorPlus loadingMsg="请稍后" opacity="0.7" />}
    </div>
  );
};

export default connect()(Detail);
