.container {
  padding: 20px 20px 50px;
  font-size: 30px;
  background-color: #fff;

  .item {
    margin-bottom: 30px;

    .title {
      margin-bottom: 10px;
    }

    .radio {
      display: flex;
      flex-direction: row;
      align-items: center;
      height: 60px;
      padding-left: 20px;

      .image {
        width: 34px;
        height: 34px;
        margin-right: 20px;
      }

      .text {
        color: #333;
      }
    }
  }

  .btnBox {
    display: flex;
    flex-direction: row;
    justify-content: center;

    .btn {
      width: 80vw;
      height: 80px;
      color: #fff;
      font-size: 35px;
      line-height: 80px;
      text-align: center;
      background-color: @brand-primary;
      border-radius: 40px;
    }
  }
}
