html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

.container {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 100%;
  background: #fbfbfb;
  background-color: #f7f7f7;

  .personCard {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 134px;
    padding: 0 24px;
    color: #fff;
    font-size: 28px;
    background: #3ad3c1;
    position: sticky;
    top: 0px;
    z-index: 999;
    .left {
      display: flex;
      align-items: center;
      p {
        margin-bottom: 0;
      }
      img {
        width: 92px;
        height: 92px;
        margin-right: 18px;
        background: #fff;
        border-radius: 50%;
      }
      .name {
        font-weight: 600;
        font-size: 36px;
        span {
          margin-left: 24px;
          font-weight: normal;
          font-size: 28px;
        }
      }
    }
    .right {
      &::after {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-top: 4px solid #fff;
        border-right: 4px solid #fff;
        transform: rotate(45deg);
        content: '';
      }
    }
  }
  .warningTips {
    white-space: pre-wrap;
    // position: sticky;
    top: 0px;
    z-index: 2;
    background: #ffffff;
    border-radius: 24px;
    padding: 32px 24px;
    margin: 24px;
    font-size: 28px;
    color: #51586d;
    margin-top: 0px;
    .title {
      font-size: 32px;
      font-weight: bolder;
      color: #03081a;
      img {
        width: 36px;
        height: 36px;
        margin-right: 16px;
        vertical-align: sub;
      }
    }
  }
  .guideImage {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 2;
    width: 100vw;
  }

  .tips {
    padding: 28px;
    color: #fc7e00;
    font-size: 24px;
    white-space: pre-wrap;
    background: #fffae4;
    // position: sticky;
    top: 0px;
    z-index: 2;
  }

  .kf {
    display: flex;
    justify-content: space-between;
    padding: 0 24px;
    color: #fc4553;
    font-size: 26px;
    line-height: 45px;
    background: #ffedee;
    align-items: center;
    .closePompt {
      font-size: 40px;
    }
  }

  .intbox {
    display: flex;
    flex-direction: column;
    height: 200px;
  }
  .title_text {
    padding: 30px 0 10px 20px;
    color: #666;
    font-size: 28px;
  }
  .carNumIpt {
    box-sizing: border-box;
    width: 100%;
    height: 100px !important;
    padding-left: 20px;
    font-size: 28px;
    border: 0;
    border-top: 1px solid #eee;
    border-bottom: 1px solid #eee;
    border-radius: 0;
    -webkit-appearance: none;
  }
  .carNumIpt::-webkit-input-placeholder {
    color: #aaa;
  }
  .submit {
    box-sizing: border-box;
    width: 100%;
    padding: 20px;
  }
  .submit > button {
    width: 100%;
    height: 98px;
    color: #fff;
    font-size: 30px;
    line-height: 98px;
    text-align: center;
    border: none;
    border-radius: 49px;
  }
  .submit > .active {
    background: #32b9aa;
  }
  .submit > .disable {
    background: #eee;
  }

  .tabList {
    height: 100%;
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 76px;
    padding: 0 28px 0 34px;
    background-color: #fdf2c3;

    span {
      color: #fc7e00;
      font-size: 28px;
    }

    .headerBtn {
      width: 132px;
      height: 52px !important;
      color: #ed7117;
      font-size: 28px;
      line-height: 52px !important;
      background-color: #fff;
      border-radius: 26px;
    }
  }

  .dateTab {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    height: 88px;
    line-height: 88px;
    padding-left: 24px;
    font-size: 32px;
    background: #fff;
    border-bottom: 1px solid #eee;
    position: sticky;
    //top: 126px;
    z-index: 99;
    // top: 63.9px;
    .dateItem {
      display: flex;
      flex: 1;
      align-items: center;
      justify-content: space-between;
      padding-right: 28px;
      height: 88px;
      .activeItem {
        color: #32b9aa;
      }
    }

    .dateIcon {
      width: 2rem;
      height: auto;
      margin-right: 0.3rem;
      .icon {
        width: 1.6rem;
        height: 1.6rem;
      }
    }

    .moreDateIcon {
      width: 3rem;
    }
  }

  .yxyList {
    flex: 1;
    overflow: auto;
  }
  .am-tabs-tab-bar-wrap,
  .am-tabs-default-bar {
    position: sticky;
    top: 110px;
  }
  :global(.am-tabs) {
    overflow: visible;
  }
  :global(.am-tabs-tab-bar-wrap) {
    position: sticky;
    //top: 216px;
    z-index: 87;
    height: 86px;
    line-height: 86px;
  }
  :global(.am-calendar .single-month .row .cell .date-wrapper .date-selected) {
    background: #3ad3c1;
  }
  :global(.am-calendar .header .right) {
    color: #3ad3c1;
  }
  :global(.am-calendar .confirm-panel .info) {
    p {
      span {
        color: #3ad3c1;
      }
    }
  }
  :global(.am-calendar .confirm-panel .button-disable),
  :global(.am-calendar .confirm-panel .button) {
    width: 150px;
    height: 60px;
    line-height: 60px;
    background: #3ad3c1;
    border-radius: 30px;
    opacity: 0.5;
    color: #fff;
    padding: 0;
    font-size: 28px;
  }
  :global(.am-calendar .confirm-panel .button) {
    opacity: 1;
  }
  :global(.am-calendar .confirm-panel .button-disable) {
    opacity: 0.5;
  }
  // :global(.am-calendar .single-month .row .cell .date-wrapper .selected-middle),
  // :global(.am-calendar .single-month .row .cell .date-wrapper .date-selected) {
  //   color: #03081a;
  //   background: #ebfbf9;
  // }
  // :global(.am-calendar .single-month .row .cell .date-wrapper .selected-start),
  // :global(.am-calendar .single-month .row .cell .date-wrapper .selected-end) {
  //   background: #3ad3c1 !important;
  //   color: #fff !important;
  //   opacity: 1 !important;
  // }

  // :global(.am-calendar .single-month .row .cell .date-wrapper .selected-single) {
  //   background: #3ad3c1;
  //   color: #fff;
  //   opacity: 1;
  // }
  :global(.am-calendar .header .left) {
    color: #3ad3c1;
  }

  :global(.am-calendar .single-month .row .cell .date-wrapper .date) {
    font-weight: 500;
  }
  :global(.am-calendar .confirm-panel .info) {
    font-size: 28px;
  }
  .sticky {
    position: fixed;
    width: 100%;
    top: 0;
    left: 0;
    z-index: 99;
  }
  .headerInfo {
    padding: 24px;
  }
}
