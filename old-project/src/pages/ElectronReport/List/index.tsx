import React, { PureComponent } from 'react';
import { connect, history, IElectronReportState, Loading } from 'umi';
import { AnyAction, Dispatch } from 'redux';
import { Modal, Tabs, Toast } from 'antd-mobile';
import { HxLocalStorage, HxSessionStorage } from '@/utils/storage';
import { isHytDoctor, isHytPerson, isWechat } from '@/utils/platform';
import queryString from 'query-string';
import { ModuleEnum, StorageEnum } from '@/utils/enum';
import { formatIdCard } from '@/utils/common';
import { HxIcon, HxIndicator } from '@/components';
import moment from 'moment';
import { createForm } from 'rc-form';
import { getAppCode, getChannelCode, getOrganCode, HxParameter } from '@/utils/parameter';
import { sensorsRequest } from '@/utils/sensors';
import { getModuleHomeUrl } from '@/utils/interceptor';
import classnames from 'classnames';
import ReoprtTabList from '../components/ReoprtTabList';

import styles from './index.less';
import { CalendarTime, InspectionInstructions, TimePicker } from '../components';
import { MODULE_TYPE } from '../utils/enum';
import { examlistNoticeText } from '../service';
import PatientCard from '@/components/PatientCard';

const activeDateIcon = require('@/assets/new-calendar-icon.png');
const dateIcon = require('@/assets/calendar-icon.png');
const warning = require('@/assets/warnning.png');

interface IProps {
  dispatch: Dispatch<AnyAction>;
  loading?: boolean;
  checkoutList: [];
  inspectList: [];
  crrentTabDate: string;
  electronReport: any;
  location: any;
  form: any;
}

interface IState {
  visible: boolean;
  tabIndex: number;
  needChecking: boolean;
  credNo: string;
  canClik: boolean;
  reportInterpretationImageVisible: boolean;
  bannerImage: string;
  canShowTip: boolean;
  organCode?: string;
  cardId: string;
  pmi: string;
  realNametips: boolean;
  cardInfoRes: any;
  buttonFlag: boolean;
  moduletype?: string;
  topNotice: string;
  newNotice: string;
  showPompt: boolean;
  docked: boolean;
  chooseTimeState: boolean; // 是否使用时间控件筛选时间
}

let dateTab = [
  {
    title: '近十四天',
  },
  {
    title: '近一个月',
  },
  {
    title: '近三个月',
  },
  {
    title: '近一年',
  },
];

const formatData = 'YYYY-MM-DD';

const deadline = 1000 * 60 * 60 * 24 * 7; // 验证时间周期为一周

const set = (key: any, value: any) => {
  const curTime = new Date().getTime();
  localStorage.setItem(key, JSON.stringify({ data: value, time: curTime }));
};

const get = (key: any, exp: any) => {
  const data = localStorage.getItem(key);

  if (!data) {
    return true;
  }
  const dataObj = JSON.parse(data);

  if (new Date().getTime() - dataObj.time > exp) {
    return true;
  }
  return dataObj.data;
};

class ElectronReport extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      visible: false,
      tabIndex: 0,
      credNo: '',
      needChecking: false,
      canClik: false,
      reportInterpretationImageVisible: false,
      bannerImage: '',
      canShowTip: true,
      organCode: '',
      cardId: '',
      pmi: '',
      realNametips: false,
      cardInfoRes: {},
      buttonFlag: true,
      moduletype: '',
      topNotice: '',
      newNotice: '',
      showPompt: true,
      docked: false,
      chooseTimeState: false,
    };
  }

  async componentDidMount() {
    // 医生端跳转过来卡号需要改
    const {
      electronReport: { tabPage = 0 },
      location: {
        query: { moduletype = '', data: cardItemData, msgCardId },
      },
    } = this.props;
    sensorsRequest('VIDEO_PAGE_REPORT', this.props.location.query);
    // 异步动态设置网页标题
    setTimeout(() => {
      document.title = moduletype === 'YXY' ? '数字影像' : '电子报告';
    }, 0);
    const hideElectronReportListModal = HxLocalStorage.get('hideElectronReportListModal');
    // 兼容老版本
    if (hideElectronReportListModal === true) {
      HxLocalStorage.set('hideElectronReportListModal', []);
    }

    if (moduletype === 'YXY' && !isHytDoctor()) {
      // 数字影像默认展示近一年数据
      // if (!HxLocalStorage.get('hideElectronReportListModal')) {
      //   !this.state.realNametips && this.renderModel();
      // }
      dateTab = [
        {
          title: '近一年',
        },
        {
          title: '近三个月',
        },
        {
          title: '近一个月',
        },

        {
          title: '近十四天',
        },
      ];

      this.props.dispatch({
        type: 'electronReport/updateState',
        payload: {
          crrentTabDate: '近一年',
          startDate: moment().subtract('days', 364).format(formatData),
          endDate: moment().add(1, 'days').format('YYYY-MM-DD'),
        },
      });
      if (msgCardId && !HxSessionStorage.get('YXYMsgCardId')) {
        await this.messageEnterInit(msgCardId, moduletype);
      }
    }
    /* 消息推送直接跳转到报告列表页 */
    if (msgCardId && moduletype === 'DZBG') {
      await this.messageEnterInit(msgCardId);
    }
    const cardData = HxSessionStorage.get('patientCardData');
    const card_verification = HxLocalStorage.get('card verification');
    const { organCode } = HxParameter;
    if (isHytDoctor()) {
      this.doctorEnter();
    } else if (cardData || HxLocalStorage.get('patientCardData_szyx')) {
      const { pmi = '', credNo, cardId, realName } = cardData || HxLocalStorage.get('patientCardData_szyx');
      const { pmi: newPmi } = queryString.parse(cardItemData);
      const needChecking =
        card_verification || isHytPerson() || isWechat() || organCode === 'HIDZX0101' ? false : get(credNo, deadline);
      console.log('needChecking--', needChecking);
      this.fetchConfigData();
      if (!needChecking) {
        await this.getNewNotice();
      }
      this.setState(
        {
          organCode: organCode === 'HYT' ? 'HID0101' : organCode,
          credNo,
          needChecking: msgCardId ? false : needChecking,
          cardId,
          moduletype,
          pmi: newPmi || pmi || '',
          tabIndex: tabPage,
        },
        () => {
          console.log('componentDidMount---');
          // if (moduletype && moduletype === 'YXY') {
          //   (!needChecking || msgCardId) && this.fetchradioscopyList();
          // } else {
          //   !needChecking && this.fetchData('1');
          //   !needChecking && this.fetchData('2');
          //   !needChecking && this.fetchDiseaseData();
          //   !needChecking && this.fetchradioscopyList();
          // }
        },
      );
    }
  }
  handleDataFromChild = (cardData: any = {}) => {
    if (!cardData?.credNo) return;
    const { pmi = '', credNo, cardId, organCode } = cardData;
    console.log('子组件回调----', cardData, this.state.needChecking);
    this.setState(
      {
        credNo,
        cardId,
        pmi,
        tabIndex: 0,
        // patientCardInfo: cardData,
      },
      () => {
        const { needChecking, moduletype } = this.state;
        const moduleType = moduletype || this.props?.location?.query?.moduletype;
        if (moduleType && moduleType === 'YXY') {
          this.fetchradioscopyList();
        } else {
          !needChecking && this.fetchData('1');
          !needChecking && this.fetchData('2');
          !needChecking && this.fetchDiseaseData();
          !needChecking && this.fetchradioscopyList(organCode);
        }
      },
    );
  };
  componentWillUnmount() {
    this.setState = () => {
      return false;
    };
  }

  // 从消息界面进入
  messageEnterInit = (msgCardId, moduletype?: string) => {
    return new Promise((reoslve) => {
      this.props.dispatch({
        type: 'patientCard/cardList',
        payload: {},
        callback: (data: any = {}) => {
          const { userCardList = [] } = data;
          const msgCard = userCardList.find((cardItem: any) => cardItem.cardId === msgCardId);
          // eslint-disable-next-line no-extra-boolean-cast
          if (!!msgCard) {
            if (moduletype === 'DZBG') {
              HxSessionStorage.set('patientCardData', msgCard);
              HxLocalStorage.set('ID_CARD_VERIFICATION', true);
            } else {
              HxSessionStorage.set('patientCardData', msgCard);
              HxLocalStorage.set('ID_CARD_VERIFICATION', true);
              HxSessionStorage.set('YXYMsgCardId', msgCardId);
            }
            reoslve(true);
          } else {
            reoslve(false);
          }
        },
      });
    });
  };

  // 提示弹出框
  getNewNotice = async () => {
    const { organCode } = HxParameter;
    const hideElectronReportListModal = HxLocalStorage.get('hideElectronReportListModal') || [];
    if (!hideElectronReportListModal.includes(organCode)) {
      const {
        location: {
          query: { moduletype = '' },
        },
      } = this.props;
      const appCode = getAppCode();
      const channelCode = getChannelCode();
      const res = await examlistNoticeText({
        hisCode: organCode,
        organCode,
        needChecking: false,
        moduleType: moduletype,
        appCode,
        channelCode,
        channel: isHytDoctor() ? 1 : 0,
      });
      const { textContent = '', show = false } = res;
      textContent && show && this.renderModel(textContent);
    }
  };

  // 从医生端进来
  doctorEnter = () => {
    const {
      electronReport: { tabPage = 0 },
    } = this.props;
    const { organCode } = HxParameter;
    const { pmi, cardID: cardId = '' } = queryString.parse(this.props.location.search);
    const needChecking = false;
    this.setState(
      {
        organCode,
        needChecking,
        cardId,
        pmi: pmi || '',
        tabIndex: tabPage,
      },
      () => {
        !needChecking && this.fetchData('1');
        !needChecking && this.fetchData('2');
        !needChecking && this.fetchDiseaseData();
        !needChecking && this.fetchradioscopyList();
        HxSessionStorage.set('patientCardData', { cardId });
      },
    );
  };

  /** 获取就诊卡详情 */
  getCardDetail = (cardId: string) => {
    const { channelCode } = HxParameter;
    this.props.dispatch({
      type: 'patientCard/cardInfo',
      payload: {
        cardId,
        channelCode,
      },
      callback: (cardInfoRes: any) => {
        console.log('cardInfoRes', cardInfoRes);
        this.setState({
          realNametips: true,
          cardInfoRes,
        });
      },
    });
  };

  /** 获取配置数据 */
  fetchConfigData = () => {
    const token = HxLocalStorage.get('token');
    this.props.dispatch({
      type: 'electronReport/configPictureOrTextDisplay',
      payload: {
        organCode: 'HID0101',
        position: 'DZBG',
        serverCode: 'M',
        token,
      },
      callback: (res: any) => {
        if (res && res.length > 0) {
          const { display, picUrl, content } = res[0];
          this.setState({
            reportInterpretationImageVisible: display,
            bannerImage: picUrl,
            topNotice: content,
          });
        }
      },
    });
  };

  /** 跳转注册全国统一健康卡页面 */
  turnRegister = () => {
    const { cardInfoRes, buttonFlag } = this.state;
    const { cardId } = cardInfoRes;
    if (!buttonFlag) {
      Toast.fail('请勿重复点击', 1);
      return false;
    }
    this.setState(
      {
        buttonFlag: false,
      },
      () => {
        this.props.dispatch({
          type: 'patientCard/upgradeCard',
          payload: { cardId },
          callback: (res: any) => {
            const { code, errCode, msg } = res;
            if (code === '1') {
              this.setState(
                {
                  realNametips: false,
                },
                () => {
                  this.fetchData('1');
                  this.fetchData('2');
                  this.fetchDiseaseData();
                  this.fetchradioscopyList();
                },
              );
            } else if (code === '0') {
              if (errCode === '2130031') {
                this.props.dispatch({
                  type: 'patientCard/getCardTypeList',
                  payload: {},
                  callback: (data: any = []) => {
                    const cardTypeList = data;
                    let obj = {};
                    cardTypeList.forEach((item: any) => {
                      const { cardMethodCode } = item;
                      if (cardMethodCode === 'IdRegister') {
                        obj = item;
                      }
                    });
                    const {
                      cardId,
                      cardNo = '',
                      credNo = '',
                      patientName = '',
                      credTypeName = '',
                      nationName = '',
                      nationCode = '',
                      tel = '',
                      occupationName = '',
                      occupationCode = '',
                      provinceCode = '',
                      provinceName = '',
                      cityCode = '',
                      cityName = '',
                      cityAreaCode = '',
                      cityAreaName = '',
                      detailAddress = '',
                      credTypeCode = '',
                    } = cardInfoRes;
                    // 升级卡就是重新注册电子健康卡
                    let registerCardInfo;
                    if (credTypeName === '户口薄') {
                      registerCardInfo = {
                        cardId,
                        cardNo,
                        credNo,
                        patientName,
                        credTypeName: '身份证',
                        tel,
                        provinceCode,
                        provinceName,
                        cityCode,
                        cityName,
                        cityAreaCode,
                        cityAreaName,
                        detailAddress,
                        credTypeCode: '01',
                        credType: [credTypeCode],
                        address: [provinceCode, cityCode, cityAreaCode],
                      };
                    } else {
                      registerCardInfo = {
                        cardId,
                        cardNo,
                        credNo,
                        patientName,
                        credTypeName,
                        tel,
                        provinceCode,
                        provinceName,
                        cityCode,
                        cityName,
                        cityAreaCode,
                        cityAreaName,
                        detailAddress,
                        credTypeCode,
                        credType: [credTypeCode],
                        address: [provinceCode, cityCode, cityAreaCode],
                      };
                    }
                    const { notice }: any = obj;
                    this.props.dispatch({
                      type: 'patientCard/saveCardInfo',
                      payload: {
                        occupationCode,
                        occupationName,
                        nationCode,
                        nationName,
                      },
                    });
                    const credNoDisabled = true;
                    const credDisabled = true;
                    HxSessionStorage.set('registerCardInfo', registerCardInfo);
                    HxSessionStorage.set('notice', notice || '暂无');
                    history.push(
                      `/${ModuleEnum.MODULE_PATIENT_CARD}/register?replaceCardId=${cardId}&credNoDisabled=${credNoDisabled}&credDisabled=${credDisabled}`,
                    );
                  },
                });
              } else {
                Modal.alert('提示', msg, [
                  {
                    text: '确定',
                    onPress: () => {
                      this.setState({ buttonFlag: true });
                    },
                  },
                ]);
              }
            }
          },
        });
      },
    );
  };

  // 获取列表数据
  fetchData = (reportype: string) => {
    console.log('电子xuanka---', reportype);
    const { dispatch, electronReport } = this.props;
    const { credNo, cardId } = this.state;
    const { startDate, endDate } = electronReport;
    console.log('electronReport--', electronReport);
    const payload = {
      cardId,
      endDate,
      reportType: reportype,
      startDate,
    };
    dispatch({
      type: 'electronReport/getreportlist',
      payload,
      callback: () => {
        set(credNo, false);
      },
    });
  };

  // 获取病理列表数据
  fetchDiseaseData = () => {
    const { dispatch, electronReport } = this.props;
    const { credNo, cardId } = this.state;
    const { startDate, endDate } = electronReport;
    const payload = {
      cardId,
      endDate,
      startDate,
    };
    if (['HID0101'].includes(getOrganCode() || '')) {
      dispatch({
        type: 'electronReport/getPathologyList',
        payload,
        callback: () => {
          set(credNo, false);
        },
      });
    }
  };

  // 获取放射检查列表
  fetchradioscopyList = (orgCode = 'HID0101') => {
    const { pmi } = this.state;
    const { dispatch, electronReport } = this.props;
    // const { query = {} } = location;
    console.log('1111pmi--', pmi);
    const cardData_local = HxLocalStorage.get('patientCardData_szyx');
    // const { customerSeq = '' } = query; // 这个是电子胶片存储购买之后收银台传递过来的参数
    const { startDate, endDate } = electronReport;
    const {
      location: {
        query: { organCode = 'HID0101', pmi: newPmi = '' },
      },
    } = this.props;
    const patientCardData = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA) || {};
    console.log('就诊卡patientCardData--', organCode, patientCardData);
    const { cardId } = patientCardData;
    const payload = {
      pageNum: 1,
      pageSize: 10000, // 由于之前他们没有做分页
      query: {
        endDate,
        // hisCode: organCode === 'HYT' ? 'HID0101' : organCode,
        hisCode: organCode === 'HYT' ? 'HID0101' : patientCardData?.organCode || orgCode,
        // patientId: pmi || customerSeq || '', // '305726',customerSeq是userid，不是就诊ID
        patientId: pmi || newPmi || cardData_local?.pmi || '',
        cardId,
        // cardNo: cardData_local?.cardNo || '', 双流医院需要加上
        startDate,
        channel: isHytDoctor() ? 1 : 0,
      },
    };
    dispatch({
      type: 'electronReport/getradioscopyList',
      payload,
    });
  };

  // goToPrecautions = () => {
  //   history.push('/electronreport/precautions');
  // };

  TabsChanged = (tab: any, index: number) => {
    const { sub } = tab;
    +sub === 3 &&
      sensorsRequest('VIDEO_CLICK_RADIATION', {
        source: 'VIDEO_PAGE_REPORT',
        channelCode: getChannelCode() || 'PATIENT_ANDROID',
        organCode: getOrganCode() || 'HID0101',
        appCode: getAppCode() || 'HXGYAPP',
      });
    this.setState(
      {
        tabIndex: index,
        canShowTip: true,
      },
      () => {
        this.props.dispatch({
          type: 'electronReport/updateState',
          payload: {
            tabPage: index,
          },
        });
      },
    );
  };

  // tab时间切换
  changeDate = (tab: string) => {
    const { moduletype } = this.state;
    let chooseTime = '';
    if (tab === '近十四天') {
      chooseTime = moment().subtract('days', 13).format(formatData);
    } else if (tab === '近一个月') {
      chooseTime = moment().subtract('days', 29).format(formatData);
    } else if (tab === '近三个月') {
      chooseTime = moment().subtract('days', 89).format(formatData);
    } else {
      chooseTime = moment().subtract('days', 364).format(formatData);
    }
    this.props.dispatch({
      type: 'electronReport/updateState',
      payload: {
        crrentTabDate: tab,
        startDate: chooseTime,
        endDate: moment().add(1, 'days').format('YYYY-MM-DD'),
      },
    });
    if (moduletype === 'YXY') {
      setTimeout(() => {
        this.fetchradioscopyList();
      }, 200);
    } else {
      setTimeout(() => {
        this.fetchData('1');
        this.fetchData('2');
        this.fetchDiseaseData();
        this.fetchradioscopyList();
      }, 200);
    }
  };

  showCalendar = () => {
    const { visible } = this.state;
    this.setState({
      visible: !visible,
    });
  };

  onCancel = () => {
    this.setState({
      visible: false,
      docked: false,
    });
  };

  onConfirm = (startDateTime: moment.MomentInput, endDateTime: moment.MomentInput, chooseState: boolean = false) => {
    const { moduletype } = this.state;
    this.setState(
      {
        visible: false,
        docked: false,
        chooseTimeState: chooseState,
      },
      () => {
        this.props.dispatch({
          type: 'electronReport/updateState',
          payload: {
            crrentTabDate: '',
            startDate: moment(startDateTime).format(formatData),
            endDate: moment(endDateTime).format(formatData),
          },
        });
        if (moduletype === 'YXY') {
          setTimeout(() => {
            this.fetchradioscopyList();
          }, 200);
        } else {
          setTimeout(() => {
            this.fetchData('1');
            this.fetchData('2');
            this.fetchDiseaseData();
            this.fetchradioscopyList();
          }, 200);
        }
      },
    );
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  goReportDetail = (item: any, tabType?: string) => {
    sensorsRequest('VIDEO_ELECTRONICREPORT', item);
    if (['HID0102'].includes(getOrganCode())) {
      console.log(tabType, item, 999999);
      history.push({
        pathname: `/${ModuleEnum.MODULE_ELECTRON_REPORT}/${tabType === '4' ? 'platformradiologydetail' : 'detail'}`,
        state: {
          item,
        },
      });
      return;
    }
    history.push({
      pathname: `/${ModuleEnum.MODULE_ELECTRON_REPORT}/detail`,
      state: {
        item,
      },
    });
  };

  // 身份证验证提交按钮
  submit = () => {
    const { canClik } = this.state;
    canClik &&
      this.props.form.validateFields((error: any, value: { idcardNo: any }) => {
        if (!error) {
          const { credNo } = this.state;
          const { idcardNo } = value;
          const upperCase = idcardNo.toLocaleUpperCase();
          const upperCaseId = credNo.toLocaleUpperCase();
          const isCarrNum = Object.is(upperCaseId, upperCase);

          if (!isCarrNum) {
            // CCEventClick(window.location.href, "身份证未通过", idcardNo);
            Toast.fail('身份验证未通过', 1);
            return;
          }
          this.setState(
            {
              needChecking: false,
            },
            () => {
              HxLocalStorage.set('ID_CARD_VERIFICATION', true);
              this.fetchData('1');
              this.fetchData('2');
              this.fetchDiseaseData();
              this.fetchradioscopyList();
              this.getNewNotice();
            },
          );
        }
      });
  };

  /** 点击报告解读图片的回调函数 */
  onGuide = () => {
    const { openId } = HxParameter;
    const token = HxLocalStorage.get('token');
    // 跳在线门诊
    window.location.href = `${API_ZXMZ}/onlineExpert/notice?token=${token}&organCode=${getOrganCode()}&openId=${openId}&servCode=zxmz`;
  };

  /** 返回就诊卡列表 */
  goBack = () => {
    const { query } = this.props.location;
    const { msgCardId } = query;
    if (msgCardId) {
      history.push({
        pathname: getModuleHomeUrl(ModuleEnum.MODULE_PATIENT_CARD),
        search: `redirect=/electronreport/list&${queryString.stringify(query)}`,
      });
    } else {
      history.go(-1);
    }
  };

  /**
   * 仅针对影像云模块渲染顶部提示模块
   */
  renderTopNoticeWidthYXY = () => {
    const { newNotice } = this.state;
    if (newNotice) {
      return <div className={styles.tips}>{newNotice}</div>;
    }
    return null;
  };

  /** 渲染顶部提示模块 */
  renderTopNoticeContent = () => {
    const { organCode } = HxParameter;
    const { canShowTip, needChecking, moduletype } = this.state;
    let notice =
      '检查类型报告仅支持放射、超声、内镜、核医学、心电图类报告查看。此报告明细仅做参考，请以医院纸质报告为准';
    if (organCode !== 'HID0101' && organCode !== 'HYT' && organCode !== 'HXTF0101') {
      notice = '';
    }
    if (organCode === 'HXTF0101') {
      const text =
        '温馨提示：住院患者的报告暂时无法线上查询，您可根据时间段筛选需要查看的电子报告(病理报告不支持线上查看)。此报告明细仅做参考，请以医院纸质报告为准。';
      notice = `${text.split('/n')[0]}\n${text.split('/n')?.[1] ?? ''}`;
    }
    if (canShowTip && isHytDoctor()) {
      // 针对华西医生端检查报告
      notice = '暂仅支持放射、超声、内镜、核医学、心电图类报告查询';
    }
    // 针对华西大众端检查报告
    if ((organCode === 'HID0101' || organCode === 'HYT') && !needChecking && moduletype === 'YXY') {
      notice = `${this.state.topNotice.split('/n')[0]}\n${this.state.topNotice.split('/n')[1]}`;
      // '1.检查类型报告仅支持放射类报告查看。此报告明细仅做参考，请以医院纸质报告为准' +
      // '\n' +
      // '2.报告出具后，10天内可免费调阅电子胶片。同时，可购买电子胶片的调阅存储服务，在有效期限内，您可无限次免费调阅和分享电子胶片';
    }
    if ((organCode === 'HID0101' || organCode === 'HYT') && !needChecking && moduletype !== 'YXY') {
      notice = '检查类型报告仅支持放射、超声、核医学、心电图类报告查看。此报告明细仅做参考，请以医院纸质报告为准';
    }
    if (notice) {
      return (
        <div className={styles.warningTips}>
          <div className={styles.title}>
            <img src={warning} />
            温馨提示
          </div>
          {notice}
        </div>
      );
    }
    return null;
  };

  closePompt = () => {
    this.setState({
      showPompt: false,
    });
  };

  /** 渲染客服信息 */
  renderKfeContent = () => {
    const { showPompt, organCode = '' } = this.state;
    return (
      showPompt &&
      !['HID0101'].includes(organCode) && (
        <div className={styles.kf}>
          <div>
            如您在查询报告或者开通影像存储服务过程中遇到问题，请联系客服咨询：028-85423666，工作日
            9：00-12：00，13：30-18：00。
          </div>
          <span className={styles.closePompt} onClick={this.closePompt}>
            &times;
          </span>
        </div>
      )
    );
  };

  /** 渲染引流banner模块 */
  renderBanner = () => {
    // state
    const { needChecking, reportInterpretationImageVisible, bannerImage, organCode } = this.state;

    const showBanner =
      organCode === 'HID0101' &&
      !needChecking &&
      reportInterpretationImageVisible &&
      (isHytPerson() || isWechat()) &&
      bannerImage;
    if (showBanner) {
      return (
        <img
          style={{ height: '80px', width: '100%', padding: '10px', background: '#f5f6f7' }}
          src={bannerImage}
          onClick={this.onGuide}
          alt=""
        />
      );
    }
    return null;
  };

  renderModel = (notice) => {
    const noticeList = notice.split('\n');
    Modal.alert(
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <HxIcon iconName="tips" style={{ marginRight: '2px' }} />
        温馨提示
      </div>,
      <div style={{ textAlign: 'justify', color: '#353948' }}>
        {noticeList.map((noticeItem: any) => (
          <p key={noticeItem}>{noticeItem}</p>
        ))}
      </div>,
      [
        {
          text: '我知道了',
          onPress: () => {
            const { organCode } = HxParameter;
            const hideOrgModalList = HxLocalStorage.get('hideElectronReportListModal') || [];
            HxLocalStorage.set('hideElectronReportListModal', [...new Set([...hideOrgModalList, organCode])]);
          },
        },
      ],
    );
  };

  render() {
    const {
      visible,
      tabIndex,
      needChecking,
      credNo,
      canClik,
      // organCode = '',
      realNametips,
      buttonFlag,
      // moduletype,
      docked,
      chooseTimeState,
    } = this.state;
    const {
      location: {
        query: { moduletype = '', organCode = '' },
      },
    } = this.props;
    // console.log('moduletype', moduletype, 'organCode', organCode);
    const { electronReport, form, loading } = this.props;
    const { checkoutList = [], inspectList = [], caseList = [], radioscopyList = {}, crrentTabDate } = electronReport;
    let content = [];
    if (radioscopyList.content) {
      content = radioscopyList.content;
    }
    const that = this;

    let tabs: any = [];
    if (organCode === 'HID0101' || isHytDoctor()) {
      tabs = [
        { title: `检验(${checkoutList.length})`, sub: '2' },
        { title: `放射检查(${content.length})`, sub: '3' },
        { title: `其他检查(${inspectList.length})`, sub: '1' },
        { title: `病理报告(${caseList.length})`, sub: '4' },
      ];
    } else if (['HID0102'].includes(organCode)) {
      tabs = [
        { title: `检验(${checkoutList.length})`, sub: '2' },
        { title: `检查(${inspectList.length})`, sub: '1' },
        { title: `病理报告(${caseList.length})`, sub: '4' },
      ];
    } else if (['HXTF0101'].includes(organCode)) {
      tabs = [
        { title: `检验(${checkoutList.length})`, sub: '2' },
        { title: `放射检查(${content.length})`, sub: '3' },
        { title: `其他检查(${inspectList.length})`, sub: '1' },
      ];
    } else {
      tabs = [
        { title: `检验(${checkoutList.length})`, sub: '2' },
        { title: `检查(${inspectList.length})`, sub: '1' },
      ];
    }

    const { getFieldProps } = form;
    const cardData = HxSessionStorage.get('patientCardData');
    HxLocalStorage.set('patientCardData_szyx', cardData);
    const cardData_local = HxLocalStorage.get('patientCardData_szyx');
    // console.log('cardData', cardData);
    // console.log('cardData_local', cardData_local);

    const { patientName, cardId, gender, age } = cardData || cardData_local || {};
    // const { patientName, cardId, gender, age } = JSON.parse(this.props.location.query?.data);
    console.log('needChecking---', needChecking);
    return (
      <div className={styles.container}>
        {!realNametips && needChecking && (
          <div className={styles.intbox}>
            <span className={styles.title_text}>请验证身份</span>
            <input
              className={styles.carNumIpt}
              maxLength={18}
              {...getFieldProps('idcardNo', {
                rules: [{ required: true }],
                initialValue: '',
                onChange(event: { target: { value: any } }) {
                  const { value } = event.target;
                  if (value.length === 0) {
                    that.setState({ canClik: false });
                    return;
                  }
                  that.setState({ canClik: true });
                },
              })}
              placeholder="请输入该就诊卡的证件号码"
            />
          </div>
        )}
        {needChecking && credNo?.length > 0 && (
          <div className={styles.submit}>
            <button type="button" onClick={this.submit} className={canClik ? styles.active : styles.disable}>
              提交
            </button>
          </div>
        )}
        {!realNametips && !needChecking && (
          <>
            {moduletype !== 'YXY' && (
              <div className={styles.headerInfo}>
                <PatientCard onDataReady={this.handleDataFromChild} />
              </div>
            )}
            {moduletype !== 'YXY' && this.renderTopNoticeContent()}
            {moduletype === 'YXY' && this.renderKfeContent()}
            {/* {moduletype !== 'YXY' && this.renderBanner()} */}
            {/* <p style={{ height: '10px', margin: '0px' }} /> */}
            {/** 展示当前就诊卡患者信息 */}
            {!isHytDoctor() && moduletype === 'YXY' && (
              <div style={{ display: visible ? 'none' : '' }} className={styles.personCard} onClick={this.goBack}>
                <div className={styles.left}>
                  <img
                    src={
                      gender === 1
                        ? 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/man.png'
                        : 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/woman.png'
                    }
                    alt=""
                  />
                  <div>
                    <p className={styles.name}>
                      {patientName}
                      <span>{gender === 1 ? '男' : '女'}</span>
                      <span>{age}岁</span>
                    </p>
                    <p>{formatIdCard(cardId)}</p>
                  </div>
                </div>
                <div className={styles.right}>切换患者</div>
              </div>
            )}
            <div className={styles.dateTab}>
              <div className={styles.dateItem}>
                {dateTab.map((item) => {
                  return (
                    <div
                      key={item.title}
                      onClick={() => {
                        this.changeDate(item.title);
                      }}
                      className={crrentTabDate === item.title ? styles.activeItem : ''}
                    >
                      {item.title}
                    </div>
                  );
                })}
              </div>
              {moduletype === 'YXY' ? (
                <div className={styles.dateIcon}>
                  <img
                    src={dateIcon}
                    className={styles.icon}
                    onClick={() => {
                      this.setState({
                        docked: !docked,
                      });
                    }}
                    alt=""
                  />
                  <CalendarTime visible={docked} onCancel={this.onCancel} onConfirm={this.onConfirm} />
                </div>
              ) : (
                <div className={classnames(styles.dateIcon, getOrganCode() === 'HID0102' && styles.moreDateIcon)}>
                  {getOrganCode() === 'HID0102' ? (
                    <div onClick={this.showCalendar}>更早</div>
                  ) : (
                    <img
                      src={chooseTimeState ? activeDateIcon : dateIcon}
                      onClick={this.showCalendar}
                      className={styles.icon}
                      alt=""
                    />
                  )}
                  {/* 优化日期选择控件 */}
                  <TimePicker
                    visible={visible}
                    onCancel={this.onCancel}
                    onConfirm={this.onConfirm}
                    onReset={() => {
                      this.setState({
                        chooseTimeState: false,
                      });
                      this.changeDate('近七天');
                    }}
                  />
                </div>
              )}
            </div>
            {loading ? (
              <HxIndicator />
            ) : moduletype === 'YXY' ? (
              <div className={styles.yxyList}>
                <ReoprtTabList
                  tabType="3"
                  goReportDetail={this.goReportDetail}
                  moduletype={moduletype}
                  fetchrRdiosCopyList={this.fetchradioscopyList}
                />
              </div>
            ) : organCode === 'HID0101' || organCode === 'HXTF0101' ? (
              // 天府医院不显示病例报告
              <div className="tabContent">
                <Tabs
                  animated={false}
                  tabs={tabs}
                  renderTabBar={(props) => <Tabs.DefaultTabBar {...props} page={3} />}
                  tabBarActiveTextColor="#03081A"
                  tabBarInactiveTextColor="#989EB4"
                  tabBarUnderlineStyle={{
                    width: '44px',
                    height: '4px',
                    backgroundColor: '#6CEBE2',
                    border: '0.1px solid #6CEBE2',
                    borderRadius: '2px',
                    transform: 'translateX(85%)',
                  }}
                  page={tabIndex}
                  onTabClick={(tab, index) => this.TabsChanged(tab, index)}
                >
                  <ReoprtTabList tabType="1" goReportDetail={this.goReportDetail} moduletype={moduletype} />
                  {(organCode === 'HID0101' || organCode === 'HXTF0101') && (
                    <ReoprtTabList tabType="3" goReportDetail={this.goReportDetail} moduletype={moduletype} />
                  )}
                  <ReoprtTabList tabType="2" goReportDetail={this.goReportDetail} moduletype={moduletype} />
                  {organCode === 'HID0101' && organCode !== 'HXTF0101' && (
                    <ReoprtTabList tabType="4" goReportDetail={this.goReportDetail} moduletype={moduletype} />
                  )}
                </Tabs>
              </div>
            ) : ['HID0102'].includes(organCode) ? (
              <div className="tabContent">
                <Tabs
                  animated={false}
                  tabs={tabs}
                  renderTabBar={(props) => <Tabs.DefaultTabBar {...props} page={3} />}
                  tabBarActiveTextColor="#03081A"
                  tabBarInactiveTextColor="#989EB4"
                  tabBarUnderlineStyle={{
                    width: '44px',
                    height: '4px',
                    backgroundColor: '#6CEBE2',
                    border: '0.1px solid #6CEBE2',
                    borderRadius: '2px',
                    transform: 'translateX(85%)',
                  }}
                  page={tabIndex}
                  onTabClick={(tab, index) => this.TabsChanged(tab, index)}
                >
                  <ReoprtTabList tabType="1" goReportDetail={this.goReportDetail} moduletype={moduletype} />
                  <ReoprtTabList tabType="2" goReportDetail={this.goReportDetail} moduletype={moduletype} />
                  <ReoprtTabList tabType="4" goReportDetail={this.goReportDetail} moduletype={moduletype} />
                </Tabs>
              </div>
            ) : (
              <Tabs
                animated={false}
                tabs={tabs}
                tabBarActiveTextColor="#3AD3C1"
                tabBarInactiveTextColor="#999999"
                tabBarUnderlineStyle={{
                  border: '2px #32b9aa solid',
                  transform: 'scale(0.3)',
                  overflowX: 'hidden',
                }}
                page={tabIndex}
                onTabClick={(tab, index) => this.TabsChanged(tab, index)}
              >
                <ReoprtTabList tabType="1" goReportDetail={this.goReportDetail} moduletype={moduletype} />
                <ReoprtTabList tabType="2" goReportDetail={this.goReportDetail} moduletype={moduletype} />
              </Tabs>
            )}
            {moduletype === MODULE_TYPE.YXY && <InspectionInstructions />}
          </>
        )}
        <Modal
          visible={realNametips}
          title="提示"
          transparent
          maskClosable={false}
          footer={[
            {
              text: '取消',
              onPress: () => {
                history.go(-1);
              },
            },
            {
              text: '确认',
              onPress: buttonFlag
                ? () => {
                    this.turnRegister();
                  }
                : () => {
                    Toast.fail('请勿重复点击', 1);
                  },
            },
          ]}
        >
          <div className={styles.content} style={{ textAlign: 'left' }}>
            <div>
              <div className="text" style={{ textAlign: 'center' }}>
                因政策原因，需升级为全国统一健康卡，请前往进行升级
              </div>
            </div>
          </div>
        </Modal>
      </div>
    );
  }
}

export default connect(({ loading, electronReport }: { electronReport: IElectronReportState; loading: Loading }) => ({
  loading: loading.effects['electronReport/getreportlist'] || loading.effects['electronReport/getradioscopyList'],
  electronReport,
}))(createForm()(ElectronReport));
