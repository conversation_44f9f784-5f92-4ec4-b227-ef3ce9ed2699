import React, { useEffect, useState } from 'react';
import { connect, Loading, IElectronReportState, history } from 'umi';
import { Carousel, Toast, Modal } from 'antd-mobile';
import { HxIndicator } from '@/components';
import { getOrganCode, HxParameter } from '@/utils/parameter';
import { getModuleHomeUrl } from '@/utils/interceptor';
import { ModuleEnum } from '@/utils/enum';
import classnames from 'classnames';
import { isHytPerson } from '@/utils/platform';
import qs from 'query-string';
import { sensorsRequest } from '@/utils/sensors';
import styles from './index.less';
import { HxSessionStorage } from '@/utils/storage';
import { HOSPITAL_CODE } from '../utils/enum';

// const menuList = [
//   {
//     menuName: '扫码查报告',
//     url: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/menu-scan.png',
//     menuId: '1'
//   },
//   {
//     menuName: '影像报告',
//     url: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/menu-report.png',
//     menuId :'2',
//   },
//   {
//     menuName: '影像咨询',
//     url: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/menu-consult.png',
//     menuId: '3',
//   },
//   {
//     menuName: '咨询订单',
//     url: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/menu-order.png',
//     menuId: '4',
//   },
// ];

const pageChannelEnum = {
  APP_HOME: '0', // 从医院主页进入
  HXKG_HOME: '1', // 从双流空港医院页面进入
  ZYDY_HOME: '2', // 资阳第一人民医院公众号进入
};

const DigitalHome = ({ dispatch, loading }) => {
  const [menuModuleList, setMenuModuleList] = useState([]);
  const [bannerModuleList, setBannerModuleList] = useState([{ bannerInfoList: [] }]);
  const [isTop, setIstop] = useState(false);
  const { appCode = '', channelCode = '', token = '', organCode } = HxParameter;
  const query: any = qs.parse(window.location.href.split('?')[1]) || {};
  const { pageChannel = pageChannelEnum.APP_HOME } = query;
  const features = ['更清晰', '更完整', '更隐私', '不丢失', '不损坏', '不污染', '易保存', '易查看', '易交流'];
  useEffect(() => {
    const { urlToVideoCloud = '0', scanTerminal = '', extra = '', appCode = '' }: any = query;
    let a: any = appCode;
    if (Array.isArray(a)) {
      [a] = appCode;
    }
    const sensorsData = { ...query, appCode: a, source: 'VIDEO_PAGE_HOME' };
    // 微信扫码进入
    if (+urlToVideoCloud === 1 && scanTerminal !== 'APP') sensorsRequest('VIDEO_IN_SCANQR', sensorsData);
    // app扫码进入
    if (scanTerminal === 'APP') sensorsRequest('VIDEO_IN_SCANQR_APP', sensorsData);
    // app banner
    if (extra === 'banner') sensorsRequest('VIDEO_IN_BANNER_APP', sensorsData);
    // app 医院进入
    if (appCode === 'HXGYAPP') {
      if (['SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX].includes(getOrganCode()))
        sensorsRequest('VIDEO_IN_HXGYAPP_APP_SL', sensorsData);
      else sensorsRequest('VIDEO_IN_HXGYAPP_APP', sensorsData);
    }
    // 进入页面
    sensorsRequest('VIDEO_PAGE_HOME', query);
    dispatch({
      type: 'electronReport/getPageHtmlConfig',
      payload: {
        pageCode: 'image_cloud_page',
        organCode: 'PUBLIC',
        channelCode: 'PATIENT_WECHAT',
      },
      callback: (res) => {
        // console.log("res===========>",res);
        const {
          resourceInfo: { menuModuleList, bannerModuleList },
        } = res || {};
        setMenuModuleList(menuModuleList);
        setBannerModuleList(bannerModuleList);
      },
    });
  }, []);
  const judgeOrganCodeEnter = () => {
    return new Promise((resolve) => {
      dispatch({
        type: 'electronReport/queryDigitalEnter',
        callback: (data) => {
          if (data?.length) {
            const index = data.findIndex((item) => item.organCode === getOrganCode());
            //机构没有开启入口
            if (index === -1) {
              Modal.alert('提示', '平台升级改造中，影像调阅服务暂无法使用，敬请谅解。', [
                {
                  text: '我知道了',
                  onPress: () => {
                    console.log('关闭----');
                  },
                },
              ]);
              resolve(true);
            } else {
              resolve(false);
            }
          }
        },
      });
    });
  };
  const handleScroll = () => {
    // 定义handleScroll事件函数
    const scrollTop = Math.max(document.body.scrollTop);
    const fixedTop = Math.max(document.documentElement.scrollTop);
    // 控制元素块A随鼠标滚动固定在顶部
    if (scrollTop >= fixedTop) {
      setIstop(false);
    } else if (fixedTop - scrollTop >= 160) {
      // 添加固定定位样式，隐藏banner
      setIstop(true);
    }
  };
  useEffect(() => {
    window.addEventListener('scroll', handleScroll);
  }, []);
  const clickMenuItem = async (item) => {
    const { parameter = '' } = item;
    const allow: any = +parameter.split('_')[1];
    const redirect = '/electronreport/list';
    // item.menuId === '1398113920494305280' && Toast.info('暂未开放');
    // console.log(allow, item, API_ZXMZ, getModuleHomeUrl(ModuleEnum.MODULE_PATIENT_CARD));
    //判断医院是否开启影像入口
    // const result = await judgeOrganCodeEnter();
    // if (result) return;
    if (allow === 0) {
      Toast.info('暂未开放');
      return;
    }
    if (item.menuCode === 'jiaopianOrder') {
      history.push({
        pathname: '/filmstorage/home',
        search: `${qs.stringify(query)}`,
      });
    }
    if (item.menuId === '1398113769447608320' || item.menuCode === 'visit_card') {
      // 已经选择医院，跳转到就诊卡列表
      // console.log('query.organCode', query, organCode);
      let search: any = `redirect=${redirect}&${qs.stringify(query)}`;
      if (item.menuCode === 'visit_card') search = `${qs.stringify(query)}`;
      if (organCode && organCode !== 'SZYX') {
        history.push({
          pathname: getModuleHomeUrl(ModuleEnum.MODULE_PATIENT_CARD),
          search,
        });
      } else {
        // 选择医院
        history.push({
          pathname: getModuleHomeUrl(ModuleEnum.MODULE_HOSPITALS),
          search: `${qs.stringify(query)}`,
        });
        sensorsRequest('VIDEO_PAGE_CHOOSE', query);
      }
    }
    if (item.menuId === '1398113842776883200') {
      const env = isHytPerson() ? 1 : 2;
      window.location.href = `${API_ZXMZ}/online/imagecloud/intro?env=${env}&organCode=${HOSPITAL_CODE.HXYY}&appCode=${appCode}&channelCode=${channelCode}&token=${token}`;
    }
    if (item.menuId === '1398114007483539456') {
      window.location.href = `${API_ZXMZ}/online/history?client=dzapp&type=yxzximage&organCode=${HOSPITAL_CODE.HXYY}&appCode=${appCode}&channelCode=${channelCode}&token=${token}`;
    }
  };
  const gotoReportlist = () => {
    const redirect = '/electronreport/list';
    history.push({
      pathname: getModuleHomeUrl(ModuleEnum.MODULE_PATIENT_CARD),
      search: `redirect=${redirect}&${qs.stringify(query)}`,
    });
  };
  const rollback = () => {
    document.documentElement.scrollTo({
      left: 0,
      top: 0,
      behavior: 'smooth',
    });
  };

  const bannerClick = (bannerItem: any) => {
    if (bannerItem && bannerItem.linkUrl) {
      window.location.href = bannerItem.linkUrl;
    }
  };

  const organList = ['SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX, HOSPITAL_CODE.ZYDY];

  return (
    <>
      {loading ? (
        <div className={styles.showLoading}>
          <HxIndicator />
        </div>
      ) : (
        <div className={styles.container}>
          {/** banner */}
          {!isTop && (
            <Carousel autoplay autoplayInterval={4000} infinite>
              {bannerModuleList[0].bannerInfoList.map((item: any, index) => (
                <div key={index.toString()} onClick={() => bannerClick(item)} className={styles.banner}>
                  <img alt="" src={item?.imgUrl} />
                </div>
              ))}
            </Carousel>
          )}
          <div className={classnames(styles.main, organList.includes(organCode as string) ? styles.static : '')}>
            {/** 菜单 */}
            {menuModuleList.map((item: any) =>
              item.moduleCode === 'top_icons' &&
              (!organList.includes(organCode as string) ||
                (organList.includes(organCode as string) && +pageChannel === 1)) ? (
                <div className={classnames(styles.menuList, isTop ? styles.fixedList : '')} key={item.moduleName}>
                  {item.menuInfoList.length > 0 &&
                    item.menuInfoList.map((ele) => {
                      let str: any = ele.parameter || 'test';
                      str = str.substring(0, str.length - 2);
                      let show: any = true;
                      if (organCode === 'HID0101') show = str === 'PUBLIC' || str === 'SZYX';
                      else show = str === 'PUBLIC' || str === organCode;
                      const dom: any = show ? (
                        <div
                          className={styles.item}
                          key={ele.menuId}
                          onClick={() => {
                            clickMenuItem(ele);
                          }}
                        >
                          <span
                            className={styles.menuIcon}
                            style={{ background: `url(${ele.imgUrl}) top center`, backgroundSize: '100% 100%' }}
                          />
                          <p className={styles.menuName}>{ele.menuName}</p>
                        </div>
                      ) : (
                        ''
                      );
                      return dom;
                    })}
                </div>
              ) : item.moduleCode === 'instruction' ? ( // 数字影像简介
                <>
                  <div className={styles.moduleBox}>
                    {/* <div className={styles.moduleTitle}>{item.moduleName}</div>
                  <div className={styles.grayBox}>{item.moduleDesc}</div>
                  <div className={styles.grayBox}>
                    <p className={styles.lineHeight40}>数字影像具有以下优点:</p>
                    {item.menuInfoList.map((ele) => (
                      <span className={styles.label} key={ele.menuName}>
                        {ele.menuName}
                      </span>
                    ))}
                  </div> */}
                    <img
                      src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/szyx_title.png"
                      className={styles.imgTitle}
                      alt=""
                    />
                    <div className={styles.moduleTitle}>
                      <span>01</span>
                      <p>什么是数字影像?</p>
                    </div>
                    <div className={styles.contentText}>
                      影像检查后完整的原始DICOM图像数据和电子报告,包含该次扫描所有高清薄层图像数据,有的高达上千幅,有的还包括三维重建数据等。
                    </div>
                    <div className={classnames(styles.moduleTitle, styles.orange)}>
                      <span>02</span>
                      <p>为什么选择数字影像?</p>
                    </div>
                    <div className={styles.contentText}>
                      <div className={styles.featureList}>
                        {features.map((item: any) => (
                          <span key={item} className={styles.feature}>
                            {item}
                          </span>
                        ))}
                      </div>

                      {/* <span className={styles.colorBlue}>
                        而电子胶片存储在云服务器上，可随时随地通过移动设备调阅查看，便于复诊，转诊。
                      </span> */}
                    </div>
                    <div className={styles.moduleTitle}>
                      <span>03</span>
                      <p>为何推荐数字影像?</p>
                    </div>
                    <div className={styles.contentText}>
                      原始数字影像可以为临床医生提供决策支撑，从而方便患者跨院就医、远程会诊、影像咨询等，这是传统打印胶片无法实现的。
                    </div>
                  </div>
                  {/** 数字影像优点 */}
                  <div className={styles.moduleBox}>
                    <img
                      src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/szyxyd_title.png"
                      className={styles.imgTitle}
                      style={{ marginBottom: '28px' }}
                      alt=""
                    />
                    <img
                      src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/szyxyd_centent.png"
                      style={{ margin: '0 auto 30px auto', width: '311px', display: 'block' }}
                      alt=""
                    />
                  </div>
                </>
              ) : item.moduleCode === 'service_info' ? ( // 服务列表
                <div className={styles.moduleBox}>
                  <img
                    src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/fwnr_title.png"
                    className={styles.imgTitle}
                    style={{ width: '168px' }}
                    alt=""
                  />

                  {/* <div className={styles.moduleTitle}>{item?.moduleName}</div> */}
                  {item?.menuInfoList.map((ele) => {
                    return (
                      <div className={styles.cardSvervice} key={ele.menuId}>
                        <img src={ele.imgUrl} alt="" className={styles.img} />
                        <div className={styles.right}>
                          <div className={styles.title}>{ele.menuName}</div>
                          <div className={styles.fontSmall}>{ele.menuDesc}</div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                item.moduleCode === 'hospitals' &&
                !organList.includes(organCode as string) && ( // 医院列表
                  <div className={styles.moduleBox}>
                    {/* <div className={styles.moduleTitle}>{item.moduleName}</div> */}
                    <img
                      src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/lmyy_title.png"
                      className={styles.imgTitle}
                      style={{ marginBottom: '28px', width: '168px' }}
                      alt=""
                    />
                    <div className={styles.flexBox}>
                      {item.menuInfoList.map((ele) => (
                        <div className={styles.cardHospital} key={ele.menuId}>
                          <img src={ele.imgUrl} alt="" className={styles.img} />
                          <div className={styles.title}>
                            {ele.menuName}
                            {/* <span className={styles.leval}>
                            <b>{ele.parameter}</b>
                          </span> */}
                            <div className={styles.hosTip}>{ele.menuDesc}</div>
                          </div>
                        </div>
                      ))}
                      {item.menuInfoList.length % 3 === 2 && (
                        <div style={{ visibility: 'hidden' }} className={styles.cardHospital} key={+new Date()}></div>
                      )}
                    </div>
                  </div>
                )
              ),
            )}
            <p className={styles.noMore}>~没有更多啦~</p>
            {/** 双流空港医院展示，直接跳转就诊卡 */}
            {+pageChannel !== 1 && organList.includes(organCode as string) && (
              <>
                <div className={styles.fixedBox}>
                  <div className={styles.fixedBtn} onClick={() => gotoReportlist()}>
                    查看影像报告
                  </div>
                </div>
                {/** 悬浮按钮，返回顶部 */}
                {isTop && (
                  <img
                    src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/rollback.png"
                    alt=""
                    className={styles.rollback}
                    onClick={() => rollback()}
                  />
                )}
              </>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default connect(({ electronReport, loading }: { electronReport: IElectronReportState; loading: Loading }) => ({
  electronReport,
  loading: loading.effects['electronReport/getPageHtmlConfig'],
}))(DigitalHome);
