.container {
  color: #03081a;
  font-weight: 400;
  background-color: #f7f7f7;
  .menuList {
    position: relative;
    top: -108px;
    display: flex;
    justify-content: space-between;
    // margin-bottom: 24px;
    padding: 32px 20px;
    font-size: 28px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 6px 12px 0 rgba(27, 125, 255, 0.06);
    margin: 0 24px 24px 24px;
    &.fixedList {
      position: sticky;
      top: 0;
      right: 0;
      left: 0;
      z-index: 10;
      margin-left: 0;
      margin-right: 0;
    }
    .item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-items: center;

      .menuIcon {
        display: inline-block;
        width: 96px;
        height: 96px;
        margin-bottom: 16px;
        background-size: 100%;
        border-radius: 50%;
      }
      .menuName {
        margin-bottom: 0;
        line-height: 40px;
        text-align: center;
      }
    }
  }
  .banner {
    height: 400px;
    background: #fff;
    img {
      height: 400px;
    }
  }

  .main {
    padding: 32px 0;
    &.static {
      .moduleBox {
        position: static;
        padding-bottom: 108px;
      }
    }
    .moduleBox {
      position: relative;
      top: -108px;
      //  margin-bottom: 24px;
      margin: 0 24px 24px 24px;
      padding: 32px 24px;
      font-size: 28px;
      background: #fff;
      border-radius: 8px;

      .imgTitle {
        display: block;
        width: 416px;
        margin: auto;
      }
      .contentText {
        margin-left: 64px;
        line-height: 48px;
        text-align: justify;
        .featureList {
          display: flex;
          justify-content: space-between;
          flex-flow: wrap;
          padding-right: 40px;
        }
        .feature {
          font-size: 28px;
          font-weight: 600;
          color: #faa80d;
          line-height: 56px;
          width: 156px;
          text-align: center;
          margin-bottom: 20px;
          background: #fef5e5;
          border-radius: 30px;
        }
      }
    }
    .moduleTitle {
      display: flex;
      align-items: center;
      margin-top: 64px;
      // font-size: 36px;
      margin-bottom: 40px;
      &.orange {
        span {
          background: #fbd07e;
        }
        p {
          background: #fff8eb;
        }
      }
      span {
        position: relative;
        z-index: 2;
        display: inline-block;
        width: 64px;
        height: 64px;
        color: #fff;
        font-size: 33px;
        line-height: 64px;
        text-align: center;
        background: #85b0ff;
        border-radius: 8px;
      }
      p {
        position: relative;
        left: -6px;
        z-index: 1;
        flex: 1;
        margin-bottom: 0;
        padding-left: 26px;
        font-weight: 500;
        line-height: 60px;
        background: #f0f5ff;
        border-radius: 8px;
      }
    }
    .colorBlue {
      color: #568df2;
    }
    .grayBox,
    .cardHospital,
    .cardSvervice {
      margin-bottom: 24px;
      padding: 24px;
      overflow: hidden;
      font-size: 28px;
      background: #f5f6fa;
      border-radius: 8px;
    }

    .lineHeight40 {
      line-height: 40px;
    }
    .label {
      float: left;
      margin-right: 34px;
      margin-bottom: 20px;
      padding: 0 34px;
      color: #3ad3c1;
      font-weight: 500;
      line-height: 60px;
      border: 2px solid #3ad3c1;
      border-radius: 30px;
      &:nth-child(3n) {
        margin-right: 0;
      }
    }
    .cardHospital {
      display: flex;
      flex-direction: column;
      align-items: center;
      // justify-content: center;
      width: 204px;
      padding-right: 4px;
      padding-left: 4px;
      .img {
        width: 96px;
        height: 96px;
        margin-bottom: 8px;
        // margin-right: 24px;
        background: chartreuse;
        border-radius: 50%;
      }
      .title {
        color: #2784ff;
        font-weight: 500;
        font-size: 20px;
        line-height: 28px;
        transform: scale(0.8);
      }
      .hosTip {
        margin-top: 8px;
        color: rgba(39, 132, 255, 0.7);
        font-size: 18px;
        text-align: left;
        transform: scale(0.75);
      }
      .leval {
        width: 48px;
        margin-left: 12px;
        color: #fff;
        font-size: 18px;
        line-height: 32px;
        background: #568df2;
        border-radius: 8px;
        b {
          display: inline-block;
          font-weight: normal;
          transform: scale(0.7);
        }
      }
      .fontGray {
        margin-top: 12px;
        color: #989eb4;
        font-size: 24px;
        line-height: 34px;
      }
    }
    .cardSvervice {
      display: flex;
      align-items: center;
      margin-top: 60px;
      .img {
        width: 176px;
        height: 176px;
        margin-right: 24px;
      }
      .right {
        display: flex;
        flex: 1;
        flex-direction: column;
        justify-content: space-between;
        min-height: 176px;
        .title {
          font-weight: 500;
          font-size: 32px;
          // color: #3ad3c1;
          line-height: 44px;
        }
        .fontSmall {
          margin-top: 12px;
          font-size: 28px;
          line-height: 40px;
        }
      }
    }
    .flexBox {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
    }
    .noMore {
      position: relative;
      top: -108px;
      color: #989eb4;
      font-size: 24px;
      line-height: 34px;
      text-align: center;
    }
    .fixedBox {
      position: fixed;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 10;
      padding: 16px 24px;
      background: #fff;
    }
    .fixedBtn {
      color: #fff;
      font-size: 32px;
      line-height: 96px;
      text-align: center;
      background: #3ad3c1;
      border-radius: 48px;
      box-shadow: 0 0 4px 0 #ebedf5;
    }
  }
  .rollback {
    position: fixed;
    right: 24px;
    bottom: 166px;
    z-index: 20;
    width: 128px;
  }
}
