import React, { memo, useEffect, useState } from 'react';
// import { ConnectState, IElectronReportItem } from '@/models/connect';
import { connect, IElectronReportState } from 'umi';
import styles from './index.less';

interface IProps {
  electronReport: any;
}

const OriginalImage = memo((props: IProps) => {
  // 设定初始state
  const [originalImageReportUrl, setoriginalImageReportUrl] = useState<string>('');

  useEffect(() => {
    console.log(props);
    const {
      electronReport: {
        radiologyDetail: { originalImageReportUrl = [] },
      },
    } = props;
    setoriginalImageReportUrl(originalImageReportUrl[0]);
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.wrapBox}>
        <iframe
          title=" "
          src={originalImageReportUrl}
          frameBorder="0"
          width="100%"
          height="100%"
          scrolling="no"
          style={{ minHeight: '100%' }}
        />
      </div>
    </div>
  );
});

export default connect(({ electronReport }: { electronReport: IElectronReportState }) => ({
  electronReport,
}))(OriginalImage);
