import { Reducer } from 'redux';
import moment from 'moment';
import { Effect, Subscription } from 'umi';
import { createAction } from '@/utils/common';
import * as electronReportApi from './service';
import { IPricesType, toPayParamsType, ITextContentType, HospitalConfig } from './data.d';

export enum DownLoadStatus {
  // -1: 可申请下载 0 下载中 1 下载完成 -2 不可以申请
  Disabled = 'NOT_AVAILABLE',
  CanRequest = 'CAN_REQUEST',
  Downloading = 'DOWNLOADING',
  Compelete = 'COMPLETED',
}

export interface IElectronReportState {
  // 检验
  checkoutList: Array<any>;
  // 检查
  inspectList: Array<any>;
  // 病历
  caseList: Array<any>;
  // 放射检查
  radioscopyList: any;
  // 放射检验详情
  radiologyDetail: any;
  // 详情
  reportDetail: { [index: string]: any };
  crrentTabDate: string;
  startDate: any;
  endDate: any;
  tabPage: number;
  pricesList: IPricesType[];
  toPayParams: toPayParamsType;
  textContentObj: ITextContentType;
  hospitalConfig: HospitalConfig; // 影像报告医院配置
  sharelinks: string; // 后台返回的分享链接
  encryptionflag: number;
  sharetimerule: string;
  password: string;
  sharetype: number;
  middleLinkParams: any;
  pageHtmlConfig: any;
  checkDetailRes: any;
  /* 病理报告列表 */
  diseaseReportList: any;
  /* 病理报告详情 */
  diseaseReportDetail: any;
  /** wechat提示框 */
  wechatShareTips: boolean;
}

export interface IElectronReportModel {
  namespace: 'electronReport';
  state: IElectronReportState;
  effects: {
    getCheckDetail: Effect;
    getCheckDetailDoctor: Effect;
    getPathologyList: Effect;
    getPathologyDetail: Effect;
    getreportlist: Effect;
    getDetail: Effect;
    getradioscopyList: Effect;
    getIsViewImage: Effect;
    getquerycheckdetail: Effect;
    fetchReportQRCode: Effect;
    configPictureOrTextDisplay: Effect;
    getUrl: Effect;
    clearSave: Effect;
    getPricesList: Effect;
    createOrder: Effect;
    filmStorageText: Effect;
    storageRemind: Effect;
    getHospitalConfig: Effect;
    getsharelink: Effect;
    doDecryptLink: Effect;
    safetycheck: Effect;
    getPageHtmlConfig: Effect;
    getSharelinksCheckdetail: Effect;
    getResetUrl: Effect;
    externalReportList: Effect;
    /** 微信JS授权 */
    singWechatJSSDK: Effect;
    queryDigitalEnter: Effect;
    checkImageRetrieval: Effect
  };
  reducers: {
    updateState: Reducer<IElectronReportState>;
  };
  subscriptions: {
    setup: Subscription;
  };
}

const Model: IElectronReportModel = {
  namespace: 'electronReport',
  state: {
    checkoutList: [],
    caseList: [],
    inspectList: [],
    radioscopyList: {},
    reportDetail: {},
    radiologyDetail: {},
    diseaseReportList: [],
    diseaseReportDetail: {},
    crrentTabDate: '近十四天',
    startDate: moment().subtract('days', 13).format('YYYY-MM-DD'), // 开始时间
    endDate: moment().add(1, 'days').format('YYYY-MM-DD'), // 结束时间
    tabPage: 0,
    pricesList: [],
    toPayParams: {},
    textContentObj: {},
    hospitalConfig: {}, // 医院配置
    sharelinks: '',
    encryptionflag: 0 /** 是否加密（1：是，0：否） */,
    sharetimerule: '' /** 有效时间，通过获取医院配置获取 */,
    password: '' /** 密码 */,
    sharetype: 0 /** 分享类型（0：微信，1：链接分享） */,
    middleLinkParams: {} /** 分享链接返回的相关数据 */,
    pageHtmlConfig: {},
    checkDetailRes: {},
    wechatShareTips: false,
  },
  effects: {
    // 获取放射检查详情数据
    *getCheckDetail({ payload, callback }, { call, put }) {
      const res = yield call(electronReportApi.getCheckDetail, payload);
      yield put(createAction('updateState')({ radiologyDetail: res }));
      callback && callback(res.imageReportUrl, res);
    },
    *getCheckDetailDoctor({ payload, callback }, { call, put }) {
      const res = yield call(electronReportApi.getCheckDetailDoctor, payload);
      yield put(createAction('updateState')({ radiologyDetail: res }));
      callback && callback(res.imageReportUrl, res);
    },
    *getPathologyList({ payload, callback }, { call, put }) {
      const res = yield call(electronReportApi.getPathologyList, payload);
      yield put(createAction('updateState')({ caseList: res }));
      callback && callback(res);
    },
    *getPathologyDetail({ payload, callback }, { call, put }) {
      const res = yield call(electronReportApi.getPathologyDetail, payload);
      yield put(createAction('updateState')({ reportDetail: res }));
      callback && callback(res);
    },
    // 获取列表数据
    *getreportlist({ payload, callback }, { call, put }) {
      const res = yield call(electronReportApi.getreportlist, payload);
      if (payload.reportType === '1') {
        yield put(createAction('updateState')({ checkoutList: res }));
      } else {
        yield put(createAction('updateState')({ inspectList: res }));
      }
      res && callback(res);
    },
    // 获取放射检查列表数据
    *getradioscopyList({ payload }, { call, put }) {
      const res = yield call(electronReportApi.getradioscopyList, payload);
      if (res && res !== undefined) {
        yield put(createAction('updateState')({ radioscopyList: res }));
      }
    },
    // 获取除放射检查外详情数据
    *getDetail({ payload }, { call, put }) {
      console.log(payload);
      const res = yield call(electronReportApi.getDetail, payload);
      yield put(createAction('updateState')({ reportDetail: res }));
    },
    // 获取放射检查详情数据
    *getquerycheckdetail({ payload }, { call, put }) {
      const res = yield call(electronReportApi.getquerycheckdetail, payload);
      yield put(createAction('updateState')({ radiologyDetail: res }));
    },
    // 是否查看电子胶片
    *getIsViewImage({ payload, callback }, { call, put }) {
      const res = yield call(electronReportApi.getIsViewImage, payload);
      yield put(createAction('updateState')({ reportDetail: res }));
      res && callback(res);
    },
    // 获取放射检查报告详情二维码
    *fetchReportQRCode({ payload, callback }, { call }) {
      const res = yield call(electronReportApi.getReportQRCode, payload);
      callback(res);
    },
    // 后端配置banner图片显影
    *configPictureOrTextDisplay({ payload, callback }, { call }) {
      const res = yield call(electronReportApi.configPictureOrTextDisplay, payload);
      if (res && res !== undefined) {
        if (res.code) {
          res.code === '1' && callback(res.data);
        } else {
          callback(res);
        }
      }
    },
    /** 短链接重定向（用于二维码跳转页面数据获取） */
    *getUrl({ payload, callback }, { call, put }) {
      const res = yield call(electronReportApi.getUrl, payload);
      yield put(createAction('updateState')({ radiologyDetail: res }));
      res && callback(res);
    },
    /** 短链接重定向（用于二维码跳转页面数据获取） */
    *getResetUrl({ payload, callback }, { call }) {
      const res = yield call(electronReportApi.getUrl, payload);
      res && callback(res);
    },
    /** 放射检查详情页进入页面清除缓存 */
    *clearSave({ payload, callback }, { put }) {
      yield put(createAction('updateState')({ radiologyDetail: payload }));
      callback();
    },
    /** 获取资费列表 */
    *getPricesList({ payload }, { call, put }) {
      const data = yield call(electronReportApi.getPricesList, payload);
      yield put(createAction('updateState')({ pricesList: data }));
    },
    /** 影像支付创建订单 */
    *createOrder({ payload, callback }, { call, put }) {
      const data = yield call(electronReportApi.createOrder, payload);
      yield put(createAction('updateState')({ toPayParams: data }));
      callback && callback(data);
    },
    *singWechatJSSDK({ payload, callback }, { call }) {
      const data = yield call(electronReportApi.singWechatJSSDK, payload);
      !_.isEmpty(data) && callback && callback(data);
    },
    /** 电子胶片选择存储期限页面温馨提示 */
    *filmStorageText({ payload }, { call, put }) {
      const data = yield call(electronReportApi.filmStorageText, payload);
      yield put(createAction('updateState')({ textContentObj: data }));
    },
    /** 电子报告详情电子胶片和影像存储过期提醒 */
    *storageRemind({ payload, callback }, { call }) {
      const data = yield call(electronReportApi.storageRemind, payload);
      callback && callback(data);
      // yield put(createAction('updateState')({ textContentObj: data }));
    },
    /** 获取医院配置信息 */
    *getHospitalConfig({ payload, callback }, { call, put }) {
      const data = yield call(electronReportApi.getHospitalConfig, payload);
      yield put(createAction('updateState')({ hospitalConfig: data }));
      callback(data);
    },
    /** 数字影像详情获取分享链接 */
    *getsharelink({ payload, callback }, { call, put }) {
      const data = yield call(electronReportApi.getsharelink, payload);
      yield put(createAction('updateState')({ sharelinks: data }));
      callback && callback(data);
    },
    /** 解码分享链接 */
    *doDecryptLink({ payload, callback }, { call, put }) {
      const data = yield call(electronReportApi.doDecryptLink, payload);
      yield put(createAction('updateState')({ middleLinkParams: data }));
      callback && callback(data);
    },
    *externalReportList({ payload, callback }, { call, put }) {
      const data = yield call(electronReportApi.externalReportList, payload);
      callback && callback(data);
    },
    /** 影像报告分享安全校验 */
    *safetycheck({ payload, callback }, { call }) {
      const data = yield call(electronReportApi.safetycheck, payload);
      console.log('data', data);
      callback && callback(data);
    },
    /** 数字影像入口页配置 */
    *getPageHtmlConfig({ payload, callback }, { call, put }) {
      const data = yield call(electronReportApi.getPageHtmlConfig, payload);
      yield put(createAction('updateState')({ pageHtmlConfig: data }));
      callback && callback(data);
    },
    *queryDigitalEnter({ payload, callback }, { call }) {
      const data = yield call(electronReportApi.queryDigitalEnter, payload);
      callback && callback(data);
    },
    *checkImageRetrieval({ payload, callback }, { call }) {
      const data = yield call(electronReportApi.checkImageRetrieval, payload);
      callback && callback(data);
    },
    /** 分享链接详情 */
    *getSharelinksCheckdetail({ payload, callback }, { call, put }) {
      const res = yield call(electronReportApi.getSharelinksCheckdetail, payload);
      yield put(createAction('updateState')({ radiologyDetail: res }));
      callback && callback(res.imageReportUrl, res);
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return { ...state, ...payload };
    },
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname }) => {
        if (pathname && !pathname.includes('electronreport')) {
          dispatch({
            type: 'updateState',
            payload: {
              tabPage: 0,
            },
          });
        }
      });
    },
  },
};

export default Model;
