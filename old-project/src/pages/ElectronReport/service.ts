import request from '@/utils/request';
import { getChannelCode, HxParameter } from '@/utils/parameter';
// import { HOSPITAL_CODE } from './utils/enum';

let node = '/cloud';
if (APP_ENV === 'prod') {
  const { organCode } = HxParameter;
  const orgCodeList = ['HID0101', 'HYT', 'SL0101', 'MS0101'];
  node = orgCodeList.includes(organCode as string) ? `${API_HXYY}/cloud` : `${API_BASE}/cloud`;
}
// 查询放射检查详情
export const getCheckDetail = async (params: object): Promise<any> => {
  return request(`${API_TEST}/imagecloud/examination/querycheckdetail`, {
    method: 'GET',
    params,
  });
};

// 开通影像云服务
export const openImageService = async (data: object): Promise<any> => {
  return request(`${API_TEST}/imagecloud/images/open-image-service`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });
};

// 开通影像云服务
export const queryImge = async (data: object): Promise<any> => {
  return request(`${API_TEST}/imagecloud/images/query-image`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });
};

export const getCheckDetailDoctor = async (params: object): Promise<any> => {
  return request(`${API_TEST}/imagecloud/examination/detail`, {
    method: 'GET',
    params,
  });
};

// 查询电子报告列表
export const getreportlist = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/elecreport/querylist`, {
    method: 'POST',
    data: {
      ...data,
      skipError: true,
    },
  });
// 查询病理报告列表
export const getPathologyList = async (data: object): Promise<any> =>
  request(
    `${['HID0102'].includes(HxParameter?.organCode ?? '')
      ? API_BASE
      : ['HID0101'].includes(HxParameter?.organCode ?? '')
        ? API_HXYY_NEW
        : API_HXYY
    }/cloud/hosplatcustomer/elecreport/getPathologyList`,
    {
      method: 'POST',
      data: {
        ...data,
        skipError: true,
      },
    },
  );
// 查询病理报告详情
export const getPathologyDetail = async (data: object): Promise<any> =>
  request(
    `${['HID0102'].includes(HxParameter?.organCode ?? '') ? API_BASE : API_HXYY_NEW
    }/cloud/hosplatcustomer/elecreport/getPathologyDetail`,
    {
      method: 'POST',
      data: {
        ...data,
        skipError: true,
      },
    },
  );

// 查询放射检查列表
export const getradioscopyList = async (data: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/examination/listexamination`, {
    method: 'POST',
    data: {
      ...data,
      // skipError: true,
    },
  });

// 查询电子报告详情
export const getDetail = async (data: object): Promise<any> =>
  request(`${node}/hosplatcustomer/elecreport/querydetails`, {
    method: 'POST',
    data: {
      ...data,
      errorModalShow: true,
    },
  });

// 查询放射检查详情
export const getquerycheckdetail = async (params: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/examination/querycheckdetail`, {
    method: 'GET',
    params,
  });

// 是否查看电子胶片
export const getIsViewImage = async (data: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/examination/isviewimage`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
      showErrorModal: true,
    },
  });

// 后端配置显隐
export const configPictureOrTextDisplay = async (params: object): Promise<any> =>
  request(`${API_YL}/customconfig/appointmentConfig/netInquiryJump`, {
    method: 'GET',
    params: { channelCode: getChannelCode(), ...params },
  });

/** 获取放射检查报告详情二维码 */
export const getReportQRCode = async (params: object): Promise<any> => {
  return request(`${API_TEST}/imagecloud/shorturl/sort/check`, {
    method: 'GET',
    params,
  });
};

/** 短链接重定向（用于二维码跳转页面数据获取） */
export const getUrl = async (data: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/shorturl/geturl`, {
    method: 'POST',
    data: {
      ...data,
      showErrorModal: true,
    },
  });

/** 获取资费列表 */
export const getPricesList = async (data: object): Promise<any> => {
  console.log('获取资费列表=>', data);
  return request(`${API_TEST}/imagecloud/image/payment/package/list`, {
    method: 'POST',
    data,
  });
};

/** 影像支付创建订单 */
export const createOrder = async (data: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/image/payment/order/create`, {
    method: 'POST',
    data,
  });

/** 查询文案---电子胶片选择存储期限页面温馨提示文案 */
export const filmStorageText = async (data: object): Promise<any> => {
  console.log('获取温馨提示=>', data);
  return request(`${API_PREVIEW}//resource/text/query`, {
    method: 'POST',
    data,
  });
};

/** 微信JS授权 */
export const singWechatJSSDK = async (data: {
  /** 授权地址 */
  url: string;
}) =>
  request('/cloud/resource/app/jsparam/query', {
    method: 'POST',
    data: {
      ...data,
    },
  });

/**
 * 获取影像云检查列表文案
 * @param data
 */
export const examlistNoticeText = async (data: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/showtext/examlist`, {
    method: 'POST',
    data,
  });

/** 电子报告存储过期提醒 */
export const storageRemind = async (data: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/image/storage/invalid/remind`, {
    method: 'POST',
    data,
  });

/** 获取医院配置 */
export const getHospitalConfig = async (params: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/imageconfig/getimageconfig`, {
    method: 'GET',
    params,
  });

/** 获取分享链接 */
export const getsharelink = async (data: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/sharelinks/getsharelink`, {
    method: 'POST',
    data,
  });

/** 解码分享链接 */
export const doDecryptLink = async (params: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/sharelinks/decrypt`, {
    method: 'GET',
    params,
  });
/** 影像报告分享安全校验 */
export const safetycheck = async (params: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/sharelinks/safetycheck`, {
    method: 'GET',
    params: { ...params, showOriginData: true },
  });
// 数字影像页入口配置 ${API_YL}
export const getPageHtmlConfig = async (data: object): Promise<any> =>
  request('/cloud/doctorcenter/home/<USER>/query', {
    method: 'POST',
    data: { channelCode: getChannelCode(), appVersionCode: APP_VERSION_CODE, ...data },
  });
//判断医院是否开放了影像云入口
export const queryDigitalEnter = async (params: object): Promise<any> => {
  return request(`/cloud/imagecloud/organizations`, {
    method: 'GET',
    params,
  });
};
// 分享链接详情
export const getSharelinksCheckdetail = async (data: object): Promise<any> =>
  request(`${API_TEST}/imagecloud/sharelinks/checkdetail`, {
    method: 'POST',
    data,
  });

// 获取放射检查列表跳转链接
export const geYXYListLink = async (data: any) =>
  request(`${API_TEST}/imagecloud/examination/get-exam-list-url`, {
    method: 'POST',
    data,
  });

/**
 * 获取影像信息
 */
export const getImageInfo = (data: any) =>
  request(`${API_TEST}/imagecloud/examination/image-info`, {
    method: 'POST',
    data,
  });

/**
 * 获取影像信息
 */
export const getImageInfoNew = (data: any) =>
  request(`${API_TEST}/imagecloud/images/download-info`, {
    method: 'POST',
    data,
  });

/**
 * 请求下载
 */
export const downloadImage = (data) =>
  request(`${API_TEST}/imagecloud/examination/download-image`, {
    method: 'POST',
    data,
  });

/** 华西健康报告查询 */
export const externalReportList = async (data: object): Promise<any> =>
  request(`${API_YL}/healthexame/health/external/reportList`, {
    method: 'POST',
    data,
  });

// 上锦影像调阅
export const checkImageRetrieval = (data) =>
  request(`${API_TEST}/imagecloud/images/queryImage`, {
    method: 'POST',
    data,
  });
