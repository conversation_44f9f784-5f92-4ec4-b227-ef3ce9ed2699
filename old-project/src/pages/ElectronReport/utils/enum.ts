const HOSPITAL_CODE = {
  ZYDY: 'HIDW2401', // 资阳市第一人民医院
  HXYY: 'HID0101', // 华西医院
  HXKG: 'SL0101', // 四川大学华西空港医院
  MSRM: 'MS0101', // 眉山市人民医院
  HID09XXXX: 'HID09XXXX', // 绵竹医院
  GS0101: 'GS0101', // 成都骨伤医院
};

const miniProgramType = {
  WXMiniProgramTypeRelease: 0, //**< 正式版  */
  WXMiniProgramTypeTest: 1, //**< 开发版  */
  WXMiniProgramTypePreview: 2, //**< 体验版  */
};

// 调用小程序参数
const MiniApp = Object.freeze({
  // 放射科智服务
  FSKZ: (params) => {
    return {
      userName: 'gh_10eaeee375ef',
      miniProgramType:
        APP_ENV === 'prod' ? miniProgramType.WXMiniProgramTypeRelease : miniProgramType.WXMiniProgramTypePreview,
      ...params,
    };
  },
});

// 模块类型
const MODULE_TYPE = {
  YXY: 'YXY', // 影像云
};

export { MiniApp, MODULE_TYPE, HOSPITAL_CODE };
