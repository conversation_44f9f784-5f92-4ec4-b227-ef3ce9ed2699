// 放射检查报告类型
const FSJC_TYPES = [
  {
    label: 'CT',
    value: '1',
    logoSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/img/elecreport/CT_logo.png',
    imageSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/img/elecreport/MRI%26CT.png',
  },
  {
    label: 'DR',
    value: '2',
    logoSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/img/elecreport/DR_logo.png',
    imageSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/img/elecreport/DR.png',
  },
  {
    label: 'MR',
    value: '3',
    logoSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/img/elecreport/MRI_logo.png',
    imageSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/img/elecreport/MRI%26CT.png',
  },
  {
    label: 'MG',
    value: '4',
    logoSrc: '',
    imageSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/img/elecreport/mg_logo.png',
  },
  {
    label: 'TBIMG',
    value: '5',
    logoSrc: '',
    imageSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/img/elecreport/radiation_logo.png',
  },
];

// 放射检查报告状态
const FSJC_STATUSES = [
  {
    label: '已预约',
    color: '#32B9AA',
    value: 1,
  },
  {
    label: '已报到',
    color: '#32B9AA',
    value: 2,
  },
  {
    label: '已检查',
    color: '#32B9AA',
    value: 3,
  },
  {
    label: '报告已出',
    color: '#FE8F3C',
    value: 4,
  },
  {
    label: '报告撤回', // 取消审核
    color: '#FE8F3C',
    value: 5,
  },
];

export { FSJC_TYPES, FSJC_STATUSES };
