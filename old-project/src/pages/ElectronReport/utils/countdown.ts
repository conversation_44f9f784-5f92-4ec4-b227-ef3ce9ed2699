// 倒计时
export const secondCountDown = (time: number) => {
  let second: number = Number(time); // 秒
  let minute: number = 0; // 分
  let hour: number = 0; // 小时
  if (second > 60) {
    minute = Math.floor(second / 60);
    second = Math.floor(second % 60);
    if (minute > 60) {
      hour = Math.floor(minute / 60);
      minute = Math.floor(minute % 60);
    }
  }
  let result = `${Math.floor(second)}秒`;
  if (minute > 0) {
    result = `${Math.floor(minute)}分${result}`;
  }
  if (hour > 0) {
    result = `${Math.floor(hour)}小时${result}`;
  }
  return result;
};
