.container {
  height: 100%;
  padding: 0 30px;
  color: #333;
  font-size: 30px;
  background: #fff;
  .list {
    padding: 20px 0;
    border-bottom: 1px solid #eee;

    :global {
      .am-checkbox.am-checkbox-checked .am-checkbox-inner {
        background: #32b9aa;
        border-color: #32b9aa;
      }
      .am-list-item {
        padding: 0;
      }
    }

    .option {
      color: #333;
      font-size: 28px;
    }
  }
  .notice {
    padding: 0.4rem 0;
    color: #32b9aa;
    font-size: 28px;
  }

  .confirmButton {
    margin-top: 20px;
    color: #fff;
    background-color: #32b9aa;
    border-radius: 20px;
  }
}
