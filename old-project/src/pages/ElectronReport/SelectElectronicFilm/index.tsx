import React, { PureComponent } from 'react';
import { Checkbox, Button, Toast } from 'antd-mobile';
import { Dispatch, AnyAction } from 'redux';
import queryString from 'query-string';
import { history, connect } from 'umi';
import { ModuleEnum } from '@/utils/enum';
import { HxParameter } from '@/utils/parameter';
import { HxLocalStorage } from '@/utils/storage';
import styles from './index.less';

const { CheckboxItem } = Checkbox;

const options = [
  {
    label: '查看电子胶片',
    value: 1,
  },
  {
    label: '不查看电子胶片',
    value: 0,
  },
];

interface IProps {
  dispatch: Dispatch<AnyAction>;
  /**
   * 区分检验与检查，tabType: 0 - 检验，1 - 检查
   */
  tabType: string;
  /**
   * 列表数据
   */
  electronReport: any;
  /**
   * 点击事件-跳转报告详情
   */
  goReportDetail: any;
  location: {
    search: any;
  };
}

interface IState {
  checkedValue: number; // 选中项的下标
  configData: {
    // 后端可配置文案
    isShow: boolean; // 是否显示
    content: Array<any>; // 配置的温馨提示文案
  };
}

class SelectElectronicFilm extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      checkedValue: 3, // 选中项的下标
      configData: {
        // 后端可配置文案
        isShow: true, // 是否显示
        content: [], // 配置的温馨提示文案
      },
    };
  }

  componentDidMount() {
    document.title = '电子胶片查看选项';
    this.fetchNoticeConfig();
    console.log(API_BASE);
  }

  /**
   * 获取后端配置文案
   */
  fetchNoticeConfig = () => {
    const token = HxLocalStorage.get('token');
    this.props.dispatch({
      type: 'electronReport/configPictureOrTextDisplay',
      payload: {
        organCode: 'HID0101',
        position: 'DZJPCKXZWXTX',
        serverCode: 'M',
        token,
      },
      callback: (res: any) => {
        if (res && res.length > 0) {
          const { display, content } = res[0];
          this.setState({
            configData: {
              isShow: display,
              content: content.split('/n'),
            },
          });
        }
      },
    });
  };

  /**
   * 选项变化的回调函数
   * @param {*} value 选中的值
   */
  onChange = (value: any) => {
    this.setState({
      checkedValue: value,
    });
  };

  /**
   * 点击确定按钮的回调函数
   * 1. 校验是否选择；
   * 2. 调用选择接口
   * 3. 报告状态为已出，跳转至报告详情，否则返回报告列表
   */
  submit = () => {
    const { organCode } = HxParameter;
    const { checkedValue } = this.state;
    console.log(queryString.parse(this.props.location.search));
    const { id, status, channel, examNo, examName } = queryString.parse(this.props.location.search);
    if (checkedValue === 3) {
      // 未选提醒
      Toast.info('请确认您的选择');
    } else {
      // 调用选择接口
      this.props.dispatch({
        type: 'electronReport/getIsViewImage',
        payload: {
          id,
          isViewImage: checkedValue,
        },
        callback: (res: any) => {
          if (res.code === '1') {
            if (Number(status) === 4) {
              // 报告状态为：报告已出, 进入报告详情页面
              history.push({
                pathname: `/${ModuleEnum.MODULE_ELECTRON_REPORT}/radiologydetail`,
                state: {
                  organId: organCode === 'HYT' ? 'HID0101' : organCode,
                  id,
                  channel,
                  examNo,
                  examName,
                },
              });
            } else {
              // 其他状态，返回报告列表
              // history.push({
              //   pathname: `/${ModuleEnum.MODULE_ELECTRON_REPORT}/list`,
              // });
              history.go(-1);
            }
          }
        },
      });
    }
  };

  render() {
    const {
      checkedValue,
      configData: { isShow, content },
    } = this.state;
    return (
      <div className={styles.container}>
        <div className={styles.list}>请选择是否查看电子胶片？</div>
        {options.map((item) => (
          <div className={styles.list} key={item.value}>
            <CheckboxItem
              key={item.value}
              onChange={() => this.onChange(item.value)}
              checked={checkedValue === item.value}
            >
              <span className={styles.option}>{item.label}</span>
            </CheckboxItem>
          </div>
        ))}
        {isShow && content.length !== 0 && (
          <div className={styles.notice}>
            <div>温馨提示：</div>
            {content.map((item) => (
              <div key={item}>{item}</div>
            ))}
          </div>
        )}
        <Button className={styles.confirmButton} onClick={this.submit}>
          确定
        </Button>
      </div>
    );
  }
}

export default connect()(SelectElectronicFilm);
