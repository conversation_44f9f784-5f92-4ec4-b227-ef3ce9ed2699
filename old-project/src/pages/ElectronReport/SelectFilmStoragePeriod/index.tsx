import React, { FC, useEffect, useState } from 'react';
import { Toast, Modal } from 'antd-mobile';
import { connect, Dispatch, Loading, IElectronReportState } from 'umi';
import { isHytPerson } from '@/utils/platform';
import { getToken, getOpenId, getOrganCode } from '@/utils/parameter';
import classnames from 'classnames';

import AppScheme from '@/utils/AppScheme';
import { HxIcon } from '@/components';
import { IPricesType, toPayParamsType } from '../data';
import styles from './index.less';
import { sensorsRequest } from '@/utils/sensors';
import { HOSPITAL_CODE } from '../utils/enum';

interface IProps {
  electronReport: IElectronReportState;
  dispatch: Dispatch;
  loading?: boolean;
  location?: {
    query?: {
      hisCode?: string;
      examNo?: string;
      itemId?: string;
      id?: string;
    };
  };
}

const SelectFilmStoragePeriod: FC<IProps> = ({ dispatch, electronReport, location }) => {
  console.log('electronReport', electronReport);
  // props
  const { pricesList = [], textContentObj = {} } = electronReport;
  const { textContent = '' } = textContentObj;
  const { query = {} }: any = location;
  const { hisCode, itemId, id } = query;

  // useState
  const [selected, setSelected] = useState('');
  // const [totalPrice, setTotalPrice] = useState(0);
  useEffect(() => {
    if (pricesList.length) {
      const [item] = pricesList;
      const { packageId = '' } = item;
      setSelected(packageId);
    }
  }, [pricesList]);
  const fetchPricesList = () => {
    /** 获取资费列表 */
    dispatch({
      type: 'electronReport/getPricesList',
      payload: {
        id,
        itemId,
        // organCode: getOrganCode(),
        hisCode,
        organCode: hisCode,
      },
    });
    /** 获取温馨提示文案 */
    dispatch({
      type: 'electronReport/filmStorageText',
      payload: {
        id,
        hisCode,
        organCode: hisCode,
        textCode: hisCode === 'HID0101' ? 'image_package_notice' : 'image_package_notice_' + hisCode,
      },
    });
  };

  useEffect(() => {
    // 异步动态设置网页标题
    // setTimeout(() => {
    //   document.title = '我的胶片存储服务'; // 标题根据网页跳转
    // }, 0);
    fetchPricesList();
    sensorsRequest('VIDEO_PAGE_PAY', query);
  }, []);

  /**
   * 选择存储年限选项
   * @param type value值
   * @param total 需要支付的价格
   */
  const selectStroagePeriod = (type: any) => {
    setSelected(type);
    // setTotalPrice(total);
  };

  /** 跳转去支付判断 */
  const judge = (bizSysSeq: string, dealSeq: string, merchantSeq: string) => {
    if (isHytPerson()) {
      AppScheme.toPay({ dealSeq, bizSysSeq, merchantSeq });
    } else {
      window.location.href = `${API_BASE}/cloud/paygateway/getCashier?bizSysSeq=${bizSysSeq}&dealSeq=${dealSeq}&merchantSeq=${merchantSeq}&accessToken=${getToken()}&openid=${getOpenId()}`;
    }
  };

  /**
   * 获取订单来源
   */
  const getOrderSource = () => {
    if (isHytPerson()) {
      return 'APP';
    } else {
      return 'WECHAT_OFFICIAL_ACCOUNT';
    }
  };

  /**
   * 去支付按钮
   */
  const toPayBtn = () => {
    // 没有选择存储期限时
    if (!selected) {
      Toast.info('请选择胶片存储期限', 1.5);
    } else {
      const orderSource = getOrderSource();
      const orderChannel = '';
      const obj: any = {
        id,
        hisCode,
        itemId,
        packageId: selected,
        source: 'VIDEO_PAGE_PAY',
        orderSource,
        orderChannel,
      };
      if (['SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX].includes(hisCode)) {
        sensorsRequest('VIDEO_TOPAY_SL', obj);
      } else {
        sensorsRequest('VIDEO_TOPAY', obj);
      }
      dispatch({
        type: 'electronReport/createOrder',
        payload: { ...obj },
        callback: (payParams: toPayParamsType) => {
          const { exist = false, bizSysSeq, dealSeq, merchantSeq } = payParams;
          // 选择存储期限之后判断该订单是否还存在待支付订单
          if (exist) {
            // 存在
            Modal.alert(
              <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                <HxIcon iconName="tips" style={{ width: '24px', height: '24px' }} />
                <div style={{ marginLeft: '4px' }}>温馨提示</div>
              </div>,
              '您有一笔未完成的订单，请尽快完成支付',
              [
                {
                  text: '取消',
                },
                {
                  text: '去支付',
                  onPress: () => judge(bizSysSeq, dealSeq, merchantSeq), // 跳转到支付页面
                },
              ],
            );
            // 不存在直接跳转到支付
          } else {
            judge(bizSysSeq, dealSeq, merchantSeq);
          }
        },
      });
    }
  };
  const profitList = [
    {
      src: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/qy1.png',
      text: '不限次数查看该项检查完整原始影像图像及检查报告',
    },
    {
      src: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/qy2.png',
      text: '免费分享影像数据（面对面、微信及链接等分享方式）',
    },
    {
      src: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/qy3.png',
      text: '实时推送检查、报告状态消息',
    },
  ];
  console.log('pricesList', pricesList);
  return (
    <div className={styles.container}>
      <div className={styles.content}>
        {/* <div className={styles.headSelect}>
          <div>如您选择在线调阅并存储影像，平台可根据您选择的存储期限保存影像。在此期间，您可免费调阅影像。</div>
          <div>请选择影像存储期限：</div>
        </div> */}
        <div className={styles.headTop}>请选择数字影像服务期限：</div>
        <div className={styles.select}>
          {pricesList &&
            pricesList.length > 0 &&
            pricesList.map((item: IPricesType, index) => {
              return (
                <div
                  key={item.packageId}
                  className={classnames(
                    styles.selectList,
                    pricesList.length === 1
                      ? styles.oneItem
                      : pricesList.length === 2
                      ? styles.twoItem
                      : styles.multipleItem,
                  )}
                  style={{
                    border: selected === item.packageId && '1px solid #FE8F3C',
                    backgroundColor: selected === item.packageId && '#FFF4EB',
                  }}
                  onClick={() => selectStroagePeriod(item.packageId)}
                >
                  {!index && pricesList.length > 1 && <div className={styles.push}>推荐</div>}
                  {!Number(item.price) && (
                    <div className={styles.free}>
                      <span>免费体验</span>
                    </div>
                  )}
                  <div className={styles.timeLimit}>
                    {item.time}
                    {item.timeUnitDesc}
                  </div>
                  <div className={styles.price}>
                    <span>￥</span>
                    <span>{item.price}</span>
                  </div>
                  <div className={styles.label}>
                    <span>{item.description}</span>
                  </div>
                </div>
              );
            })}
        </div>
        <p className={styles.grayTip}>选择数字影像云端存储+在线调阅服务，根据选择的服务期限保存影像数据。</p>
      </div>
      <div className={styles.profit}>
        <p className={styles.title}>享受权益</p>
        <p className={styles.tip}>服务有效期内，您可以享受以下权益：</p>
        <div className={styles.profitlist}>
          {profitList.map((item) => (
            <div className={styles.item} key={item.text}>
              <img src={item.src} className={styles.profitImg} alt="" />
              <div className={styles.profitText}>{item.text}</div>
            </div>
          ))}
        </div>
      </div>
      <div className={styles.notice} style={{ whiteSpace: 'pre-line' }}>
        {textContent}
      </div>
      <div className={styles.footer}>
        <div className={styles.toPayBtn} onClick={() => toPayBtn()}>
          去支付
        </div>
      </div>
      {/* <div className={styles.bottomTab}>
        <div className={styles.totalPrice}>
          总计：<span>￥{totalPrice}</span>
        </div>
        <div className={styles.toPayBtn} onClick={() => toPayBtn()}>
          去支付
        </div>
      </div> */}
    </div>
  );
};

export default connect(({ electronReport, loading }: { electronReport: IElectronReportState; loading: Loading }) => ({
  electronReport,
  loading: loading.effects['address/findListAddress'],
}))(SelectFilmStoragePeriod);
