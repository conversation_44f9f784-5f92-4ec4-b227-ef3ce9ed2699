.container {
  min-height: 100vh;
  background-color: #fff;
  padding-bottom: 120px;
  .content {
    //padding: 28px 28px 60px 28px;
    background-color: #fff;
    .headTop {
      position: relative;
      z-index: 2;
      padding: 0 24px;
      color: #fff;
      font-size: 32px;
      line-height: 132px;
      background: linear-gradient(270deg, #ffc26e 0%, #fe8f3c 100%);
    }
    .headSelect {
      color: #03081a;
      //color: #333;
      //font-weight: 700;
      font-size: 32px;
      line-height: 48px;
      div {
        margin-bottom: 24px;
      }
    }
    .select {
      position: relative;
      top: -12px;
      z-index: 3;
      display: flex;
      justify-content: space-between;
      //margin-top: 60px;
      padding: 48px 24px 24px 24px;
      background: #fff;
      border-radius: 16px 16px 0 0;
      .push {
        position: absolute;
        top: -18px;
        left: -2px;
        width: 68px;
        height: 36px;
        color: #fff;
        font-size: 24px;
        text-align: center;
        background: linear-gradient(270deg, #ffb873 0%, #ff823f 100%);
        border-radius: 16px 0 16px 0;
      }
      .free {
        position: absolute;
        top: 16px;
        right: -18px;
        color: #fff;
        font-size: 16px;
        text-align: center;
        width: 80px;
        height: 28px;
        background: #ff823f;
        box-shadow: 0px 4px 8px 0px rgba(255, 130, 63, 0.5);
        border-radius: 16px 16px 16px 0px;
        span {
          transform: scale(0.7);
          // display: inline-block;
          position: absolute;
          width: 120px;
          text-align: center;
          line-height: 28px;
          top: 0;
          left: -20px;
        }
      }
      .selectList {
        padding: 24px 0 24px 0;
        text-align: center;
        border: 2px solid #e1e1e1;
        border-radius: 16px;
        position: relative;
        &.oneItem {
          width: 336px;
          margin-left: auto;
          margin-right: auto;
        }
        &.twoItem {
          width: calc((100% - 30px) / 2);
          box-sizing: border-box;
        }
        &.multipleItem {
          width: 218px;
          .timeLimit {
            font-weight: 500;
          }
          .label {
            color: #fff;
            font-weight: 700;
            font-size: 20px;
            // transform: scale(.8);
            height: 36px;
            width: 170px;
            text-align: center;
            background: linear-gradient(90deg, #fe8f3c 0%, #ffc26e 100%);
            border-radius: 18px;
            margin: auto;
            * {
              transform: scale(0.8);
              display: inline-block;
              width: 100%;
              text-overflow: ellipsis;
              white-space: nowrap;
              overflow: hidden;
              line-height: 36px;
            }
          }
        }
        .timeLimit {
          color: #03081a;
          //font-weight: 700;
          font-size: 40px;
          line-height: 56px;
        }
        .price {
          margin: 16px auto;
          color: #fc4553;
          :first-child {
            color: #fe8f3c;
            font-weight: 600;
            font-size: 36px;
            line-height: 50px;
          }
          :last-child {
            color: #fe8f3c;
            font-weight: bold;
            font-size: 80px;
            line-height: 94px;
            background: linear-gradient(270deg, #ffc26e 0%, #fe8f3c 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
          }
        }
        .label {
          color: #03081a;
          font-size: 20px;
          // transform: scale(.8);
          line-height: 40px;
          text-align: center;
        }
      }
    }
    .grayTip {
      padding: 0 24px;
      color: #989eb4;
      font-size: 24px;
      line-height: 40px;
      text-align: justify;
    }
  }
  .profit {
    margin: 80px auto 0 auto;
    padding: 0 24px;
    .title {
      margin-bottom: 24px;
      color: #03081a;
      font-weight: 600;
      font-size: 36px;
      line-height: 48px;
    }
    .tip {
      margin-bottom: 24px;
      color: #03081a;
      font-size: 28px;
      line-height: 48px;
    }
    .profitlist {
      .item {
        display: flex;
        align-items: center;
        min-height: 128px;
        padding: 12px;
        font-size: 28px;
        background: #fff1de;
        &:first-child {
          border-radius: 8px 8px 0 0;
        }
        &:nth-child(2) {
          background: #fff8f0;
        }
        &:last-child {
          border-radius: 0 0 8px 8px;
        }
        .profitImg {
          width: 80px;
          margin-right: 24px;
        }
        .profitText {
          color: #03081a;
          line-height: 40px;
          font-weight: 600;
        }
      }
    }
  }

  .notice {
    padding: 28px 32px;
    color: #989eb4;
    font-size: 24px;
    line-height: 50px;
  }

  .bottomTab {
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 20px;
    font-size: 28px;
    background-color: #fff;
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.5);
    .totalPrice {
      color: #333;
      font-size: 28px;
      span {
        color: #fc4553;
        font-weight: 700;
      }
    }
  }
  .toPayBtn {
    // width: 650px;
    height: 90px;
    color: #fff;
    font-size: 32px;
    line-height: 90px;
    text-align: center;
    background: linear-gradient(270deg, #ffc26e 0%, #fe8f3c 100%);
    border-radius: 46px;
  }
  .footer {
    position: fixed;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 10;
    padding: 16px 24px;
    background: #fff;
  }
}
