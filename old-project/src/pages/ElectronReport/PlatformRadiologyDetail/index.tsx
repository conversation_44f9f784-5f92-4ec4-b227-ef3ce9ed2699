import React, { memo, useEffect } from 'react';
import { Dispatch, AnyAction } from 'redux';
import { getChannelCode, getOrganCode } from '@/utils/parameter';
// import moment from 'moment';
import { HxIndicator, HxEmpty } from '@/components';
import { HxLocalStorage, HxSessionStorage } from '@/utils/storage';
import { connect, Loading, IElectronReportState } from 'umi';
import qs from 'query-string';
import styles from './index.less';

// import { Button, Toast, Tabs } from 'antd-mobile';

interface IProps {
  dispatch: Dispatch<AnyAction>;
  loading?: boolean;
  reportDetail: { [index: string]: any };
  location: {
    state: {
      item: any;
    };
  };
}

const ElectronReportDetail = memo((props: IProps) => {
  const cardData = HxSessionStorage.get('patientCardData');
  const organInfo = HxLocalStorage.get('organInfo') || {};
  const { organName = '' } = organInfo;
  const { cardId = '' } = cardData || {};
  const {
    location: { state },
  } = props;
  const { item } = state;
  const { dispatch, reportDetail, loading } = props;
  const {
    checkDesc = '',
    checkResult = '',
    pisNo = '',
    patAge = '',
    inspecItem = '',
    reportDate = '',
    reportDoc = '',
    eyeSee = '',
    examSee = '',
    diagnose = '',
    imageList = [],
    pdfBaseEncode = '',
    specName = '',
  } = reportDetail || {};
  const { reportType = '', reportNo = '', reportName = '', reclocDr = '' } = item;

  // 获取token
  const token = HxLocalStorage.get('token');

  const fetchData = () => {
    console.log(reportType, 'reportType', item);
    dispatch({
      type: 'electronReport/getPathologyDetail',
      payload: {
        cardId,
        reportType,
        reportNo,
        reclocDr,
        reportName,
      },
    });
  };

  /** 查看base64pdf */
  const turnBase64Pdf = () => {
    const params = {
      cardId,
      reportType,
      reportNo,
      reclocDr,
      reportName,
    };
    window.location.href = `${COMMON_DOMAIN}/parking-project/tfpdf?channelCode=${getChannelCode()}&organCode=${getOrganCode()}&${qs.stringify(
      params,
    )}&token=${token}`;
  };

  useEffect(() => {
    setTimeout(() => {
      document.title = reportName === '' ? '病理报告详情' : reportName;
    }, 0);
    fetchData();
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.pompt}>
        <span>此明细只做参考，请以窗口提供的明细为准</span>
      </div>
      <div className={styles.detail}>
        <div>
          <p>
            <span className={styles.detailTitle}>病理号</span>
            <span>{pisNo}</span>
          </p>
          <p>
            <span className={styles.detailTitle}>患者年龄</span>
            <span>{patAge}</span>
          </p>
          <p>
            <span className={styles.detailTitle}>取材部位</span>
            <span>{specName}</span>
          </p>
          <p>
            <span className={styles.detailTitle}>检查项目</span>
            <span>{inspecItem}</span>
          </p>
          <p>
            <span className={styles.detailTitle}>报告时间</span>
            <span>{reportDate}</span>
          </p>
          <p>
            <span className={styles.detailTitle}>诊断医生</span>
            <span>{reportDoc}</span>
          </p>
        </div>
      </div>
      {pdfBaseEncode && (
        <div className={styles.btnCard}>
          <div className={styles.left}>
            <img src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/yxbg.png" alt="" />
            <div className={styles.info}>
              <div className={styles.title}>检查/检验报告</div>
              <div className={styles.tip}>{organName}</div>
            </div>
          </div>
          <div className={styles.right}>
            <div className={styles.btn} onClick={turnBase64Pdf}>
              查看
            </div>
          </div>
        </div>
      )}
      {loading && <HxIndicator />}
      {!loading && eyeSee && eyeSee !== ' ' && (
        <div className={styles.textContainer}>
          <p className={styles.title}>肉眼所见</p>
          <pre>{eyeSee}</pre>
        </div>
      )}
      {!loading && ((examSee && examSee !== ' ') || imageList.length > 0) && (
        <div className={styles.textContainer}>
          <p className={styles.title}>镜下所见</p>
          <pre>{examSee}</pre>
          {imageList.map((itm: any) => {
            return <img className={styles.examSeeImg} src={itm.imageBase64} alt="" />;
          })}
        </div>
      )}
      {!loading && diagnose && diagnose !== ' ' && (
        <div className={styles.textContainer}>
          <p className={styles.title}>病理诊断</p>
          <pre>{diagnose}</pre>
        </div>
      )}
      {!loading && reportType === '2' && !checkDesc && !checkResult && (
        <div className={styles.emptyBox}>
          <HxEmpty
            emptyMsg={reportType === '2' ? '暂无检查报告，请耐心等待' : '暂无报告，请耐心等待'}
            emptyIcon="norecord"
            canRefresh={false}
          />
        </div>
      )}
    </div>
  );
});

export default connect(({ loading, electronReport }: { electronReport: IElectronReportState; loading: Loading }) => ({
  loading: loading.effects['electronReport/getDetail'],
  reportDetail: electronReport.reportDetail,
}))(ElectronReportDetail);
