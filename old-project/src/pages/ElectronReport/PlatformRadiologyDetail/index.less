@import '~@/styles/mixin.less';

// 电子报告详情页
.container {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 100%;
  background-color: #f7f7f7;

  // 顶部提示
  .pompt {
    display: flex;
    // background: #fffae4;
    padding: 0 24px;
    color: #fc4553;
    //padding: 28px;
    //color: #fc7e00;
    font-size: 26px;
    line-height: 72px;
    background: #ffedee;
  }

  .detail {
    box-sizing: border-box;
    padding: 30px;
    background-color: #fff;
    margin-bottom: 20px;

    .detailTitle {
      display: inline-block;
      min-width: 4em;
      margin-right: 40px;
      color: #03081a;
      font-weight: 600;
      font-size: 32px;
    }

    p {
      margin-bottom: 10px;
      color: #03081a;
      font-size: 32px;
    }

    pre {
      font-size: 18px;
    }

    p:last-child {
      margin: 0;
    }
  }

  .btnCard {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    padding: 24px;
    color: #989eb4;
    font-weight: 500;
    font-size: 24px;
    background: #fff;
    min-height: 144px;
    position: relative;
    .left {
      display: flex;
      align-items: center;
      img {
        width: 96px;
        height: 96px;
        margin-right: 30px;
      }
    }
    .info {
      .title {
        color: #03081a;
        font-size: 32px;
      }
      .tip {
        margin-top: 8px;
        font-weight: 400;
        line-height: 40px;
      }
      .fontBtn {
        margin-left: 16px;
        color: #ff823f;
        text-decoration: underline;
      }
    }
    .right {
      .btn {
        width: 150px;
        color: #fff;
        font-size: 28px;
        line-height: 60px;
        text-align: center;
        background: linear-gradient(270deg, #6cebe2 0%, #3ad3c1 100%);
        border-radius: 30px;
        &.btnCantClick {
          background: linear-gradient(270deg, #dfe7e6 0%, #ccd3d2 100%);
        }
        &.innormal {
          background: linear-gradient(270deg, #ffc26e 0%, #fe8f3c 100%);
        }
      }
    }
  }

  // 检验
  .dataList {
    box-sizing: border-box;
    margin-top: 10px;
    background-color: #fff;
    .itemBoxTitle {
      padding: 24px 24px 22px 24px;
      border-bottom: 4px solid #f4f5fa;
      display: flex;
      align-items: center;
      span:nth-of-type(1) {
        flex: 148;
      }
      span:nth-of-type(2) {
        flex: 74;
      }
      span:nth-of-type(3) {
        // flex: 45;
        width: 146px;
      }
      span {
        font-size: 28px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #989eb4;
      }
    }
    .itemBox {
      min-height: 90px;
      padding: 24px;
      .hxOnepxB();
      .list {
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;

        .listLeft {
          flex: 148;
        }
        .listEnd {
          // flex: 45;
          width: 146px;
          word-break: break-all;
        }
        .listRight {
          flex: 74;
        }
      }
      .list1 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-sizing: border-box;
        font-size: 28px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        .listLeft {
          flex: 148;
        }
        .listRight1 {
          flex: 140;
          color: #989eb4;
        }
      }
    }

    .listActive {
      background-color: #f3e5e7;
    }
  }

  // 检查
  .textContainer {
    margin-bottom: 20px;
    padding: 0 30px 20px 30px;
    //color: #aaa;
    color: #03081a;
    font-size: 28px;
    line-height: 48px;
    background: #fff;
    //border-top: 2px solid #eee;

    .title {
      padding: 30px 30px 30px 0px;
      color: #333;
      font-weight: 600;
      font-size: 30px;
      background: #fff;
      margin: 0;
    }

    pre {
      font-size: 28px;
      font-family: var(--adm-font-family) !important;
      white-space: pre-wrap;
      white-space: -moz-pre-wrap;
      white-space: -pre-wrap;
      white-space: -o-pre-wrap;
      word-wrap: break-word;
    }
  }

  .emptyBox {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
  }
}
.examSeeImg {
  width: 100%;
  margin-bottom: 10px;
}
