import React, { memo, useState, useEffect } from 'react';
// import { HxIcon } from '@/components';
import { connect, Loading, IElectronReportState } from 'umi';
import ImageSlides from 'react-imageslides';
import styles from './index.less';

interface IProps {
  location: {
    state: any;
  };
  electronReport: {
    radiologyDetail: any;
  };
}

const ElectronicFilmList = memo((props: IProps) => {
  const [imgArray, setImgArray] = useState<Array<any>>([]);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [imgIndex, setImgIndex] = useState<number | undefined>(undefined);

  useEffect(() => {
    document.title = '电子胶片';
    const {
      electronReport: { radiologyDetail = {} },
    } = props;
    const { imageReportUrl } = radiologyDetail;
    setImgArray(imageReportUrl);
  }, []);

  const onClose = () => {
    setIsOpen(false);
    setImgIndex(0);
  };

  const onOpenImg = (imgIndex: any) => {
    setIsOpen(true);
    setImgIndex(imgIndex);
  };

  const renderContent = (
    <div className={styles.container}>
      <div className={styles.main}>
        {imgArray &&
          imgArray.length &&
          imgArray.map((item, index) => {
            return (
              <div
                className={styles.imgBox}
                onClick={() => onOpenImg(index + 1)}
                key={(Math.random().toString().substr(3, 12) + Date.now()).toString()}
              >
                <img src={item} alt="" />
              </div>
            );
          })}
        <ImageSlides tapClose={false} images={imgArray} index={imgIndex} isOpen={isOpen} onClose={onClose} />
      </div>
    </div>
  );

  return renderContent;
});

export default connect(({ loading, electronReport }: { electronReport: IElectronReportState; loading: Loading }) => ({
  loading: loading.effects['electronReport/getreportlist'] || loading.effects['electronReport/getradioscopyList'],
  electronReport,
}))(ElectronicFilmList);
