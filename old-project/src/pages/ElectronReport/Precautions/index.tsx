import React from 'react';
import styles from './index.less';

const Precautions = () => {
  const steps = [
    '1、医学遗传产前诊断中心报告不能微信查询，请根据导诊单取报告。',
    '2、医学遗传产前诊断中心报告不能微信查询，请根据导诊单取报告。',
    '3、医学遗传产前诊断中心报告不能微信查询，请根据导诊单取报告。',
    '4、根据不同医院规定不同。内容也将会有变化。',
  ];

  return (
    <div className={styles.container}>
      {steps.map((item: String) => {
        return (
          <span className={styles.item} key={Math.random().toString(36).substr(3, 11)}>
            {item}
          </span>
        );
      })}
    </div>
  );
};

export default Precautions;
