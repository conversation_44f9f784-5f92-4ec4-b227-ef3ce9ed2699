import { But<PERSON>, DatePicker, Popup, Space } from 'antd-mobile-v5';
import React, { useCallback, useEffect, useState } from 'react';
import close from '@/assets/closeTime.png';
import moment from 'moment';
import classNames from 'classnames';
import styles from './index.less';

interface IProps {
  visible: boolean;
  onCancel?: () => void;
  onConfirm?: (startTime: moment.MomentInput, endTime: moment.MomentInput) => void;
  onReset?: () => void;
}
const TimePicker: React.FC<IProps> = (props) => {
  const { visible, onCancel, onConfirm, onReset } = props;
  const [startTimeVisible, setStartTimeVisible] = useState(false);
  const [endTimeVisible, setEndTimeVisible] = useState(false);
  const [startTime, setStartTime] = useState(undefined);
  const [endTime, setEndTime] = useState(undefined);

  const labelRenderer = useCallback((type: string, data: number) => {
    switch (type) {
      case 'year':
        return `${data}年`;
      case 'month':
        return `${data}月`;
      case 'day':
        return `${data}日`;
      case 'hour':
        return `${data}时`;
      case 'minute':
        return `${data}分`;
      case 'second':
        return `${data}秒`;
      default:
        return data;
    }
  }, []);

  const getDateLabel = (time) => {
    return `${moment(time).format('YYYY年MM月DD日')}`;
  };

  const clear = () => {
    setEndTime(undefined);
    setStartTime(undefined);
  };
  return (
    <Popup
      visible={visible}
      onMaskClick={() => {}}
      bodyStyle={{ height: '40vh', borderRadius: '12px 12px 0 0' }}
      // afterClose={() => {
      //   clear();
      // }}
    >
      <div className={styles.timePopup}>
        <div className={styles.header}>
          <span>筛选时间</span>
          <img src={close} alt="" onClick={() => onCancel && onCancel()} />
        </div>
        <div className={styles.content}>
          <div
            className={classNames(styles.startTime, startTime && styles.active)}
            onClick={() => setStartTimeVisible(true)}
          >
            {startTime ? <span className={styles.time}>{getDateLabel(startTime)}</span> : <span>起始时间</span>}
          </div>
          <span className={styles.zhi}>至</span>
          <div className={classNames(styles.endTime, endTime && styles.active)} onClick={() => setEndTimeVisible(true)}>
            {endTime ? <span className={styles.time}>{getDateLabel(endTime)}</span> : <span>终止时间</span>}
          </div>
        </div>
        <div className={styles.footer}>
          <Space style={{ '--gap': '23px', width: '100%' }} block>
            <Button
              fill="outline"
              color="primary"
              size="large"
              block
              shape="rounded"
              onClick={() => {
                clear();
                onReset && onReset();
                onCancel && onCancel();
              }}
            >
              重置
            </Button>
            <Button
              color="primary"
              size="large"
              block
              shape="rounded"
              onClick={() => {
                onConfirm && onConfirm(moment(startTime), moment(endTime), !!(startTime && endTime));
              }}
              disabled={!(startTime && endTime)}
            >
              确定
            </Button>
          </Space>
        </div>
      </div>
      <DatePicker
        title="开始日期"
        defaultValue={startTime}
        visible={startTimeVisible}
        onClose={() => setStartTimeVisible(false)}
        onConfirm={(v: any) => {
          setStartTime(v);
        }}
        style={{}}
        max={endTime}
        renderLabel={labelRenderer}
      />
      <DatePicker
        title="结束日期"
        defaultValue={endTime}
        visible={endTimeVisible}
        onClose={() => setEndTimeVisible(false)}
        onConfirm={(v: any) => {
          setEndTime(v);
        }}
        min={startTime}
        renderLabel={labelRenderer}
      />
    </Popup>
  );
};

export default TimePicker;
