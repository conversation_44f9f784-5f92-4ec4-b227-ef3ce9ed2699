.timePopup {
  width: 100%;
  padding: 0 32px;
  display: flex;
  height: 100%;
  flex-direction: column;
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 32px 0;
    span {
      font-size: 36px;
      font-weight: bold;
      text-align: left;
      color: #03081a;
    }
    img {
      width: 36px;
      height: 36px;
    }
  }
  .content {
    flex: 1;
    display: flex;
    align-items: center;
    .startTime {
      width: 298px;
      height: 72px;
      background: #f5f6fa;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28px;
      text-align: center;
      color: #bbbeca;
      flex: 1;
      &.active {
        color: #03081a;
      }
    }
    .zhi {
      font-size: 28px;
      color: #03081a;
      margin: 0 32px;
    }
    .endTime {
      width: 296px;
      height: 72px;
      background: #f5f6fa;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #bbbeca;
      font-size: 28px;
      flex: 1;
      &.active {
        color: #03081a;
      }
    }
  }
  .footer {
    height: 136px;
    * {
      flex: 1;
    }
  }
}
