import { HxIcon } from '@/components';
import React, { useEffect, useRef } from 'react';
import { Modal } from 'antd-mobile';
import { history } from 'umi';
import { ModuleEnum } from '@/utils/enum';
import queryString from 'query-string';
import { HOSPITAL_CODE } from '../../utils/enum';

export default function PayConfirm(props) {
  const {
    electronReport: {
      radiologyDetail: { itemId = '', hisCode = '', id = '', imageServiceStatus, examName = '' },
    },
  } = props;
  const YxyPopupShownDate = 'YxyPopupShownDate';
  const isModalRef = useRef<boolean>(false);
  const toSlectFilmStoragePeriod = () => {
    history.push({
      pathname: `/${ModuleEnum.MODULE_ELECTRON_REPORT}/selectfilmstorageperiod`,
      query: {
        hisCode,
        itemId,
        id,
      },
    });
  };

  // 获取当前日期字符串
  const getTodayString = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1;
    const date = now.getDate();
    return `${year}-${month}-${date}`;
  };

  // 存储key
  const getStorageKey = (itemId) => {
    return `${YxyPopupShownDate}-${itemId}`;
  };

  // 检查弹窗是否已经显示过
  const checkPopupShown = (itemId) => {
    const today = getTodayString();
    const key = getStorageKey(itemId);
    const shownDate = localStorage.getItem(key);
    return shownDate === today;
  };

  useEffect(() => {
    const { sourceType } = queryString.parse(props.location.search);
    // 华西医院屏蔽付费功能
    if ([HOSPITAL_CODE.HXYY].includes(hisCode) || sourceType === 'share') {
      return;
    }
    if (itemId && !checkPopupShown(itemId) && Number(imageServiceStatus) === 2 && !isModalRef.current) {
      Modal.alert(
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <HxIcon iconName="tips" style={{ marginRight: '2px' }} />
          温馨提示
        </div>,
        <div style={{ textAlign: 'left' }}>
          【{examName}】影像存储调阅服务己过期，服务过期后将不能调阅影像数据。如果继续浏览影像，可选择续费服务。
        </div>,
        [
          { text: '取消' },
          {
            text: '去续费',
            onPress: () => toSlectFilmStoragePeriod(), // 跳转到支付页面
          },
        ],
      );
      const today = getTodayString();
      const key = getStorageKey(itemId);
      localStorage.setItem(key, today);
      isModalRef.current = true;
    }
  }, [props]);

  return <></>;
}
