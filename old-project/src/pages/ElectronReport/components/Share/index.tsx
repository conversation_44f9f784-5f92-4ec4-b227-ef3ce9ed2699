import React, { useCallback, useEffect, useState } from 'react';
import { Dispatch, AnyAction } from 'redux';
import { connect } from 'dva';
// import { IElectronReportItem } from '@/models/connect';
import { CSSTransition, TransitionGroup } from 'react-transition-group';
import classnames from 'classnames';
import { configWechatShareStyle, copyText, getCurEvnHref, getSignUrl, str2utf8 } from '@/utils/tool';
import { SingWechatJSSDKDataType } from '@/typings/global';
import { isWechat, isHytPerson, isAndroid } from '@/utils/platform';
import AppScheme from '@/utils/AppScheme';
import { HxParameter, getChannelCode } from '@/utils/parameter';

import { IElectronReportState } from 'umi';
import { Icon, Switch, Toast } from 'antd-mobile';
import styles from './index.less';
import ReportQRCode from '../ReportQRCode';
import { sensorsRequest } from '@/utils/sensors';
import { HOSPITAL_CODE } from '../../utils/enum';

interface ShareParams {
  id: any;
  channel: string;
  examNo: string;
  hisCode: string;
  itemId: string;
}

interface IProps {
  dispatch: Dispatch<AnyAction>;
  showShare: boolean;
  cancle: any;
  shareParams: ShareParams;
  electronReport: any;
}

interface IIProps {
  dispatch: Dispatch<AnyAction>;
  cancle: any;
  close: any;
  electronReport: any;
  shareParams: ShareParams;
}

interface WXProps {
  dispatch: Dispatch<AnyAction>;
  chanelId: any;
  closeWXSetting: any;
  electronReport: any;
  shareParams: ShareParams;
  cancle: any;
}

const timeUnit = [
  {
    timeUnit: 'HOUR',
    unitText: '小时',
  },
  {
    timeUnit: 'DAY',
    unitText: '天',
  },
  {
    timeUnit: 'MONTH',
    unitText: '月',
  },
  {
    timeUnit: 'YEAR',
    unitText: '年',
  },
];

interface DUProps {
  dispatch: Dispatch<AnyAction>;
  closeModel: any;
  setTimeTextVal: any;
  electronReport: any;
  timeText: string;
}

interface EditProps {
  dispatch: Dispatch<AnyAction>;
  closeModel: any;
}

let str = '';
let arr = [];

/** 修改分享密码 */
const EditPassword = (props: EditProps) => {
  const { closeModel, dispatch } = props;
  const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 0];
  const [password, setPassword] = useState('');
  const [numberBox, setNumberBox] = useState([
    {
      value: '',
      index: 0,
    },
    {
      value: '',
      index: 1,
    },
    {
      value: '',
      index: 2,
    },
    {
      value: '',
      index: 3,
    },
    {
      value: '',
      index: 4,
    },
    {
      value: '',
      index: 5,
    },
  ]);
  const confirmPassword = () => {
    if (!password) {
      Toast.info('请设置密码');
    } else if (password.length < 6) {
      Toast.info('密码需是6位数');
    } else {
      dispatch({
        type: 'electronReport/updateState',
        payload: {
          password,
        },
      });
      closeModel();
    }
  };
  const setArrVal = (type) => {
    arr = JSON.parse(JSON.stringify(numberBox));
    if (type === 'add') {
      for (let i = 0; i < str.length; i += 1) {
        arr[i].value = str.charAt(i);
      }
    } else {
      for (let i = 0; i < arr.length; i += 1) {
        if (i < str.length) {
          arr[i].value = str.charAt(i);
        } else {
          arr[i].value = '';
        }
      }
    }

    setNumberBox(arr);
  };
  const inputPassword = (item) => {
    if (str.length === 6) return;
    str += item;
    setPassword(str);
    setArrVal('add');
  };

  const clearPassword = () => {
    if (!str || !str.length) return;
    if (str && str.length > 0) {
      str = str.slice(0, -1);
      setPassword(str);
      setArrVal('minus');
    }
  };
  return (
    <div className={styles.editPassword}>
      <div className={styles.top}>
        <div className={styles.header}>
          <Icon type="left" size="md" onClick={() => closeModel()} />
          <span>修改密码</span>
          <span className={styles.color3AD3C1} onClick={() => confirmPassword()}>
            确定
          </span>
        </div>
        <p>请输入6位数字密码：</p>
        <div className={styles.inputList}>
          {numberBox.map((item) => (
            <span className={styles.inputBox} key={item.index}>
              {item.value}
            </span>
          ))}
        </div>
      </div>
      {/** 键盘 */}
      <div className={styles.keybordBox}>
        <div className={styles.keybord}>
          {numbers.map((item) => (
            <span className={styles.key} onClick={() => inputPassword(item)} key={item}>
              {item}
            </span>
          ))}
        </div>
        <span className={styles.clear} onClick={() => clearPassword()} />
      </div>
    </div>
  );
};

/** 有效期设置 */
const DurationSetting = (props: DUProps) => {
  const { closeModel, setTimeTextVal, dispatch, electronReport, timeText } = props;
  // let sharetime = [
  //   { timeUnit: 'HOUR', time: 1 },
  //   { timeUnit: 'DAY', time: 5 },
  // ];
  const sharetime = JSON.parse(electronReport.hospitalConfig?.sharetime);
  sharetime.map((item) => {
    const { unitText } = timeUnit.filter((ele) => {
      return ele.timeUnit === item.timeUnit;
    })[0];
    item.text = `${unitText}内有效`;
    item.checked = item.time + item.text === timeText;
    return item;
  });
  const [timeList, setTimeList] = useState(sharetime);
  const clickTimeItem = (item, index) => {
    const arr = JSON.parse(JSON.stringify(timeList));
    arr.forEach((item) => {
      item.checked = false;
    });
    arr[index].checked = true;
    setTimeList(arr);
    setTimeTextVal(item);
    dispatch({
      type: 'electronReport/updateState',
      payload: {
        sharetimerule: JSON.stringify({ timeUnit: item.timeUnit, time: item.time }),
      },
    });
    closeModel(false);
  };
  return (
    <div className={styles.duration}>
      <div className={styles.header}>
        <Icon type="left" size="md" onClick={() => closeModel()} />
        <span>有效期设置</span>
      </div>
      {timeList.map((item: any, index) => (
        <div className={styles.timeList} key={item.text + index} onClick={() => clickTimeItem(item, index)}>
          <span>
            <b className={styles.color3AD3C1}>{item.time}</b>
            {item.text}
          </span>
          {item.checked && <Icon type="check" size="md" color="#3AD3C1" />}
        </div>
      ))}
    </div>
  );
};

/** 微信分享设置 */
const WXSetting = (props: WXProps) => {
  const { chanelId, closeWXSetting, dispatch, electronReport } = props;
  const [checked, setChecked] = useState(false);
  const [showDuration, setShowDuration] = useState(false);
  const [showEditPassword, setShowEditPassword] = useState(false);
  const [defaultTime, setDefaultTime] = useState({ timeUnit: '', time: '' });
  const imgUrl = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/default-doctor-logo.png'; // 需要更换，临时占位用
  // const defaultTime = JSON.parse(electronReport.hospitalConfig.sharetime)[0];
  const [timeText, setTimeText] = useState('');
  useEffect(() => {
    dispatch({
      type: 'electronReport/updateState',
      payload: {
        encryptionflag: checked ? 1 : 0,
        password: checked ? '123456' : '',
      },
    });
  }, [checked]);
  useEffect(() => {
    console.log('defaultTime========>', defaultTime);
    if (defaultTime && defaultTime.timeUnit && defaultTime.time) {
      const text = timeUnit.filter((item) => {
        return item.timeUnit === defaultTime.timeUnit;
      })[0].unitText;
      const defaultText = `${defaultTime.time}${text}内有效`;
      setTimeText(defaultText);
      dispatch({
        type: 'electronReport/updateState',
        payload: {
          sharetimerule: JSON.stringify({ timeUnit: defaultTime.timeUnit, time: defaultTime.time }),
        },
      });
    }
  }, [defaultTime]);
  const switchCheck = () => {
    setChecked(!checked);
  };
  const setDuration = () => {
    setShowDuration(true);
  };
  const showPassWordModel = () => {
    str = '';
    setShowEditPassword(true);
  };
  const closePassWordModel = () => {
    setShowEditPassword(false);
  };
  const closeDurationModel = () => {
    setShowDuration(false);
  };
  const setTimeTextVal = (item: any) => {
    setTimeText(item.time + item.text);
  };

  /** 设置分享给朋友的样式 */
  const defineShareAppStyle = useCallback(
    (link) => {
      const { radiologyDetail } = electronReport;
      // debugger;
      // const { id, channel, examNo, itemId, hisCode } = radiologyDetail;
      // const link = `${getCurEvnHref()}?sourceType=share&id=${id}&channel=${channel}&examNo=${examNo}&itemId=${itemId}&hisCode=${hisCode}`;
      if (chanelId === 0) {
        dispatch({
          type: 'electronReport/singWechatJSSDK',
          payload: {
            url: getSignUrl(),
            organCode: 'HYT',
            useDestineOrgan: true,
          },
          callback: (data: SingWechatJSSDKDataType) => {
            const title = '患者影像报告';
            const configData = {
              ...data,
              debug: false,
              jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'],
            };

            const shareData = {
              title,
              desc: `${radiologyDetail.patientName}丨向你分享了一份检查报告，请注查收`,
              imgUrl: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/share.png',
              link,
              success: () => {
                console.log('lcc-分享成功');
              },
            };
            console.log('configData', configData);
            dispatch({
              type: 'electronReport/updateState',
              payload: {
                wechatShareTips: true,
              },
            });
            setTimeout(() => {
              dispatch({
                type: 'electronReport/updateState',
                payload: {
                  wechatShareTips: false,
                },
              });
            }, 10 * 1000);
            configWechatShareStyle(configData, shareData);
          },
        });
      }
    },
    [electronReport, chanelId],
  );
  // useEffect(() => {
  //   defineShareAppStyle();
  // }, []);
  useEffect(() => {
    //重新生成分享链接
    if (HxParameter?.organCode === 'SL0101' && chanelId === 0) {
      doShare();
    }
  }, [chanelId]);
  const doShare = () => {
    // exeCommand('copy')不支持异步，所以写原生同步ajax 请求
    let node = '/cloud';
    if (APP_ENV === 'prod') {
      const { organCode } = HxParameter;
      const orgCodeList = ['HID0101', 'HYT', 'SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX];
      node = orgCodeList.includes(organCode as string) ? `${API_HXYY}/cloud` : `${API_BASE}/cloud`;
    } else {
      node = `${API_BASE}/cloud`;
    }
    const { password, encryptionflag, sharetimerule, sharetype, radiologyDetail } = electronReport;
    const { shareParams, cancle } = props;
    console.log('shareParams', shareParams);
    const { id, channel = 0, examNo, itemId, hisCode } = shareParams;
    const ajax = new XMLHttpRequest();
    ajax.open('post', `${node}/imagecloud/sharelinks/getsharelink`, false);
    ajax.setRequestHeader('Content-type', 'application/json');
    ajax.setRequestHeader('token', HxParameter.token);
    ajax.setRequestHeader('accessToken', HxParameter.token);
    ajax.send(
      JSON.stringify({
        // organCode: HxParameter.organCode,
        organCode: hisCode,
        hisCode,
        id,
        appCode: HxParameter.appCode,
        examinationId: id,
        channel,
        examNo,
        itemId,
        password,
        encryptionflag,
        sharetimerule: sharetimerule || JSON.stringify({ timeUnit: 'HOUR', time: 1 }),
        sharetype,
        channelCode: getChannelCode() || 'PATIENT_WECHAT',
      }),
    );

    if (Number(ajax.readyState === 4) && Number(ajax.status) === 200) {
      console.log(JSON.parse(ajax.responseText), '222');
      const { data, msg } = JSON.parse(ajax.responseText) || {};
      if (!data) {
        Toast.fail(msg);
        return;
      }
      if (chanelId === 0) {
        console.log('老数据调起微信分享----', data);
        // 调起微信分享
        if (isWechat()) {
          defineShareAppStyle(data);
        } // 公众号分享 原生分享待完成
        if (isHytPerson()) {
          const params = `title=患者影像报告+++text=${radiologyDetail.patientName}丨向你分享了一份检查报告，请注查收+++url=${data}&path=person/preview`;
          AppScheme.goToShareWechat(params);
        }
      } else if (chanelId === 1) {
        console.log('data------------>', data);
        // 链接分享，复制分享链接
        copyText(data);
      }
      cancle(); // 关闭分享窗口
    }
  };
  useEffect(() => {
    let defaultItem;
    const sharetime = electronReport.hospitalConfig.sharetime
      ? JSON.parse(electronReport.hospitalConfig.sharetime)
      : '';
    console.log('sharetime', sharetime);
    [defaultItem] = sharetime;
    sharetime &&
      sharetime.forEach((item) => {
        if (Number(item.time) === 6 && item.timeUnit === 'HOUR') {
          defaultItem = item;
        }
      });
    setDefaultTime(defaultItem);
  }, []);
  return (
    <>
      <div className={styles.settingBox}>
        <div className={styles.header}>
          <Icon type="left" size="md" onClick={() => closeWXSetting()} />
          <span>{chanelId === 0 ? '微信分享设置' : '链接分享设置'}</span>
        </div>
        <div className={styles.time}>
          <span>分享时长</span>
          <span className={styles.timeLong} onClick={setDuration}>
            <b>{timeText.slice(0, 1)}</b>
            {timeText.slice(1)}
            <Icon type="right" size="sm" color="#989EB4" />
          </span>
        </div>
        <div className={classnames(styles.secreat, !checked && styles.mgbt160)}>
          <span>加密设置</span>
          <Switch color="#3AD3C1" checked={checked} onClick={switchCheck} />
        </div>
        {checked && (
          <div className={styles.passWord}>
            <span>分享密码</span>
            <div>
              <span>{electronReport?.password}</span>
              <span className={styles.color3AD3C1} style={{ marginLeft: '12px' }} onClick={() => showPassWordModel()}>
                修改
                <Icon type="right" size="sm" color="#989EB4" />
              </span>
            </div>
          </div>
        )}
        <div className={styles.shareBtn} onClick={() => doShare()}>
          {chanelId === 0 ? '立即分享' : '生成链接'}
        </div>
      </div>
      {showDuration && (
        <DurationSetting
          closeModel={closeDurationModel}
          electronReport={electronReport}
          setTimeTextVal={setTimeTextVal}
          dispatch={dispatch}
          timeText={timeText}
        />
      )}
      {showEditPassword && <EditPassword closeModel={closePassWordModel} dispatch={dispatch} />}
    </>
  );
};
/** 分享渠道 */
const ShareChanel = (props: IIProps) => {
  const { cancle, close, dispatch, electronReport, shareParams } = props;
  const [showWXSetting, setShowWXSetting] = useState(false);
  const [chanelId, setChanelId] = useState('');
  const isAndroid = navigator.userAgent.match(/android/i);
  console.log(isAndroid, isHytPerson(), isWechat(), '平台判断');

  // const isIos = navigator.userAgent.match(/(iPhone|iPod|iPad);?/i);
  const chanelList = [
    {
      chanelName: '面对面扫码',
      chanelId: 2,
      imgSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/ewm.png',
    },
    {
      chanelName: '微信好友',
      chanelId: 0,
      imgSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/wx.png',
    },
    {
      chanelName: '链接分享',
      chanelId: 1,
      imgSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/link.png',
    },
  ];
  // const chanelList =
  //   (isAndroid && isHytPerson()) || isWechat()
  //     ? [
  //         // 华医通APP的安卓手机不展示微信分享
  //         // 分享渠道
  //         {
  //           chanelName: '面对面扫码',
  //           chanelId: 2,
  //           imgSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/ewm.png',
  //         },
  //         {
  //           chanelName: '链接分享',
  //           chanelId: 1,
  //           imgSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/link.png',
  //         },
  //       ]
  //     : [
  //         {
  //           chanelName: '面对面扫码',
  //           chanelId: 2,
  //           imgSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/ewm.png',
  //         },
  //         {
  //           chanelName: '微信好友',
  //           chanelId: 0,
  //           imgSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/wx.png',
  //         },
  //         {
  //           chanelName: '链接分享',
  //           chanelId: 1,
  //           imgSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/link.png',
  //         },
  //       ];
  useEffect(() => {
    dispatch({
      // 初始化密码
      type: 'electronReport/updateState',
      payload: {
        password: '123456',
      },
    });
  }, [chanelId]);

  /** 选择分享渠道 */
  const chooseChanel = (item: any) => {
    setChanelId(item.chanelId);
    const { radiologyDetail } = electronReport;
    const { hisCode = 'SL0101' } = radiologyDetail;
    const itemObj = { ...item, source: 'VIDEO_PAGE_DETAIl', organCode: hisCode };
    if ([0, 1].includes(item.chanelId)) {
      if (item.chanelId === 0) {
        //  weChart
        if (['SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX].includes(hisCode))
          sensorsRequest('VIDEO_SHARE_WECHART_SL', itemObj);
        else sensorsRequest('VIDEO_SHARE_WECHART', itemObj);
      }
      if (['SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX].includes(hisCode))
        sensorsRequest('VIDEO_SHARE_LINK_SL', itemObj);
      else sensorsRequest('VIDEO_SHARE_LINK', itemObj);
      setShowWXSetting(true);
    } else if (item.chanelId === 2) {
      if (['SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX].includes(hisCode))
        sensorsRequest('VIDEO_SHARE_SCANQR_SL', itemObj);
      else sensorsRequest('VIDEO_SHARE_SCANQR', itemObj);
      // 关闭所有弹窗，展示二维码弹窗
      close(false);
    }
    dispatch({
      type: 'electronReport/updateState',
      payload: {
        sharetype: item.chanelId,
      },
    });
  };
  const closeWXSetting = () => {
    setShowWXSetting(false);
  };
  return (
    <>
      <div className={styles.contentBox}>
        <p className={styles.title}>分享到</p>
        <div className={styles.chanelList}>
          {chanelList.map((item: any) => {
            return (
              <div className={styles.item} key={item.chanelId} onClick={() => chooseChanel(item)}>
                <img src={item.imgSrc} alt="" />
                <p className={styles.chanelName}>{item.chanelName}</p>
              </div>
            );
          })}
        </div>
        <p className={styles.tip}>
          <b className={styles.warning}>!</b>电子报告及影像数据涉及个人隐私，分享前请确认对方身份，避免个人隐私泄露
        </p>
        <div
          className={styles.cancle}
          onClick={() => {
            cancle();
          }}
        >
          取消
        </div>
      </div>
      {showWXSetting && (
        <WXSetting
          chanelId={chanelId}
          closeWXSetting={closeWXSetting}
          electronReport={electronReport}
          shareParams={shareParams}
          dispatch={dispatch}
          cancle={cancle}
        />
      )}
    </>
  );
};

/** 设置分享密码等 */

const Share = (props: IProps) => {
  const { showShare, cancle, shareParams, dispatch } = props;
  const [showShareChanel, setShowShareChanel] = useState(true);
  const { electronReport } = props;
  const close = (flag) => {
    setShowShareChanel(flag);
  };
  return (
    <>
      <TransitionGroup>
        {showShareChanel && (
          <CSSTransition appear in={showShare} timeout={500} classNames="slide">
            <div className={styles.mask}>
              <ShareChanel
                cancle={cancle}
                close={close}
                electronReport={electronReport}
                shareParams={shareParams}
                dispatch={dispatch}
              />
            </div>
          </CSSTransition>
        )}
        {!showShareChanel && <ReportQRCode shareParams={shareParams} cancle={cancle} />}
      </TransitionGroup>
    </>
  );
};

export default connect(({ electronReport }: { electronReport: IElectronReportState }) => ({
  electronReport,
}))(Share);
