.chanelList {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 48px;
  padding: 0 112px;
  font-size: 28px;
  text-align: center;
  .item {
    width: 33.333%;
    img {
      width: 128px;
      margin-bottom: 8px;
    }
    .chanelName {
      line-height: 56px;
    }
  }
}
.warning {
  display: inline-block;
  width: 24px;
  margin-right: 6px;
  color: #989eb4;
  font-size: 24px;
  line-height: 24px;
  text-align: center;
  border: 2px solid #989eb4;
  border-radius: 50%;
}
.mask {
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 10;
  width: 100%;
  color: #03081a;
  background: rgba(0, 0, 0, 0.6);

  &.show {
    top: 0;
  }
}
.contentBox {
  position: absolute;
  bottom: 0;
  width: 100%;
  font-size: 28px;
  background: #f5f6fa;
  border-radius: 16px 16px 0 0;
  .title {
    margin: 32px 0 40px 0;
    color: #989eb4;
    font-weight: 500;
    font-size: 32px;
    line-height: 56px;
    line-height: 28px;
    text-align: center;
  }
  .cancle {
    padding: 32px 0;
    font-size: 36px;
    line-height: 56px;
    letter-spacing: 1px;
    text-align: center;
    background: #fff;
  }
  .tip {
    margin-bottom: 24px;
    color: #989eb4;
    font-weight: 400;
    font-size: 20px;
    line-height: 28px;
    transform: scale(0.8);
  }
}
.settingBox,
.duration {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 32px 24px;
  font-size: 28px;
  background: #fff;
  border-radius: 16px 16px 0 0;
  .header {
    position: relative;
    margin-bottom: 38px;
    font-weight: 500;
    font-size: 32px;
    line-height: 56px;
    text-align: center;
    :global {
      .am-icon-left {
        position: absolute;
        top: 7px;
        left: 0;
      }
    }
  }
  .time,
  .secreat,
  .passWord {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 40px;
    line-height: 56px;
  }
  .passWord {
    margin-bottom: 64px;
  }

  .timeLong,
  .passWord {
    b {
      color: #3ad3c1;
    }
    :global {
      .am-icon-right {
        position: relative;
        top: 12px;
        margin-left: 14px;
      }
    }
  }
  .mgbt160 {
    margin-bottom: 160px;
  }
}
.color3AD3C1 {
  color: #3ad3c1;
}
.shareBtn {
  margin-bottom: 12px;
  color: #fff;
  font-weight: 600;
  font-size: 32px;
  line-height: 90px;
  text-align: center;
  background: #3ad3c1;
  border-radius: 23px;
}
.editPassword {
  position: absolute;
  bottom: 0;
  width: 100%;
  font-size: 28px;
  background: #d3d6db;
  border-radius: 16px 16px 0 0;
  .top {
    padding: 32px 24px 120px 24px;
    background: #fff;
    border-radius: 16px 16px 0 0;
    p {
      margin-bottom: 24px;
    }
  }
  .header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 38px;
    font-weight: 500;
    font-size: 32px;
    line-height: 56px;
  }
  .inputList {
    display: flex;
    justify-content: center;
    .inputBox {
      width: 88px;
      height: 88px;
      font-weight: 500;
      font-size: 40px;
      line-height: 88px;
      text-align: center;
      background: #f5f6fa;
      border-radius: 8px;
      &:not(:last-child) {
        margin-right: 16px;
      }
    }
  }
  .keybordBox {
    position: relative;
    .clear {
      position: absolute;
      right: 121px;
      bottom: 108px;
      width: 46px;
      height: 46px;
      background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/Delete.png')
        no-repeat center center;
      background-size: 100%;
    }
  }
  .keybord {
    position: relative;
    display: flex;
    flex-flow: wrap;
    justify-content: center;
    padding: 16px;
    padding-bottom: 80px;
    .key {
      width: 230px;
      margin-bottom: 10px;
      font-weight: 500;
      font-size: 56px;
      line-height: 96px;
      text-align: center;
      background: #fff;
      border-radius: 12px;

      &:not(:nth-child(3n)) {
        margin-right: 10px;
      }
    }
  }
}
.duration {
  min-height: 844px;
  line-height: 56px;
  .timeList {
    display: flex;
    justify-content: space-between;
    margin-bottom: 40px;
    b {
      font-weight: 500;
    }
  }
}

:global(.slide-enter),
:global(.slide-appear) {
  bottom: -100% !important;
}
:global(.slide-enter-active),
:global(.slide-appear-active) {
  bottom: 0 !important;
  transition: all 600 ease-in;
}
:global(.slide-exit) {
  bottom: 0 !important;
}
:global(.slide-exit-active) {
  bottom: -100% !important;
  transition: all 600 ease-in;
}
