import React, { PureComponent } from 'react';
import { connect } from 'dva';
import { Dispatch, AnyAction } from 'redux';
import { HxLocalStorage } from '@/utils/storage';
import { HxParameter, getOrganCode } from '@/utils/parameter';
// import { isHytDoctor } from '@/utils/platform';
import { Toast } from 'antd-mobile';
import QRCode from 'qrcode.react';
// import { HxIcon } from '@/components';
import falseImg from '@/assets/ElectronReport/二维码失效@3x .png';
import { IElectronReportState } from 'umi';
import { secondCountDown } from '../../utils/countdown';

import styles from './index.less';
import queryString from 'query-string';

interface ShareParams {
  id: any;
  channel: string;
  examNo: string;
  hisCode: string;
  itemId: string;
}
interface IProps {
  dispatch: Dispatch<AnyAction>;
  electronReport: any;
  shareParams: ShareParams;
  cancle: any;
}

interface IState {
  configData: Array<any>;
  qrImg: string;
  countdown: string;
  qrData: any;
}

let timer: any = null;

class ReportQRCode extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      configData: [], // 后端配置数据
      qrImg: '',
      countdown: '', // 二维码有效倒计时
      qrData: {},
    };
  }

  componentDidMount() {
    document.title = '报告二维码';
    this.fetchConfigData();
    this.fetchQRCode();
    console.log('sharePareamdsakdas', this.props);
  }

  componentWillUnmount() {
    clearInterval(timer);
    this.setState = () => {
      return false;
    };
  }

  /** 获取后端配置数据 */
  fetchConfigData = () => {
    const token = HxLocalStorage.get('token');
    this.props.dispatch({
      type: 'electronReport/configPictureOrTextDisplay',
      payload: {
        organCode: 'HID0101',
        position: 'DZBGEWMWXTX',
        serverCode: 'M',
        token,
      },
      callback: (res: any) => {
        res &&
          res.length > 0 &&
          this.setState({
            configData: res,
          });
      },
    });
  };

  /**
   * @desc 函数防抖
   * @param func 函数
   * @param wait 延迟执行毫秒数
   * @param immediate true 表立即执行，false 表非立即执行
   */
  debounce = (wait: number, immediate: boolean) => {
    let timeout = null;
    if (timeout) {
      clearTimeout(timeout);
    } else if (immediate) {
      const callNow = !timeout;
      timeout = setTimeout(() => {
        timeout = null;
      }, wait);
      if (callNow) {
        this.fetchQRCode();
      }
    } else {
      timeout = setTimeout(() => {
        this.fetchQRCode();
      }, wait);
    }
  };

  /** 获取二维码 */
  fetchQRCode = () => {
    clearInterval(timer);
    const token = HxLocalStorage.get('token');
    const { id, channel, examNo, hisCode, itemId } = this.props.shareParams;
    // const { id, channel, examNo, hisCode, itemId } = queryString.parse(this.props.location.search);
    console.log('this.props.location.search2', this.props, this.props.shareParams);
    const payload = {
      id,
      channel,
      examNo,
      token,
      organCode: hisCode,
      itemId,
    };
    const { channelCode = '', appCode = '' } = HxParameter;
    const organCode = getOrganCode() === 'HYT' ? 'HID0101' : getOrganCode();
    console.log('organCode-------------', organCode);
    Toast.loading('加载中', 1);
    this.props.dispatch({
      type: 'electronReport/fetchReportQRCode',
      payload,
      callback: (res: any) => {
        if (res.url) {
          this.setState(
            {
              qrImg: `${res.url}&organCode=${organCode}&id=${id}&channel=${channel}&channelCode=${channelCode}&appCode=${appCode}&hisCode=${hisCode}&itemId=${itemId}&share=1`,
              qrData: res,
            },
            () => {
              const { qrData } = this.state;
              let { seconds } = qrData;
              if (seconds !== '' && seconds !== 0) {
                timer = setInterval(() => {
                  if (seconds <= 0) {
                    clearInterval(timer);
                    this.setState({
                      countdown: '0秒',
                    });
                  } else {
                    this.setState({
                      countdown: secondCountDown((seconds -= 1)),
                    });
                  }
                }, 1000);
              }
            },
          );
        }
      },
    });
  };

  /** 渲染温馨提示 */
  renderNotice = () => {
    const { configData } = this.state;
    const showNotice = configData.length > 0 && configData[0].display && configData[0].content;
    if (showNotice) {
      return <div className={styles.notice}>{configData[0].content}</div>;
    }
    return null;
  };

  render() {
    const { qrImg, countdown } = this.state;
    console.log('qrImg=======>', qrImg);
    const {
      electronReport: { radiologyDetail },
    } = this.props;
    return (
      <div className={styles.container}>
        <div className={styles.main}>
          <div className={styles.title}>
            <span className={styles.icon} /> {radiologyDetail.patientName}的【{radiologyDetail.type}】检查报告
          </div>
          <div className={styles.content}>
            <div id="reportQrcode" style={{ marginLeft: '.90px' }} />
            <div className={styles.qrBox} onClick={() => this.debounce(5000, true)}>
              {qrImg && <QRCode value={qrImg} size={144} fgColor="#000000" />}
              {countdown === '0秒' && (
                <div className={styles.qrFailBox}>
                  <div className={styles.wrapBox}>
                    <img src={falseImg} alt="" />
                  </div>
                </div>
              )}
            </div>
            {countdown === '0秒' ? (
              <div className={styles.times}>
                <span>点击刷新二维码</span>
              </div>
            ) : (
              <div className={styles.times}>
                <span>{countdown}</span>后失效
              </div>
            )}
            <div className={styles.tip}>使用华医通APP/微信扫一扫，调阅检查报告</div>
            {/* {!isHytDoctor() && this.renderNotice()} */}
          </div>
          {/* <HxIcon iconName="close" /> */}
        </div>
        <span className={styles.close} onClick={() => this.props.cancle()}>
          &times;
        </span>
      </div>
    );
  }
}

export default connect(({ electronReport }: { electronReport: IElectronReportState }) => ({
  electronReport,
}))(ReportQRCode);
