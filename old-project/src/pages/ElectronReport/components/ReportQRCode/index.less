.container {
  position: fixed;
  top: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  font-size: 24px;
  background: rgba(0, 0, 0, 0.6);

  .close {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 64px;
    height: 64px;
    margin: 40px auto 0 auto;
    color: #fff;
    font-weight: 500;
    font-size: 50px;
    text-align: center;
    border: 6px solid #fff;
    border-radius: 50%;
  }
  .main {
    box-sizing: border-box;
    width: 630px;
    padding: 40px;
    color: #03081a;
    background-color: #fff;
    border-radius: 16px;

    .title {
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 48px;
      font-weight: 600;
      font-size: 32px;
      line-height: 48px;
      text-align: center;
      .circle {
        position: absolute;
        bottom: -20px;
        width: 40px;
        height: 40px;
        background-color: #32b9aa;
        border-radius: 20px;
      }
      .icon {
        display: inline-block;
        width: 48px;
        height: 48px;
        margin-right: 14px;
        background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/yxbg.png')
          no-repeat top center;
        background-size: 100% 100%;
      }
    }
    .content {
      display: flex;
      flex-direction: column;
      align-items: center;
      .qrBox {
        position: relative;
        display: flex;
        align-items: center;
        justify-items: center;
        width: 288px;
        height: 288px;
        margin: 0 auto;

        .qrFailBox {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          display: flex;
          align-items: center;
          justify-items: center;
          margin: 0 auto;
          background-color: rgba(255, 255, 255, 0.933);
          .wrapBox {
            display: flex;
            align-items: center;
            justify-items: center;
            width: 300px;
            height: 300px;
            margin: auto auto;
            border: 20px dashed #fff;
            img {
              width: 100px;
              height: 85px;
              margin: auto auto;
            }
          }
        }
      }
      .notice {
        padding: 60px 0;
        color: #32b9aa;
      }
      .times {
        margin-top: 32px;
        margin-bottom: 40px;
        font-size: 28px;
        span {
          color: #32b9aa;
        }
      }
      .tip {
        margin-bottom: 0;
        text-align: center;
      }
    }
  }
}
