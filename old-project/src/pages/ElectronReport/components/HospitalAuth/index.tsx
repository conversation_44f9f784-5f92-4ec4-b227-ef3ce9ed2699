import React, { FC, useEffect, useState } from 'react';
import { getOrganCode } from '@/utils/parameter';
import { getPlatform } from '@/utils/platform';
import hospitalAuth from '../../utils/hospitalAuth';

interface Props {
  orgCode: string[] | string;
  platFormList?: any[];
}

const HospitalAuth: FC<Props> = (props) => {
  const { orgCode: propsOrgCode, children, platFormList = [] } = props;
  const [orgRender, setOrgRender] = useState(false);
  const [platFormRender, setPlatFormRender] = useState(true);
  const orgCode = getOrganCode();

  useEffect(() => {
    const auth = hospitalAuth(propsOrgCode, orgCode);
    setOrgRender(auth);
  }, [propsOrgCode, orgCode]);

  useEffect(() => {
    if (platFormList && platFormList.length > 0) {
      setPlatFormRender(platFormList.includes(getPlatform()));
    } else {
      // 如果没有配置平台。默认都显示
      setPlatFormRender(true);
    }
  }, [platFormList]);

  return <>{orgRender && platFormRender && children}</>;
};

export default HospitalAuth;
