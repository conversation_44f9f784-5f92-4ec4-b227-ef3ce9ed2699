import React, { FC } from 'react';
import background from '@/assets/ElectronReport/<EMAIL>';
import launchMiniProgram from '@/utils/launchMiniProgram';
import { PlatformEnum, StorageEnum } from '@/utils/enum';
import { HOSPITAL_CODE, MiniApp } from '../../utils/enum';
import HospitalAuth from '../HospitalAuth';
import { connect, IElectronReportState } from 'umi';
import { Modal } from 'antd-mobile';
import { HxSessionStorage } from '@/utils/storage';
import { Toast } from 'antd-mobile-v5';

const InspectionInstructions: FC<any> = (props: { electronReport: IElectronReportState }) => {
  const { electronReport } = props;
  const { radioscopyList } = electronReport;
  const { HYT_PERSON } = PlatformEnum;
  const hospitalList = [HOSPITAL_CODE.HXYY];
  const platformList = [HYT_PERSON, PlatformEnum.BROWSER];
  const style: any = {
    // eslint-disable-next-line quotes
    backgroundImage: `url(${background})`,
    backgroundSize: '100% 100%',
    position: 'fixed',
    right: 0,
    bottom: '69px',
    display: 'inline-block',
    width: '80px',
    height: '80px',
  };
  const launchMiniApp = () => {
    const { content } = radioscopyList || { content: [] };
    if (content && content.length) {
      const { FSKZ } = MiniApp;
      const cardInfo = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA) || {};
      const { pmiNo } = cardInfo;
      const params = FSKZ({
        path: `pages/lauch/lauch?orgId=3274843079017472&patientCode=${pmiNo}`,
      });
      launchMiniProgram(params);
    } else {
      Toast.show({
        content: '您还没有检查哦',
      });
    }
  };

  return (
    <HospitalAuth orgCode={hospitalList} platFormList={platformList}>
      <span style={style} onClick={launchMiniApp} />
    </HospitalAuth>
  );
};

export default connect(({ electronReport }: { electronReport: IElectronReportState }) => ({
  electronReport,
}))(InspectionInstructions);
