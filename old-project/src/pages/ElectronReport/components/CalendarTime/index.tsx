import React, { useCallback, useState } from 'react';
import { Button, DatePicker, Grid, List, Popup } from 'antd-mobile-v5';
import moment from 'moment';
import styles from './index.less';

interface Props {
  visible: boolean;
  onCancel: Function;
  onConfirm: Function;
}

function CalendarTime(props: Props) {
  const { visible, onCancel, onConfirm } = props;
  const [startTimeVisible, setStartTimeVisible] = useState(false);
  const [endTimeVisible, setEndTimeVisible] = useState(false);
  const [startTime, setStartTime] = useState(undefined);
  const [endTime, setEndTime] = useState(undefined);
  const weekMap = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
  const labelRenderer = useCallback((type: string, data: number) => {
    switch (type) {
      case 'year':
        return `${data}年`;
      case 'month':
        return `${data}月`;
      case 'day':
        return `${data}日`;
      case 'hour':
        return `${data}时`;
      case 'minute':
        return `${data}分`;
      case 'second':
        return `${data}秒`;
      default:
        return data;
    }
  }, []);

  const getDateLabel = (time) => {
    return `${moment(time).format('YYYY年MM月DD日')} ${weekMap[moment(time).day()]}`;
  };

  const clear = () => {
    setEndTime(undefined);
    setStartTime(undefined);
  };

  return (
    <Popup
      visible={visible}
      onMaskClick={() => {}}
      bodyStyle={{ height: '100vh' }}
      afterClose={() => {
        clear();
      }}
    >
      <div className={styles.flexEnd}>
        <Button
          color="primary"
          fill="none"
          onClick={() => {
            clear();
          }}
        >
          清除
        </Button>
      </div>
      <List>
        <List.Item
          onClick={() => setStartTimeVisible(true)}
          style={{
            color: 'unset',
          }}
          extra={startTime ? getDateLabel(startTime) : '--年--月--日'}
        >
          开始日期
        </List.Item>
        <List.Item
          style={{
            color: 'unset',
          }}
          onClick={() => setEndTimeVisible(true)}
          extra={endTime ? getDateLabel(endTime) : '--年--月--日'}
        >
          结束日期
        </List.Item>
      </List>
      <div className={styles.footer}>
        <Grid columns={2} gap={20}>
          <Grid.Item>
            <Button block onClick={() => onCancel()}>
              取消
            </Button>
          </Grid.Item>
          <Grid.Item>
            <Button
              block
              color="primary"
              disabled={!(startTime && endTime)}
              onClick={() => {
                onConfirm(moment(startTime), moment(endTime));
              }}
            >
              确定
            </Button>
          </Grid.Item>
        </Grid>
      </div>
      <DatePicker
        title="开始日期"
        visible={startTimeVisible}
        onClose={() => setStartTimeVisible(false)}
        onConfirm={(v: any) => {
          setStartTime(v);
        }}
        style={{}}
        max={endTime}
        renderLabel={labelRenderer}
      />
      <DatePicker
        title="结束日期"
        visible={endTimeVisible}
        onClose={() => setEndTimeVisible(false)}
        onConfirm={(v: any) => {
          setEndTime(v);
        }}
        min={startTime}
        renderLabel={labelRenderer}
      />
    </Popup>
  );
}
export default CalendarTime;
