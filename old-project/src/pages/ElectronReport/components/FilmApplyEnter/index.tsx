import React, { FC, useEffect, useState } from 'react';
import { getOrganCode } from '@/utils/parameter';
import styles from './index.less';
import filmPrintIcon from '@/assets/ElectronReport/filmPrint.png';
import { connect, history } from 'umi';
import HxModal from '@/components/HxModal';
import { Modal } from 'antd-mobile-v5';
import { doPay } from '@/utils/common';

interface Props {
  inspection?: string;
}

const FilmApplyEnter: FC<Props> = (props) => {
  const { inspection } = props;

  const toApply = () => {
    //调接口判断
    //已申请
    HxModal.show({
      title: '温馨提示',
      content: '该检查已提交过胶片打印申请，可直接前往缴费，请选择是否缴费？',
      actions: [
        {
          text: '取消',
          key: 'cancel',
          onClick: () => {
            Modal.clear();
          },
        },
        {
          text: '去缴费',
          key: 'confirm',
          className: 'primary',
          onClick: () => {
            const res = {};
            const { bizSysSeq = '', dealSeq = '', merchantCode = '' } = res || {};
            Modal.clear();
            dealSeq && doPay(dealSeq, bizSysSeq, merchantCode);
          },
        },
      ],
    });
    //未申请
    // history.push('/FilmPrinting/FilmChioce');
  };

  useEffect(() => {}, []);

  return (
    <div className={styles.printEnter}>
      <div className={styles.row}>
        <div>
          <img src={filmPrintIcon} alt="" />
        </div>
        <div className={styles.title}>
          <div>胶片打印申请</div>
          <p>申请缴费后可去线下打印实体胶片</p>
        </div>
        <div className={styles.apply} onClick={toApply}>
          申请
        </div>
      </div>
      <div className={styles.tips}>* 仅支持开单日期在2025年4月30日后的医嘱</div>
    </div>
  );
};

export default FilmApplyEnter;
