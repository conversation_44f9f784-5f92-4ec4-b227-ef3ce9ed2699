.container {
  min-height: 144px;
  margin-top: 24px;
  background: #ffffff;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .content {
    display: flex;
    align-items: center;
    > span {
      display: inline-block;
      min-width: 96px;
      max-width: 96px;
      min-height: 96px;
      max-height: 96px;
      background-image: url(../../../../assets/downloadImg.png);
      background-size: 100% 100%;
      margin-right: 24px;
    }
    > div {
      display: flex;
      flex-direction: column;
      // justify-content: space-between;
      span:nth-of-type(1) {
        font-size: 32px;
        font-weight: 500;
        color: #03081a;
      }
      span:nth-of-type(2) {
        margin-top: 8px;
        font-size: 24px;
        font-weight: 400;
        color: #b0b3bf;
      }
    }
  }
  .btn {
    min-width: 150px;
    max-width: 150px;
    min-height: 60px;
    max-height: 60px;
    background: linear-gradient(90deg, #3ad3c1 0%, #68e9db 100%);
    border-radius: 30px;
    text-align: center;
    line-height: 60px;
    font-size: 28px;
    font-weight: 500;
    color: #ffffff;
    margin-left: 24px;
  }
  .disable {
    background: #ebedf5;
    color: #b0b3bf;
  }
}
