import React, { useCallback, useEffect, useRef, useState } from 'react';
import classnames from 'classnames';
import queryString from 'query-string';
import { getOrganCode } from '@/utils/parameter';
import { Modal } from 'antd-mobile';
import { isHytDoctor } from '@/utils/platform';
import { DownLoadStatus, useHistory } from 'umi';
import { HOSPITAL_CODE } from '../../utils/enum';
import { downloadImage, getImageInfoNew } from '../../service';
import styles from './index.less';

export default function RequestToDownload(props: { itemId: any }) {
  const { itemId } = props;
  const { sourceType } = queryString.parse(window.location.search);
  const history = useHistory();
  const organCode = getOrganCode() as string;
  const isShow =
    [HOSPITAL_CODE.MSRM, HOSPITAL_CODE.HID09XXXX].includes(organCode) && sourceType !== 'share' && !isHytDoctor();
  const isShowRef = useRef(isShow);
  const [imageCloudInfo, setImageCloudInfo] = useState<any>({
    expireDate: null,
    hisCode: null,
    itemId: null,
    status: null,
    items: [],
  });
  const { status, expireDate, items } = imageCloudInfo;
  const copyToClipboard = (text) => {
    // 创建一个临时输入框元素
    const tempInput = document.createElement('input');
    // 设置元素的值为需要复制的文本
    tempInput.value = text;
    // 将元素添加到文档中
    document.body.appendChild(tempInput);
    // 选择元素中的文本内容
    tempInput.select();
    // 执行复制命令
    document.execCommand('copy');
    // 将临时元素从文档中移除
    document.body.removeChild(tempInput);
  };

  const copyContent = async (text) => {
    try {
      if (navigator.clipboard && navigator.clipboard.writeText && navigator.permissions) {
        console.log('navigator.clipboard');
        await navigator.clipboard.writeText(text);
      } else {
        console.log('copyToClipboard');
        copyToClipboard(text);
      }
      console.log('Content copied to clipboard');
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const timerRef = useRef<any>(null);
  const getDownloadImageInfo = useCallback(async () => {
    if (itemId && isShowRef.current) {
      try {
        clearTimeout(timerRef.current);
        const result = await getImageInfoNew({
          itemId,
          organCode: getOrganCode(),
        });
        console.log('result');
        console.log(result);
        setImageCloudInfo(
          result || {
            expireDate: null,
            hisCode: null,
            itemId: null,
            status: 'CAN_REQUEST',
            items: [],
          },
        );
      } catch (error) {
        console.log(error);
      } finally {
        timerRef.current = setTimeout(() => {
          getDownloadImageInfo();
        }, 50 * 1000);
      }
    }
  }, [itemId]);

  const requestDowload = async () => {
    try {
      await downloadImage({
        itemId,
        organCode: getOrganCode(),
      });
      clearTimeout(timerRef.current);
      await getDownloadImageInfo();
      Modal.alert(
        <></>,
        <div style={{ textAlign: 'left' }}>影像下载申请已提交数据准备中，完成准备后可进行下载操作</div>,
        [
          {
            text: '确定',
            onPress: async () => {}, // 跳转到支付页面
          },
        ],
      );
    } catch (error) {
      console.log(error);
    } finally {
      getDownloadImageInfo();
    }
  };
  const download = () => {
    if (items.length) {
      if (items.length === 1) {
        const [{ url }] = items;
        copyContent(url);
        Modal.alert(
          <></>,
          <div style={{ textAlign: 'left' }}>
            <div>下载链接已复制到粘贴板，可通过手机或电脑端打开浏览器复制链接进行下载</div>
            <div style={{ color: 'red' }}>注：影像数据文件较大，请注意流量使用</div>
          </div>,
          [
            {
              text: '确定',
              onPress: () => {}, // 跳转到支付页面
            },
          ],
        );
      } else {
        history.push({
          pathname: '/electronreport/imagecloud-list',
          query: {
            ...queryString.parse(window.location.search),
            showImageType: 2,
          },
        });
      }
    }
  };

  useEffect(() => {
    getDownloadImageInfo();
    return () => {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    };
  }, [getDownloadImageInfo]);

  return isShow ? (
    <div className={styles.container}>
      <div className={styles.content}>
        <span />
        <div>
          <span>影像下载</span>
          {[DownLoadStatus.CanRequest, DownLoadStatus.Disabled].includes(status) && (
            <span>影像存储服务有效期内，提供免费下载</span>
          )}
          {status === DownLoadStatus.Downloading && <span>影像下载申请已提交，请耐心等待</span>}
          {status === DownLoadStatus.Compelete && (
            <span>影像数据已完成准备,可点击下载;临时数据将暂存至{expireDate},超过有效时间需重新申请下载.</span>
          )}
        </div>
      </div>
      {status === DownLoadStatus.CanRequest && (
        <div className={styles.btn} onClick={requestDowload}>
          申请
        </div>
      )}
      {status === DownLoadStatus.Downloading && <div className={classnames(styles.btn, styles.disable)}>下载</div>}
      {status === DownLoadStatus.Disabled && <div className={classnames(styles.btn, styles.disable)}>申请</div>}
      {status === DownLoadStatus.Compelete && (
        <div className={styles.btn} onClick={download}>
          下载
        </div>
      )}
    </div>
  ) : (
    <></>
  );
}
