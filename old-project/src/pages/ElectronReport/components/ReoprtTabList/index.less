@import '~@/styles/mixin.less';

.tabList {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 24px;
  overflow: auto;
  background-color: #f5f6f7;

  p {
    margin: 0;
  }

  .newTips {
    padding-left: 32px;
    color: #ff4f48;
    font-size: 26px;
    margin-bottom: 20px;
  }

  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 118px;
    padding: 0 30px;
    background-color: #fff;
    margin-bottom: 24px;
    border-radius: 16px;
    .hxOnepxB();

    img {
      width: 42px;
    }

    .itemLeft {
      display: flex;
      flex: 1;
      align-items: center;

      /* 垂直居中 */
      // padding: 0 20px;

      .headImg {
        padding-right: 20px;

        .headIcon {
          width: 8px;
          height: 32px;
          display: block;
          position: absolute;
          top: 24px;
          left: 0;
        }

        .blueBG {
          background-color: #568df2;
        }

        .orangeBG {
          background-color: #fe8f3c;
        }

        .greenBG {
          background-color: #3ad3c1;
        }

        .redBG {
          background-color: red;
        }
      }

      .title {
        padding: 30px 0 10px 20px;
        padding-left: 20px;
        color: #666;
        font-size: 28px;
        background: #fbfbfb;
      }

      .mark {
        display: flex;
        flex-direction: column;
        line-height: 40px;

        .reportName {
          width: 400px;
          max-height: 34px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          font-size: 32px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #03081a;
          line-height: 34px;

          span {
            font-size: 24px;
            font-family: PingFang SC, PingFang SC-Regular;
            font-weight: Regular;
            text-align: left;
            color: #568df2;
            background: linear-gradient(90deg, #dee9fc, #ecf5ff);
            border-radius: 8px;
            padding: 4px 8px;
            box-sizing: border-box;
            margin-left: 8px;
          }
        }

        span {
          display: block;
        }

        span:first-child {
          color: #222;
        }

        span:last-child {
          font-size: 28px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #989eb4;
          margin-top: 16px;
        }
      }
    }

    .itemRight {
      font-size: 26px;

      div {
        display: flex;
        align-items: center;

        .arrow {
          width: 32px;
          height: 32px;
          margin-left: 20px;
        }
      }
    }
  }

  .emptyBox {
    height: 700px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .fsjcReportList {
    margin-bottom: 24px;
    padding: 40px 0;
    padding-right: 24px;
    //padding: 30px 27px 29px 28px;
    font-size: 28px;
    background: #fff;
    border-bottom: 1px solid #eee;
    border-radius: 8px;

    .mark {
      position: relative;
      top: 10px;
      width: 8px;
      height: 32px;
      margin-right: 24px;
      background: #3ad3c1;

      &.red {
        background: #fc4553;
      }
    }

    .cloudInfo {
      display: flex;
      justify-content: space-between;
      padding: 0 32px;
      padding-right: 0;
      margin-bottom: 40px;
      color: #03081a;
      //margin-top:32px;
      font-size: 28px;
    }

    .mgtp0 {
      margin-top: 0 !important;
    }

    .goNext {
      color: #fe8f3c;
      font-size: 24px;

      &::after {
        display: inline-block;
        width: 16px;
        height: 16px;
        border-top: 4px solid #fe8f3c;
        border-right: 4px solid #fe8f3c;
        transform: rotate(45deg);
        content: '';
      }
    }

    .color3ad3c1 {
      color: #3ad3c1;
    }

    .colorfe8f3c {
      color: #fe8f3c;
    }

    .free {
      margin-right: 8px;
      padding: 4px 8px;
      color: #fff;
      font-size: 24px;
      line-height: 32px;
      background: linear-gradient(270deg, #ffc26e 0%, #fe8f3c 100%);
      border-radius: 8px;
    }

    .noborder {
      border-bottom: none !important;
    }

    .listInfo {
      display: flex;
      padding-bottom: 19px;
      border-bottom: 2px solid #eee;

      .images {
        position: relative;
        width: 128px;
        height: 128px;
        margin-right: 20px;
        border-radius: 8px;

        .logo {
          position: absolute;
          top: 0;
          right: 0;
          width: 40px;
        }

        .image {
          width: 128px;
          height: 128px;
          border-radius: 8px;
        }
      }

      .desc {
        flex: 1;
        padding-bottom: 20px;

        .nameAndStatus {
          display: flex;
          justify-content: space-between;
          margin-bottom: 14px;
          .reportType {
            display: inline-block;
            background: #2cd0bd;
            margin-right: 20px;
            // padding: 2px 10px;
            color: #fff;
            font-size: 24px;
            width: 64px;
            height: 32px;
            line-height: 32px;
            border-radius: 8px;
            text-align: center;
          }
          .name {
            display: -webkit-box;
            width: 374px;
            overflow: hidden;
            color: #333;
            font-weight: 700;
            font-size: 32px;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
          }

          .status {
            color: #fc7e00;
            font-size: 26px;
            text-align: right;

            .tips {
              font-size: 20px;
            }
          }
        }

        .time {
          font-size: 28px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #989eb4;

          &.mglt {
            margin-bottom: 8px;
            line-height: 40px;
          }
        }

        .note {
          color: #ce0000 !important;
          font-size: 24px;
        }
      }
    }

    .freeStorage {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      margin-top: 24px;

      .freeTime {
        margin-right: 36px;
        color: #333;
        font-size: 24px;

        span {
          color: #32b9aa;
          font-weight: 700;
        }
      }

      .renewal {
        display: flex;
        align-items: center;
        color: #fe8f3c;
        font-size: 28px;

        .fillArrow {
          width: 9px;
          height: 12px;
          margin-left: 8px;
        }
      }
    }

    .deadline {
      margin-top: 24px;
      color: #333;
      font-size: 24px;
      text-align: right;

      span {
        color: #32b9aa;
        font-weight: 700;
      }
    }

    .buttons {
      display: flex;
      justify-content: flex-end;
      width: 100%;

      .text {
        display: flex;
        align-items: center;
        justify-items: flex-end;
        height: 62px;
        margin-top: 47px;
        padding: 14px 18px;
        color: #32b9aa;
        font-size: 24px;
        border: 2px solid #32b9aa;
        border-radius: 32px;
        // .arrow {
        //   width: 15px;
        //   height: 26px;
        //   margin-left: 20px;
        // }
      }

      .viewBox {
        display: flex;

        // margin-top: 47px;
        div {
          height: 60px;
          // margin-top: 40px;
          margin-left: 24px;
          // width: 182px;
          padding: 0 16px;
          color: #03081a;
          font-size: 28px;
          line-height: 58px;
          text-align: center;
          border: 2px solid #b0b3bf;
          border-radius: 30px;

          &.normal {
            color: #03081a;
            border-color: #b0b3bf;
          }

          &.orange {
            color: #fe8f3c;
            border-color: #fe8f3c;
          }
        }

        // :first-of-type {
        //   margin-right: 10px;
        // }
      }
      .checkData {
        border-radius: 12px;
        height: 52px;
        line-height: 52px;
        width: 104px;
        padding: 0px;
        display: inline-block;
      }
    }
  }
}
.checkImage {
  display: flex;
  justify-content: end;
  div {
    margin-left: 24px;
    min-width: 140px;
    color: #03081a;
    font-size: 28px;
    text-align: center;
    border: 2px solid #b0b3bf;
    margin-bottom: 10px;
    line-height: 60px;
    border-radius: 30px;
    padding: 0 16px;
    height: 60px;
  }
}
