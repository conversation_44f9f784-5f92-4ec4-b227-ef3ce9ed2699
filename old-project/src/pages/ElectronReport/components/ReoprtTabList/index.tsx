import React from 'react';
import { HxEmpty, HxIcon } from '@/components';
import { AnyAction, Dispatch } from 'redux';
import { Modal, Toast } from 'antd-mobile';
// import { IElectronReportItem } from '@/models/connect';
import { getChannelCode, HxParameter } from '@/utils/parameter';
import { getPlatform, isHytDoctor, isHytPerson, isWechat } from '@/utils/platform';
import { ModuleEnum, PlatformEnum } from '@/utils/enum';
import _ from 'lodash';
import { connect, history, IElectronReportState, useLocation } from 'umi';
import classnames from 'classnames';

import checkActive from '@/assets/ElectronReport/<EMAIL>';
import rightArrow from '@/assets/ElectronReport/rightArrow.png';
import nocheckActive from '@/assets/ElectronReport/<EMAIL>';
import inspectionActive from '@/assets/ElectronReport/<EMAIL>';
import { sensorsRequest } from '@/utils/sensors';
import { FSJC_STATUSES, FSJC_TYPES } from '../../utils/dataDictionary';
import styles from './index.less';
import { getCheckDetail, openImageService } from '../../service';
import { HOSPITAL_CODE, MODULE_TYPE } from '../../utils/enum';
import { PROVIDER } from '../../enum';

interface IProps {
  dispatch: Dispatch<AnyAction>;
  /**
   * 区分检验与检查，tabType: 0 - 检验，1 - 检查
   */
  tabType: string;
  /**
   * 列表数据
   */
  electronReport: any;
  /**
   * 点击事件-跳转报告详情
   */
  goReportDetail: any;
  /**
   * 数字影像入口进入必传字段
   */
  moduletype?: string;
  /**
   * 刷新列表数据
   */
  fetchrRdiosCopyList?: any;
}

interface IIProps {
  showList: any;
  toSelectElectronicFilm: any;
  toDetail: any;
  moduletype?: string;
}

const platform = getPlatform();

// 放射检查报告列表
const ListItem3 = (props: IIProps) => {
  const { showList, toSelectElectronicFilm, toDetail, moduletype } = props;
  const { organCode } = HxParameter;
  if (isHytDoctor()) {
    // 医生端
    return showList.map((item: any) => {
      // 报告类型
      const reportType: any = FSJC_TYPES.find((items) => items.label === item.type);
      const mrImg = FSJC_TYPES.find((items) => items.label === 'TBIMG');
      const { logoSrc, imageSrc } = reportType !== undefined ? reportType : mrImg;
      // 报告状态
      const reportStatus = FSJC_STATUSES.find((items) => items.value === item.status);
      const { label } = reportStatus || {};
      console.log('label---', label);
      return (
        <div
          className={styles.fsjcReportList}
          onClick={() => {
            toDetail(item);
          }}
          key={item.id}
        >
          <div className={styles.listInfo}>
            <div className={styles.images}>
              <img className={styles.logo} src={logoSrc} alt="" />
              <img className={styles.image} src={imageSrc} alt="" />
            </div>
            <div className={styles.desc}>
              <div className={styles.nameAndStatus}>
                <div className={styles.name}>{item.examName}</div>
                <div className={styles.status}>{label}</div>
              </div>
              <div className={styles.time}>{item.hisName}</div>
              <div className={styles.time}>医嘱时间：{item.itemDate}</div>
              {/* {item.status === 3 && <div className={styles.note}>{item.note}</div>} */}
            </div>
            {/* <div style={{ marginTop: '25px', marginLeft: '15px' }}>
              <HxIcon className={styles.arrow} iconName="arrow-right" />
            </div> */}
          </div>
        </div>
      );
    });
  }
  // 除医生端
  return _.map(showList, (item) => {
    // 报告类型
    // const reportType: any = FSJC_TYPES.find((items) => items.label === item.type);
    // const mrImg = FSJC_TYPES.find((items) => items.label === 'TBIMG');
    // const { logoSrc, imageSrc } = reportType !== undefined ? reportType : mrImg;
    // 报告状态
    const reportStatus = FSJC_STATUSES.find((items) => items.value === item.status);
    const { label } = reportStatus || {};

    const channel = 0;

    // const toExportConsult = () => {
    //   const { organCode = '', appCode = '', channelCode = '', token = '' } = HxParameter;
    //   const env = isHytPerson() ? 1 : 2;
    //   window.location.href = `${API_ZXMZ}/online/imagecloud/intro?env=${env}&organCode=${organCode}&appCode=${appCode}&channelCode=${channelCode}&token=${token}`;
    // };

    return (
      <div className={styles.fsjcReportList} key={item.id} style={{ paddingBottom: item.status === 5 ? 0 : '20px' }}>
        <div className={classnames(styles.listInfo, styles.noborder)}>
          <div className={classnames(styles.mark, item.status === 4 ? styles.red : '')}>
            {/* <img className={styles.logo} src={logoSrc} alt="" />
            <img className={styles.image} src={imageSrc} alt="" /> */}
          </div>
          <div className={styles.desc}>
            <div className={styles.nameAndStatus}>
              <div className={styles.name}>
                {item.admType && (
                  <span
                    className={styles.reportType}
                    style={
                      item.admType === 'I'
                        ? { background: '#568df2' }
                        : item.admType === 'O'
                        ? { background: '#ff606d' }
                        : {}
                    }
                  >
                    {item.admType === 'O' ? '门诊' : item.admType === 'I' ? '住院' : '急诊'}
                  </span>
                )}
                {item.examName}
              </div>
              <div className={styles.status} style={{ color: item.status === 4 ? '#FC4553' : '#3AD3C1' }}>
                <div> {item.admType !== 'I' && label}</div>
                <div className={styles.tips}>{item.status === 3 ? '(报告未出)' : ''}</div>
              </div>
            </div>
            <div className={classnames(styles.time, styles.mglt)}>{item.hisName}</div>
            <div className={classnames(styles.time, styles.mglt)}>医嘱时间：{item.itemDate}</div>
            {item.status === 5 && (
              <p className={classnames(styles.time, styles.mglt, styles.note)}>
                您的报告已取消审核并撤回，请在报告重新出具后，再进行查看。
              </p>
            )}
            {/* {item.status === 3 && <div className={styles.note}>{item.note}</div>} */}
          </div>
        </div>
        {/* 判断是否是报告已出状态，payFlag为0展示天数，为1展示截止日期 status */}
        {/** status:检查状态：1.已预约 2.已报到 3.已检查 4.报告已出 5.报告撤销 */}
        {/* <div> */}
        {item.flag === 1 &&
          item.payFlag === 1 &&
          /** 未购买 */
          // <div className={styles.cloudInfo}>
          //   <div className={styles.freeTime}>
          //     {item.imageExpDays > 0 ? (
          //       <>
          //         <span className={styles.free}>限免</span>影像存储体验：剩余
          //         <span className={styles.colorfe8f3c}>{item.imageExpDays}</span>天
          //       </>
          //     ) : (
          //       <>
          //         <span className={styles.free}>限免</span>影像存储体验已到期
          //       </>
          //     )}
          //   </div>
          //   <div className={styles.goNext} onClick={() => toSelectElectronicFilm(item, channel, moduletype)}>
          //     续费
          //   </div>
          // </div>

          /** 已购买 */
          organCode !== HOSPITAL_CODE.ZYDY &&
          moduletype === MODULE_TYPE.YXY &&
          item.providerId === PROVIDER.ProviderLzImc && (
            <div>
              <div className={styles.cloudInfo} style={{ marginBottom: item.status === 5 ? 0 : '20px' }}>
                <div>
                  <span style={{ color: '#989EB4' }}>影像存储有效期：</span>{' '}
                  <span className={styles.color3ad3c1}>{item.imageExpDate} &nbsp;截止</span>
                </div>
                {organCode !== HOSPITAL_CODE.HXYY && (
                  <div
                    className={styles.goNext}
                    onClick={() => {
                      const itemObj = { ...item, source: 'VIDEO_PAGE_REPORT' };
                      if (['SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX].includes(item.hisCode))
                        sensorsRequest('VIDEO_RENEW_SL', itemObj);
                      else sensorsRequest('VIDEO_RENEW', itemObj);
                      toSelectElectronicFilm(item, channel, moduletype);
                    }}
                  >
                    续费
                  </div>
                )}
              </div>
            </div>
          )}
        {/* </div> */}
        {item.status === 3 && (
          <div
            className={styles.checkImage}
            onClick={() => {
              toDetail(item);
            }}
          >
            <div>查看</div>
          </div>
        )}
        {item.status === 3 && (
          <div className={styles.newTips}>
            <div>*报告出具后，我们将第一时间通知您，请耐心等待。</div>
            {organCode !== HOSPITAL_CODE.ZYDY && moduletype === MODULE_TYPE.YXY && (
              <div>*如已开通数字影像存储服务，可在报告出具后查看。</div>
            )}
          </div>
        )}
        <div className={styles.buttons}>
          {/* {item.flag === 1 && item.isSelectViewImage === 0 && item.status !== 4 ? ( */}
          <div className={classnames(styles.viewBox, styles.mgtp0)}>
            {/* <span className={styles.interpretation}>报告解读</span> */}
            {item.status === 4 && (
              <>
                {/* {item.type !== 'DR' &&
                item.hisCode !== 'SL0101' && ( // 双流医院不展示此按钮，华西上线前的数据不展示此按钮 暂时屏蔽，产品说不上线
                    <div
                      className={styles.normal}
                      onClick={() => {
                        toExportConsult();
                      }}
                    >
                      影像咨询
                    </div>
                  )} */}

                <div
                  className={styles.view}
                  onClick={async () => {
                    const itemObj = { ...item, source: 'VIDEO_PAGE_REPORT' };
                    if (['SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX].includes(item.hisCode))
                      sensorsRequest('VIDEO_WATCHREPORT_SL', itemObj);
                    else sensorsRequest('VIDEO_WATCHREPORT', itemObj);
                    const { examNo, itemId, hisCode } = item;
                    const param = {
                      examNo,
                      hisCode,
                      organCode: hisCode,
                      itemId: encodeURIComponent(itemId),
                      channel: 0,
                    };
                    const res = await getCheckDetail(param);
                    if (res) toDetail(item, moduletype);
                    else Toast.info('报告数据正在获取，请稍候再试');
                  }}
                >
                  {organCode === 'HID0101' ? <span className={styles.checkData}>查看</span> : '查看报告'}
                </div>
              </>
            )}
            {/* 资阳第一人民医院不显示开通 */}
            {((organCode !== HOSPITAL_CODE.ZYDY &&
              moduletype === MODULE_TYPE.YXY &&
              item.flag === 1 &&
              item.payFlag === 0 &&
              item.status !== 5 &&
              organCode !== HOSPITAL_CODE.HXYY) ||
              item.showOpenOption) && (
              /** 未购买, 但撤销状态不展示此按钮 */
              <div
                className={styles.orange}
                onClick={() => {
                  toSelectElectronicFilm(item, channel, moduletype);
                }}
                style={{ marginTop: 0 }}
              >
                开通影像存储服务
              </div>
            )}
          </div>
        </div>
      </div>
    );
  });
};

const ReoprtTabList = (props: IProps) => {
  const locations: any = useLocation();
  let { organCode } = HxParameter;
  organCode = organCode === 'HYT' ? 'HID0101' : organCode;
  const {
    tabType,
    goReportDetail,
    electronReport: { checkoutList = [], inspectList = [], caseList = [], radioscopyList = {} },
    moduletype,
    fetchrRdiosCopyList,
  } = props;
  let content = [];
  if (radioscopyList) {
    content = radioscopyList.content;
  }
  const showList =
    tabType === '1'
      ? checkoutList
      : tabType === '2'
      ? inspectList
      : tabType === '4'
      ? caseList
      : tabType === '3'
      ? content || []
      : [];
  const emptyText = ['SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX].includes(organCode as string)
    ? '暂无报告'
    : tabType === '1'
    ? '暂无检验报告'
    : tabType === '2'
    ? '暂无其他检查报告'
    : tabType === '4'
    ? '暂无病理报告'
    : tabType === '3'
    ? '暂无放射检查报告'
    : '暂无检查报告';

  const checkTime = (end: string) => {
    // 判断存储时间是否超过3年
    const startDate = new Date().valueOf();
    const endDate = new Date(end.replace(/-/g, '/')).valueOf();
    const days = (endDate - startDate) / (1 * 24 * 60 * 60 * 1000);
    // console.log(days, endDate, startDate, end);
    // return days >= 10950;
    return days >= 1820;
  };
  /**
   * 跳转到电子胶片查看选择页面
   * @param {*} rowData 某条放射检查数据
   */
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const toSelectElectronicFilm = (rowData: any, channel?: number, moduletype?: string) => {
    const {
      examNo = '',
      id = '',
      hisCode = 'SL0101',
      itemId = '',
      imageExpDate = '',
      showOpenOption = false,
    } = rowData;
    const rowDataObj = { ...rowData, source: 'VIDEO_PAGE_REPORT ' };
    if (['SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX].includes(hisCode)) sensorsRequest('VIDEO_OPEN_SL', rowDataObj);
    else sensorsRequest('VIDEO_OPEN', rowDataObj);
    // 华西医院开通服务
    if (showOpenOption && [HOSPITAL_CODE.HXYY].includes(hisCode)) {
      Modal.alert(
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <HxIcon iconName="tips" style={{ marginRight: '2px' }} />
          开通服务
        </div>,
        <div style={{ textAlign: 'center', color: '#353948' }}>请确认开通数字影像存储服务?</div>,
        [
          { text: '取消' },
          {
            text: '确认开通',
            onPress: async () => {
              try {
                const body = {
                  itemId,
                  organCode: hisCode,
                };
                const result = await openImageService(body);
                console.log('result', result);
                if (result && result.code === '1') {
                  Toast.show('开通成功');
                  setTimeout(() => {
                    Toast.hide();
                    fetchrRdiosCopyList && fetchrRdiosCopyList();
                  }, 300);
                }
              } catch (err) {
                console.log(err);
              }
            },
          },
        ],
      );
      return;
    }
    if (checkTime(imageExpDate)) {
      Modal.alert(
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <HxIcon iconName="tips" style={{ marginRight: '2px' }} />
          温馨提示
        </div>,
        <div style={{ textAlign: 'justify', color: '#353948' }}>
          <p>该项检查的影像存储有效期为{imageExpDate},请确认是否继续续费？</p>
        </div>,
        [
          { text: '取消' },
          {
            text: '继续续费',
            onPress: () => {
              history.push(
                // `/${ModuleEnum.MODULE_ELECTRON_REPORT}/selectfilmstorageperiod?channel=${channel}&id=${id}&examNo=${examNo}&reportName=${examName}&status=${status}&organId=${organCode}&moduletype=${moduletype}`,
                `/${ModuleEnum.MODULE_ELECTRON_REPORT}/selectfilmstorageperiod?examNo=${examNo}&hisCode=${hisCode}&itemId=${itemId}&id=${id}`,
              );
            },
          },
        ],
      );
    } else {
      history.push(
        `/${ModuleEnum.MODULE_ELECTRON_REPORT}/selectfilmstorageperiod?examNo=${examNo}&hisCode=${hisCode}&itemId=${itemId}&id=${id}`,
      );
    }
  };

  /**
   * 跳转到放射详情页面
   * @param {*} rowData 某条放射检查数据
   */
  const toRadiologyDetail = (rowData: any, moduletype: string) => {
    console.log(11111111, rowData);
    if (rowData.status === 5) {
      Toast.info('报告已取消审核并撤回，请在报告重新出具后，再进行查看。');
      return;
    }
    if (rowData.status !== 4 && rowData.status !== 3) {
      Toast.info('报告未出');
      return;
    }
    const {
      query: { servType = '' },
    } = locations;
    const {
      id = '',
      examNo = '',
      examName = '',
      isSelectViewImage,
      flag,
      hisCode = '',
      itemId = '',
      doctorCode = '',
    } = rowData;
    let channel = 0;
    if (isHytDoctor()) {
      channel = 1;
    } else if (isHytPerson() || isWechat() || platform === PlatformEnum.BROWSER) {
      channel = 0;
    }
    if (!isHytDoctor() && isSelectViewImage === 0 && flag === 1) {
      // 非医生端并且没有选择过查看电子胶片，跳转到选择页面
      toSelectElectronicFilm(rowData, channel, moduletype);
    } else {
      // 跳转详情页面
      history.push({
        pathname: `/${ModuleEnum.MODULE_ELECTRON_REPORT}/radiologydetail`,
        query: {
          channelCode: getChannelCode(),
          organId: organCode,
          id,
          channel,
          examNo,
          examName,
          hisCode,
          itemId,
          servType,
          doctorCode,
        },
      });
    }
  };
  const renderContent = (
    <div className={styles.tabList}>
      {/* 只针对华西医院且除医生端以外 */}
      {(organCode === 'HID0101' || organCode === 'HXTF0101') && platform !== PlatformEnum.HYT_DOCTOR ? (
        showList.length > 0 ? (
          //  放射检查报告列表
          tabType === '3' ? (
            <ListItem3
              showList={showList}
              toSelectElectronicFilm={toSelectElectronicFilm}
              toDetail={toRadiologyDetail}
              moduletype={moduletype}
            />
          ) : (
            showList.map((item: any) => {
              return (
                <div
                  key={item.reportNo}
                  className={styles.item}
                  onClick={
                    tabType === '1'
                      ? item.reportStatus !== 'E'
                        ? () => goReportDetail(item)
                        : organCode === 'HXTF0101' && tabType === '1'
                        ? () => goReportDetail(item)
                        : () => {}
                      : () => (tabType === '4' ? goReportDetail({ ...item, reportType: '4' }) : goReportDetail(item))
                  }
                >
                  <div className={styles.itemLeft}>
                    <p className={styles.headImg}>
                      {tabType === '1' && item.reportStatus === 'R' && (
                        // <img src={checkActive} alt="." />
                        <span className={`${styles.headIcon} ${styles.blueBG}`} />
                      )}
                      {tabType === '1' && item.reportStatus !== 'R' && (
                        // <img src={nocheckActive} alt="." />
                        <span className={`${styles.headIcon} ${styles.blueBG}`} />
                      )}
                      {tabType === '2' && (
                        // <img src={inspectionActive} alt="." />
                        <span className={`${styles.headIcon} ${styles.greenBG}`} />
                      )}
                      {tabType === '4' && (
                        // <img src={inspectionActive} alt="." />
                        <span className={`${styles.headIcon} ${styles.orangeBG}`} />
                      )}
                    </p>
                    <p className={styles.mark}>
                      <span
                        className={styles.reportName}
                        style={{ color: tabType !== '1' && tabType !== '2' && tabType !== '4' ? '#aaa' : '#222' }}
                      >
                        {tabType === '4'
                          ? `病理号：${item.reportNo}`
                          : tabType === '1'
                          ? item.reportName +
                            (item?.mergeFlag !== null && item?.mergeFlag !== undefined ? (
                              <span>{item.mergeFlag}</span>
                            ) : (
                              ''
                            ))
                          : item.reportName}
                      </span>
                      <span>医嘱时间{item.reportDate}</span>
                    </p>
                  </div>
                  {tabType === '1' && (
                    <div className={styles.itemRight}>
                      {item.reportStatus !== 'E' ? (
                        <div style={{ color: '#3AD3C1' }}>
                          报告已出
                          {/* <HxIcon className={styles.arrow} iconName="arrow-right" /> */}
                          <img className={styles.arrow} src={rightArrow} alt="" />
                        </div>
                      ) : (
                        <span style={{ color: '#CE0000' }}>{organCode !== 'HXTF0101' ? '报告未出' : ''}</span>
                      )}
                    </div>
                  )}
                  {tabType === '2' && (
                    <div className={styles.itemRight}>
                      <div style={{ color: '#32B9AA' }}>
                        {/* <HxIcon className={styles.arrow} iconName="arrow-right" /> */}
                        <img className={styles.arrow} src={rightArrow} alt="" />
                      </div>
                    </div>
                  )}
                  {tabType === '4' && (
                    <div className={styles.itemRight}>
                      <div style={{ color: '#32B9AA' }}>
                        {/* <HxIcon className={styles.arrow} iconName="arrow-right" /> */}
                        <img className={styles.arrow} src={rightArrow} alt="" />
                      </div>
                    </div>
                  )}
                </div>
              );
            })
          )
        ) : (
          <div className={`${styles.emptyBox} test2`}>
            <HxEmpty emptyMsg={emptyText} emptyIcon="norecord" canRefresh={false} isNewImg />
          </div>
        )
      ) : // 针对华西医生端和其他医院的检查报告
      showList.length > 0 ? (
        //  放射检查报告列表
        tabType === '3' ? (
          <ListItem3
            showList={showList}
            toSelectElectronicFilm={toSelectElectronicFilm}
            toDetail={toRadiologyDetail}
            moduletype={moduletype}
          />
        ) : (
          showList.map((item: any) => {
            return (
              <div key={item.reportNo} className={styles.item} onClick={() => goReportDetail(item, tabType)}>
                <div className={styles.itemLeft}>
                  <p className={styles.headImg}>
                    {tabType === '1' && item.reportStatus === 'R' && <img src={checkActive} alt="." />}
                    {tabType === '1' && item.reportStatus !== 'R' && <img src={nocheckActive} alt="." />}
                    {tabType === '2' && <img src={inspectionActive} alt="." />}
                  </p>
                  <p className={styles.mark}>
                    <span style={{ color: tabType !== '1' && tabType !== '2' ? '#aaa' : '#222' }}>
                      {item.reportName}
                    </span>
                    <span>医嘱时间：{item.reportDate}</span>
                  </p>
                </div>
                <div className={styles.itemRight}>
                  {/* {item.reportStatus !== 'E' ? (
                    <div style={{ color: '#32B9AA' }}>
                      报告已出
                      <HxIcon className={styles.arrow} iconName="arrow-right" />
                    </div>
                  ) : (
                    <span style={{ color: '#CE0000' }}>报告未出</span>
                  )} */}
                  {/* <HxIcon className={styles.arrow} iconName="arrow-right" /> */}
                  <img className={styles.arrow} src={rightArrow} alt="" />
                </div>
              </div>
            );
          })
        )
      ) : (
        <div className={`${styles.emptyBox} test1`}>
          <HxEmpty emptyMsg={emptyText} emptyIcon="norecord" canRefresh={false} />
        </div>
      )}
    </div>
  );

  return renderContent;
};

export default connect(({ electronReport }: { electronReport: IElectronReportState }) => ({
  electronReport,
}))(ReoprtTabList);
