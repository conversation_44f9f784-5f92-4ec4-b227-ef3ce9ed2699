import React from 'react';
import { Modal } from 'antd-mobile';

const OnlineConsultationNoticeModal = ({ visible, onCancel, onOk, content, showCancel = true }: any) => {
  return (
    <Modal
      wrapClassName="onlineConsultationNotice"
      visible={visible}
      transparent
      title="电子报告咨询须知"
      footer={
        showCancel
          ? [
              {
                text: '我已了解，进入咨询',
                onPress: onOk,
              },
              {
                text: '取消',
                onPress: onCancel,
              },
            ]
          : [
              {
                text: '我已了解，进入咨询',
                onPress: onOk,
              },
            ]
      }
    >
      <div style={{ textAlign: 'left' }}>
        {content && content.split('/n').map((item: React.ReactNode) => <div>{item}</div>)}
      </div>
    </Modal>
  );
};

export default OnlineConsultationNoticeModal;
