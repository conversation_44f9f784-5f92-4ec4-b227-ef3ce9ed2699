// @import './Common.less';

// 跳转进入在线咨询须知modal样式
:global(.onlineConsultationNotice .am-modal-footer) {
  height: 100% !important;
  padding: 0.3rem;
}

:global(.onlineConsultationNotice .am-modal-footer .am-modal-button-group-h) {
  flex-direction: column;
}
:global(.onlineConsultationNotice .am-modal-button:first-child) {
  color: #fff !important;
  background-color: @colorTheme !important;
  border-radius: 0.2rem;
}
:global(.onlineConsultationNotice .am-modal-button-group-h::before) {
  content: none !important;
}
:global(.onlineConsultationNotice .am-modal-button:last-child::before) {
  content: none !important;
}
:global(.onlineConsultationNotice .am-modal-button) {
  color: #c6c6c6 !important;
}
