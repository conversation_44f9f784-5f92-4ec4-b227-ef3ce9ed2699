import React, { FC, useEffect, useState } from 'react';
import { Toast, Modal } from 'antd-mobile';
import { connect, Dispatch, Loading, IElectronReportState, history } from 'umi';
import styles from './index.less';
import icon1 from '@/assets/ElectronReport/icon1.png';
import icon2 from '@/assets/ElectronReport/icon2.png';
import rightArrow from '@/assets/ElectronReport/rightArrow.png';
import qs from 'query-string';

interface IProps {
  electronReport: IElectronReportState;
  dispatch: Dispatch;
  loading?: boolean;
  location?: {
    query?: {
      cardId?: string;
      patientName?: string;
      gender?: string;
    };
  };
}

const ExamReprort: FC<IProps> = ({ dispatch, electronReport, location }) => {
  const { query = {} } = location || {};
  const { cardId = '', patientName = '', gender = '' } = query || {};
  const [cardInfo, setCardInfo] = useState<any>([]);
  const EXAM_TYPE = [
    {
      type: 1,
      icon: icon1,
      label: '电子报告',
      desc: '门诊、住院、检验检查报告查看',
    },
    {
      type: 2,
      icon: icon2,
      label: '体检报告',
      desc: '个人体检健康报告查看',
    },
  ];
  useEffect(() => {
    getCardDetail();
  }, []);
  /* 获取卡详情 */
  const getCardDetail = async () => {
    await dispatch({
      type: 'patientCard/cardInfo',
      payload: {
        cardId: cardId || '641727197424939008',
      },
      callback: (cardInfoRes: any) => {
        console.log('cardInfoRes', cardInfoRes);
        setCardInfo(cardInfoRes);
      },
    });
  };
  /* 前往报告 */
  /*  华医通电子报告(上级页面是健康档案，所以机构默认华西) */
  const goReport = async (type: number) => {
    switch (type) {
      case 1:
        // history.push(`/reportclip/home?cardId=${cardId}&patientName=${patientName}&gender=${gender}`);
        history.push(`/electronreport/list?data=${JSON.stringify(cardInfo)}&organCode=HID0101`);
        break;

      case 2: // 华西本院电子报告
        // history.push(`/patientcard/home?hxExam=${2}&organCode=HID0101`);
        await dispatch({
          type: 'electronReport/externalReportList',
          payload: {
            name: cardInfo?.patientName,
            mobile: cardInfo?.tel,
            idcard: cardInfo?.credNo,
            type: 1,
          },
          callback(res: any) {
            // hxurl = res;
            window.location.href = res;
          },
        });
        break;

      default:
        break;
    }
  };
  return (
    <div className={styles.container}>
      <div className={styles.examPortTxt1}>请选择服务类型</div>
      <div className={styles.examPortTxt2}>按照您自身的需要进行选择</div>
      <div className={styles.examPortBox}>
        {EXAM_TYPE.map((item) => (
          <div onClick={() => goReport(item.type)} className={styles.examPortBoxItem}>
            <div className={styles.examPortBoxItemL}>
              <img src={item.icon} className={styles.examPortBoxItemLIcon} alt="" />
              <div className={styles.examPortBoxItemLTxt}>
                <div className={styles.examPortBoxItemLTxt_1}>{item.label}</div>
                <div className={styles.examPortBoxItemLTxt_2}>{item.desc}</div>
              </div>
            </div>
            <img src={rightArrow} className={styles.examPortBoxItemR} alt="" />
          </div>
        ))}
      </div>
    </div>
  );
};

export default connect(({ electronReport, loading }: { electronReport: IElectronReportState; loading: Loading }) => ({
  electronReport,
  loading: loading.effects['address/findListAddress'],
}))(ExamReprort);
