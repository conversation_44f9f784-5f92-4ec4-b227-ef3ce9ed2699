import React, { useEffect, useState } from 'react';
import queryString from 'query-string';
import { DownLoadStatus, useDispatch, useSelector } from 'umi';
import { HxLocalStorage, HxSessionStorage } from '@/utils/storage';
import { HxParameter, getOrganCode } from '@/utils/parameter';
import { isHytDoctor } from '@/utils/platform';
import { Button, Empty, List } from 'antd-mobile-v5';
import { Modal, Toast } from 'antd-mobile';
import { HOSPITAL_CODE } from '../utils/enum';
import { getImageInfoNew } from '../service';

const { Item } = List;

export default function () {
  /**
   * showImageType- 展示方式：1. 查看影像调阅 2. 下载影像
   */
  const { showImageType = '1' } = queryString.parse(window.location.search);
  const dispatch = useDispatch();
  const electronReport = useSelector((store: any) => store.electronReport) || { radiologyDetail: {} };
  const { imageReportUrl = [], imageTotal = 0 } = electronReport.radiologyDetail || {};
  const [imageCloudInfo, setImageCloudInfo] = useState<any>({
    expireDate: null,
    hisCode: null,
    itemId: null,
    status: null,
    items: [],
  });
  /** 获取报告详情数据 */
  const fetchReportDetail = () => {
    const { shortKey, examNo: sourceExamNo } = queryString.parse(window.location.search);
    if (shortKey) {
      const shareData = HxSessionStorage.get('SHARE_DATA');
      if (shareData) {
        dispatch({
          type: 'electronReport/updateState',
          payload: {
            radiologyDetail: shareData,
          },
        });
      } else {
        // 通过二维码分享查看详情，调用重定向接口
        Toast.loading('加载中...');
        dispatch({
          type: 'electronReport/getUrl',
          payload: {
            shortKey,
          },
          callback: (res: any) => {
            HxSessionStorage.set('SHARE_DATA', res);
            Toast.hide();
          },
        });
      }
    } else {
      const { organCode } = HxParameter;
      const token = HxLocalStorage.get('token');
      const query = queryString.parse(window.location.search);
      const { middleLinkParams } = electronReport;
      const { examNo = '', servType = '', itemId = '', sharePassword, shareKey, sourceType } =
        query || middleLinkParams || {}; // 列表页进来参数在state, 分享链接进来参数在model里
      if (sourceType !== 'share') {
        let payload: any = {
          organCode: organCode === 'HYT' ? HOSPITAL_CODE.HXYY : organCode,
          channel: isHytDoctor() ? 1 : 0,
          examNo: sourceExamNo || examNo,
          token,
          itemId: encodeURIComponent(itemId),
        };
        if (isHytDoctor()) payload = { ...payload, servType };
        Toast.loading('加载中...');
        dispatch({
          type: isHytDoctor() ? 'electronReport/getCheckDetailDoctor' : 'electronReport/getCheckDetail',
          payload,
          callback() {
            Toast.hide();
          },
        });
      } else {
        Toast.loading('加载中...');
        dispatch({
          type: 'electronReport/getSharelinksCheckdetail',
          payload: {
            key: shareKey,
            password: sharePassword,
          },
          callback() {
            Toast.hide();
          },
        });
      }
    }
  };

  const getImageDownLoadInfo = async () => {
    const query = queryString.parse(window.location.search);
    const { middleLinkParams } = electronReport;
    const { itemId = '' } = query || middleLinkParams || {};
    try {
      Toast.loading('');
      const result = await getImageInfoNew({
        itemId,
        organCode: getOrganCode(),
      });
      console.log('result');
      console.log(result);
      setImageCloudInfo(
        result || {
          expireDate: null,
          hisCode: null,
          itemId: null,
          status: 'CAN_REQUEST',
          items: [],
        },
      );
    } catch (error) {
      console.log(error);
    } finally {
      Toast.hide('');
      console.log('finally');
    }
  };

  const copyToClipboard = (text) => {
    // 创建一个临时输入框元素
    const tempInput = document.createElement('input');
    // 设置元素的值为需要复制的文本
    tempInput.value = text;
    // 将元素添加到文档中
    document.body.appendChild(tempInput);
    // 选择元素中的文本内容
    tempInput.select();
    // 执行复制命令
    document.execCommand('copy');
    // 将临时元素从文档中移除
    document.body.removeChild(tempInput);
  };

  const copyContent = async (text) => {
    try {
      if (navigator.clipboard && navigator.clipboard.writeText && navigator.permissions) {
        console.log('navigator.clipboard');
        await navigator.clipboard.writeText(text);
      } else {
        console.log('copyToClipboard');
        copyToClipboard(text);
      }
      console.log('Content copied to clipboard');
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  useEffect(() => {
    if (showImageType === '1') {
      fetchReportDetail();
    } else if (showImageType === '2') {
      getImageDownLoadInfo();
    }
  }, []);

  return (
    <>
      {showImageType === '1' &&
        (imageReportUrl.length > 0 && imageReportUrl.length === imageTotal ? (
          <List header="">
            {imageReportUrl.map((item: any, index: number) => {
              return (
                <Item
                  key={item}
                  onClick={() => {
                    window.location.href = item;
                  }}
                >
                  <div>影像云{index + 1}</div>
                </Item>
              );
            })}
          </List>
        ) : (
          <Empty description="暂无影像" />
        ))}
      {showImageType === '2' &&
        (imageCloudInfo.status === DownLoadStatus.Compelete ? (
          <List header="">
            {imageCloudInfo.items.map((item: any, index: number) => {
              return (
                <Item
                  key={item.url}
                  extra={
                    <Button
                      size="small"
                      color="primary"
                      onClick={() => {
                        const { url } = item;
                        copyContent(url);
                        Modal.alert(
                          <></>,
                          <div style={{ textAlign: 'left' }}>
                            <div>下载链接已复制到粘贴板，可通过手机或电脑端打开浏览器复制链接进行下载</div>
                            <div style={{ color: 'red' }}>注：影像数据文件较大，请注意流量使用</div>
                          </div>,
                          [
                            {
                              text: '确定',
                              onPress: () => {}, // 跳转到支付页面
                            },
                          ],
                        );
                      }}
                    >
                      下载
                    </Button>
                  }
                >
                  <div>影像云 {index + 1}</div>
                </Item>
              );
            })}
          </List>
        ) : (
          <Empty description="暂无下载影像" />
        ))}
    </>
  );
}
