.container {
  position: fixed;
  top: 0;
  bottom: 0;
  width: 100%;
  color: #03081a;
  font-size: 28px;
  background: #fff;
  .tip {
    position: absolute;
    top: 0;
    display: flex;
    justify-content: center;
    box-sizing: border-box;
    width: 100%;
    padding: 0 24px;
    color: #fc4553;
    font-size: 26px;
    line-height: 62px;
    background: #ffedee;
    .close {
      font-weight: 500;
      font-size: 40px;
    }
  }
  .main {
    position: absolute;
    top: 192px;
    width: 100%;
    text-align: center;
    .icon {
      width: 88px;
      height: 88px;
      margin-bottom: 40px;
    }
    .tip1 {
      margin-bottom: 40px;
      font-size: 32px;
    }
    .tip2 {
      margin-bottom: 24px;
      color: #989eb4;
    }
  }
  .inputList {
    display: flex;
    justify-content: center;
    margin-bottom: 24px;
    .inputBox {
      width: 88px;
      height: 88px;
      font-weight: 500;
      font-size: 40px;
      line-height: 88px;
      text-align: center;
      background: #f5f6fa;
      border-radius: 8px;
      &:not(:last-child) {
        margin-right: 16px;
      }
    }
  }
  .error {
    color: #fc4553;
    -webkit-transform-origin: center center;
    -ms-transform-origin: center center;
    transform-origin: center center;
    -webkit-animation-name: shaky-slow;
    -ms-animation-name: shaky-slow;
    animation-name: shaky-slow;
    -webkit-animation-duration: 3s;
    -ms-animation-duration: 3s;
    animation-duration: 3s;
    -webkit-animation-play-state: running;
    -ms-animation-play-state: running;
    animation-play-state: running;
    // -webkit-animation-iteration-count: infinite;
    //-ms-animation-iteration-count: infinite;
    // animation-iteration-count: infinite;
    -webkit-animation-timing-function: ease-in-out;
    -ms-animation-timing-function: ease-in-out;
    animation-timing-function: ease-in-out;
    -webkit-animation-delay: 0s;
    -ms-animation-delay: 0s;
    animation-delay: 0s;
  }
  .keybordBox {
    position: absolute;
    bottom: 0;
    width: 100%;
    background: #d3d6db;
    .clear {
      position: absolute;
      right: 121px;
      bottom: 108px;
      width: 46px;
      height: 46px;
      background: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/Delete.png')
        no-repeat center center;
      background-size: 100%;
    }
  }
  .keybord {
    position: relative;
    display: flex;
    flex-flow: wrap;
    justify-content: center;
    padding: 16px;
    padding-bottom: 80px;
    .key {
      width: 230px;
      margin-bottom: 10px;
      font-weight: 500;
      font-size: 56px;
      line-height: 96px;
      text-align: center;
      background: #fff;
      border-radius: 12px;

      &:not(:nth-child(3n)) {
        margin-right: 10px;
      }
    }
  }
}
.showLoading {
  position: absolute;
  top: 50%;
  left: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  transform: translate(-50%, -50%);
}
@-webkit-keyframes shaky-slow {
  0% {
    -webkit-transform: translate(0, 0) rotate(0deg);
  }
  2% {
    -webkit-transform: translate(-1px, 1.5px) rotate(1.5deg);
  }
  4% {
    -webkit-transform: translate(1.3px, 0) rotate(-0.5deg);
  }
  6% {
    -webkit-transform: translate(1.4px, 1.4px) rotate(-2deg);
  }
  8% {
    -webkit-transform: translate(-1.3px, -1px) rotate(-1.5deg);
  }
  10% {
    -webkit-transform: translate(1.4px, 0) rotate(-2deg);
  }
  12% {
    -webkit-transform: translate(-1.3px, -1px) rotate(-2deg);
  }
  14% {
    -webkit-transform: translate(1.5px, 1.3px) rotate(1.5deg);
  }
  16% {
    -webkit-transform: translate(1.5px, -1.5px) rotate(-1.5deg);
  }
  18% {
    -webkit-transform: translate(1.3px, -1.3px) rotate(-2deg);
  }
  20% {
    -webkit-transform: translate(1px, 1px) rotate(-0.5deg);
  }
  22% {
    -webkit-transform: translate(1.3px, 1.5px) rotate(-2deg);
  }
  24% {
    -webkit-transform: translate(-1.4px, -1px) rotate(2deg);
  }
  26% {
    -webkit-transform: translate(1.3px, -1.3px) rotate(0.5deg);
  }
  28% {
    -webkit-transform: translate(1.6px, -1.6px) rotate(-2deg);
  }
  30% {
    -webkit-transform: translate(-1.3px, -1.3px) rotate(-1.5deg);
  }
  32% {
    -webkit-transform: translate(-1px, 0) rotate(2deg);
  }
  34% {
    -webkit-transform: translate(1.3px, 1.3px) rotate(-0.5deg);
  }
  36% {
    -webkit-transform: translate(1.3px, 1.6px) rotate(1.5deg);
  }
  38% {
    -webkit-transform: translate(1.3px, -1.6px) rotate(1.5deg);
  }
  40% {
    -webkit-transform: translate(-1.4px, -1px) rotate(-0.5deg);
  }
  42% {
    -webkit-transform: translate(-1.4px, 1.3px) rotate(-0.5deg);
  }
  44% {
    -webkit-transform: translate(-1.6px, 1.4px) rotate(0.5deg);
  }
  46% {
    -webkit-transform: translate(-2.1px, -1.3px) rotate(-0.5deg);
  }
  48% {
    -webkit-transform: translate(1px, 1.6px) rotate(1.5deg);
  }
  50% {
    -webkit-transform: translate(1.6px, 1.6px) rotate(1.5deg);
  }
  52% {
    -webkit-transform: translate(-1.4px, 1.6px) rotate(0.5deg);
  }
  54% {
    -webkit-transform: translate(1.6px, -1px) rotate(-2deg);
  }
  56% {
    -webkit-transform: translate(1.3px, -1.6px) rotate(-2deg);
  }
  58% {
    -webkit-transform: translate(-1.3px, -1.6px) rotate(0.5deg);
  }
  60% {
    -webkit-transform: translate(1.3px, 1.6px) rotate(-0.5deg);
  }
  62% {
    -webkit-transform: translate(0, 0) rotate(-1.5deg);
  }
  64% {
    -webkit-transform: translate(-1.6px, -1.6px) rotate(-2deg);
  }
  66% {
    -webkit-transform: translate(1.6px, -1.6px) rotate(0.5deg);
  }
  68% {
    -webkit-transform: translate(0, -1.6px) rotate(-2deg);
  }
  70% {
    -webkit-transform: translate(-1.6px, 1px) rotate(1.5deg);
  }
  72% {
    -webkit-transform: translate(-1.6px, 1.6px) rotate(2deg);
  }
  74% {
    -webkit-transform: translate(1.3px, -1.6px) rotate(-0.5deg);
  }
  76% {
    -webkit-transform: translate(1.4px, 1px) rotate(-0.5deg);
  }
  78% {
    -webkit-transform: translate(-1px, 1.4px) rotate(2deg);
  }
  80% {
    -webkit-transform: translate(1.4px, 1.6px) rotate(2deg);
  }
  82% {
    -webkit-transform: translate(-1.6px, -1.6px) rotate(-0.5deg);
  }
  84% {
    -webkit-transform: translate(-1.4px, 1.4px) rotate(-2deg);
  }
  86% {
    -webkit-transform: translate(1px, 1.4px) rotate(-2deg);
  }
  88% {
    -webkit-transform: translate(-1.4px, 1.4px) rotate(-1.5deg);
  }
  90% {
    -webkit-transform: translate(-1.6px, -1.6px) rotate(-2deg);
  }
  92% {
    -webkit-transform: translate(-1.6px, 1.6px) rotate(2deg);
  }
  94% {
    -webkit-transform: translate(-1.6px, -1.6px) rotate(-2deg);
  }
  96% {
    -webkit-transform: translate(-1.4px, 1.3px) rotate(-2deg);
  }
  98% {
    -webkit-transform: translate(1.3px, 1px) rotate(-0.5deg);
  }
}
@keyframes shaky-slow {
  0% {
    transform: translate(0, 0) rotate(0deg);
  }
  2% {
    transform: translate(-1px, 1.5px) rotate(1.5deg);
  }
  4% {
    transform: translate(1.3px, 0) rotate(-0.5deg);
  }
  6% {
    transform: translate(1.4px, 1.4px) rotate(-2deg);
  }
  8% {
    transform: translate(-1.3px, -1px) rotate(-1.5deg);
  }
  10% {
    transform: translate(1.4px, 0) rotate(-2deg);
  }
  12% {
    transform: translate(-1.3px, -1px) rotate(-2deg);
  }
  14% {
    transform: translate(1.5px, 1.3px) rotate(1.5deg);
  }
  16% {
    transform: translate(1.5px, -1.5px) rotate(-1.5deg);
  }
  18% {
    transform: translate(1.3px, -1.3px) rotate(-2deg);
  }
  20% {
    transform: translate(1px, 1px) rotate(-0.5deg);
  }
  22% {
    transform: translate(1.3px, 1.5px) rotate(-2deg);
  }
  24% {
    transform: translate(-1.4px, -1px) rotate(2deg);
  }
  26% {
    transform: translate(1.3px, -1.3px) rotate(0.5deg);
  }
  28% {
    transform: translate(1.6px, -1.6px) rotate(-1.5deg);
  }
  30% {
    transform: translate(-1.3px, -1.3px) rotate(-1.5deg);
  }
  32% {
    transform: translate(-1px, 0) rotate(2deg);
  }
  34% {
    transform: translate(1.3px, 1.3px) rotate(-0.5deg);
  }
  36% {
    transform: translate(1.3px, 1.6px) rotate(1.5deg);
  }
  38% {
    transform: translate(1.3px, -1.6px) rotate(1.5deg);
  }
  40% {
    transform: translate(-1.4px, -1px) rotate(-0.5deg);
  }
  42% {
    transform: translate(-1.4px, 1.3px) rotate(-0.5deg);
  }
  44% {
    transform: translate(-1.6px, 1.4px) rotate(0.5deg);
  }
  46% {
    transform: translate(-2.1px, -1.3px) rotate(-0.5deg);
  }
  48% {
    transform: translate(1px, 1.6px) rotate(1.5deg);
  }
  50% {
    transform: translate(1.6px, 1.6px) rotate(1.5deg);
  }
  52% {
    transform: translate(-1.4px, 1.6px) rotate(0.5deg);
  }
  54% {
    transform: translate(1.6px, -1px) rotate(-2deg);
  }
  56% {
    transform: translate(1.3px, -1.6px) rotate(-2deg);
  }
  58% {
    transform: translate(-1.3px, -1.6px) rotate(0.5deg);
  }
  60% {
    transform: translate(1.3px, 1.6px) rotate(-0.5deg);
  }
  62% {
    transform: translate(0, 0) rotate(-1.5deg);
  }
  64% {
    transform: translate(-1.6px, -1.6px) rotate(-2deg);
  }
  66% {
    transform: translate(1.6px, -1.6px) rotate(0.5deg);
  }
  68% {
    transform: translate(0, -1.6px) rotate(-2deg);
  }
  70% {
    transform: translate(-1.6px, 1px) rotate(1.5deg);
  }
  72% {
    transform: translate(-1.6px, 1.6px) rotate(2deg);
  }
  74% {
    transform: translate(1.3px, -1.6px) rotate(-0.5deg);
  }
  76% {
    transform: translate(1.4px, 1px) rotate(-0.5deg);
  }
  78% {
    transform: translate(-1px, 1.4px) rotate(2deg);
  }
  80% {
    transform: translate(1.4px, 1.6px) rotate(2deg);
  }
  82% {
    transform: translate(-1.6px, -1.6px) rotate(-0.5deg);
  }
  84% {
    transform: translate(-1.4px, 1.4px) rotate(-2deg);
  }
  86% {
    transform: translate(1px, 1.4px) rotate(-2deg);
  }
  88% {
    transform: translate(-1.4px, 1.4px) rotate(-1.5deg);
  }
  90% {
    transform: translate(-1.6px, -1.6px) rotate(-2deg);
  }
  92% {
    transform: translate(-1.4px, 1.6px) rotate(2deg);
  }
  94% {
    transform: translate(-1.6px, -1.6px) rotate(-2deg);
  }
  96% {
    transform: translate(-1.4px, 1.3px) rotate(-2deg);
  }
  98% {
    transform: translate(1.3px, 1px) rotate(-0.5deg);
  }
}
