import React, { useEffect, useState, useRef } from 'react';
import { Dispatch, AnyAction } from 'redux';
import { connect } from 'dva';
import { HxIndicator, HxIcon } from '@/components';
import { ModuleEnum } from '@/utils/enum';
import { Route, Redirect } from 'react-router';
import { getCurEvnHref } from '@/utils/tool';
import queryString from 'query-string';
import { IElectronReportState, Loading, history } from 'umi';
import { Toast } from 'antd-mobile';
import styles from './index.less';

interface IProps {
  loading?: boolean;
  dispatch: Dispatch<AnyAction>;
  electronReport: any;
}

let str = '';
let arr = [];
const hiscode = '';
/** 输入密码 */
const MiddleLink = (props: IProps) => {
  console.log('props==================', props);
  const { dispatch, loading, electronReport } = props;
  const {
    middleLinkParams: { endTime },
    shareData,
  } = electronReport;
  const {
    middleLinkParams: { id, examNo, itemId, organCode },
  } = electronReport;
  // const { sharelinks } = electronReport;
  const [validflag, setValidflag] = useState('1');
  const [shareSeq, setShareSeq] = useState('');
  const [showtip] = useState(true);
  const numbers = [1, 2, 3, 4, 5, 6, 7, 8, 9, 0];
  const [password, setPassword] = useState('');
  const [showError, setShowError] = useState(false);
  const [leftTimeStr, setLeftTimeStr] = useState('');
  const [leftTime, setLeftTime] = useState(0);
  const [numberBox, setNumberBox] = useState([
    {
      value: '',
      index: 0,
    },
    {
      value: '',
      index: 1,
    },
    {
      value: '',
      index: 2,
    },
    {
      value: '',
      index: 3,
    },
    {
      value: '',
      index: 4,
    },
    {
      value: '',
      index: 5,
    },
  ]);
  const s = getCurEvnHref();
  // const s = '{host}/electronreport/MiddleLink?key=kHCSFZtAd0zE6iwjH3IzDiZSDb8pb/z1kCK83MzCN4eI4kCYxu2x/k8puE6KFFNNRxPKHDsb2JtdxxtkUztGXTdM/JrIZHNr6uBtQDVMFD46NHXFqu6uhpAcBsdcePh7LVD7PyygiAmpxOVFs4NSSGe7nkVNk0HWxEWoASEk0FN50sB4s8ejkGa5KC9keoCXfYNLwsxz0n8maaf3kfCeQg==';
  const { key = '' } = queryString.parse(s.split('?')[1]);
  // console.log('sharelinks',sharelinks)
  // console.log('....', queryString.parse(s.split('?')[1]));
  useEffect(() => {
    dispatch({
      type: 'electronReport/doDecryptLink',
      payload: {
        key,
      },
      callback: (res: any) => {
        console.log('res', res);
        const { encryptionflag, shareLinksSeq } = res;
        setValidflag(encryptionflag);
        setShareSeq(shareLinksSeq);
      },
    });
  }, []);

  const setArrVal = (type) => {
    arr = JSON.parse(JSON.stringify(numberBox));
    if (type === 'add') {
      for (let i = 0; i < str.length; i += 1) {
        arr[i].value = str.charAt(i);
      }
    } else {
      for (let i = 0; i < arr.length; i += 1) {
        if (i < str.length) {
          arr[i].value = str.charAt(i);
        } else {
          arr[i].value = '';
        }
      }
    }
    setNumberBox(arr);
  };
  const inputPassword = (item) => {
    if (str.length === 6) return;
    str += item;
    setPassword(str);
    setArrVal('add');
  };

  const clearPassword = () => {
    if (!str || !str.length) return;
    if (str && str.length > 0) {
      str = str.slice(0, -1);
      setPassword(str);
      setArrVal('minus');
    }
  };
  useEffect(() => {
    if (password.length === 6) {
      dispatch({
        type: 'electronReport/safetycheck',
        payload: {
          shareLinksSeq: shareSeq,
          password,
        },
        callback: (res: any) => {
          setShowError(false);
          console.log('res', res);
          const { code, msg } = res;
          if (code === '1') {
            history.replace({
              pathname: `/${ModuleEnum.MODULE_ELECTRON_REPORT}/radiologydetail`,
              search: `sourceType=share&id=${id}&channel=0&examNo=${examNo}&itemId=${itemId}&hisCode=${organCode}&sharePassword=${password}&shareKey=${key}`,
            });
          } else if (code === '0') {
            Toast.info(msg);
            if (msg === '密码错误') {
              setShowError(true);
              setPassword(''); // 清空密码
              setNumberBox([
                {
                  value: '',
                  index: 0,
                },
                {
                  value: '',
                  index: 1,
                },
                {
                  value: '',
                  index: 2,
                },
                {
                  value: '',
                  index: 3,
                },
                {
                  value: '',
                  index: 4,
                },
                {
                  value: '',
                  index: 5,
                },
              ]);
              str = '';
            }
          }
        },
      });
    }
  }, [password]);

  // const closeTip = () => {
  //   setShowtip(false);
  // };
  const timerID = useRef();
  useEffect(() => {
    if (endTime) {
      setLeftTime(new Date(endTime.replace(/-/g, '/')).valueOf() - new Date().valueOf());
      console.log(endTime, new Date(endTime.replace(/-/g, '/')).valueOf(), new Date().valueOf(), leftTime);
      timerID.current = setInterval(() => {
        // setLeftTime(new Date(endTime).getTime() - new Date().getTime());
        setLeftTime(new Date(endTime.replace(/-/g, '/')).valueOf() - new Date().valueOf());
      }, 1000);
    }
  }, [endTime, loading]);
  useEffect(() => {
    return () => {
      // 清除定时器
      clearInterval(timerID.current);
    };
  }, []);
  useEffect(() => {
    if (leftTime > 0) {
      let d = 0;
      let h = 0;
      let m = 0;
      let s = 0;
      d = Math.floor(leftTime / 1000 / 60 / 60 / 24);
      h = Math.floor((leftTime / 1000 / 60 / 60) % 24);
      m = Math.floor((leftTime / 1000 / 60) % 60);
      s = Math.floor((leftTime / 1000) % 60);
      let dstr = '';
      let hstr = '';
      let mstr = '';
      let sstr = '';
      dstr = d > 0 ? `${d}天` : '';
      hstr = h > 0 ? `${h}小时` : '';
      mstr = m > 0 ? `${m}分钟` : '';
      sstr = s > 0 ? `${s}秒` : '';
      setLeftTimeStr(dstr + hstr + mstr + sstr);
    } else {
      clearInterval(timerID.current);
    }
  }, [leftTime]);

  const isLocationRef = useRef(false);

  return (
    <>
      {/** 分享加密，需要输入分享密码, 分享未加密,直接跳转详情,详情页接受一个sourceType 参数，用于逻辑判断 */}
      {loading ? (
        <div className={styles.showLoading}>
          <HxIndicator />
        </div>
      ) : validflag === '1' && leftTime > 0 ? (
        <div className={styles.container}>
          {showtip && (
            <p className={styles.tip}>
              当前检查可读有效期剩余：{leftTimeStr}
            </p> /** <span className={styles.close} onClick={() => closeTip()}>&times;</span> */
          )}
          <div className={styles.main}>
            <HxIcon iconName="electronic-safe" className={styles.icon} />
            <p className={styles.tip1}>此页面已被锁定，需要输入解锁密码</p>
            <p className={styles.tip2}>请输入6位数字密码</p>
            <div className={styles.inputList}>
              {numberBox.map((item) => (
                <span className={styles.inputBox} key={item.index}>
                  {item.value}
                </span>
              ))}
            </div>
            {showError && <p className={styles.error}>密码输入错误，请重新输入</p>}
          </div>
          {/** 键盘 */}
          <div className={styles.keybordBox}>
            <div className={styles.keybord}>
              {numbers.map((item) => (
                <span className={styles.key} onClick={() => inputPassword(item)} key={item}>
                  {item}
                </span>
              ))}
            </div>
            <span className={styles.clear} onClick={() => clearPassword()} />
          </div>
        </div>
      ) : leftTime > 0 ? (
        <Route
          render={() => {
            if (isLocationRef.current) {
              return;
            }
            isLocationRef.current = true;
            return (
              <Redirect
                to={{
                  pathname: `/${ModuleEnum.MODULE_ELECTRON_REPORT}/radiologydetail`,
                  search: `sourceType=share&id=${id}&channel=0&examNo=${examNo}&itemId=${itemId}&hisCode=${organCode}&shareKey=${key}`,
                }}
              />
            );
          }}
        />
      ) : (
        <div style={{ textAlign: 'center', paddingTop: '50%' }}>链接已失效</div>
      )}
    </>
  );
};

export default connect(({ loading, electronReport }: { electronReport: IElectronReportState; loading: Loading }) => ({
  loading: loading.effects['electronReport/doDecryptLink'],
  electronReport,
}))(MiddleLink);
