.container {
  height: 100vh;
  padding: 100px 80px;
  font-size: 24px;
  background-color: #32b9aa;

  .main {
    background-color: #fff;
    border-radius: 20px;
    .title {
      position: relative;
      height: 180px;
      color: #333;
      font-size: 32px;
      line-height: 180px;
      text-align: center;
      border-bottom: 1px dashed #707070;
      .circle {
        position: absolute;
        bottom: -20px;
        width: 40px;
        height: 40px;
        background-color: #32b9aa;
        border-radius: 20px;
      }
    }
    .content {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 60px;
      .qrBox {
        position: relative;
        display: flex;
        align-items: center;
        justify-items: center;
        margin: 0 auto;

        .qrFailBox {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          display: flex;
          align-items: center;
          justify-items: center;
          margin: 0 auto;
          background-color: rgba(255, 255, 255, 0.933);
          .wrapBox {
            display: flex;
            align-items: center;
            justify-items: center;
            width: 300px;
            height: 300px;
            margin: auto auto;
            border: 20px dashed #fff;
            img {
              width: 100px;
              height: 85px;
              margin: auto auto;
            }
          }
        }
      }
      .notice {
        padding: 60px 0;
        color: #32b9aa;
      }
      .times {
        margin-top: 10px;
        color: #333;
        font-weight: 500;
        font-size: 32px;
        span {
          color: #32b9aa;
        }
      }
    }
  }
}
