import React, { PureComponent } from 'react';
import { history, connect } from 'umi';
import { ModuleEnum } from '@/utils/enum';
import queryString from 'query-string';
import { Dispatch, AnyAction } from 'redux';
import { Checkbox } from 'antd';
import { HxLocalStorage } from '@/utils/storage';
import { isHytDoctor } from '@/utils/platform';
import { Button, Toast } from 'antd-mobile';

import styles from './index.less';

interface IProps {
  dispatch: Dispatch<AnyAction>;
  location: any;
  electronReport: any;
}

interface IState {
  configData: Array<any>;
  readed: boolean;
}

class ReportShareNotice extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      configData: [], // 后端配置数据
      readed: false, // 是否已读
    };
  }

  componentDidMount() {
    document.title = '报告分享须知';
    this.fetchConfigData();
  }

  /** 获取后端配置数据 */
  fetchConfigData = () => {
    const token = HxLocalStorage.get('token');
    this.props.dispatch({
      type: 'electronReport/configPictureOrTextDisplay',
      payload: {
        organCode: 'HID0101',
        position: 'DZBGFXWXTX',
        serverCode: '',
        token,
      },
      callback: (res: any) => {
        res &&
          res.length > 0 &&
          this.setState({
            configData: res,
          });
      },
    });
  };

  /** 渲染温馨提示内容 */
  renderNoticeContent = () => {
    const { configData = [] } = this.state;
    let showNotice;
    if (isHytDoctor()) {
      showNotice = configData.length > 0 && configData[1].display && configData[1].content;
    } else {
      showNotice = configData.length > 0 && configData[0].display && configData[0].content;
    }
    if (showNotice) {
      let notices: any[];
      if (isHytDoctor()) {
        notices = configData[1].content.split('/n');
      } else {
        notices = configData[0].content.split('/n');
      }
      return (
        <div className={styles.content}>
          {notices.map((item: any) => (
            <div key={item}>{item}</div>
          ))}
        </div>
      );
    }
    return null;
  };

  /** 点击checkbox的回调函数 */
  onSelectReaded = (e: { target: { checked: any } }) => {
    this.setState({
      readed: e.target.checked,
    });
  };

  submit = () => {
    const { id, channel, examNo, organId, token, hisCode, itemId } = queryString.parse(this.props.location.search);
    const { readed } = this.state;
    if (readed) {
      history.push(
        `/${ModuleEnum.MODULE_ELECTRON_REPORT}/reportQRCode?hisCode=${hisCode}&itemId=${itemId}&id=${id}&channel=${channel}&examNo=${examNo}&token=${token}&organId=${organId}`,
      );
    } else {
      Toast.info('请同意并勾选阅读须知', 1);
    }
  };

  render() {
    return (
      <div className={styles.container}>
        <div className={styles.title}>电子报告分享须知</div>
        {this.renderNoticeContent()}
        <div className={styles.buttons}>
          <Checkbox onChange={this.onSelectReaded}>已阅读并同意《报告分享须知》</Checkbox>
          <Button className={styles.confirmButton} onClick={this.submit}>
            确定
          </Button>
        </div>
      </div>
    );
  }
}

export default connect()(ReportShareNotice);
