export interface IUserInfo {}

export interface IPricesType {
  /** 资费描述 */
  description?: string;
  /** 包id */
  packageId?: string;
  /** 存储价格 */
  price?: number;
  /** 存储时间 */
  time?: number;
  /** 存储时间单位 */
  timeUnitDesc?: number;
}

export interface toPayParamsType {
  bizSysSeq?: string;
  dealSeq?: string;
  exist?: boolean;
  merchantSeq?: string;
  openid?: string;
}

export interface ITextContentType {
  /** 文案编码 */
  textCode?: string;
  /** 文案内容 */
  textContent?: string;
  /** 文案id */
  textId?: string;
  /** 文案类型 */
  textType?: string;
  /** 数据版本编码 */
  versionCode?: string;
  /** 数据版本序列 */
  versionSort?: number;
}
export interface HospitalConfig {
  /** 医院编码 */
  hiscode?: string;
  /** 医院名称 */
  hisname?: string;
  /** 影像存储免费天数 */
  freedays?: string;
  /** 免费时间设置列表 */
  sharetime?: string;
  /** 二维码有效时间（单位：秒） */
  qrcodetime?: number;
}
