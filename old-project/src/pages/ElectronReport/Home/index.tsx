import React, { PureComponent } from 'react';
import { Toast } from 'antd-mobile';
// import qs from 'query-string';
import { history } from 'umi';
import { HxIcon } from '@/components';
import styles from './index.less';

const mockData = [
  {
    menuName: '扫描医嘱条形码',
    menuIcon: 'electronreport-scan',
    menuDesc: '可根据提供的条形码，扫描后直接进入查看报告详情',
  },
  {
    menuName: '选择就诊卡',
    menuIcon: 'electronreport-card',
    menuDesc: '可根据选择就诊卡后，查看该卡相关的所有检查、检验报告',
  },
];

interface IMenuItem {
  menuName: string;
  menuIcon: string;
  menuDesc: string;
}

interface IProps {}

interface IState {}

class ElectronicReport extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);

    this.state = {};
  }

  componentDidMount() {}

  clickBusinessMenu = (item: IMenuItem) => {
    const { menuName } = item;
    Toast.info(menuName, 1);
    if (item.menuIcon === 'electronreport-card') {
      const redirect = '/electronreport/list';
      history.push(`/patientcard/home?redirect=${redirect}`);
    }
  };

  render() {
    return (
      <div className={styles.container}>
        {mockData.length > 0 &&
          mockData.map((item: IMenuItem) => (
            <div onClick={() => this.clickBusinessMenu(item)} className={styles.item} key={item.menuName}>
              <HxIcon className={styles.menuIcon} iconName={item.menuIcon} />
              <div className={styles.itemMin}>
                <p>{item.menuName}</p>
                <p>{item.menuDesc}</p>
              </div>
              <HxIcon className={styles.arrow} iconName="arrow-right" />
            </div>
          ))}
      </div>
    );
  }
}

export default ElectronicReport;
