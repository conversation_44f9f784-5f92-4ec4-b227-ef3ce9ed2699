import React from 'react';
import qs from 'query-string';
import { HxRedirect, getModuleHomeUrl } from '@/utils/interceptor';
import { ModuleEnum } from '@/utils/enum';
import { isHytDoctor } from '@/utils/platform';

// 是否需要选卡
const needChooseCard = true;

// 是否需要访问口令
const needAccessToken = false;

// 组装参数
const query: any = qs.parse(window.location.href.split('?')[1]) || {};
query.needAccessToken = needAccessToken;
const { moduletype, organCode } = query;
const getString = (value: any) => {
  return Array.isArray(value) ? value[0] : value;
};
// 重定向地址
// const redirectUrl = getModuleHomeUrl(ModuleEnum.MODULE_ELECTRON_REPORT);
const redirectUrl = `/${ModuleEnum.MODULE_ELECTRON_REPORT}/digitalHome`.toLowerCase();
const redirectUrl_list = `/${ModuleEnum.MODULE_ELECTRON_REPORT}/list`.toLowerCase();

const defaultParams = {
  // 华医通大众端，华西医院公众号, 双流医院‘数字影像’进入数字影象新入口，医生端逻辑不变
  pathname:
    (needChooseCard && isHytDoctor()) ||
    (needChooseCard && !isHytDoctor() && moduletype !== 'YXY' && getString(organCode) !== 'SZYX')
      ? redirectUrl_list
      : redirectUrl,
  search:
    (needChooseCard && isHytDoctor()) ||
    (needChooseCard && !isHytDoctor() && moduletype !== 'YXY' && getString(organCode) !== 'SZYX')
      ? `redirect=${redirectUrl_list}&${qs.stringify(query)}`
      : `${qs.stringify(query)}`,
};
export default () => {
  return <HxRedirect params={defaultParams} />;
};
