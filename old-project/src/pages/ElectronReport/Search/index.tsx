import { checkIdNum } from '@/utils/common';
import { Toast } from 'antd-mobile';
import React, { useEffect, useState } from 'react';
import { getOpenId } from '@/utils/parameter';
import { isWechat } from '@/utils/platform';
import { useSelector } from 'umi';
import { geYXYListLink } from '../service';

import styles from './index.less';

export default function () {
  const global = useSelector((store: any) => store.global) || { WxAppIdLoad: false };
  const [idCard, setIdCard] = useState('');
  const [patientName, setPatientName] = useState('');
  useEffect(() => {
    if (isWechat() && global.WxAppIdLoad) {
      (async () => {
        await geYXYListLink({
          clientId: 'HYTYXY',
          skipError: true,
        });
      })();
    }
  }, [global.WxAppIdLoad]);

  const checkIdCard = (value: any) => {
    if (!value) {
      return Promise.reject(new Error('身份证号码不能为空'));
    }
    if (checkIdNum(value)) {
      return Promise.resolve();
    }
    return Promise.reject(new Error('请输入正确的身份证号码'));
  };

  const checkPatientName = (value: any) => {
    if (!value) {
      return Promise.reject(new Error('姓名不能为空'));
    }
  };

  const onFinish = async () => {
    try {
      await checkIdCard(idCard);
      await checkPatientName(patientName);
      const body = {
        idCard,
        patientName,
      };
      Toast.loading('加载中', 0);
      const res = await geYXYListLink({
        clientId: 'HYTYXY',
        ...body,
      });
      if (res) {
        window.location.href = `${res}&openId=${getOpenId()}`;
      }
      Toast.hide();
    } catch (error) {
      if (error && error.message) {
        Toast.info(error?.message);
      }
    }
  };

  return (
    <div className={styles.conainer}>
      <div className={styles.formContainer}>
        <div className={styles.formContent}>
          <div className={styles.formHeader}>请输入您的信息查阅数字影像</div>
          <div className={styles.formItem}>
            <span>
              <span>身</span>
              <span>份</span>
              <span>证</span>
              <span>号</span>
              <span>码</span>
            </span>
            <input
              type="text"
              placeholder="请输入您的身份证号码"
              maxLength={18}
              value={idCard}
              onChange={(e) => {
                setIdCard(e.target.value.trim() || '');
              }}
            />
          </div>
          <div className={styles.formItem}>
            <span>
              <span>姓</span>
              <span>名</span>
            </span>
            <input
              type="text"
              value={patientName}
              placeholder="请输入您的姓名"
              onChange={(e) => {
                setPatientName(e.target.value.trim() || '');
              }}
            />
          </div>
          <div className={styles.search} onClick={onFinish}>
            查询
          </div>
          {/* <Form form={form}>
            <Form.Item label="身份证号码" name="idCard">
              <Input placeholder="请输入身份证号码" clearable />
            </Form.Item>
            <Form.Item label="姓名" name="patientName">
              <Input placeholder="请输入姓名" clearable />
            </Form.Item>
          </Form> */}
          {/* <Button
            color="primary"
            block
            style={{
              marginTop: 40,
            }}
            size="large"
            onClick={onFinish}
          >
            查询
          </Button> */}
        </div>
      </div>
    </div>
  );
}
