/* eslint-disable no-case-declarations */
import React, { PureComponent } from 'react';
import { connect, history, IElectronReportState, Loading } from 'umi';
import queryString from 'query-string';
import { HxEmpty, HxIcon, HxIndicator } from '@/components';
import { HxLocalStorage, HxSessionStorage } from '@/utils/storage';
import { AnyAction, Dispatch } from 'redux';
import classnames from 'classnames';
import { ModuleEnum, PlatformEnum } from '@/utils/enum';
import { getChannelCode, getOrganCode, getToken, HxParameter } from '@/utils/parameter';
import { getPlatform, isAndroid, isHytDoctor, isHytPerson, isWechat } from '@/utils/platform';
import { Modal, Toast } from 'antd-mobile';
// import { itemsPrepay } from '@/pages/ConvenienceClinic/service';
import Share from '../components/Share';
import styles from './index.less';
import { sensorsRequest } from '@/utils/sensors';
import { filmStorageText, openImageService, queryImge } from '@/pages/ElectronReport/service';
import { query } from '@/pages/PatientPerson/service';
import { HOSPITAL_CODE } from '../utils/enum';
import { PayConfirm, RequestToDownload, FilmApplyEnter } from '../components';
import { PROVIDER } from '../enum';
import wechatShare from '../../../assets/wechatShare.png';
// import { FSJC_TYPES } from '../utils/dataDictionary';
import filmPrintIcon from '@/assets/ElectronReport/filmPrint.png';

const platform = getPlatform();

interface IProps {
  loading?: boolean;
  dispatch: Dispatch<AnyAction>;
  location: {
    state: {
      id: string;
      channel: string;
      examNo: string;
      examName: string;
      hisCode: string;
      itemId: string;
    };
    search: any;
  };
  electronReport: any;
}

interface IState {
  configData: Array<any>;
  showImgFindingMore: boolean;
  showImgDiagnosisMore: boolean;
  visible: boolean;
  modalType: string;
  showShare: boolean;
  // chanelList:Array<any>;
  // chanelId:string;
  shareParams: {
    id: any;
    channel: any;
    examNo: string;
    hisCode: string;
    itemId: string;
  };
  showPompt: boolean;
  leftTime: number;
  leftTimeStr: string;
  newNotice: any;
}

let timer: any = null;

class RadiologyDetail extends PureComponent<IProps, IState> {
  constructor(props: IProps) {
    super(props);
    this.state = {
      newNotice: '',
      configData: [], // 后端配置数据
      showImgFindingMore: true, // 是否显示展开影像学表现
      showImgDiagnosisMore: true, // 是否显示展开影像学诊断
      // eslint-disable-next-line react/no-unused-state
      visible: false, // 控制过期弹框提示显隐
      modalType: '', // 弹窗类型
      showShare: false, // 展示分享窗
      shareParams: {
        // 分享组件所需参数
        id: '',
        channel: 0,
        examNo: '',
        hisCode: '',
        itemId: '',
      },
      showPompt: true,
      leftTime: 100, // 分享链接剩余时间
      leftTimeStr: '', // 分享链接剩余时间倒计时
    };
  }

  async componentDidMount() {
    const {
      location: { state, search = '' },
    } = this.props;
    const { examName = '' } = state || {};
    const { sourceType, sharePassword, shareKey } = queryString.parse(this.props.location.search) || state || {};
    const searchParams = queryString.parse(search);
    const { hisCode = HOSPITAL_CODE.HXYY } = searchParams;
    const res: any = await filmStorageText({
      hisCode,
      organCode: hisCode,
      textCode: `report_notice_${hisCode}`,
    });
    // const res: any = await filmStorageText({
    //   hisCode: 'MS0101',
    //   organCode: 'MS0101',
    //   textCode: `report_notice_MS0101`,
    // });
    const { textContent = '' } = res;
    this.setState({ newNotice: textContent });
    setTimeout(() => {
      document.title = examName === '' ? '放射检查详情' : examName;
    }, 0);
    this.props.dispatch({
      type: 'electronReport/clearSave',
      payload: '',
      callback: () => {
        sourceType !== 'share' && this.fetchConfigData();
        this.fetchReportDetail();
      },
    });
    this.fetchHospitalConfig();
    sensorsRequest('VIDEO_PAGE_DETAIl', { ...state, organCode: getOrganCode() || HOSPITAL_CODE.HXYY });
    let {
      middleLinkParams: { endTime },
    } = this.props.electronReport || {};
    endTime && HxLocalStorage.set('endTime', endTime);
    if (!endTime) {
      endTime = HxLocalStorage.get('endTime');
    }
    if (endTime && sourceType === 'share') {
      timer = setInterval(this.timeCheck, 1000);
    }
  }

  componentWillUnmount() {
    clearInterval(timer);
  }

  closePompt = () => {
    this.setState({ showPompt: false });
  };

  timeCheck = () => {
    // let {
    //   middleLinkParams: { endTime },
    // } = this.props.electronReport;
    // endTime && HxLocalStorage.set('endTime', endTime);
    // if (!endTime) {
    //   endTime = HxLocalStorage.get('endTime');
    // }
    const endTime = HxLocalStorage.get('endTime');
    this.setState(
      {
        leftTime: new Date(endTime.replace(/-/g, '/')).valueOf() - new Date().valueOf(),
      },
      () => {
        if (this.state.leftTime > 0) {
          let d = 0;
          let h = 0;
          let m = 0;
          let s = 0;
          d = Math.floor(this.state.leftTime / 1000 / 60 / 60 / 24);
          h = Math.floor((this.state.leftTime / 1000 / 60 / 60) % 24);
          m = Math.floor((this.state.leftTime / 1000 / 60) % 60);
          s = Math.floor((this.state.leftTime / 1000) % 60);
          let dstr = '';
          let hstr = '';
          let mstr = '';
          let sstr = '';
          dstr = d > 0 ? `${d}天` : '';
          hstr = h > 0 ? `${h}小时` : '';
          mstr = m > 0 ? `${m}分钟` : '';
          sstr = s > 0 ? `${s}秒` : '';
          this.setState({ leftTimeStr: dstr + hstr + mstr + sstr });
        } else {
          clearInterval(timer);
        }
      },
    );
  };

  /** 获取医院配置信息 */
  fetchHospitalConfig = () => {
    const { hisCode = '' } = this.props?.location?.query || {};
    if (hisCode) {
      this.props.dispatch({
        type: 'electronReport/getHospitalConfig',
        payload: {
          hisCode,
        },
        callback: (data) => {},
      });
    }
  };

  /** 获取配置数据 */
  fetchConfigData = () => {
    const { shortKey } = queryString.parse(this.props.location.search);
    if (!shortKey) {
      const token = HxLocalStorage.get('token');
      this.props.dispatch({
        type: 'electronReport/configPictureOrTextDisplay',
        payload: {
          organCode: HOSPITAL_CODE.HXYY,
          position: 'DZBGFSJCXQ',
          serverCode: 'M',
          token,
        },
        callback: (res: any) => {
          res &&
            res.length > 0 &&
            this.setState({
              configData: res,
            });
        },
      });
    }
  };

  /** 获取报告详情数据 */
  fetchReportDetail = () => {
    const { shortKey, examNo: sourceExamNo } = queryString.parse(this.props.location.search);
    if (shortKey) {
      const shareData = HxSessionStorage.get('SHARE_DATA');
      if (shareData) {
        this.props.dispatch({
          type: 'electronReport/updateState',
          payload: {
            radiologyDetail: shareData,
          },
        });
      } else {
        // 通过二维码分享查看详情，调用重定向接口
        this.props.dispatch({
          type: 'electronReport/getUrl',
          payload: {
            shortKey,
          },
          callback: (res: any) => {
            HxSessionStorage.set('SHARE_DATA', res);
          },
        });
      }
    } else {
      const { organCode } = HxParameter;
      const token = HxLocalStorage.get('token');
      const {
        location: { query },
      } = this.props;
      const {
        electronReport: { middleLinkParams },
      } = this.props;
      const { id = '', channel = '', examNo = '', servType = '', itemId = '', sharePassword, shareKey, sourceType } =
        query || middleLinkParams || {}; // 列表页进来参数在state, 分享链接进来参数在model里
      if (sourceType !== 'share') {
        let payload: any = {
          organCode: organCode === 'HYT' ? HOSPITAL_CODE.HXYY : organCode,
          channel: isHytDoctor() ? 1 : 0,
          examNo: sourceExamNo || examNo,
          token,
          // id,
          itemId: encodeURIComponent(itemId),
        };
        if (isHytDoctor()) payload = { ...payload, servType };
        this.props.dispatch({
          type: isHytDoctor() ? 'electronReport/getCheckDetailDoctor' : 'electronReport/getCheckDetail',
          // type: 'electronReport/getCheckDetail',
          payload,
        });
      } else {
        this.props.dispatch({
          type: 'electronReport/getSharelinksCheckdetail',
          payload: {
            key: shareKey,
            password: sharePassword,
          },
        });
      }
    }
  };

  /**
   * 跳转到某个页面
   * @param {*} page 跳转页面
   */
  toPage = (page: any) => {
    const { openId } = HxParameter;
    let token = '';
    let url = '';
    switch (page) {
      case 'onlineClinic': // 在线门诊
        token = HxLocalStorage.get('token');
        // 跳在线门诊
        window.location.href = `${API_ZXMZ}/online/notice?token=${token}&organCode=${HOSPITAL_CODE.HXYY}&openId=${openId}`;
        break;
      case 'electronicFilmList': // 电子胶片列表
        url = `/${ModuleEnum.MODULE_ELECTRON_REPORT}/electronicfilmlist`;
        history.push(url);
        break;
      // case 'OriginalImage': // 原始影像页面
      // url = `/${ModuleEnum.MODULE_ELECTRON_REPORT}/OriginalImage`;
      // history.push(url);
      // break;

      default:
        break;
    }
  };

  checkTime = (end: string) => {
    // 判断存储时间是否超过3年
    const startDate = new Date().valueOf();
    const endDate = new Date(end.replace(/-/g, '/')).valueOf();
    const days = (endDate - startDate) / (1 * 24 * 60 * 60 * 1000);
    return days >= 1820; // 改成五年
  };
  /**
   * 跳转到选择电子胶片存储期限续费
   */

  toSlectFilmStoragePeriod = (code?: string, itemid?: string) => {
    const {
      electronReport: {
        radiologyDetail: { imageExpDate = '', itemId = '', hisCode = '', id = '', showOpenOption },
      },
    } = this.props;
    const { organCode } = HxParameter;

    // 华西医院开通服务
    if (showOpenOption && [HOSPITAL_CODE.HXYY].includes(hisCode)) {
      Modal.alert(
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <HxIcon iconName="tips" style={{ marginRight: '2px' }} />
          开通服务
        </div>,
        <div style={{ textAlign: 'center', color: '#353948' }}>请确认开通数字影像存储服务?</div>,
        [
          { text: '取消' },
          {
            text: '确认开通',
            onPress: async () => {
              try {
                const body = {
                  itemId,
                  organCode: hisCode,
                };
                const result = await openImageService(body);
                console.log('result', result);
                if (result && result.code === '1') {
                  Toast.show('开通成功');
                  setTimeout(() => {
                    window.location.reload();
                  }, 300);
                }
              } catch (err) {
                console.log(err);
              }
            },
          },
        ],
      );
      return;
    }

    if (this.checkTime(imageExpDate)) {
      Modal.alert(
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <HxIcon iconName="tips" style={{ marginRight: '2px' }} />
          温馨提示
        </div>,
        organCode !== HOSPITAL_CODE.ZYDY && (
          <div style={{ textAlign: 'justify', color: '#353948' }}>
            <p>该项检查的影像存储有效期为{imageExpDate},请确认是否继续续费？</p>
          </div>
        ),
        [
          { text: '取消' },
          {
            text: '继续续费',
            onPress: () => {
              history.push(
                `/${ModuleEnum.MODULE_ELECTRON_REPORT}/selectfilmstorageperiod?hisCode=${hisCode}&itemId=${itemId}&id=${id}`,
              );
            },
          },
        ],
      );
    } else {
      history.push(
        `/${ModuleEnum.MODULE_ELECTRON_REPORT}/selectfilmstorageperiod?hisCode=${hisCode}&itemId=${itemId}&id=${id}`,
      );
    }
  };

  /** 大众端过期/未购买弹框 */
  personModal = (type: string) => {
    const isExpired = type === 'expired';
    const {
      location: { state = {} },
    } = this.props;
    const { hisCode = '', itemId = '' }: any = state;
    if (isHytDoctor()) {
      Modal.alert(
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <HxIcon iconName="tips" style={{ marginRight: '2px' }} />
          温馨提示
        </div>,
        <div style={{ textAlign: 'left' }}>该检查的影像数据未上传影像云平台，暂无法提供线上调阅服务</div>,
        [{ text: '确定' }],
      );
    } else {
      if (hisCode === HOSPITAL_CODE.HXYY) {
        return;
      }
      Modal.alert(
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <HxIcon iconName="tips" style={{ marginRight: '2px' }} />
          温馨提示
        </div>,
        <div style={{ textAlign: 'left' }}>
          影像调阅<span style={{ color: '#FC7E00' }}>存储服务{isExpired ? '已到期' : '未购买'}</span>，如需
          {isExpired ? '继续' : ''}调阅影像，请{isExpired ? '续费' : '购买'}服务。
        </div>,
        [
          { text: '取消' },
          {
            text: isExpired ? '去续费' : '去购买',
            onPress: () => this.toSlectFilmStoragePeriod(hisCode, itemId), // 跳转到支付页面
          },
        ],
      );
    }
  };

  isProviderImagePlatform = () => {
    const {
      electronReport: {
        radiologyDetail: { hisCode, providerId },
      },
    } = this.props;
    console.log('HOSPITAL_CODE.HXYY--', HOSPITAL_CODE.HXYY, hisCode, providerId, PROVIDER.ProviderImagePlatform);

    return [HOSPITAL_CODE.HXYY, 'HXTF0101'].includes(hisCode) && providerId === PROVIDER.ProviderImagePlatform;
  };

  /** 扫码分享进入过期/未购买弹框 */
  sanInterModal = (type: string) => {
    const isExpired = type === 'expired';
    Modal.alert(
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <img
          src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/cry.png"
          alt=""
          style={{ width: '122px' }}
        />
      </div>,
      <div style={{ textAlign: 'center', color: '#03081A' }}>
        <p style={{ marginBottom: '6px', marginTop: '8px' }}>影像存储服务{isExpired ? '已到期' : '未购买'}</p>
        <p style={{ marginBottom: '10px' }}>您暂时无法调阅影像数据~</p>
        {/* 该影像报告的影像和原始影像调阅<span style={{ color: '#FC7E00' }}>存储服务{isExpired ? '已到期' : '未购买'}</span>
    ，如需继续调阅影像，需用户{ isExpired ? '续费' : '购买'}服务。 */}
      </div>,
      [
        {
          text: ' 我知道了',
          // onPress: () => this.datelinePush(),
          style: {
            width: '196px',
            height: '36px',
            background: 'linear-gradient(90deg, #3AD3C1 0%, #6CEBE2 100%)',
            borderRadius: '18px',
            margin: 'auto  auto 20px auto',
            color: '#fff',
            lineHeight: '36px',
          },
        },
      ],
    );
  };

  /** 扫码进入之后存储期限提醒 */
  datelinePush = () => {
    const { hisCode = '', itemId = '' } = queryString.parse(this.props.location.search);
    this.props.dispatch({
      type: 'electronReport/storageRemind',
      payload: {
        hisCode,
        itemId,
      },
      callback: (res: any) => {},
    });
  };

  /**
   * 华西影像云迁移
   */
  imagePlatformJump = async (body) => {
    // const result = await queryImge(body);
    // if ((result && result.code === '1') || (result && result.url)) {
    //   window.location.href = result?.data?.url || result?.url || '';
    // }
  };

  onCheck = (checkType: any, option?: string) => {
    const DOCTOR = isHytDoctor();
    const {
      electronReport: {
        middleLinkParams,
        radiologyDetail: {
          imageReportUrl = [],
          pdfReportUrl = '',
          isImageExpired,
          imageExpDate,
          imageServiceStatus,
          showOpenOption = false,
          hisCode,
          providerId,
          admType,
          imageUrl,
        },
      },
      location: { query },
    } = this.props;
    console.log('radiologyDetail111--', imageUrl);
    const { shortKey = '', orderStatus = '', sourceType, shareKey = '', sharePassword = '' } = queryString.parse(
      this.props.location.search,
    );
    switch (checkType) {
      case 0:
        // if (admType === 'I') {
        //   Toast.info('住院报告，暂不允许查询！');
        //   return;
        // }
        // 查看PDF报告

        if (pdfReportUrl) {
          window.location.href = `https://cdnhyt.cd120.com/3/pdfjs/web/viewer.html?file=${encodeURIComponent(
            pdfReportUrl,
          )}`;
        } else {
          Toast.info('数据获取中，请稍后查看');
        }
        break;

      case 1: // 查看电子胶片报告
        if (
          orderStatus === 'consultIng' ||
          (sourceType === 'order' && isHytDoctor()) // 影像咨询咨询中的出来的不判断有没有直接看 测试说的--肖亮
        ) {
          this.toPage('electronicFilmList');
        } else if (orderStatus === 'consultEnd') {
          Modal.alert('提示', '影像咨询期间，您可免费调阅影像。咨询结束后，需购买影像调阅存储服务，方可调阅影像', [
            {
              text: '确定',
              onPress: () => {
                if (imageReportUrl.length && Number(isImageExpired) === 0) {
                  this.toPage('electronicFilmList');
                } else if (isHytPerson() && Number(isImageExpired) === 1) {
                  this.personModal('expired');
                }
              },
            },
          ]);
        } else if (imageReportUrl.length && Number(isImageExpired) === 0) {
          this.toPage('electronicFilmList');
        } else if (Number(isImageExpired) === 1) {
          // Toast.info('胶片已过期');
          // 医生端扫描二维码进入时
          if (isHytPerson()) {
            this.personModal('expired');
          } else if (shortKey) {
            this.sanInterModal('expired');
          } else {
            this.toPage('electronicFilmList');
          }
        } else {
          Toast.info('影像数据获取中，请稍后再试');
        }
        break;
      case 2: // 查看原始影像
        // eslint-disable-next-line no-case-declarations
        let paramsObj: any = query;
        delete paramsObj.id;
        const { hisCode = '', itemId, organCode = '' } = paramsObj;
        paramsObj = { ...paramsObj, organCode: hisCode || organCode, itemId: encodeURIComponent(itemId) };
        const jump = (res, result) => {
          const { imageTotal } = result || { imageTotal: 1 };
          if (DOCTOR && res.length !== 0) {
            const url = res[0];
            window.location.href = url;
          } else {
            if (option && option === 'pay') {
              // 点击续费，直接跳转
              const {
                location: { state = {} },
              } = this.props;
              const { hisCode = '', itemId = '' }: any = state;
              this.toSlectFilmStoragePeriod(hisCode, itemId);
              return;
            }
            // 联影没过期判断
            if (this.isProviderImagePlatform()) {
              if (!shortKey && sourceType !== 'share') {
                const body = {
                  itemId,
                  organCode: hisCode,
                };
                window.location.href = imageUrl;
                // this.imagePlatformJump(body); //这个接口后端取消了
              } else {
                if (res.length === 0) {
                  Toast.info('影像数据获取中，请稍后再试');
                  return;
                }
                Toast.loading('');
                const url = res[0];
                window.location.href = url;
              }
              return;
            }
            if (Number(isImageExpired) === 1 || new Date(imageExpDate).getTime() < new Date().getTime()) {
              // 过期, imageExpDate
              if (shortKey || sourceType === 'share') {
                // 二维码或者分享链接进来
                this.sanInterModal('expired');
                return;
              }
              this.personModal('expired');
              // else { // 产品确认了，公众号app大众端都不能直接进入
              //   const url = originalImageReportUrl[0];
              //   window.location.href = url;
              // }
            } else if (imageServiceStatus === 0 || new Date(imageExpDate).getTime() < new Date().getTime()) {
              // 未购买
              if (shortKey || sourceType === 'share') {
                // 二维码或者分享链接进来
                this.sanInterModal('unpurchase');
                return;
              }
              this.personModal('unpurchase');
            } else {
              // Toast.info('数据获取中，请稍后查看');
              if (res.length === 0 || imageTotal > res.length) {
                Toast.info('影像数据获取中，请稍后再试');
                return;
              }
              Toast.loading('');
              // const url = originalImageReportUrl[0];
              if (imageTotal === 1) {
                const url = res[0];
                window.location.href = url;
              } else {
                history.push({
                  pathname: '/electronreport/imagecloud-list',
                  query: queryString.parse(window.location.search),
                });
              }
              Toast.hide();
            }
          }
        };
        if (sourceType === 'share') {
          this.props.dispatch({
            type: 'electronReport/getSharelinksCheckdetail',
            payload: {
              key: shareKey,
              password: sharePassword,
            },
            callback: (res: any, result: any) => {
              console.log('点击按钮调接口', res);
              jump(res, result);
            },
          });
        } else if (shortKey) {
          this.props.dispatch({
            type: 'electronReport/getResetUrl',
            payload: {
              shortKey,
            },
            callback: (res: any) => {
              jump(res.imageReportUrl, res);
              HxSessionStorage.set('SHARE_DATA', res);
            },
          });
        } else {
          // 华西医院开通服务
          if ([HOSPITAL_CODE.HXYY].includes(hisCode) && providerId === PROVIDER.ProviderImagePlatform) {
            if (showOpenOption) {
              Modal.alert(
                <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <HxIcon iconName="tips" style={{ marginRight: '2px' }} />
                  开通服务
                </div>,
                <div style={{ textAlign: 'center', color: '#353948' }}>请确认开通数字影像存储服务?</div>,
                [
                  { text: '取消' },
                  {
                    text: '确认开通',
                    onPress: async () => {
                      try {
                        const body = {
                          itemId,
                          organCode: hisCode,
                        };
                        const result = await openImageService(body);
                        console.log('result', result);
                        if (result && result.code === '1') {
                          window.location.reload();
                        }
                      } catch (err) {
                        console.log(err);
                      }
                    },
                  },
                ],
              );
            } else {
              const body = {
                itemId,
                organCode: hisCode,
              };
              window.location.href = imageUrl;
              // this.imagePlatformJump(body);
            }
            return;
          }

          this.props.dispatch({
            type: DOCTOR ? 'electronReport/getCheckDetailDoctor' : 'electronReport/getCheckDetail',
            payload: { ...paramsObj, channel: DOCTOR ? 1 : 0 },
            callback: (res: any, result: any) => {
              console.log('点击按钮调接口', res);
              jump(res, result);
            },
          });
        }
        break;

      default:
        break;
    }
  };

  onSensors = (param, type) => {
    const { hisCode = HOSPITAL_CODE.HXKG } = this.props?.location?.query || {};
    const paramsObj = { ...param, source: 'VIDEO_PAGE_DETAIl' };
    if (type === 1) {
      if (['SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX].includes(hisCode))
        sensorsRequest('VIDEO_CLICKWATCH_SL', paramsObj);
      else sensorsRequest('VIDEO_CLICKWATCH', paramsObj);
    } else {
      if (['SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX].includes(hisCode))
        sensorsRequest('VIDEO_CLICKWATCHVIDEO_SL', paramsObj);
      else sensorsRequest('VIDEO_CLICKWATCHVIDEO', paramsObj);
    }
  };

  /**
   * 渲染电子报告查看类型面板
   * @param {number} isViewImage 是否查看电子胶片 0否1是
   * @param {number} isViewOriginalImage 是否查看原始影像 0否1是
   */
  renderReportTypes = (isViewImage: any, isViewOriginalImage: any) => {
    const { sourceType } = queryString.parse(this.props.location.search) || {};
    const {
      electronReport: { radiologyDetail = {}, checkDetailRes = {} },
    } = this.props;
    const { imageReportUrl = [], admType, imagePrintStatus = 0, isImageExpired = 1, imageTotal = 0 } = radiologyDetail;
    const {
      location: { state = {} },
    } = this.props;
    console.log('radiologyDetail---', admType, radiologyDetail);
    const { hisCode = '', itemId = '' }: any = state;
    const { organCode } = HxParameter;
    const REPORT_TYPES = [
      {
        label: '检查报告',
        value: 0,
        desc: '点击查看PDF报告',
        logoSrc: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/yxbg.png',
        color: '#FF5A66',
      },
    ];
    const { shortKey = '' } = queryString.parse(this.props.location.search);
    // 不查看电子胶片
    if (isViewImage === 0) {
      const viewImage = REPORT_TYPES.find((item) => item.value === 1);
      viewImage && REPORT_TYPES.splice(REPORT_TYPES.indexOf(viewImage, 1));
    }
    // 通过二维码分享查看的
    if (shortKey || sourceType === 'share' || isHytDoctor()) {
      return (
        <>
          (
          <div className={styles.btnCard}>
            <div className={styles.left}>
              <img src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/yxbg.png" alt="" />
              <div className={styles.info}>
                <p className={styles.title}>检查报告</p>
                <p className={styles.tip}>{radiologyDetail.hisName}</p>
              </div>
            </div>
            <div className={styles.right}>
              <div
                className={styles.btn}
                onClick={() => {
                  this.onSensors(radiologyDetail, 1);
                  this.onCheck(0);
                }}
              >
                查看
              </div>
            </div>
          </div>
          ){/* {(!hasOriginalImage && isViewOriginalImage === 1) ||
            (sourceType === 'share' && ( */}
          <div className={styles.btnCard}>
            <div className={styles.left}>
              <img src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/yxdy.png" alt="" />
              <div className={styles.info}>
                <p className={styles.title}>影像调阅</p>
              </div>
            </div>
            {!isHytDoctor() && (
              <>
                <div className={styles.right}>
                  <div
                    className={styles.btn}
                    onClick={() => {
                      this.onSensors(radiologyDetail, 2);
                      this.onCheck(2);
                    }}
                  >
                    查看
                  </div>
                </div>
              </>
            )}
            {isHytDoctor() && (
              <>
                <div className={styles.right}>
                  <div
                    className={`${styles.btn} ${
                      imageReportUrl.length !== 0 || imageReportUrl.length < imageTotal ? '' : styles.btnCantClick
                    }`}
                    onClick={() => {
                      if (imageReportUrl.length === 0) return;
                      this.onSensors(radiologyDetail, 2);
                      this.onCheck(2);
                    }}
                  >
                    查看
                  </div>
                </div>
              </>
            )}
          </div>
          {!isHytDoctor() && (
            <div>
              {imagePrintStatus === 0 &&
                radiologyDetail.imageServiceStatus === 1 &&
                !!radiologyDetail.imageExpDays &&
                imageReportUrl.length === 0 && (
                  <div className={styles.addTips}>
                    影像数据正在从医院获取，此过程需要一定的时间，待数据获取成功后，系统将推送消息通知您，请耐心等待。
                  </div>
                )}
              {imagePrintStatus === 2 &&
                radiologyDetail.imageServiceStatus === 1 &&
                !!radiologyDetail.imageExpDays &&
                imageReportUrl.length === 0 && (
                  <div className={styles.addTips}>您已在院内完成了胶片打印，如需继续调阅影像数据，可续费服务。</div>
                )}
            </div>
          )}
          {/* ))} */}
        </>
      );
    }
    return (
      <>
        {/* 华西住院报告不显示 */}
        {getOrganCode() === 'HID0101' && admType === 'I' ? (
          ''
        ) : (
          <div className={styles.btnCard}>
            <div className={styles.left}>
              <img src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/yxbg.png" alt="" />
              <div className={styles.info}>
                <p className={styles.title}>检查报告</p>
                <p className={styles.tip}>{radiologyDetail.hisName}</p>
              </div>
            </div>
            <div className={styles.right}>
              <div className={styles.btn}>
                {/* 安卓使用按钮下载，ios使用pdf查看器下载 */}
                {isHytPerson() && getChannelCode() === 'PATIENT_ANDROID' && (
                  <a href={radiologyDetail?.pdfReportUrl} download={'报告.pdf'}>
                    下载报告
                  </a>
                )}
              </div>
              <div
                className={styles.btn}
                onClick={() => {
                  this.onSensors(radiologyDetail, 1);
                  this.onCheck(0);
                }}
              >
                查看
              </div>
            </div>
          </div>
        )}

        {/* 没过期才显示胶片 */}
        {getOrganCode() === 'HID0101' && isImageExpired === 1 ? (
          ''
        ) : (
          <div className={styles.btnCard}>
            <div className={styles.left}>
              <img src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/yxdy.png" alt="" />
              <div className={styles.info}>
                <p className={styles.title}>影像调阅</p>
                {/** imageServiceStatus：0：未购买，是否过期的状态值（1：购买未过期，2：已过期) */}
                {/* {organCode !== HOSPITAL_CODE.ZYDY &&
                !this.isProviderImagePlatform() &&
                radiologyDetail.imageServiceStatus === 1 && (
                  <p className={styles.tip}>
                    {radiologyDetail.imageExpDays > 0 ? (
                      <span>有效期：{radiologyDetail.imageExpDate}截止</span>
                    ) : (
                      <span>影像存储已到期</span>
                    )}
                    {organCode !== HOSPITAL_CODE.ZYDY && organCode !== HOSPITAL_CODE.HXYY && (
                      <span className={styles.fontBtn} onClick={() => this.toSlectFilmStoragePeriod(hisCode, itemId)}>
                        续费
                      </span>
                    )}
                  </p>
                )} */}
                {/* {radiologyDetail.imageServiceStatus === 0 && !radiologyDetail.oldReportFlag && (
                <p className={styles.tip}> */}
                {/** 上线后未购买 上线前的不展示 */}
                {/* {radiologyDetail.imageExpDays > 0 ? (
                    <span>影像存储免费体验：剩余{radiologyDetail.imageExpDays}天</span>
                  ) : (
                    <span>影像存储免费体验已到期</span>
                  )}
                  <span className={styles.fontBtn} onClick={() => this.toSlectFilmStoragePeriod(hisCode, itemId)}>
                    续费
                  </span>
                </p>
              )} */}
                {radiologyDetail.imageServiceStatus === 0 && (
                  /** 未开通影像存储服务（双流，华西医院一致） * */
                  <p className={styles.tip}>
                    <span>开通影像存储服务，在线调阅、分享影像数据</span>
                  </p>
                )}
              </div>
            </div>

            <div className={styles.right}>
              {/* 华西和天府去掉这个按钮 */}
              {getOrganCode() !== 'HXTF0101' && getOrganCode() !== 'HID0101' && (
                <>
                  {radiologyDetail.imageServiceStatus === 1 && !!radiologyDetail.imageExpDays && (
                    // 购买了服务，未过期
                    <div
                      className={styles.btn}
                      onClick={() => {
                        this.onSensors(radiologyDetail, 2);
                        this.onCheck(2);
                      }}
                    >
                      {imageReportUrl.length === 0 || imageReportUrl.length < imageTotal ? '下载中' : '查看'}
                    </div>
                  )}
                </>
              )}
              {((radiologyDetail.imageServiceStatus === 0 && organCode !== HOSPITAL_CODE.HXYY) ||
                radiologyDetail.showOpenOption) && (
                <div
                  className={classnames(styles.btn, styles.innormal)}
                  onClick={() => {
                    this.onCheck(2, 'pay');
                  }}
                >
                  开通服务
                </div>
              )}

              {/* {!radiologyDetail.showOpenOption && this.isProviderImagePlatform() && (
                <div
                  className={styles.btn}
                  onClick={() => {
                    this.onSensors(radiologyDetail, 2);
                    this.onCheck(2);
                  }}
                >
                  查看
                </div>
              )} */}
              {isImageExpired !== 1 && (
                <div
                  className={styles.btn}
                  onClick={() => {
                    this.onSensors(radiologyDetail, 2);
                    this.onCheck(2);
                  }}
                >
                  查看
                </div>
              )}
              {/* {radiologyDetail.imageExpDays <= 0 &&
                radiologyDetail.imageServiceStatus === 2 &&
                organCode !== HOSPITAL_CODE.HXYY && (
                  <div
                    className={classnames(styles.btn, styles.innormal)}
                    onClick={() => {
                      this.onCheck(2, 'pay');
                    }}
                  >
                    立即续费
                  </div>
                )} */}
            </div>
          </div>
        )}

        <div>
          {imagePrintStatus === 0 &&
            radiologyDetail.imageServiceStatus === 1 &&
            !!radiologyDetail.imageExpDays &&
            imageReportUrl.length === 0 && (
              <div className={styles.addTips}>
                {!['HXTF0101', 'HID0101'].includes(getOrganCode()) &&
                  ' 影像数据正在从医院获取，此过程需要一定的时间，待数据获取成功后，系统将推送消息通知您，请耐心等待。'}
              </div>
            )}
          {imagePrintStatus === 2 &&
            radiologyDetail.imageServiceStatus === 1 &&
            !!radiologyDetail.imageExpDays &&
            imageReportUrl.length === 0 && (
              <div className={styles.addTips}>您已在院内完成了胶片打印，如需继续调阅影像数据，可续费服务。</div>
            )}
        </div>
      </>
    );
  };

  toDoctorList = () => {
    const { organCode = '', appCode = '', channelCode = '', token = '' } = HxParameter;
    const env = isHytPerson() ? 1 : 2;
    window.location.href = `${API_ZXMZ}/online/imagecloud/intro?env=${env}&organCode=${organCode}&appCode=${appCode}&channelCode=${channelCode}&token=${token}`;
  };

  /** 渲染电子报告解读引流banner */
  renderReadBanner = () => {
    const { configData = [] } = this.state;
    const { shortKey = '' } = queryString.parse(this.props.location.search);
    const organList = ['SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX];
    const organCode = getOrganCode();
    const showBanner =
      !shortKey &&
      !organList.includes(organCode) &&
      (isHytPerson() || isWechat() || platform === PlatformEnum.BROWSER) &&
      configData.length > 1 &&
      configData[1].picUrl &&
      configData[1].display;
    if (showBanner) {
      return (
        // <div
        //   onClick={() => {
        //     // this.toPage('onlineClinic');
        //     this.toDoctorList();
        //   }}
        //   style={{ flex: 1 }}
        // >
        //   影像咨询
        // </div> // 暂时屏蔽，不上线
        <></>
      );
    }
    return null;
  };

  /** 点击分享banner的回调函数：跳转到报告分享须知页面 */
  toReportShareNotice = () => {
    const token = HxLocalStorage.get('token');
    const {
      location: { state },
      electronReport: {
        radiologyDetail: {
          isImageExpired,
          imageServiceStatus,
          imageExpDays,
          itemId = '',
          hisCode = HOSPITAL_CODE.HXKG,
          id = '',
          examNo = '',
          patientId = '',
        },
      },
    } = this.props;
    const sensorsData = { ...this.props.electronReport.radiologyDetail, source: 'VIDEO_PAGE_DETAIl' };
    if (['SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX].includes(hisCode))
      sensorsRequest('VIDEO_TOSHARE_SL', sensorsData);
    else sensorsRequest('VIDEO_TOSHARE', sensorsData);
    const { channel = 0 } = state || {};
    // 医生端不需要判断是否过期直接跳转
    if (!isHytDoctor()) {
      if (Number(isImageExpired) === 1 && hisCode !== HOSPITAL_CODE.HXYY) {
        this.setState({
          visible: true,
          modalType: 'expired',
        });
      } else {
        // history.push(
        //   `/${ModuleEnum.MODULE_ELECTRON_REPORT}/reportsharenotice?hisCode=${hisCode}&itemId=${itemId}&id=${id}&channel=${channel}&examNo=${examNo}&token=${token}`,
        // );
        if (imageServiceStatus === 0 || imageExpDays <= 0 || isImageExpired === 1) {
          if (hisCode !== HOSPITAL_CODE.HXYY) {
            // 未购买或者已过期
            this.setState({
              visible: true,
              modalType: imageServiceStatus === 0 ? 'unpurchase' : 'expired',
            });
            return;
          }
        }
        /** 打开分享弹窗 */
        this.setState({
          showShare: true,
          shareParams: {
            // id: patientId || '',
            channel: 0,
            examNo,
            id: id || '',
            hisCode,
            itemId,
          },
        });
      }
    } else {
      history.push(
        `/${ModuleEnum.MODULE_ELECTRON_REPORT}/reportsharenotice?hisCode=${hisCode}&itemId=${itemId}&id=${id}&channel=${channel}&examNo=${examNo}&token=${token}`,
      );
    }
  };

  /** 渲染分享banner */
  renderShareBanner = () => {
    const { configData = [] } = this.state;
    const {
      electronReport: { radiologyDetail = {} },
    } = this.props;
    const { shortKey = '', share = 0 } = queryString.parse(this.props.location.search);
    const showBanner =
      !shortKey &&
      (isHytPerson() || isWechat() || platform === PlatformEnum.BROWSER) &&
      configData.length > 2 &&
      configData[2].picUrl &&
      configData[2].display;

    const organList = ['SL0101', 'MS0101', HOSPITAL_CODE.HID09XXXX];
    const organCode = getOrganCode();

    const showBannerNew =
      !shortKey &&
      !organList.includes(organCode) &&
      (isHytPerson() || isWechat() || platform === PlatformEnum.BROWSER) &&
      configData.length > 1 &&
      configData[1].picUrl &&
      configData[1].display;
    return (
      <>
        {showBanner && !isHytDoctor() && showBannerNew && radiologyDetail.type !== 'DR' && (
          <div className={styles.doctorShareButton} onClick={this.toReportShareNotice} style={{ flex: 1 }}>
            分享
          </div>
        )}
        {((showBanner && radiologyDetail.type === 'DR') || isHytDoctor() || organList.includes(organCode)) &&
          !share &&
          share !== 1 && (
            <div className={styles.doctorShareButton} onClick={this.toReportShareNotice} style={{ flex: 1 }}>
              分享报告
            </div>
          )}
        {/* {isHytDoctor() && (
          <div className={styles.doctorShareButton} onClick={this.toReportShareNotice}  style={{ flex: 1 }}>
            分享报告
          </div>
        )} */}
      </>
    );
  };

  /**
   * 改变展开状态
   * @param {*} type 医学影像/医学诊断
   */
  changeShowMore = (type: any) => {
    const { showImgFindingMore, showImgDiagnosisMore } = this.state;
    if (type === 'finding') {
      this.setState({
        showImgFindingMore: !showImgFindingMore,
      });
    }
    if (type === 'diagnosis') {
      this.setState({
        showImgDiagnosisMore: !showImgDiagnosisMore,
      });
    }
  };

  /**
   * 渲染内容（比较长的）
   * @param {*} type
   * @param {*} isShow
   * @param {*} content
   */
  renderContent = (type: any, isShow: any, content: string) => {
    let textNode: any = null;
    let label = '';
    switch (type) {
      case 'finding':
        label = '影像学表现';
        break;

      case 'diagnosis':
        label = '影像学诊断';
        break;

      default:
        break;
    }
    if (content && content.length && content.length > 250) {
      // @ts-ignore
      textNode = (
        <div className={styles.textContainer}>
          {isShow ? `${content.slice(0, 250)}......` : content}
          <div
            className={styles.lessOrMore}
            onClick={() => {
              this.changeShowMore(type);
            }}
          >
            <div className={styles.expend}>
              {isShow ? '展开' : '收起'}
              <HxIcon
                iconName={isShow ? 'appointment-arrow-down' : 'appointment-arrow-up'}
                className={styles.expendIcon}
              />
            </div>
          </div>
        </div>
      );
    } else {
      textNode = (
        <div className={styles.textContainer} style={{ color: content ? '#03081a' : '#989eb4' }}>
          {content || '暂无'}
        </div>
      );
    }
    return (
      <div>
        <div className={styles.textTitle}>{label}</div>
        {textNode}
      </div>
    );
  };

  cancle = () => {
    this.setState({
      showShare: false,
    });
  };
  toApply = (examName, itemId, radiologyDetail) => {
    const { radiographApplyStatus = '', radiographOrderId = '' } = radiologyDetail;
    if (radiographApplyStatus === 2) {
      history.push(`/filmPrinting/detail?orderId=${radiographOrderId}`);
      return;
    }
    history.push(`/filmPrinting/notice?examName=${examName}&itemId=${itemId}`);
  };
  /**
   * 渲染顶部提示
   */
  renderNotice = () => {
    const A = this.state.newNotice.split('\r\n');
    return (
      <div className={classnames(styles.pompt, this.state.showPompt ? '' : styles.hiden)}>
        {A && A.length && (
          <div
            style={{
              lineHeight: '5vw',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              padding: '10px 0',
            }}
          >
            {A.map((ele: any, i: number) => (
              <div style={{ margin: 0 }} key={i.toString()}>
                {ele}
              </div>
            ))}
          </div>
        )}
        <span className={styles.closePompt} onClick={this.closePompt}>
          &times;
        </span>
      </div>
    );
  };

  /** 分享链接倒计时 */

  render() {
    const {
      electronReport: { radiologyDetail = {}, checkDetailRes = {}, wechatShareTips },
      loading,
    } = this.props;
    const { sourceType, shortKey, doctorCode = '' } = queryString.parse(this.props.location.search);
    const {
      type = '',
      patientName = '',
      sex = 0,
      age = 0,
      hisName = '',
      examNo = '',
      checkPoint = '',
      imageDate = '',
      imageDiagnosis = '',
      imgFinding = '',
      isViewImage = '',
      examName = '',
      isViewOriginalImage = '',
      hisCode = 'MID0101',
      itemId: detailItemId,
      radiographApplyStatus = 0,
      isImageExpired = '',
    } = radiologyDetail;
    console.log('详情--', radiologyDetail);
    const showSex: any = radiologyDetail.sex === null || radiologyDetail.sex === undefined;
    // const token = HxLocalStorage.get('token');
    const {
      location: { state },
    } = this.props;
    const { itemId } = state || {};
    // const checkType = FSJC_TYPES.find((item: any) => item.value === type) || { label: '' };
    const basicData = [
      {
        label: '姓名',
        value: patientName || '',
        otherInfo: [
          { label: '性别', value: sex || '' },
          { label: '年龄', value: age || '' },
        ],
      },
      { label: '医院', value: hisName || '' },
      { label: '检查类型', value: type || '', color: '#32B9AA' },
      { label: '检查编号', value: examNo || '' },
      { label: '检查部位', value: checkPoint || '' },
      { label: '影像日期', value: imageDate || '' },
    ];
    const {
      configData = [],
      showImgFindingMore = true,
      showImgDiagnosisMore = true,
      visible = false,
      showShare,
      shareParams,
    } = this.state;
    const { organCode } = HxParameter;
    return (
      <div className={styles.container} style={radiographApplyStatus !== 0 ? { paddingBottom: '120px' } : {}}>
        {loading ? (
          <div className={styles.showLoading}>
            <HxIndicator />
          </div>
        ) : (
          ((this.state.leftTime > 0 && sourceType === 'share') || sourceType !== 'share') && (
            <>
              {configData.length !== 0 &&
                configData[0].display &&
                configData[0].content &&
                sourceType !== 'share' &&
                this.renderNotice()}

              {shortKey && (
                <div className={classnames(styles.pompt, this.state.showPompt ? '' : styles.hiden)}>
                  此报告明细仅做参考，请以医院纸质报告为准
                  <span className={styles.closePompt} onClick={this.closePompt}>
                    &times;
                  </span>
                </div>
              )}
              {organCode !== HOSPITAL_CODE.ZYDY && sourceType === 'share' && (
                <div
                  className={classnames(styles.pompt, this.state.showPompt ? '' : styles.hiden)}
                  style={{ justifyContent: 'center' }}
                >
                  当前检查可读有效期剩余：{this.state.leftTimeStr}
                </div>
              )}
              <div className={styles.wrapBox}>
                <div className={styles.detail}>
                  <div>
                    {basicData.map((item) => {
                      const { label = '', value = '', color = '' } = item;
                      return (
                        <p key={Number(Math.random().toString().substr(3, 12) + Date.now()).toString(36)}>
                          <span className={styles.detailTitle}>{label}</span>
                          <span style={{ color: color || '#353948' }}>{value}</span>
                          {!showSex &&
                            radiologyDetail &&
                            JSON.stringify(radiologyDetail) !== '{}' &&
                            item.otherInfo &&
                            item.otherInfo.map((ele) => (
                              <span style={{ color: color || '#353948', marginLeft: '12px' }} key={ele.label}>
                                {ele.label === '性别' ? (Number(ele.value) ? '女' : '男') : ele.value}
                              </span>
                            ))}
                        </p>
                      );
                    })}
                  </div>
                </div>
                {hisCode === 'HID0101' && sourceType !== 'share' && (
                  <div>
                    <img
                      style={{ height: '80px', width: '100%', padding: '10px', background: '#f5f6f7' }}
                      src="https://hytstatic0.cd120.info/articleimage/20200722/1595407787917008827.png"
                      onClick={() => {
                        window.location.href = `${
                          window.location.origin
                        }/person/codeIntermediatePage?token=${getToken()}&channelCode=${getChannelCode()}&appCode=HXGYAPP&purePlayKey=ACQRCODE&from=reportDetail&docCode=${doctorCode}&organCode=HID0101&purePlayParam=docCode@${doctorCode}`;
                      }}
                      alt=""
                    />
                  </div>
                )}
                {/**  查看报告，影像调阅 */}
                {this.renderReportTypes(isViewImage, isViewOriginalImage)}
                {/* 影像下载 */}
                <RequestToDownload itemId={detailItemId} />
                {/* 增加胶片申请入口 */}
                {radiographApplyStatus !== 0 && (
                  <div className={styles.printEnter}>
                    <div className={styles.row}>
                      <div>
                        <img src={filmPrintIcon} alt="" />
                      </div>
                      <div className={styles.title}>
                        <div>胶片打印申请</div>
                        <p>申请缴费后可去线下打印实体胶片</p>
                      </div>
                      <div
                        className={styles.apply}
                        onClick={() => this.toApply(examName, detailItemId, radiologyDetail)}
                      >
                        申请
                      </div>
                    </div>
                    <div className={styles.tips}>* 仅支持检查日期在2025年6月14日后的医嘱</div>
                  </div>
                )}
                {/* {this.renderReadBanner()} */}
                <div
                  className={
                    radiologyDetail.imageServiceStatus === 1 &&
                    !!radiologyDetail.imageExpDays &&
                    radiologyDetail.imageReportUrl.length === 0
                      ? `${styles.dataList} ${styles.dataListAdd}`
                      : styles.dataList
                  }
                  style={{ paddingBottom: sourceType === 'share' || shortKey ? '0' : '75px' }}
                >
                  {imgFinding || imageDiagnosis ? (
                    <div className={styles.textWrapper}>
                      {this.renderContent('finding', showImgFindingMore, imgFinding)}
                      {this.renderContent('diagnosis', showImgDiagnosisMore, imageDiagnosis)}
                    </div>
                  ) : !(radiographApplyStatus !== 0) && isImageExpired !== 1 ? (
                    <div
                      className={styles.nodata}
                      style={radiographApplyStatus !== 0 ? { top: '90%' } : { top: '73%' }}
                    >
                      <HxEmpty emptyMsg="暂无检查报告，请耐心等待" emptyIcon="norecord" canRefresh={false} />
                    </div>
                  ) : (
                    ''
                  )}
                </div>
                {!sourceType && (
                  <div className={styles.btns}>
                    {this.renderShareBanner()}
                    {!isHytDoctor() && type !== 'DR' && this.renderReadBanner()}
                  </div>
                )}
                {showShare && <Share showShare={showShare} cancle={this.cancle} shareParams={shareParams} />}
                {wechatShareTips && (
                  <div
                    className={styles.mask}
                    onClick={() => {
                      this.props.dispatch({
                        type: 'electronReport/updateState',
                        payload: {
                          wechatShareTips: false,
                        },
                      });
                    }}
                  >
                    <img src={wechatShare} alt="" className={styles.wechatShare} />
                  </div>
                )}
                {sourceType === 'share' && (
                  <p className={styles.warning}>
                    <b>!</b>此报告明细仅做参考，请以医院纸质报告为准
                  </p>
                )}
              </div>
            </>
          )
        )}
        <Modal
          visible={visible}
          className="shareModal"
          transparent
          maskClosable
          onClose={() => {
            this.setState({
              visible: false,
            });
          }}
          title={
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <HxIcon iconName="tips" style={{ marginRight: '2px' }} />
              温馨提示
            </div>
          }
          closable
          footer={[
            {
              text: '去分享',
              onPress: () => {
                /** 打开分享弹窗 */
                const {
                  electronReport: {
                    radiologyDetail: { itemId = '', hisCode = '', examNo = '', id = '' },
                  },
                } = this.props;
                console.log('debug', this.props);
                this.setState({
                  showShare: true,
                  visible: false,
                  shareParams: {
                    id,
                    channel: 0,
                    examNo,
                    hisCode,
                    itemId,
                  },
                });
              },
            },
            {
              text: this.state.modalType === 'expired' ? '去续费' : '去购买',
              onPress: () => this.toSlectFilmStoragePeriod(hisCode, itemId), // 跳转到支付页面
            },
          ]}
        >
          <div style={{ textAlign: 'left', color: '#03081A' }}>
            该影像报告的影像存储服务{this.state.modalType === 'expired' ? '已到期' : '未购买'},
            <span style={{ color: '#FC7E00' }}>医生无法进行影像调阅,仅可查看分享的检查报告。</span>
            为了让医生更好的了解您的病情，建议{this.state.modalType === 'expired' ? '续费' : '开通'}服务。
          </div>
          ,
        </Modal>
        <PayConfirm {...this.props} />
      </div>
    );
  }
}

export default connect(({ loading, electronReport }: { electronReport: IElectronReportState; loading: Loading }) => ({
  // loading: loading.effects['electronReport/getCheckDetail'] || loading.effects['electronReport/getUrl'],
  electronReport,
}))(RadiologyDetail);
