.container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #f5f6f7;

  .personShare {
    width: 60px;
    margin-right: 24px;
    color: #03081a !important;
    line-height: 34px !important;
    text-align: center;
    background: transparent !important;
    font-size: 24px !important;
    .shareIcon {
      position: relative;
      left: 6px;
      display: block;
      width: 50px;
      margin-bottom: 8px;
    }
  }
  .warning {
    padding: 16px 0;
    color: #989eb4;
    font-size: 20px;
    line-height: 28px;
    transform: scale(0.9);
    b {
      display: inline-block;
      width: 40px;
      height: 40px;
      margin-right: 6px;
      line-height: 40px;
      text-align: center;
      border: 2px solid #989eb4;
      border-radius: 50%;
    }
  }

  .showLoading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-top: 350px;
  }

  p {
    margin: 0;
  }

  .guideImage {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 2;
    width: 100vw;
  }

  // .doctorShareButton {
  //   position: fixed;
  //   right: 80px;
  //   bottom: 80px;
  //   width: 100px;
  //   height: 100px;
  //   color: #fff;
  //   font-weight: 600;
  //   font-size: 34px;
  //   line-height: 100px;
  //   text-align: center;
  //   background: #32b9aa;
  //   border-radius: 50%;
  //   box-shadow: 0 6px 8px 0 rgba(0, 139, 124, 0.3);
  // }
}

.wrapBox {
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: auto;
  .btns {
    position: fixed;
    bottom: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 20px 28px;
    background-color: #fff;
    div {
      // width: 332px;
      height: 90px;
      //margin-right: 20px;
      color: #fff;
      line-height: 90px;
      text-align: center;
      background-color: #3ad3c1;
      border-radius: 48px;
      font-size: 32px;
      &:nth-child(2) {
        flex: 1;
      }
    }
  }
}

.detail {
  // display: flex;
  // flex-direction: row;
  padding: 20px 20px;
  background-color: #fff;
  .detailTitle {
    display: inline-block;
    min-width: 4em;
    margin-right: 40px;
    color: #03081a;
    font-weight: 600;
    font-size: 32px;
  }
  p {
    color: #aaa;
    font-size: 32px;
    &:not(:last-child) {
      padding-bottom: 10px;
    }
  }
  // span {
  //   margin-left: 19px;
  // }
  .toDetail {
    margin-left: auto;
    color: #aaa;
  }
}

// .imgList {
//   display: flex;
//   flex-wrap: wrap;
//   margin-top: 10px;
//   padding: 0 0 20px 20px;
//   background-color: #fff;
//   border-top: 1px solid #eee;
//   border-bottom: 1px solid #eee;
//   img {
//     width: 150px;
//     height: 150px;
//     margin: 20px 20px 0 0;
//   }
// }

.sendMail {
  box-sizing: border-box;
  width: 100%;
  margin-bottom: 20px;
  padding: 0 20px 0 20px;
  text-align: center;
  a {
    width: 100%;
    color: #fff;
    font-size: 30px;
    background-color: #32b9aa;
    border-radius: 49px;
  }
}

.imgContainer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background-color: #a0a0a0;
  > div {
    display: flex;
    flex: 1;
    align-items: center;
  }
  > p {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 100px;
    color: #fff;
    font-size: 28px;
    line-height: 100px;
    text-indent: 20px;
    background-color: rgba(0, 0, 0, 0.3);
  }
}

.dataList {
  flex: 1;
  margin-top: 24px;
  margin-bottom: 20px;
  //margin-bottom: 150px;
  //color: '#222';
  &.dataListAdd {
    margin-top: 104px;
  }
  .textWrapper {
    .textTitle {
      padding: 30px 28px;
      color: #333;
      font-weight: 600;
      font-size: 30px;
      background: #fff;
    }

    .textContainer {
      margin-bottom: 20px;
      padding: 0 20px 20px 20px;
      //color: #aaa;
      color: #03081a;
      font-size: 28px;
      line-height: 48px;
      background: #fff;
      //border-top: 2px solid #eee;
    }
  }

  .icon {
    width: 1em;
    height: 1em;
    overflow: hidden;
    //color: '#222';
    vertical-align: -15px;
    fill: currentColor;
  }
  .jiang {
    font-size: 36px;
  }
  p {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100px;
    padding: 0 20px;
    color: #222;
    font-size: 28px;
    background-color: #fff;
    border-top: 1px solid #eee;
    &:last-child {
      border-bottom: 1px solid #eee;
    }
    span {
      color: #aaa;
      font-size: 24px;
    }
  }

  .title {
    color: #666;
    font-size: 28px;
    background: #f7f3f7;
  }

  .nodata {
    position: absolute;
    top: 60%;
    left: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #aaa;
    font-size: 28px;
    transform: translate(-50%, -50%);
    img {
      width: 120px;
    }
    span {
      display: block;
      margin-top: 20px;
    }
  }

  .lessOrMore {
    margin-top: 24px;
    color: #32b9aa;
    font-size: 24px;
    text-align: center;
    .expend {
      display: flex;
      justify-content: center;
      .expendIcon {
        width: 32px;
        height: 32px;
      }
    }
  }
}

.disabled {
  position: fixed;
  bottom: 20px;
  box-sizing: border-box;
  width: 100%;
  padding: 0 20px 0 20px;
  text-align: center;
  a {
    width: 100%;
    color: #fff;
    font-size: 30px;
    background-color: #eee;
    border-radius: 49px;
  }
}

.qrInput {
  position: relative;
  padding-top: 10px;
  img {
    position: absolute;
    top: 28px;
    right: 22px;
    width: 44px;
    height: 44px;
  }
  :global(div::after) {
    height: 0 !important;
  }
}

.pompt {
  display: flex;
  justify-content: space-between;
  // background: #fffae4;
  padding: 0 24px;
  color: #fc4553;
  //padding: 28px;
  //color: #fc7e00;
  font-size: 26px;
  line-height: 72px;
  background: #ffedee;
  .closePompt {
    font-size: 40px;
  }
}
.hiden {
  display: none;
}

.reportTypes {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  font-size: 28px;
  background-color: #f4f5f6;
  .reportTypeItem {
    display: flex;
    padding: 28px 40px 28px 14px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.08);
    img {
      width: 80px;
      height: 80px;
    }
    .typeName {
      font-size: 30px;
    }
    .typeDesc {
      color: #999;
      font-size: 24px;
    }
  }
  .reportTypeItems {
    display: flex;
    padding: 17px 50px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 3px 8px 0 rgba(0, 0, 0, 0.08);
    img {
      width: 80px;
      height: 80px;
    }
    .typeName {
      font-size: 30px;
    }
    .typeDesc {
      color: #999;
      font-size: 24px;
    }
  }
}
.btnCard {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 24px;
  padding: 24px;
  color: #989eb4;
  font-weight: 500;
  font-size: 24px;
  background: #fff;
  min-height: 144px;
  position: relative;
  .left {
    display: flex;
    align-items: center;
    img {
      width: 96px;
      height: 96px;
      margin-right: 30px;
    }
  }
  .info {
    .title {
      color: #03081a;
      font-size: 32px;
    }
    .tip {
      margin-top: 8px;
      font-weight: 400;
      line-height: 40px;
    }
    .fontBtn {
      margin-left: 16px;
      color: #ff823f;
      text-decoration: underline;
    }
  }
  .right {
    .btn {
      width: 150px;
      display: inline-block;
      margin-left: 10px;
      color: #fff;
      font-size: 28px;
      line-height: 60px;
      text-align: center;
      background: linear-gradient(270deg, #6cebe2 0%, #3ad3c1 100%);
      border-radius: 30px;
      &.btnCantClick {
        background: linear-gradient(270deg, #dfe7e6 0%, #ccd3d2 100%);
      }
      &.innormal {
        background: linear-gradient(270deg, #ffc26e 0%, #fe8f3c 100%);
      }
      a {
        color: #fff;
      }
    }
  }
}

:global(.shareModal) {
  :global(.am-modal-close-x) {
    padding: 20px;
    border: 2px solid #999;
    border-radius: 50%;
    background-size: 20px;
    background-position: center center;
  }
  :global(.am-modal-close) {
    width: auto;
    height: auto;
  }
}
.addTips {
  // position: absolute;
  color: #ff4f48;
  padding-right: 15px;
  background-color: #ffffff;
  bottom: -80px;
  margin-left: -90px;
  padding-left: 100px;
  padding-bottom: 20px;
}

.wechatShare {
  position: fixed;
  right: 16px;
  top: 12px;
  width: 504px;
  height: 172px;
}

.mask {
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 10;
  width: 100%;
  color: #03081a;
  background: rgba(0, 0, 0, 0.6);

  &.show {
    top: 0;
  }
}
.printEnter {
  background: #fff;
  padding: 24px;
  margin-top: 24px;
  .row {
    display: flex;
    justify-content: center;
    .title {
      font-size: 32px;
      font-weight: bolder;
      color: #03081a;
      margin-left: 24px;
      flex: 1;
      p {
        color: #b0b3bf;
        font-size: 24px;
        font-weight: normal;
      }
    }
    .apply {
      width: 150px;
      height: 60px;
      line-height: 60px;
      background: linear-gradient(90deg, #3ad3c1, #6cebe2);
      border-radius: 30px;
      font-size: 28px;
      font-weight: bolder;
      text-align: center;
      color: #ffffff;
    }
    img {
      width: 100px;
      height: 100px;
    }
  }
  .tips {
    text-align: center;
    color: #ff984b;
    padding-top: 30px;
  }
}
