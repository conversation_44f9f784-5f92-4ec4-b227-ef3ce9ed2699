import React, { FC, useEffect, useState } from 'react';
import { Modal, Toast } from 'antd-mobile';
import styles from './index.less';
import { connect, IElectronReportState, Dispatch, history } from 'umi';
import rightArrow from '@/assets/ElectronReport/rightArrow.png';
const alert = Modal.alert;
interface IProps {
  dispatch: Dispatch;
  electronReport: IElectronReportState;
  location: {
    query: {
      id: string;
    };
  };
}

const ReportEntrance: FC<IProps> = (props) => {
  const {
    dispatch,
    electronReport,
    location: { query = {} },
  } = props;
  const { id = '' }: any = query;
  const [navArr, setNavArr] = useState<any>([
    {
      id: 1,
      label: '电子报告',
      icon: '',
      text: '门诊、住院检查检验报告查看',
    },
    {
      id: 2,
      label: '体检报告',
      icon: '',
      text: '个人体检健康报告查看',
    },
  ]);

  useEffect(() => {}, []);

  return (
    <>
      <div className={styles.content}>
        {navArr.map((item: any) => (
          <div className={styles.items}>
            <div className={styles.itemsL}>
              <img src={item?.icon} className={styles.itemsL1} />
              <div className={styles.itemsL2}>
                <div className={styles.itemsL2Txt1}>{item?.label}</div>
                <div className={styles.itemsL2Txt2}>{item?.text}</div>
              </div>
            </div>
            <img src={rightArrow} className={styles.itemsR} />
          </div>
        ))}
      </div>
    </>
  );
};

export default connect(({ electronReport }: { electronReport: IElectronReportState }) => ({
  electronReport,
}))(ReportEntrance);
