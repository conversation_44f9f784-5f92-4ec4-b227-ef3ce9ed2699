import React, { memo, useEffect, useState } from 'react';
import { Dispatch, AnyAction } from 'redux';
import { getChannelCode, getOrganCode, HxParameter } from '@/utils/parameter';
// import moment from 'moment';
import { HxIndicator, HxEmpty } from '@/components';
import { Button } from 'antd-mobile-v5';
import { HxLocalStorage, HxSessionStorage } from '@/utils/storage';
import { connect, Loading, IElectronReportState } from 'umi';
import { getPlatform, isHytDoctor } from '@/utils/platform';
import qs from 'query-string';
import { PlatformEnum } from '@/utils/enum';
import styles from './index.less';
import { goOutpatientPayment } from '@/pages/ConvenienceClinicNew/utils/tool';
import filmPrintIcon from '@/assets/ElectronReport/filmPrint.png';
import { FilmApplyEnter } from '../components';
import { StorageEnum } from '@/utils/enum';

// const up = require('@/assets/ElectronReport/上升.png');
// const down = require('@/assets/ElectronReport/下降.png');
// import { Button, Toast, Tabs } from 'antd-mobile';

interface IProps {
  dispatch: Dispatch<AnyAction>;
  loading?: boolean;
  reportDetail: { [index: string]: any };
  location: {
    state: {
      item: any;
    };
  };
}

interface IDatas {
  itemName: string;
  resultValue: string;
  referValue: string;
  unit: string;
  upDownFlag: string;
  resultType: number;
}

const ElectronReportDetail = memo((props: IProps) => {
  const cardData = HxSessionStorage.get('patientCardData');
  const organInfo = HxLocalStorage.get('organInfo') || {};
  const { organName = '' } = organInfo;
  const { patientName = '', gender = '0', cardId = '' } = cardData || {};
  const {
    location: { state },
  } = props;
  const { item } = state;
  const { dispatch, reportDetail, loading } = props;
  const {
    resultItems = [],
    checkDesc = '',
    checkResult = '',
    patNo = '',
    pisNo = '',
    patName = '',
    patAge = '',
    patSex = '',
    reportDate: reportDateCase = '',
    reportDoc = '',
    verifyDoc = '',
    eyeSee = '',
    examSee = '',
    diagnose = '',
    imageList = [],
    pdfBaseEncode = '',
    reportIssueTime = '',
    chargeFlag,
  } = reportDetail || {};
  const { reportType = '', reportNo = '', reportName = '', reportDate = '', reclocDr = '', doctorCode = '' } = item;
  const platform = getPlatform();

  // 获取openId,prganCode
  const { openId, organCode } = HxParameter;
  // 获取token
  const token = HxLocalStorage.get('token');

  // 设定初始state
  const [reportInterpretationImageVisible, setreportInterpretationImageVisible] = useState<boolean>(false);
  const [bannerImage, setbannerImage] = useState<string>('');

  const fetchData = () => {
    console.log(reportType, 'reportType', item);
    dispatch({
      type: reportType === '4' ? 'electronReport/getPathologyDetail' : 'electronReport/getDetail',
      payload: {
        cardId,
        reportType,
        reportNo,
        reclocDr,
        reportName,
      },
    });
  };

  /** 后端配置报告解读引流图片的显隐 */
  const fetchConfigData = () => {
    props.dispatch({
      type: 'electronReport/configPictureOrTextDisplay',
      payload: {
        organCode: 'HID0101',
        position: reportType === '2' ? 'DZBGJCXQ' : 'DZBGJYXQ',
        serverCode: '',
      },
      callback: (res: any) => {
        if (res.length > 0) {
          const { display, picUrl } = res[0];
          setreportInterpretationImageVisible(display);
          if (['HID0101'].includes(getOrganCode())) {
            setbannerImage('https://hytstatic0.cd120.info/articleimage/20200722/1595407787917008827.png');
          } else {
            setbannerImage(picUrl);
          }
        }
      },
    });
  };

  /** 点击报告解读图片的回调函数 */
  const onGuide = () => {
    if (['HID0101'].includes(getOrganCode())) {
      window.location.href = `${
        window.location.origin
      }/person/codeIntermediatePage?token=${token}&channelCode=${getChannelCode()}&appCode=HXGYAPP&purePlayKey=ACQRCODE&from=reportDetail&docCode=${doctorCode}&organCode=HID0101&purePlayParam=docCode@${doctorCode}`;
    } else {
      // 跳在线门诊
      window.location.href = `${API_ZXMZ}/onlineExpert/notice?token=${token}&organCode=${getOrganCode()}&openId=${openId}&servCode=zxmz`;
    }
  };

  /** 查看base64pdf */
  const turnBase64Pdf = () => {
    const params = {
      cardId,
      reportType,
      reportNo,
      reclocDr,
      reportName,
    };
    window.location.href = `${COMMON_DOMAIN}/parking-project/tfpdf?channelCode=${getChannelCode()}&organCode=${getOrganCode()}&${qs.stringify(
      params,
    )}&token=${token}`;
  };
  const checkImage = () => {
    const { pmiNo } = HxSessionStorage.get(StorageEnum.PATIENTCARD_DATA) || {};
    props.dispatch({
      type: 'electronReport/checkImageRetrieval',
      payload: {
        organCode: 'HID0102',
        patientNo: pmiNo,
        itemId: reportNo,
      },
      callback: (res) => {
        console.log(11111, res);
        if (res?.url) {
          window.location.href = res.url;
        }
      },
    });
  };
  /* 华西医院乙肝检验异常跳转到在线门诊感染科 */
  const goToXszm = () => {
    window.location.href = `${API_ZXMZ}/onlinefast/home?channelCode=PATIENT_WECHAT&organCode=HID0101&servCode=zxmz&deptCode=4398&token=${token}`;
  };
  const ygState = () => {
    try {
      return (
        !loading &&
        reportType === '1' &&
        getOrganCode() === 'HID0101' &&
        ['乙肝两对半定量', '乙肝病毒DNA定量'].some((item) => reportName.includes(item)) &&
        resultItems.some((item) => item?.resultType !== 0)
      );
    } catch (error) {
      return false;
    }
  };

  useEffect(() => {
    setTimeout(() => {
      document.title = reportType === '4' ? '病理报告详情' : reportName === '' ? '详情' : reportName;
    }, 0);
    fetchData();
    if (organCode === 'HID0101' || organCode === 'HYT') {
      fetchConfigData();
    }
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.pompt}>
        <span>此明细只做参考，请以窗口提供的明细为准</span>
        {['HXXM0101'].includes(getOrganCode()) && <span>，截屏无效</span>}
      </div>
      <div className={styles.detail}>
        <div>
          {getOrganCode() === 'HXTF0101' && (
            <p>
              <span className={styles.detailTitle}>医院</span>
              <span>华西天府医院</span>
            </p>
          )}
          {reportType === '4' ? (
            <p>
              <span className={styles.detailTitle}>登记号</span>
              <span>{patNo}</span>
            </p>
          ) : (
            <p>
              <span className={styles.detailTitle}>项目名称</span>
              <span>{reportName}</span>
            </p>
          )}
          {reportType === '4' ? (
            <p>
              <span className={styles.detailTitle}>病理号</span>
              <span>{pisNo}</span>
            </p>
          ) : (
            <p>
              <span className={styles.detailTitle}>医嘱时间</span>
              <span>{reportDate}</span>
            </p>
          )}
          {getOrganCode() === 'HID19001' && (
            <p>
              <span className={styles.detailTitle}>报告时间</span>
              <span>{reportIssueTime}</span>
            </p>
          )}
          {reportType === '4' ? (
            <p>
              <span className={styles.detailTitle}>患者姓名</span>
              <span>{patName}</span>
            </p>
          ) : (
            !isHytDoctor() && (
              <p>
                <span className={styles.detailTitle}>患者姓名</span>
                <span>{patientName}</span>
              </p>
            )
          )}
          {reportType === '4' ? (
            <p>
              <span className={styles.detailTitle}>患者年龄</span>
              <span>{patAge}</span>
            </p>
          ) : (
            !isHytDoctor() &&
            Number(gender) !== 0 && (
              <p>
                <span className={styles.detailTitle}>患者性别</span>
                <span>{Number(gender) === 1 ? '男' : Number(gender) === 2 ? '女' : ''}</span>
              </p>
            )
          )}
          {reportType === '4' && (
            <p>
              <span className={styles.detailTitle}>患者性别</span>
              <span>{patSex}</span>
            </p>
          )}
          {reportType === '4' && (
            <p>
              <span className={styles.detailTitle}>报告日期</span>
              <span>{reportDateCase}</span>
            </p>
          )}
          {reportType === '4' && (
            <p>
              <span className={styles.detailTitle}>报告医生</span>
              <span>{reportDoc}</span>
            </p>
          )}
          {reportType === '4' && (
            <p>
              <span className={styles.detailTitle}>审核医生</span>
              <span>{verifyDoc}</span>
            </p>
          )}
        </div>
      </div>
      {console.log('pdfBaseEncode==', pdfBaseEncode)}

      {pdfBaseEncode && (
        <div className={styles.btnCard}>
          <div className={styles.left}>
            <img src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/yxbg.png" alt="" />
            <div className={styles.info}>
              <div className={styles.title}>检查/检验报告</div>
              <div className={styles.tip}>{organName}</div>
            </div>
          </div>
          <div className={styles.right}>
            <div className={styles.btn} onClick={turnBase64Pdf}>
              查看
            </div>
          </div>
        </div>
      )}
      {/* 增加胶片申请入口 */}
      {/* <FilmApplyEnter /> */}

      {loading && <HxIndicator />}
      {(platform === PlatformEnum.HYT_PERSON ||
        platform === PlatformEnum.WECHAT ||
        platform === PlatformEnum.BROWSER) &&
        (organCode === 'HID0101' || organCode === 'HYT') &&
        bannerImage &&
        reportInterpretationImageVisible && (
          <div>
            <img
              style={{ height: '80px', width: '100%', padding: '10px', background: '#f5f6f7' }}
              src={bannerImage}
              onClick={onGuide}
              alt=""
            />
          </div>
        )}
      {!loading && reportType === '1' && (
        <div className={styles.dataList}>
          <div className={styles.itemBoxTitle}>
            <span>项目</span>
            <span>结果</span>
            <span>单位</span>
          </div>
          {resultItems.length !== 0 &&
            resultItems.map((item: IDatas) => {
              // 返回的resultType字段
              // 1 偏高
              // 2 偏低
              // 0 正常
              const { itemName, resultValue, referValue, resultType, unit, upDownFlag } = item;
              return (
                <div
                  key={Math.random().toString(36).substr(3, 10)}
                  className={resultType === 0 ? styles.itemBox : `${styles.itemBox} ${styles.listActive}`}
                  style={{
                    backgroundColor: resultType === 2 ? '#EDF3FF' : resultType === 1 ? '#FFF0F1' : '#FFFFFF',
                  }}
                >
                  {itemName === '新型冠状病毒RNA' ? (
                    <div
                      style={{
                        color: '#03081A',
                      }}
                      className={styles.list}
                    >
                      <div className={styles.listLeft}>{itemName}</div>
                      <div className={styles.listRight}>
                        <span>{resultValue}</span>
                      </div>
                      <div className={styles.listEnd} />
                    </div>
                  ) : (
                    <div>
                      <div
                        style={{
                          color: resultType === 2 ? '#568DF2' : resultType === 1 ? '#FC4553' : '#03081A',
                        }}
                        className={styles.list}
                      >
                        <div className={styles.listLeft}>{itemName}</div>
                        <div className={styles.listRight}>
                          <span>
                            {resultValue}
                            {/* {resultType === 2 ? ' ↓' : resultType === 1 ? ' ↑' : ''} */}
                            {upDownFlag || ''}
                          </span>
                        </div>
                        <div className={styles.listEnd}>{unit}</div>
                      </div>
                      <div
                        style={{
                          color: resultType === 2 ? '#568DF2' : resultType === 1 ? '#FC4553' : '#03081A',
                        }}
                        className={styles.list1}
                      >
                        <div className={styles.listLeft} />
                        <div className={styles.listRight1}>
                          {referValue && <span>参考范围：{`(${referValue})`}</span>}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
        </div>
      )}
      {organCode === 'HID0102' && (
        <div className={styles.btnCard}>
          <div className={styles.left}>
            <img src="https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronic-report/yxdy.png" alt="" />
            <div className={styles.info}>
              <p className={styles.title}>影像调阅</p>
            </div>
          </div>
          {!isHytDoctor() && (
            <>
              <div className={styles.right}>
                <div className={styles.btn} onClick={checkImage}>
                  查看
                </div>
              </div>
            </>
          )}
        </div>
      )}
      {!loading && reportType === '2' && (
        <div className={styles.textContainer}>
          <p className={styles.title}>检查所见</p>
          {checkDesc}
        </div>
      )}
      {!loading && reportType === '2' && (
        <div className={styles.textContainer}>
          <p className={styles.title}>诊断结果意见</p>
          {checkResult}
        </div>
      )}

      {!loading && reportType === '4' && (
        <div className={styles.textContainer}>
          <p className={styles.title}>肉眼所见</p>
          {eyeSee}
        </div>
      )}
      {!loading && reportType === '4' && (
        <div className={styles.textContainer}>
          <p className={styles.title}>镜下所见</p>
          {examSee}
          {imageList.map((itm: any) => {
            return <img className={styles.examSeeImg} src={`data:image/jpeg;base64,${itm.imageBase64}`} alt="" />;
          })}
        </div>
      )}
      {!loading && reportType === '4' && (
        <div
          className={styles.textContainer}
          style={{
            whiteSpace: 'pre-wrap',
          }}
        >
          <p className={styles.title}>病理诊断</p>
          {diagnose}
        </div>
      )}
      {!loading && reportType === '2' && !checkDesc && !checkResult && (
        <div className={styles.emptyBox}>
          <HxEmpty
            emptyMsg={reportType === '2' ? '暂无检查报告，请耐心等待' : '暂无报告，请耐心等待'}
            emptyIcon="norecord"
            canRefresh={false}
          />
        </div>
      )}
      {/* 病理报告底部跳转按钮显示 */}
      {!loading && reportType === '4' && chargeFlag === 1 && (
        <>
          <div className={styles.seizeBox} />
          <div className={styles.chargeBtn}>
            <Button
              color="primary"
              block
              shape="rounded"
              size="large"
              onClick={() => {
                goOutpatientPayment();
              }}
            >
              门诊缴费
            </Button>
          </div>
        </>
      )}
      {/* 华西医院乙肝异常跳转按钮显示 */}
      {ygState() && (
        <>
          <div className={styles.seizeBox} />
          <div className={styles.chargeBtn}>
            <Button
              color="primary"
              block
              shape="rounded"
              size="large"
              onClick={() => {
                goToXszm();
              }}
            >
              结果异常，咨询专科医生&gt;&gt;
            </Button>
          </div>
        </>
      )}
    </div>
  );
});

export default connect(({ loading, electronReport }: { electronReport: IElectronReportState; loading: Loading }) => ({
  loading: loading.effects['electronReport/getDetail'],
  reportDetail: electronReport.reportDetail,
}))(ElectronReportDetail);
