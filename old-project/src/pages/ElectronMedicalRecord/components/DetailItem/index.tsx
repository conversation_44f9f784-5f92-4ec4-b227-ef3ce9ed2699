import React from 'react';
import styles from './index.less';

interface IProps {
  /**
   * 标题
   */
  label: any;
  /**
   * 值
   */
  value: any;
}

const DetailItem = (props: IProps) => {
  const { label = '', value = '' } = props;
  return (
    <div className={styles.item}>
      <div className={styles.content}>
        <div className={styles.left}>{label}</div>
        <div className={styles.right}>{value}</div>
      </div>
    </div>
  );
};
export default DetailItem;
