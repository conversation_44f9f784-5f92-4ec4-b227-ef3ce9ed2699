.calendarContainer {
  :global {
    .am-calendar .single-month .row .cell .date-wrapper .disable {
      color: #b0b3bf;
      background-color: #fff !important;
    }

    .am-calendar .week-panel .cell-grey {
      color: #989eb4;
    }

    .am-calendar {
      .content {
        height: 1043px;
        bottom: 0;
        top: unset;

        .wrapper {
          .row {
            display: flex;
          }

          .cell {
            .info {
              padding: 0;
            }

            .date-wrapper {
              font-size: 0;
              display: flex;
              height: 80px;
              width: 101%;

              .date {
                height: 100%;
                width: 80px;
              }

              span {
                height: 80px;
                border-radius: 0;

                &.date-selected {
                  position: relative;

                  &::before {
                    content: '';
                    border-radius: 0;
                    width: 3px;
                    height: calc(100% - 2px);
                    position: absolute;
                    left: -2px;
                    top: 1px;
                    background-color: #3ad3c1;
                  }

                  &::after {
                    content: '';
                    border-radius: 0;
                    width: 3px;
                    height: calc(100% - 2px);
                    position: absolute;
                    top: 1px;
                    right: -2px;
                    background-color: #3ad3c1;
                  }
                }
              }

              .date-selected {
                background-color: #3ad3c1;
              }
            }
          }
        }

        .week-panel {
          padding-top: 24px;
          padding-bottom: 24px;
          border-bottom: none;
        }

        .confirm-panel {
          display: none;
        }

        header {
          width: 100%;
          height: 96px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0 32px;

          span {
            font-size: 32px;
            line-height: 48px;
          }
        }
      }
    }

    .date {
      div {
        padding-left: 0 !important;
      }
    }
  }
}
