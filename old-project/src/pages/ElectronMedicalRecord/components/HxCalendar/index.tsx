import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import React, { useEffect, useState } from 'react';
import { Calendar, Toast } from 'antd-mobile';
import styles from './index.less';

interface IProps {
  visible: boolean;
  onConfirm: (data: any) => void;
  /* 选择类型 one: 单日 range: 日期区间 */
  type: 'one' | 'range';
  /* title */
  title?: string;
  /* 关闭弹框回调 */
  onCancel?: () => void;
}

/* 初始化日历 */
dayjs.extend(isSameOrBefore);
const dayZero = dayjs(dayjs().format('YYYY/MM/DD'));
const MinDate = dayZero.add(1, 'day');
const MaxDate = dayZero.add(1, 'day').add(12, 'month');
const extra: any = {};

const i = 1;

// while (dayjs(dayZero.add(i, 'day')).isSameOrBefore(MaxDate)) {
//   if (dayZero.add(i, 'day').day() === 6 || dayZero.add(i, 'day').day() === 0) {
//     // extra[+dayZero.add(i, 'day').valueOf()] = { disable: true };
//   }
//   i += 1;
// }

Object.keys(extra).forEach((key) => {
  const info = extra[key];
  const date = new Date(key);
  if (!Number.isNaN(+date) && !extra[+date]) {
    extra[+date] = info;
  }
});

interface ISelectDate {
  type: number; // 1 开始时间 2 结束数据
  date: string;
}

const HxCalendar: React.FC<IProps> = (props) => {
  const { visible = false, onConfirm, type = 'range', title = '请选择时间', onCancel: _onCancel = () => {} } = props;
  const [calendarVisible, setCalendarVisible] = useState<boolean>(visible);
  const [selectDate, setSelectDate] = useState<ISelectDate[]>([]); // 点击选择的日期
  const [confirmSelectDate, setConfirmSelectDate] = useState<ISelectDate[]>([]); // 确认选择的日期
  const [devaultCalendarValue, setDevaultCalendarValue] = useState<Date[]>([]);

  /** 确认选择取件时间 */
  const confirmSelectPickupTime = () => {
    console.log('selectDate:', selectDate);
    if (selectDate.length && (selectDate[0]?.type === 1 || selectDate.length === 1) && type === 'range') {
      // 起止时间异常
      Toast.info('请选择正确的日期区间');
    } else {
      let _searchWord: string = '';
      let _selectDate: ISelectDate[] = [];
      try {
        /* 判断起止时间先后顺序 */
        _selectDate = selectDate.slice(0); // 拷贝数组
        const firstSelectDate = new Date(selectDate[0]?.date)?.getTime();
        const secendSelectDate = new Date(selectDate[1]?.date)?.getTime();
        if (firstSelectDate > secendSelectDate) _selectDate.reverse();
      } catch (error) {
        console.log('error:', error);
      }
      try {
        _selectDate.forEach((item, inx) => {
          _searchWord += `${item.date}${inx < 1 ? '-' : ''}`;
        });
      } catch (error) {
        console.log('error:', error);
      }

      onConfirm(selectDate); // 确认时间回调

      console.log('_searchWord:', _searchWord);

      setConfirmSelectDate(selectDate); // 确认时间

      /* 关闭弹框 */
      setCalendarVisible(false);
    }
  };

  /* 打开日历 */
  const clickShowCalendar = () => {
    setCalendarVisible(true);
    let defalutvValue: Date[] = [];
    if (confirmSelectDate.length === 2) {
      defalutvValue = confirmSelectDate.map((i) => new Date(i?.date));
      setSelectDate(confirmSelectDate); // 打开日历设置选择的数据
    }
    setDevaultCalendarValue(defalutvValue);
  };

  const getDateExtra = (date: any) => {
    return extra[+date];
  };

  /**
   * @name: 选择时间
   * @param {*} @date 选择的时间
   */
  const handleSelectDate = (date: Date) => {
    /* 打开日历控件设为空 */
    let _selectDate: ISelectDate[] = selectDate;
    let type = 0; // 起止时间判断 只有当选中的时间第一个type为0时
    const formatDate: string = dayjs(date).format('YYYY/MM/DD');

    /* 每次选择时间都截取最后一个 */
    if (_selectDate.length > 1) {
      _selectDate = _selectDate.slice(_selectDate.length - 1);
    }
    /* 判断起止type */
    try {
      type = _selectDate && _selectDate.length ? 1 - _selectDate[0]?.type : 0;
    } catch (error) {
      console.log(error);
    }
    _selectDate.push({ date: formatDate, type });

    setSelectDate(_selectDate);
  };

  /**
   * 关闭弹框回调函数
   * @return {*}
   */
  const onCancel = () => {
    setCalendarVisible(false);
    _onCancel && _onCancel();
  };

  useEffect(() => {
    if (visible) clickShowCalendar();
  }, [visible]);
  return (
    <div className={styles.calendarContainer}>
      <Calendar
        type={type}
        renderHeader={() => {
          return (
            <>
              <header>
                <span
                  style={{ color: 'rgb(152, 158, 180)' }}
                  onClick={() => {
                    onCancel();
                  }}
                >
                  取消
                </span>
                <span className={styles.calTitle} style={{ color: '#03081A' }}>
                  {title}
                </span>
                <span
                  style={{ color: '#3AD3C1' }}
                  onClick={() => {
                    confirmSelectPickupTime();
                  }}
                >
                  确定
                </span>
              </header>
            </>
          );
        }}
        minDate={MinDate.toDate()}
        maxDate={MaxDate.toDate()}
        getDateExtra={getDateExtra}
        defaultValue={devaultCalendarValue}
        visible={calendarVisible}
        onSelect={handleSelectDate}
      />
    </div>
  );
};

export default HxCalendar;
