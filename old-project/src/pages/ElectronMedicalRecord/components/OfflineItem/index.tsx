import classNames from 'classnames';
import React from 'react';
import styles from './index.less';

interface IProps {
  /**
   * 标题
   */
  label: any;
  /**
   * 值
   */
  value?: any;

  newline?: boolean;
}

const OfflineItem = (props: IProps) => {
  const { label = '', value = '', newline = false } = props;
  return (
    <div className={styles.item}>
      <div className={styles.content}>
        <span className={styles.label}>{label}</span>
        {newline && <br />}
        <span className={classNames(styles.value, newline && styles.newLine)}>{value}</span>
      </div>
    </div>
  );
};
export default OfflineItem;
