.itemCard {
  width: 100%;
  margin-top: 20px;
  padding: 0 24px;
  background: #ffffff;
  border-radius: 16px;

  .header {
    display: flex;
    // align-items: center;
    justify-content: space-between;
    padding: 24px 0;
    border-bottom: 2px solid #ebedf5;

    .left {
      font-size: 28px;
      line-height: 40px;
      .time {
        margin-top: 8px;
        color: #989eb4;
      }
      .typeName {
        color: #3ad3c1;
        font-weight: bold;
      }
    }

    .right {
      display: flex;
      flex-direction: row;
      align-items: flex-start;
      color: #999;
      font-size: 28px;
      line-height: 40px;
      height: auto;
      .more {
        color: #03081a;
      }
      .img {
        width: 24px;
        height: 40px;
        object-fit: contain;
        margin-left: 2px;
        vertical-align: middle;
      }
    }
  }

  .detail {
    padding: 8px 0 24px 0;

    .item {
      display: flex;
      flex-direction: row;
      align-items: center;
      margin-top: 16px;

      .introduce {
        color: #989eb4;
        font-size: 28px;
        .name {
          color: #03081a;
        }
      }
    }
  }
}
