import React from 'react';
import { HxIcon } from '@/components';
import styles from './index.less';

const DetailItem = (props: { [key: string]: any }) => {
  const { name, title } = props;
  const a = '';
  return (
    <div className={styles.item}>
      <div className={styles.introduce}>
        <span className={styles.title}>{title}：</span>
        <span className={styles.name}>{name}</span>
      </div>
    </div>
  );
};

interface IProps {
  /**
   * 电子病例列表参数
   */
  item: any;
  /**
   * 点击事件
   */
  onClick: (e: any) => void;
}

const ListItem = (props: IProps) => {
  const { item, onClick } = props;
  const { createTime = '', organName = '', deptName = '', type = 1 } = item;
  return (
    <div className={styles.itemCard} onClick={() => onClick(item)}>
      <div className={styles.header}>
        <div className={styles.left}>
          <div className={styles.typeName}>电子病历</div>
          <div className={styles.time}>{createTime}</div>
        </div>
        <div className={styles.right}>
          <div className={styles.more}>查看</div>
          <HxIcon iconName="arrow-right" className={styles.img} />
        </div>
      </div>
      <div className={styles.detail}>
        <DetailItem title="就诊医院" name={organName} />
        <DetailItem title="就诊科室" name={deptName} />
        {/* <DetailItem title="病历名称" name={deptName} /> */}
      </div>
    </div>
  );
};
export default ListItem;
