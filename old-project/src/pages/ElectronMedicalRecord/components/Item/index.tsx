import React from 'react';
import styles from './index.less';

interface IProps {
  /**
   * 标题
   */
  label: any;
  /**
   * 值
   */
  value?: any;
}

const Item = (props: IProps) => {
  const { label = '', value = '无' } = props;
  return (
    <div className={styles.item}>
      <div className={styles.content}>
        <div className={styles.label}>{label}</div>
        <div className={styles.value}>{value}</div>
      </div>
    </div>
  );
};
export default Item;
