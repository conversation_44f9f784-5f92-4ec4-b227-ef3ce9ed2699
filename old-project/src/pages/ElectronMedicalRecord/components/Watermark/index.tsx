import React, { useEffect, useState } from 'react';
import styles from './index.less';

interface IProps {
  rotate?: number;
  height?: number;
  width?: number;
  text: string[];
  fontSize?: string;
  fontFamily?: string;
  fillStyle?: string;
  position?: {
    x: number;
    y: number;
  };
  spaceWidth?: number;
  pageHeight: number | undefined; // 页面高度
}
const Watermark: React.FC<IProps> = (props) => {
  const {
    rotate = -30,
    height = 75,
    width = 85,
    text = ['添加水印'],
    fontSize = '28px',
    fontFamily = 'microsoft yahei',
    fillStyle = 'rgba(252, 69, 83, 0.3)',
    position = { x: 50, y: 110 },
    spaceWidth = 40,
    pageHeight,
  } = props;
  const WaterMarkImg = () => {
    const image = new Image();
    image.crossOrigin = 'Anonymous';
    const canvas = document.createElement('canvas');
    const context: any = canvas.getContext('2d');
    canvas.width = width;
    canvas.height = height;
    context.font = `${fontSize} ${fontFamily}`;
    context.rotate((rotate * Math.PI) / 180);
    context.fillStyle = fillStyle;
    context.textAlign = 'center';
    context.textBaseline = 'hanging';
    text.forEach((textItem, index) => {
      context.fillText(textItem, position.x, position.y + index * spaceWidth);
    });
    return canvas.toDataURL('image/png');
  };
  /* 动态计算需要多少张水印 */
  const count = (pageHeight && Math.floor(pageHeight / 300)) || 1;
  let imgNote: any = '';
  try {
    let i = 0;
    for (i === 0; i < count; i++) {
      imgNote += `<img src=${WaterMarkImg()} alt="" style="top: ${i * 400}px"  />`;
    }
  } catch (error) {
    console.log('error:', error);
  }
  return (
    <div className={styles.waterMarkContainer}>
      {/* <img src={WaterMarkImg()} alt="" />
      <img src={WaterMarkImg()} alt="" style={{ top: '400px' }} /> */}
      <div dangerouslySetInnerHTML={{ __html: imgNote || '' }} />
    </div>
  );
};

export default Watermark;
