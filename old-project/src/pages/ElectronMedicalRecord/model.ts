import { Effect, Reducer, Subscription } from 'umi';
import { IElectronMedicalRecordModelState } from './data';
import { newMedicalList, medicalDetail, newMedicalDetail } from './service';

interface IElectronMedicalRecordModel {
  namespace: string;
  state: IElectronMedicalRecordModelState;
  effects: {
    medicalList: Effect;
    medicalDetail: Effect;
    queryMedicalDetail: Effect;
  };
  reducers: {
    updateState: Reducer;
  };
  subscriptions: {
    setup: Subscription;
  };
}

const ElectronMedicalRecord: IElectronMedicalRecordModel = {
  namespace: 'electronMedicalRecord',
  state: {},
  effects: {
    *medicalList({ payload, callback }, { call }) {
      const data = yield call(newMedicalList, payload) || [];
      callback && callback(data);
    },
    *medicalDetail({ payload, callback }, { call }) {
      const data = yield call(medicalDetail, payload) || [];
      callback && callback(data);
    },
    *queryMedicalDetail({ payload, callback }, { call }) {
      const data = yield call(newMedicalDetail, payload) || [];
      callback && callback(data);
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
  subscriptions: {
    setup({ dispatch, history }) {
      history.listen(({ pathname }) => {
        const pathArr = [
          '/electronmedicalrecord/offlineDetail',
          '/electronmedicalrecord/detail',
          '/electronmedicalrecord/home',
        ];
        if (!pathArr.includes(pathname)) {
          dispatch({
            type: 'updateState',
            payload: {
              chooseDate: null,
            },
          });
        }
      });
    },
  },
};

export default ElectronMedicalRecord;
