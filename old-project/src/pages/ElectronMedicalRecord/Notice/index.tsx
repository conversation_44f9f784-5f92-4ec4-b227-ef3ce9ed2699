import React, { useState } from 'react';
import { Button, Toast, Checkbox } from 'antd-mobile';
import { history } from 'umi';
import { ModuleEnum } from '@/utils/enum';
import styles from './index.less';

const Notice: React.FC = () => {
  // props

  // state
  const [checked, setChecked] = useState(false);

  const onChangeCheck = (e: any) => {
    setChecked(e.target.checked);
  };

  const onSure = () => {
    if (checked) {
      history.push(
        `/patientcard/home?redirect=/${ModuleEnum.MODULE_ELECTRON_MEDICAL_RECORD.toLowerCase()}/home&cardBusinessCode=EMR`,
      );
    } else {
      Toast.info('请同意并勾选就诊须知', 1);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        <h2 className={styles.title}>请仔细阅读以下内容：</h2>
        {/* <h3>电子病历查阅须知</h3> */}
        <p className={styles.FocusOn}>
          1、线上查阅电子病历需要进行短信验证。如您将短信验证码告知他人，将视为您授权该用户查看您的就诊信息（包括线上门诊病历信息、线下门诊病历信息等）。
        </p>
        <p>2、电子病历为患者本人隐私，不可随意复制、转发、截图、录屏等，否则可能会承担法律风险。</p>
        {/* <p className={styles.endTitle}>四川大学华西医院</p> */}
      </div>
      <div className={styles.bottom}>
        <div className={styles.check}>
          <Checkbox onChange={onChangeCheck} checked={checked}>
            已阅读并同意《电子病历查阅须知》
          </Checkbox>
        </div>
        <Button
          type="primary"
          className={styles.button}
          onClick={onSure}
          style={{ color: '#ffffff', background: checked ? '#3AD3C1' : '#C6C9D6' }}
        >
          确认
        </Button>
      </div>
    </div>
  );
};

export default Notice;
