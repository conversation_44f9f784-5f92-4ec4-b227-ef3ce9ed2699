.container {
  min-height: 100vh;
  padding: 20px;
  color: #333;
  background: #fff;
  padding-bottom: 300px;

  .content {
    .endTitle {
      text-align: right;
    }

    padding: 20px 12px;

    .title {
      font-size: 36px;
      font-weight: 600;
      color: #03081a;
      line-height: 50px;
    }

    .FocusOn {
      font-size: 28px;
      line-height: 44px;
    }

    p {
      font-size: 28px;
      color: #03081a;
      line-height: 44px;
    }
  }

  .check {
    padding: 20px 0 30px 0;

    :global {
      .am-checkbox-wrapper {
        display: flex;
        align-items: center;
        font-weight: bold;
        font-size: 28px;
      }

      .ant-checkbox-checked .ant-checkbox-inner {
        background-color: #3ad3c1;
      }

      .ant-checkbox-checked::after {
        border: 1px solid #3ad3c1;
      }

      .am-checkbox-checked {
        .am-checkbox-inner {
          border-color: #3ad3c1 !important;
          background-color: #3ad3c1;
        }
      }

      .am-checkbox-inner:after {
        width: 12px;
        border-width: 0 4px 4px 0;
      }

      .am-checkbox-inner {
        border: 3px solid rgb(160, 163, 177);
      }

      .am-checkbox {
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .button {
    border-radius: 100px;
  }

  :global {
    .am-button-primary::before {
      border: none !important;
    }
  }

  .bottom {
    width: 100%;
    position: fixed;
    padding: 20px;
    bottom: 0;
    left: 0;
    background-color: #fff;
    padding-bottom: 30px;
    box-shadow: 0px -4px 8px 0px rgba(152, 158, 180, 0.1);
  }
}
