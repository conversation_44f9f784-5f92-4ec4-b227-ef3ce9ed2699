import request from '@/utils/request';

const node = `${API_ONLINE}`;
/**
 * 查询电子病例列表
 * @param data
 */
export const medicalList = async (data: object) =>
  request(`${node}/netInquiry/medical/history/find/list`, {
    method: 'POST',
    data,
  });
/**
 * 查询病例详情
 */
export const medicalDetail = async (data: object) =>
  request(`${node}/netInquiry/medical/history/find/detail`, {
    method: 'POST',
    data,
  });

/**
 * 2022-07-29新增电子病历列表
 * 线上和线下
 */

export const newMedicalList = async (data: object) =>
  request(`${node}/netInquiry/medical/history/querPatientMedicalRecordList`, {
    method: 'POST',
    data,
  });

/**
 * 2022-07-29新增电子病历详情
 */
export const newMedicalDetail = async (data: object) =>
  request(`${node}/netInquiry/medical/history/querPatientMedicalRecord`, {
    method: 'POST',
    data,
  });
