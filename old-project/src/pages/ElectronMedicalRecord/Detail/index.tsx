import React, { FC, useEffect, useState, useRef } from 'react';
import { connect, history, Dispatch, useLocation } from 'umi';
import _ from 'lodash';
import dayjs from 'dayjs';
import { StorageEnum } from '@/utils/enum';
import { HxLocalStorage } from '@/utils/storage';
import { Item, TitleItem } from '../components';
import { IElectronMedicalRecordModelState } from '../data.d';
import Watermark from '../components/Watermark/index';
import styles from './index.less';

const nextGreen = require('@/assets/nextGreen.png');

interface IProps {
  electronMedicalRecord: IElectronMedicalRecordModelState;
  dispatch: Dispatch;
}

const Detail: FC<IProps> = (props) => {
  const { dispatch } = props;
  const [detailData, setDetailData] = useState<any>({});
  const containerRef = useRef<HTMLDivElement | null>(null);
  const {
    query: { medicalId, admissionId },
  }: any = useLocation();
  /* 用户就诊卡电话号码 */
  const [telNum, setTelNum] = useState<string>('');

  /** 判断卡是否需要实名 */
  const fetchData = () => {
    dispatch({
      type: 'electronMedicalRecord/medicalDetail',
      payload: {
        medicalId,
        admissionId,
        type: 2,
      },
      callback: (res) => {
        setDetailData(res);
      },
    });
  };

  /** 跳转详情 */
  const goPatientDetail = () => {
    const { patientInfo = {} } = detailData;
    history.push(`/electronmedicalrecord/patientinfo?patientInfo=${JSON.stringify(patientInfo)}`);
  };

  // /** 查看pdf */
  // const viewPdf = () => {
  //   const { patientInfo } = detailData;
  // };

  useEffect(() => {
    fetchData();
    const { accountNo = '' } = HxLocalStorage.get(StorageEnum.USER_INFO) || {};
    setTelNum(accountNo);
  }, []);

  const { patientInfo = {}, caImage = '', admissionInfo = {}, previousInfo = {} } = detailData;
  const { name = '', gender, age, pmiNo } = patientInfo;
  const {
    principle,
    irritability,
    previous,
    personal,
    lastHospital,
    lastDiagnosis,
    drug,
    examine = [],
    auxiliaryExaminations = '',
    dischargeAdvice = '',
    presentHistory = '',
  } = previousInfo;
  const {
    checkInfos = [],
    drugAdviceInfos = [],
    imgUrls = [],
    admissionDate,
    dept,
    desc,
    diagnose,
    patAdmDesc,
    isPatAdmDesc,
    docSuggestion,
  } = admissionInfo;
  const a = '';
  return (
    <div className={styles.container} ref={containerRef}>
      <div className={styles.header}>{a}</div>
      <div className={styles.name}>
        <div className={styles.one}>
          <div className={styles.patientName}>
            {name}
            <span>{gender}</span>
            <span>{age ? `${age}岁` : ''}</span>
          </div>
          <div className={styles.change} onClick={goPatientDetail}>
            <div className={styles.hint}>基础信息</div>
            <img src={nextGreen} className={styles.img} alt="" />
          </div>
        </div>
        <div className={styles.pmiNo}>登记号：{pmiNo}</div>
      </div>
      {/* <div className={styles.previousData}>
        <TitleItem label="既往资料" />
        <Item label="上次就诊医院" value={lastHospital} />
        <Item label="上次诊断结果" value={lastDiagnosis} />
        <Item label="正在使用的药品" value={drug} />
      </div> */}
      <div className={styles.medicalInformation}>
        <TitleItem label="本次就诊信息" />
        <Item label="就诊日期" value={admissionDate} />
        <Item label="就诊科室" value={dept} />
        <Item label={`本次就诊是否存在以下情况之一：【${isPatAdmDesc === 'yes' ? '是' : '否'}】`} value={patAdmDesc} />
        <Item label="诊断" value={diagnose} />
        <Item label="主诉" value={principle ? principle.split('|').join('、') : ''} />
        <Item label="现病史" value={presentHistory} />
        <Item label="既往史" value={previous} />
        <Item label="家族史" value={personal} />
        <Item label="药物过敏史" value={irritability} />
        <Item label="辅助检查" value={auxiliaryExaminations} />
        {/* <div className={styles.imgs}>
          <div className={styles.label}>辅助检查</div>
          <div className={styles.list}>
            {examine.length > 0 &&
              examine.map((item: any) => {
                return <img src={item} className={styles.img} alt="" />;
              })}
          </div>
        </div> */}
      </div>
      <div className={styles.handelSuggestion}>
        <TitleItem label="处理意见" />
        <div className={styles.list}>
          <div className={styles.content}>
            <div className={styles.label}>药品医嘱</div>
            <div className={styles.value}>
              {drugAdviceInfos.length > 0 ? (
                drugAdviceInfos.map((item: any) => {
                  const { drugInfos = [], medicineType, cmUsageDesc } = item || {};
                  if (medicineType === 1) {
                    return drugInfos.length > 0 && <div className={styles.item}>{cmUsageDesc}</div>;
                  }
                  return (
                    drugInfos.length > 0 &&
                    drugInfos.map((v: any) => {
                      const { dosage, duration, name, unit, usage } = v;
                      return (
                        <div className={styles.item} key={_.uniqueId()}>
                          {name}：{usage}， 每次{dosage}
                          {unit}，服用时长：{duration}天
                        </div>
                      );
                    })
                  );
                })
              ) : (
                <div className={styles.empty}>暂无内容</div>
              )}
            </div>
          </div>
        </div>
        <div className={styles.list}>
          <div className={styles.content}>
            <div className={styles.label}>检验检查</div>
            <div className={styles.valueList}>
              {checkInfos.length > 0 ? (
                checkInfos.map((item: any) => {
                  const { items = [] } = item;
                  return (
                    items.length > 0 &&
                    items.map((v: any) => {
                      return (
                        <div className={styles.item} key={_.uniqueId()}>
                          {v}、
                        </div>
                      );
                    })
                  );
                })
              ) : (
                <div className={styles.empty}>暂无内容</div>
              )}
            </div>
          </div>
        </div>
        <div className={styles.imglist}>
          {imgUrls.length > 0 &&
            imgUrls.map((item: any) => {
              return <img src={item} className={styles.img} alt="" />;
            })}
        </div>
        <div className={styles.list}>
          <div className={styles.content}>
            <div className={styles.label}>其他</div>
            <div className={styles.value}>{docSuggestion || <div className={styles.empty}>暂无内容</div>}</div>
          </div>
        </div>
        <Item label="离院建议" value={dischargeAdvice} />
      </div>
      {caImage && <img src={`data:image/jpeg;base64,${caImage}`} className={styles.caImage} alt="" />}
      {/* <div className={styles.viewPdf} onClick={viewPdf}>查看PDF</div> */}
      <Watermark
        text={[telNum, dayjs(new Date()).format('YYYY.MM.DD HH:mm'), '截屏无效']}
        width={250}
        height={220}
        pageHeight={containerRef?.current?.clientHeight}
        fontSize="26px"
      />
    </div>
  );
};

export default connect(({ electronMedicalRecord }: { electronMedicalRecord: IElectronMedicalRecordModelState }) => ({
  electronMedicalRecord,
}))(Detail);
