.container {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  padding-bottom: 180px;
  background-color: #f5f6f7;
  .header {
    width: 100%;
    height: 120px;
    background-color: #3ad3c1;
  }
  .name {
    width: 710px;
    margin: -90px auto 0;
    padding: 30px;
    background-color: #fff;
    border-radius: 10px;
    .one {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      .patientName {
        color: #333;
        font-weight: bold;
        font-size: 34px;
        span {
          margin-left: 20px;
        }
      }
      .change {
        position: relative;
        z-index: 999;
        display: flex;
        flex-direction: row;
        align-items: center;
        color: #3ad3c1;
        font-size: 28px;
        .img {
          width: 15px;
          height: 26px;
          margin-left: 14px;
        }
      }
    }
    .pmiNo {
      margin-top: 10px;
      color: #989eb4;
      font-size: 30px;
      // margin-left: 10px;
    }
  }
  .previousData {
    margin-top: 20px;
    .imgs {
      width: 100%;
      padding: 20px 30px;
      background-color: #fff;
      .label {
        color: #333;
        font-weight: bold;
        font-size: 30px;
      }
      .list {
        display: flex;
        flex-wrap: wrap;
        margin-top: 20px;
        .img {
          width: 140px;
          height: 140px;
          margin-right: 20px;
          margin-bottom: 20px;
        }
      }
    }
  }
  .handelSuggestion,
  .medicalInformation {
    width: 100%;
    margin-top: 20px;
    .list {
      width: 100%;
      padding: 20px 30px;
      background-color: #fff;
      .label {
        color: #333;
        font-weight: bold;
        font-size: 30px;
      }
      .value {
        margin-top: 20px;
        color: #03081a;
        font-size: 28px;
        .item {
          margin-bottom: 10px;
        }
        .empty {
          color: #bbb;
          font-size: 28px;
        }
      }
      .valueList {
        display: flex;
        flex-wrap: wrap;
        margin-top: 20px;
        color: #03081a;
        font-size: 28px;
        .empty {
          color: #bbb;
          font-size: 28px;
        }
        .item {
          margin-right: 10px;
          margin-bottom: 10px;
        }
      }
    }
    .imglist {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      padding: 20px 30px;
      background-color: #fff;
      .img {
        width: 140px;
        height: 140px;
        margin-right: 20px;
        margin-bottom: 20px;
      }
    }
    .imgs {
      width: 100%;
      padding: 20px 30px;
      background-color: #fff;
      .label {
        color: #333;
        font-weight: bold;
        font-size: 30px;
      }
      .list {
        display: flex;
        flex-wrap: wrap;
        margin-top: 20px;
        .img {
          width: 140px;
          height: 140px;
          margin-right: 20px;
          margin-bottom: 20px;
        }
      }
    }
  }
  .caImage {
    width: 100%;
    height: 150px;
    margin-top: 20px;
    background-color: #fff;
  }
  .viewPdf {
    position: fixed;
    bottom: 30px;
    left: 30px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 690px;
    height: 90px;
    color: #fff;
    font-size: 32px;
    background: #32b9aa;
    border-radius: 16px;
  }
}
