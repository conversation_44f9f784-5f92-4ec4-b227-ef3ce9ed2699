import Mock from 'mockjs';
import { commonSuccessResponse } from '../../../mock/util';

const { Random } = Mock;

// 电子报告列表
const querylist = (req: any, res: any) => {
  const response = Mock.mock({
    ...commonSuccessResponse,
    'data|5-12': [
      {
        'reclocDr|10000000000-9999999999999999': 10000040000,
        reportDate: () => Random.datetime(),
        reportName: () => Random.ctitle(5, 10),
        'reportNo|10000000000-9999999999999999': 10000030000,
        'reportStatus|1': ['E', 'R'],
        reportTime: () => Random.datetime(),
        'reportType|1': ['1', '2'],
      },
    ],
  });
  // 模拟请求延迟
  setTimeout(() => {
    return res.status(200).send(response);
  }, 500);
};

// 放射检查列表
const radioscopyList = (req: any, res: any) => {
  const response = Mock.mock({
    ...commonSuccessResponse,
    data: {
      asc: true,
      'content|5-9': [
        {
          examName: () => Random.ctitle(5, 10),
          'examNo|10000000000-9999999999999999': 10000030000,
          'flag|+1': [0, 1],
          'hisCode|10000000000-9999999999999999': 10000030000,
          'id|10000000000-9999999999999999': 10000030000,
          'isSelectViewImage|+1': [0, 1],
          isSelectViewImageNote: '请选择是否查看电子胶片',
          itemDate: () => Random.datetime(),
          'itemId|10000000000-9999999999999999': 10000030000,
          note: () => Random.ctitle(5, 20),
          'patientId|10000000000-9999999999999999': 10000030000,
          status: 4,
          'type|1': ['1', '2', '3'],
        },
      ],
      orderBy: 'string',
      pageNum: 0,
      pageSize: 0,
      total: 0,
      totalPages: 0,
    },
  });
  // 模拟请求延迟
  setTimeout(() => {
    return res.status(200).send(response);
  }, 500);
};

// 电子报告详情
const querydetail = (req: any, res: any) => {
  const response = Mock.mock({
    ...commonSuccessResponse,
    data: {
      checkDesc: () => Random.ctitle(30, 50),
      checkResult: () => Random.ctitle(50, 100),
      note: () => Random.ctitle(10, 30),
      'gender|1': [1, 2],
      'patientName|1': ['杜甫', '李白', '李贺', '白居易'],
      reportDate: Random.date('yyyy-MM-dd HH:mm:ss'),
      reportName: () => Random.ctitle(5, 7),
      'resultItems|3-7': [
        {
          itemName: () => Random.ctitle(7, 12),
          'referValue|1': ['35~70', '40~95'],
          'resultType|1': [0, 1, 2],
          'resultValue|35-95': 35,
          unit: 'umol/L',
        },
      ],
    },
  });
  // 模拟请求延迟
  setTimeout(() => {
    return res.status(200).send(response);
  }, 500);
};

// 放射检查详情
const radiologyDetail = (req: any, res: any) => {
  const response = Mock.mock({
    ...commonSuccessResponse,
    data: {
      'channel|1': [0, 1],
      checkPoint: () => Random.ctitle(2, 5),
      examName: () => Random.ctitle(5, 10),
      'examNo|10000000000-9999999999999999': 10000030000,
      'hisCode|10000000000-9999999999999999': 10000030000,
      hisName: () => Random.ctitle(2, 5),
      'id|10000000000-9999999999999999': 10000030000,
      imageDate: () => Random.datetime(),
      imageDiagnosis: () => Random.ctitle(260, 300),
      imageReportUrl: [
        Random.image('200x100', '#ffcc33', '#FFF', 'png', '!'),
        Random.image('200x100', '#ffcc33', '#FFF', 'svg', '!'),
        Random.image('200x100', '#ffcc33', '#FFF', 'png', '!'),
        Random.image('200x100', '#ffcc33', '#FFF', 'svg', '!'),
      ],
      imgFinding: () => Random.ctitle(260, 300),
      isImageExpired: 0,
      isSelectViewImage: 0,
      isViewImage: 0,
      isViewOriginalImage: 0,
      note: () => Random.ctitle(30, 50),
      originalImageReportUrl: ['string'],
      'patientId|10000000000-9999999999999999': 10000030000,
      patientName: '王自健',
      pdfReportUrl: '',
      'type|1': ['1', '2', '3'],
    },
  });
  // 模拟请求延迟
  setTimeout(() => {
    return res.status(200).send(response);
  }, 500);
};

export default {
  'POST /cloud/hosplatcustomer/elecreport/querylist': querylist,
  'POST /cloud/hosplatcustomer/elecreport/querydetails': querydetail,
  'POST /cloud/imagecloud/examination/listexamination': radioscopyList,
  'GET /cloud/imagecloud/examination/querycheckdetail': radiologyDetail,
};
