import React, { FC, useCallback, useEffect, useState } from 'react';
import { connect, history, Dispatch, Loading } from 'umi';
import { Modal, Toast, InputItem, Icon, Calendar } from 'antd-mobile';
import { HxSessionStorage } from '@/utils/storage';
import classnames from 'classnames';
import { ModuleEnum } from '@/utils/enum';
import { HxEmpty, HxIndicator } from '@/components';
import { DatePicker } from 'antd-mobile-v5';
import dayjs from 'dayjs';
import { HxParameter } from '@/utils/parameter';
import { ListItem } from '../components';
import { IElectronMedicalRecordModelState, IRecordItem } from '../data.d';
import styles from './index.less';

const no_data_icon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/medical-record-order/no_data_icon.png';
const calendarIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/calendar.png';

interface IProps {
  electronMedicalRecord: IElectronMedicalRecordModelState;
  dispatch: Dispatch;
  loading?: boolean;
}

const Home: FC<IProps> = (props) => {
  const {
    dispatch,
    loading = true,
    electronMedicalRecord: { chooseDate = new Date() },
  } = props;
  const [cardInfoRes, setCardInfoRes] = useState<{ [key: string]: string }>({});
  const [realNametips, setRealNametips] = useState<boolean>(false);
  const [buttonFlag, setButtonFlag] = useState<boolean>(true);
  const [recordList, setRecordList] = useState<{ [key: string]: string }[]>([]);
  const [searchWord, setSearchWord] = useState<string>(dayjs(new Date()).year().toString());
  const [visible, setVisible] = useState(false);

  /** 获取就诊卡详情 */
  const getCardDetail = (cardId: string) => {
    const { channelCode } = HxParameter;
    dispatch({
      type: 'patientCard/cardInfo',
      payload: {
        cardId,
        channelCode,
      },
      callback: (cardInfoRes: any) => {
        setCardInfoRes(cardInfoRes);
        setRealNametips(true);
      },
    });
  };

  /** 获取电子病例列表数据 */
  const getRecordList = (val: Date = new Date()) => {
    const cardData = HxSessionStorage.get('patientCardData');
    const { pmi } = cardData;
    dispatch({
      type: 'electronMedicalRecord/medicalList',
      payload: {
        papmi: pmi,
        startDate: dayjs(val).startOf('year').format('YYYY-MM-DD'),
        endDate: dayjs(val).endOf('year').format('YYYY-MM-DD'),
      },
      callback: (res: any) => {
        setRecordList(res);
      },
    });
  };

  /** 判断卡是否需要实名 */
  const fetchData = () => {
    const cardData = HxSessionStorage.get('patientCardData');
    const { cardId, realName } = cardData;
    const { organCode } = HxParameter;
    if (!realName && (organCode === 'HID0101' || organCode === 'HYT')) {
      getCardDetail(cardId);
    } else {
      getRecordList(chooseDate || new Date());
    }
  };

  /** 跳转注册全国统一健康卡页面 */
  const turnRegister = () => {
    const { cardId } = cardInfoRes;
    if (!buttonFlag) {
      Toast.fail('请勿重复点击', 1);
      return false;
    }
    setButtonFlag(false);
    dispatch({
      type: 'patientCard/upgradeCard',
      payload: { cardId },
      callback: (res: any) => {
        const { code, errCode, msg } = res;
        if (code === '1') {
          setRealNametips(false);
          getRecordList();
        } else if (code === '0') {
          if (errCode === '2130031') {
            dispatch({
              type: 'patientCard/getCardTypeList',
              payload: {},
              callback: (data: any = []) => {
                const cardTypeList = data;
                let obj = {};
                cardTypeList.forEach((item: any) => {
                  const { cardMethodCode } = item;
                  if (cardMethodCode === 'IdRegister') {
                    obj = item;
                  }
                });
                const {
                  cardId,
                  cardNo = '',
                  credNo = '',
                  patientName = '',
                  credTypeName = '',
                  nationName = '',
                  nationCode = '',
                  tel = '',
                  occupationName = '',
                  occupationCode = '',
                  provinceCode = '',
                  provinceName = '',
                  cityCode = '',
                  cityName = '',
                  cityAreaCode = '',
                  cityAreaName = '',
                  detailAddress = '',
                  credTypeCode = '',
                } = cardInfoRes;
                // 升级卡就是重新注册电子健康卡
                let registerCardInfo;
                if (credTypeName === '户口薄') {
                  registerCardInfo = {
                    cardId,
                    cardNo,
                    credNo,
                    patientName,
                    credTypeName: '身份证',
                    tel,
                    provinceCode,
                    provinceName,
                    cityCode,
                    cityName,
                    cityAreaCode,
                    cityAreaName,
                    detailAddress,
                    credTypeCode: '01',
                    credType: [credTypeCode],
                    address: [provinceCode, cityCode, cityAreaCode],
                  };
                } else {
                  registerCardInfo = {
                    cardId,
                    cardNo,
                    credNo,
                    patientName,
                    credTypeName,
                    tel,
                    provinceCode,
                    provinceName,
                    cityCode,
                    cityName,
                    cityAreaCode,
                    cityAreaName,
                    detailAddress,
                    credTypeCode,
                    credType: [credTypeCode],
                    address: [provinceCode, cityCode, cityAreaCode],
                  };
                }
                const { notice }: any = obj;
                dispatch({
                  type: 'patientCard/saveCardInfo',
                  payload: {
                    occupationCode,
                    occupationName,
                    nationCode,
                    nationName,
                  },
                });
                const credNoDisabled = true;
                const credDisabled = true;
                HxSessionStorage.set('registerCardInfo', registerCardInfo);
                HxSessionStorage.set('notice', notice || '暂无');
                const redirectUrl = `/${ModuleEnum.MODULE_ELECTRON_MEDICAL_RECORD}/home`.toLowerCase();
                history.push(
                  `/${ModuleEnum.MODULE_PATIENT_CARD}/register?replaceCardId=${cardId}&redirect=${redirectUrl}&credNoDisabled=${credNoDisabled}&credDisabled=${credDisabled}`,
                );
              },
            });
          } else {
            Modal.alert('提示', msg, [
              {
                text: '确定',
                onPress: () => {
                  setButtonFlag(true);
                },
              },
            ]);
          }
        }
      },
    });
  };

  /** 跳转详情 */
  const goDetail = (item: IRecordItem) => {
    const { medicalId = '', hisAdmId = '', type } = item;
    if (type === 2) {
      /* 线下 */
      history.push(`/electronmedicalrecord/offlineDetail?admissionId=${hisAdmId}`);
      return;
    }
    /* 线上 */
    history.push(`/electronmedicalrecord/detail?medicalId=${medicalId}`);
  };

  /**
   * 自定义渲染内容
   * @param {*} useCallback
   * @return {*}
   */

  const labelRenderer = useCallback((type: string, data: number) => {
    switch (type) {
      case 'year':
        return `${data}年`;
      default:
        return data;
    }
  }, []);

  /**
   * 确认选择时间
   * @return {*}
   */
  const handleDate = (val: Date) => {
    const year = val && dayjs(val).year();
    setSearchWord(`${year}`);
    getRecordList(val);
    /* 缓存当前选择的时间 */
    dispatch({
      type: 'electronMedicalRecord/updateState',
      payload: {
        chooseDate: val,
      },
    });
  };

  useEffect(() => {
    fetchData();
    /* 返回列表页面缓存的时间筛选数据 */
    if (chooseDate) {
      const year = chooseDate && dayjs(chooseDate).year();
      setSearchWord(`${year}`);
    }
  }, []);

  return (
    <div className={classnames(styles.container, styles.electronContainer)}>
      <div className={styles.list}>
        {/* 日期筛选 */}
        <header className={styles.searchHeader}>
          <div className={styles.inputWrap}>
            <InputItem placeholder="请选择日期区间" disabled value={`${searchWord}${searchWord && '年'}`} />
            <div
              className={styles.searchButton}
              onClick={() => {
                setVisible(true);
              }}
            >
              <img src={calendarIcon} alt="calendar" />
              筛选
            </div>
          </div>
        </header>
        {loading ? (
          <HxIndicator />
        ) : recordList?.length > 0 ? (
          <div className={styles.listWrap}>
            {recordList.map((item: { [key: string]: any }) => {
              return <ListItem item={item} onClick={goDetail} key={item.medicalId} />;
            })}
          </div>
        ) : (
          <div className={styles.emptyWrap}>
            <HxEmpty emptyMsg="暂无电子病历" isNewImg emptyImg={no_data_icon} canRefresh={false} />
          </div>
        )}
      </div>
      <Modal
        visible={realNametips}
        title="提示"
        transparent
        maskClosable={false}
        footer={[
          {
            text: '取消',
            onPress: () => {
              history.go(-1);
            },
          },
          {
            text: '确认',
            onPress: buttonFlag
              ? () => {
                  turnRegister();
                }
              : () => {
                  Toast.fail('请勿重复点击', 1);
                },
          },
        ]}
      >
        <div className={styles.content} style={{ textAlign: 'left' }}>
          <div>
            <div className="text" style={{ textAlign: 'center' }}>
              您的就诊卡未做实名认证，根据国家卫建委要求需升级为全国统一实名电子健康卡，升级后将本卡切换为电子健康卡！
            </div>
          </div>
        </div>
      </Modal>

      {/* 选择日期picker */}
      <DatePicker
        title="日期筛选"
        getContainer={document.body}
        max={new Date()}
        defaultValue={chooseDate || new Date()}
        visible={visible}
        onClose={() => {
          setVisible(false);
        }}
        className="electronTimePicker"
        precision="year"
        renderLabel={labelRenderer}
        onConfirm={(val) => {
          handleDate(val);
        }}
      />
    </div>
  );
};

export default connect(
  ({
    electronMedicalRecord,
    loading,
  }: {
    electronMedicalRecord: IElectronMedicalRecordModelState;
    loading: Loading;
  }) => ({
    electronMedicalRecord,
    loading: loading.effects['electronMedicalRecord/medicalList'],
  }),
)(Home);
