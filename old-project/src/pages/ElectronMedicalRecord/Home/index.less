.container {
  width: 100vw;
  height: 100vh;
  background: #f5f6f7;

  .list {
    height: 100vh;
    position: relative;
    // padding-top: 24px;
    box-sizing: border-box;
    overflow-y: scroll;
    padding-bottom: 24px;

    .searchHeader {
      width: 100%;
      height: 120px;
      display: flex;
      align-items: center;
      padding: 24px 24px 0;
      box-sizing: border-box;

      .inputWrap {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 24px;
        background: #ffffff;
        height: 96px;
        border-radius: 16px;

        .clear {
          position: absolute;
          right: 0;
          font-size: 40px;
          top: 50%;
          margin-top: -20px;
        }

        :global {
          .am-input-item {
            flex: 1;
            border-bottom: 0;
            box-sizing: border-box;
            padding-right: 40px;

            .am-list-line {
              border-bottom: 0 !important;

              .am-input-control {
                input {
                  font-size: 32px;
                  font-weight: 400;
                  color: #03081a;

                  &::placeholder {
                    color: #989eb4 !important;
                  }
                }
              }

              &::after {
                height: 0;
              }
            }

            .am-input-label {
              width: auto !important;
              font-size: 0;
              padding: 0;

              img {
                width: 40px;
                height: 40px;
              }
            }
          }
        }

        .searchButton {
          display: flex;
          flex-shrink: 0;
          align-items: center;
          flex-shrink: 0;
          width: 140px;
          height: 64px;
          background: #3ad3c1;
          border-radius: 12px;
          color: #fff;
          font-size: 32px;
          justify-content: center;

          img {
            width: 40px;
            height: 40px;
            object-fit: cover;
            margin-right: 2px;
          }
        }
      }
    }

    .listWrap {
      height: calc(100vh - 120px);
      display: flex;
      flex-direction: column;
      align-items: center;
      // height: -webkit-fill-available;
      width: 100%;
      padding: 0 24px 24px;
    }

    .empty {
      margin-top: 50%;
    }
  }

  .emptyWrap {
    width: 100%;
    height: 60vh;
  }
}

:global {
  .electronTimePicker {
    --item-height: 110px;
    --item-font-size: 40px;

    .adm-picker-view-column-item {
      // font-size: 36px;
      color: #989eb4;
      font-size: 36px;
      transition: 0.1;
    }

    .adm-picker-view-column-item[aria-label='active'] {
      color: #03081a;
      font-size: 40px;
    }

    .adm-picker-header {
      background-color: #f9fafc;
      height: 96px;

      .adm-picker-header-button {
        font-size: 32px;
      }

      .adm-picker-header-title {
        font-size: 32px;
        font-weight: 400;
        color: #03081a;
      }
    }
  }
}
