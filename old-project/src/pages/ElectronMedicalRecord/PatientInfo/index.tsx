import React, { FC, useEffect, useState, useRef } from 'react';
import { connect, Dispatch, useLocation } from 'umi';
import dayjs from 'dayjs';
import { HxLocalStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import { DetailItem } from '../components';
import { IElectronMedicalRecordModelState } from '../data.d';
import styles from './index.less';
import Watermark from '../components/Watermark/index';

interface IProps {
  electronMedicalRecord: IElectronMedicalRecordModelState;
  dispatch: Dispatch;
}

const PatientInfo: FC<IProps> = () => {
  const [cardInfo, setCardInfo] = useState<{ [key: string]: any }>({});
  const containerRef = useRef<HTMLDivElement | null>(null);
  /* 用户就诊卡电话号码 */
  const [telNum, setTelNum] = useState<string>('');

  const {
    query: { patientInfo = '{}' },
  }: any = useLocation();
  /** 获取卡信息 */
  const fetchData = () => {
    const data = JSON.parse(patientInfo);
    setCardInfo(data);
  };

  useEffect(() => {
    fetchData();
    const { accountNo = '' } = HxLocalStorage.get(StorageEnum.USER_INFO) || {};
    setTelNum(accountNo);
  }, []);

  const { name, gender, pmiNo, age, marriage, nation, profession, detailAddress, telPhone, address } = cardInfo;
  return (
    <div className={styles.container} ref={containerRef}>
      <DetailItem label="姓名" value={name} />
      <DetailItem label="登记号" value={pmiNo} />
      <DetailItem label="年龄" value={age ? `${age}岁` : ''} />
      <DetailItem label="性别" value={gender} />
      <DetailItem label="婚姻状况" value={marriage} />
      <DetailItem label="民族" value={nation} />
      <DetailItem label="职业" value={profession} />
      <DetailItem label="联系地址" value={address} />
      <DetailItem label="详细地址" value={detailAddress} />
      <DetailItem label="联系方式" value={telPhone} />
      <Watermark
        text={[telNum, dayjs(new Date()).format('YYYY.MM.DD HH:mm'), '截屏无效']}
        width={250}
        height={220}
        pageHeight={containerRef?.current?.clientHeight}
      />
    </div>
  );
};

export default connect(({ electronMedicalRecord }: { electronMedicalRecord: IElectronMedicalRecordModelState }) => ({
  electronMedicalRecord,
}))(PatientInfo);
