import React, { useEffect, useRef, useState } from 'react';
import { Dispatch, connect, history } from 'umi';
import { HxSessionStorage, HxLocalStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import dayjs from 'dayjs';
import styles from './index.less';
import { IElectronMedicalRecordModelState } from '../data.d';
import { OfflineItem } from '../components';
import Watermark from '../components/Watermark/index';

const logo = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/electronMedicalRecord/hxlogo.png';
const nextGreen = require('@/assets/nextGreen.png');

interface IProps {
  dispatch: Dispatch;
  electronMedicalRecord: IElectronMedicalRecordModelState;
  location: {
    query: {
      admissionId: string;
    };
  };
}
const OfflineDetail: React.FC<IProps> = (props) => {
  const {
    dispatch,
    location: {
      query: { admissionId = '' },
    },
  } = props;
  const [detailData, setDetailData] = useState<any>({});
  const containerRef = useRef<HTMLDivElement | null>(null);
  /* 用户就诊卡电话号码 */
  const [telNum, setTelNum] = useState<string>('');

  /** 判断卡是否需要实名 */
  const fetchData = () => {
    const cardData = HxSessionStorage.get('patientCardData');
    const { pmi } = cardData;
    dispatch({
      type: 'electronMedicalRecord/queryMedicalDetail',
      payload: {
        pmi,
        admId: admissionId,
      },
      callback: (res) => {
        setDetailData(res);
      },
    });
  };

  /** 跳转详情 */
  const goPatientDetail = () => {
    const { patientInfo = {} } = detailData;
    history.push(`/electronmedicalrecord/patientinfo?patientInfo=${JSON.stringify(patientInfo)}`);
  };
  useEffect(() => {
    fetchData();
    const { accountNo = '' } = HxLocalStorage.get(StorageEnum.USER_INFO) || {};
    setTelNum(accountNo);
  }, []);

  const {
    patName = '',
    pmiNo = '',
    sex,
    age,
    admDate,
    admDoc,
    admLoc,
    mainSuit,
    admType,
    allergyHistory,
    familyHistory,
    diseaseHistory,
    physiqueCk,
    assistCk,
    diagnose,
    handlingSugg,
    remark,
    advise,
  } = detailData;
  // const { name = '', gender, age, pmiNo } = patientInfo;
  // const { principle, irritability, previous, personal, lastHospital, lastDiagnosis, drug, examine = [] } = previousInfo;
  // const { checkInfos = [], drugAdviceInfos = [], imgUrls = [], admissionDate, dept, desc, diagnose } = admissionInfo;
  return (
    <div className={styles.container} ref={containerRef}>
      <header>
        <img src={logo} alt="" />
        <h2>四川大学华西医院门诊病历</h2>
      </header>
      <div className={styles.name}>
        <div className={styles.one}>
          <div className={styles.patientName}>
            {patName}
            <span>{sex}</span>
            <span>{age ? `${age}` : ''}</span>
          </div>
          <div className={styles.change} onClick={goPatientDetail}>
            <div className={styles.hint}>基础信息</div>
            <img src={nextGreen} className={styles.img} alt="" />
          </div>
        </div>
        <div className={styles.pmiNo}>登记号：{pmiNo}</div>
      </div>
      <div className={styles.offlineDetail}>
        <OfflineItem label="就诊日期：" value={admDate} />
        <OfflineItem label="就诊科室：" value={admLoc} />
        <OfflineItem label="就诊医生：" value={admDoc} />
        <OfflineItem label={admType} />
        <OfflineItem label="药物过敏史：" value={allergyHistory} newline />
        <OfflineItem label="主诉：" value={mainSuit ? mainSuit.split('|').join('、') : ''} newline />
        <OfflineItem label="病史：" value={diseaseHistory} newline />
        <OfflineItem label="家族史：" value={familyHistory} newline />
        <OfflineItem label="体格检查：" value={physiqueCk} newline />
        <OfflineItem label="辅助检查：" value={assistCk || '暂无'} newline />
        <OfflineItem label="诊断：" value={diagnose} newline />
        <OfflineItem label="处理意见：" value={handlingSugg} newline />
        <OfflineItem label="备注：" value={remark} newline />
        <OfflineItem label="离院建议：" value={advise} newline />
      </div>
      <Watermark
        text={[telNum, dayjs(new Date()).format('YYYY.MM.DD HH:mm'), '截屏无效']}
        width={250}
        height={220}
        pageHeight={containerRef?.current?.clientHeight}
        fontSize="26px"
      />
    </div>
  );
};

export default connect(({ electronMedicalRecord }: { electronMedicalRecord: IElectronMedicalRecordModelState }) => ({
  electronMedicalRecord,
}))(OfflineDetail);
