import React, { useState, useEffect, FC } from 'react';
import { connect, Dispatch, history, Loading } from 'umi';
import styles from './index.less';
import Icon1 from '../../../assets/team-desc-icon1.png';
import Icon2 from '../../../assets/team-desc-icon2.png';

interface PageProps {}
const App: FC<PageProps> = (props: any) => {
  const {
    piTeam: { piTeamInfo },
  } = props;
  useEffect(() => {}, []);
  return (
    <div className={styles.main}>
      <div className={styles.headerBox}>
        <div className={styles.teamTitle}>{piTeamInfo.teamName}</div>
        <div className={styles.teamHospital}>
          <span className={styles.teamHospitalLevel}>三甲</span>
          <span className={styles.teamHospitalName}>{piTeamInfo.organName}</span>
        </div>
      </div>
      <div className={styles.center}>
        <div className={styles.centerDescBox}>
          <div className={styles.centerDescBoxHeader}>
            <img className={styles.centerDescBoxHeaderIcon} src={Icon1} alt="" />
            <span className={styles.centerDescBoxHeaderName}>擅长疾病</span>
          </div>
          <div className={styles.centerDescBoxDesc}>{piTeamInfo.profession}</div>
        </div>
        <div className={styles.centerDescBox}>
          <div className={styles.centerDescBoxHeader}>
            <img className={styles.centerDescBoxHeaderIcon} src={Icon2} alt="" />
            <span className={styles.centerDescBoxHeaderName}>团队简介</span>
          </div>
          <div className={styles.centerDescBoxDesc}>{piTeamInfo.introduction}</div>
        </div>
      </div>
    </div>
  );
};

export default connect(({ loading, piTeam }: { loading: Loading; piTeam: any }) => ({
  loading,
  piTeam,
}))(App);
