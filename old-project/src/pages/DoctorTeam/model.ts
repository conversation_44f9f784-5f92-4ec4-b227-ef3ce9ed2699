/*
 * @Author: <PERSON><PERSON>
 * @Date: 2021-06-01 15:41:50
 * @LastEditTime: 2022-04-11 14:37:02
 * @LastEditors: Please set LastEditors
 * @Description:
 * @FilePath: /hyt-person/src/pages/Department/model.ts
 */
import { Reducer } from 'redux';
import { Effect } from 'umi';
import { createAction } from '@/utils/common';
import { sensorsRequest } from '@/utils/sensors';
import * as API from './service';

export interface IMessageModelState {}

export interface IDeptModelState {
  piTeamInfo: any;
}

export interface IDepartmentModel {
  namespace: 'piTeam';
  state: IDeptModelState;
  effects: {
    getPiTeamInfo: Effect;
  };
  reducers: {
    updateState: Reducer<IDeptModelState>;
  };
}

const DemoModel: IDepartmentModel = {
  namespace: 'piTeam',
  state: {
    piTeamInfo: null,
  },

  effects: {
    *getPiTeamInfo({ payload, callback }, { put }) {
      const res = yield API.queryPI({ ...payload });
      if (res) {
        // 埋点
        sensorsRequest('PiHome_Num', { teamName: res.teamName, teamId: payload.teamId });
        yield put(createAction('updateState')({ piTeamInfo: res }));
        callback && callback(res);
      }
    },
  },
  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};

export default DemoModel;
