.main {
  background: #f5f6fa;

  :global {
    .adm-steps-vertical {
      padding: 0 !important;
    }
  }
  .headerBox {
    position: relative;
    width: 100%;
    height: 274px;
    background-image: url('../../../assets/team-info-bg.png');
    background-repeat: no-repeat;
    background-size: 100% 274px;
    .he<PERSON><PERSON><PERSON>n {
      position: absolute;
      top: 40px;
      width: 100%;
      padding: 0 12px;
      background: rgb(245, 246, 250);
      .headerBoxVisit {
        width: 100%;
        height: 58px;
        color: #fff;
        font-weight: 400;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        line-height: 58px;
        background: #6be6dd;
        border-radius: 16px 16px 0 0;
        span {
          margin-left: 40px;
        }
      }
      .teamTitle {
        height: 50px;
        padding: 0 24px;
        color: #03081a;
        font-weight: 600;
        font-size: 36px;
        font-family: PingFangSC-Semibold, PingFang SC;
        line-height: 50px;
      }
      .teamHospital {
        display: flex;
        align-items: center;
        margin-top: 16px;
        padding: 0 24px;
        .teamHospitalLevel {
          width: 60px;
          height: 30px;
          padding: 0 8px;
          color: #fff;
          font-weight: 400;
          font-size: 18px;
          font-family: PingFangSC-Regular, PingFang SC;
          line-height: 30px;
          background: #568df2;
          border-radius: 8px;
        }
        .teamHospitalName {
          height: 40px;
          margin-left: 24px;
          color: #03081a;
          font-weight: 400;
          font-size: 28px;
          font-family: PingFangSC-Regular, PingFang SC;
          line-height: 40px;
        }
      }

      .time {
        display: flex;
        flex-direction: row;
        align-items: baseline;
        width: 100%;
        margin: 0.2rem 0;
        margin-top: 16px;
        padding: 0 24px;
        font-size: 0.93rem;
        p {
          display: flex;
          flex-direction: row;
          align-items: baseline;
          justify-content: center;
          margin-bottom: 0.2rem;
          font-size: 0.93rem;

          span {
            &:first-child {
              margin-right: 0.2rem;
              color: #989eb4;
              font-size: 0.93rem;
            }

            &:nth-child(2) {
              color: #03081a;
              font-weight: bold;
              font-size: 1.1rem;
            }
          }
        }
      }

      .intro {
        margin-top: 32px;
        padding: 0 24px;
        padding-bottom: 20px;
        color: #03081a;
        font-size: 0.93rem;
        //background-color: #fff;

        .introduce {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding: 0.1rem 0;
        }

        .introContent {
          display: -webkit-box;
          margin-left: 0.2rem;
          overflow: hidden;
          color: #03081a;
          text-overflow: ellipsis;

          /*! autoprefixer: off */
          -webkit-box-orient: vertical;

          /* autoprefixer: on */
          -webkit-line-clamp: 1;
        }
        .more {
          height: 40px;
          padding-right: 12px;
          color: #989eb4;
          font-weight: 400;
          font-size: 24px;
          font-family: PingFangSC-Regular, PingFang SC;
          line-height: 40px;
          text-align: right;
        }
        img {
          height: 0.93rem;
          margin-right: 0.2rem;
        }
      }
      .teamLeader {
        display: flex;
        width: 100%;
        height: 248px;
        padding: 24px;
        color: rgba(3, 8, 26, 0.7);
        font-weight: 400;
        font-size: 24px;
        font-family: PingFangSC-Regular, PingFang SC;
        line-height: 34px;
        background: linear-gradient(180deg, #ffe6b8 0%, #fff 100%);
        border-radius: 12px;
        .teamLeaderLeft {
          width: 120px;
          img {
            position: relative;
            width: 120px;
            height: 120px;
            border: 2px solid #ffe4a7;
            border-radius: 50%;
          }
          div {
            position: relative;
            z-index: 1;
            width: 118px;
            height: 38px;
            margin-top: -16px;
            color: #aa7000;
            font-weight: 600;
            font-size: 24px;
            font-family: PingFangSC-Semibold, PingFang SC;
            line-height: 38px;
            text-align: center;
            background: linear-gradient(180deg, #ffe4a7 0%, #facd7c 100%);
            border-radius: 20px;
            box-shadow: 0 2px 0 0 #e1a63b;
          }
        }
        .teamLeaderRight {
          width: 100%;
          margin-left: 24px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          .doctorName {
            height: 52px;
            color: #03081a;
            font-weight: 600;
            font-size: 36px;
            font-family: PingFangSC-Semibold, PingFang SC;
            line-height: 52px;
          }
          .titleName {
            width: 112px;
            height: 40px;
            margin-left: 24px;
            color: #03081a;
            font-weight: 400;
            font-size: 28px;
            font-family: PingFangSC-Regular, PingFang SC;
            line-height: 40px;
          }
          .profession {
            margin-top: 16px;
          }
          .introduction {
            margin-top: 8px;
          }
          .more {
            height: 40px;
            margin-top: 16px;
            padding-right: 12px;
            color: #989eb4;
            font-weight: 400;
            font-size: 24px;
            font-family: PingFangSC-Regular, PingFang SC;
            line-height: 40px;
            text-align: right;
          }
        }
      }
      .teamMembers {
        padding: 24px 32px;
        border-radius: 12px;
        .teamMembersItem {
          display: flex;
          padding: 24px 0 30px 0;
          border-bottom: 1px solid #f5f6fa;
          .portrait {
            width: 100px;
            height: 100px;
            border-radius: 50%;
          }
          .info {
            width: 100%;
            margin-left: 24px;
            .doctorName {
              height: 52px;
              color: #03081a;
              font-weight: 600;
              font-size: 30px;
              font-family: PingFangSC-Semibold, PingFang SC;
            }
            .titleName {
              height: 40px;
              margin-left: 24px;
              color: #03081a;
              font-weight: 400;
              font-size: 28px;
              font-family: PingFangSC-Regular, PingFang SC;
              line-height: 40px;
            }
            .goodAt2 {
              width: 52px;
              height: 28px;
            }
            .profession {
              width: 514px;
              margin-top: 16px;
              padding: 0;
              color: #989eb4;
              font-weight: 400;
              font-size: 24px;
              font-family: PingFangSC-Regular, PingFang SC;
              line-height: 32px;
            }
            .commont {
              margin: 16px 0 16px 0;
              color: #989eb4;
              font-size: 24px;

              span {
                color: #3ad3c1;
                font-weight: bold;
                font-size: 32px;
              }
            }
            .btn {
              float: right;
              width: 160px;
              height: 52px;
              color: #fff;
              font-weight: 400;
              font-size: 28px;
              font-family: PingFangSC-Regular, PingFang SC;
              line-height: 52px;
              text-align: center;
              background: linear-gradient(90deg, #3ad3c1 0%, #68e9db 100%);
              border-radius: 26px;
            }
          }
        }
        .applyGuide {
          margin-top: 24px;
          color: #fe8f3c;
        }
      }
    }
  }
}
.box_title {
  height: 44px;
  color: #03081a;
  font-weight: 600;
  font-size: 32px;
  font-family: PingFangSC-Semibold, PingFang SC;
  line-height: 44px;
}

.favoriteCont {
  width: 4.57rem;
  height: 1.86rem;
  margin-right: 30px;
  color: #3ad3c1;
  font-size: 0.93rem;
  line-height: 1.86rem;
  background: #fff;
  border-radius: 0.93rem;
  box-shadow: 0 0 0.43rem 0 rgba(0, 0, 0, 0.06);
  .favorite {
    display: flex;
    align-items: center;
    padding-left: 0.8rem;
    color: #3ad3c1;

    .addIcon {
      width: 19px;
      height: 19px;
      margin-right: 6px;
    }
  }
}
.serviceContent {
  width: 100%;
  margin-top: 24px;
}
.selectFavorite {
  width: 4.57rem;
  height: 1.86rem;
  margin-right: 30px;
  color: #d7daea;
  font-size: 0.93rem;
  line-height: 1.86rem;
  text-align: center;
  background-color: #fff;
  border-radius: 0.93rem;
  box-shadow: 0 0 12px 0 rgba(0, 0, 0, 0.06);
}
.fans {
  position: absolute;
  top: 100px;
  right: 16px;
}
body {
  padding: 0 !important;
}
.division {
  height: 24px;
  background: '#f5f6fa';
}
.imgContent {
  width: 100%;
}
