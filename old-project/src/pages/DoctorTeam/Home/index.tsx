import React, { useState, useEffect, FC } from 'react';
import { connect, Dispatch, history, Loading } from 'umi';
import { Steps, Toast, Modal } from 'antd-mobile-v5';
import qs from 'query-string';
import { sensorsRequest } from '@/utils/sensors';
import styles from './index.less';
import Icon1 from '../../../assets/team-desc-icon1.png';
import Icon2 from '../../../assets/team-desc-icon2.png';
import avatar from '../../../assets/speMedicine/default.jpg';

const followIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-follow.png';
const introduceIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-main-intro.png';
const goodAtIcon = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-main-goodat.png';

interface PageProps {}
const { Step } = Steps;

const App: FC<PageProps> = (props: any) => {
  const {
    piTeam: { piTeamInfo },
  } = props;
  console.log(piTeamInfo, 'props');
  const [fans, setFans] = useState({
    fansCount: 0,
    status: 2,
  });
  /* 获取关注数 */
  const getFollowers = () => {
    props.dispatch({
      type: 'appointment/followStatus',
      payload: {
        doctorId: props.location.query.teamId,
      },
      callback: (data: any = {}) => {
        console.log(data);
        setFans(data);
      },
    });
  };

  /** 关注医生 */
  const onFavorite = async (value: number) => {
    await props.dispatch({
      type: 'appointment/followAction',
      payload: {
        actionType: value,
        doctorId: props.location.query.teamId,
        sourceChannel: 'PI',
      },
      callback: (data: any = {}) => {
        const { status } = data;
        if (status === 1) {
          Toast.show('您已成功关注该医生');
        } else if (status === 2) {
          Toast.show('已取消关注该医生');
        }
        setFans({
          status: value,
          fansCount: data.fansCount,
        });
      },
    });
  };

  const goToDoctorDetails = (data: any) => {
    const { deptCode, doctorId, hospitalAreaCode, deptCategoryCode, doctorName } = data;
    const pathname = '/doctor/index';
    // 埋点
    const { sku = 2 } = props.location.query || {};
    const queryObj = {
      hospitalAreaCode,
      deptCode,
      deptCategoryCode,
      doctorId,
      selectedIndex: 1,
      sourceChannel: 1,
      teamName: piTeamInfo.teamName,
      piTeamId: piTeamInfo.teamId,
      sku: sku || 2, // 渠道来源标记
    };
    history.push({
      pathname,
      query: queryObj,
    });
  };
  useEffect(() => {
    getFollowers();
    props.dispatch({
      type: 'piTeam/getPiTeamInfo',
      payload: {
        teamId: props.location.query.teamId,
        teamWorkId: props.location.query.teamWorkId,
      },
      callback: (data: any = {}) => {
        console.log('ddddddddddddddddd', data.winPops);
        if (data.winPops) {
          Modal.alert({
            content: data.winPops,
            onConfirm: () => {},
          });
        }
      },
    });
  }, []);
  return (
    <div className={styles.main}>
      {piTeamInfo && (
        <div>
          <div className={styles.headerBox}>
            <div className={styles.henderConten}>
              <div style={{ background: '#fff', paddingTop: '24px' }}>
                <div className={styles.fans}>
                  {fans.status === 2 ? (
                    <div className={styles.favoriteCont} onClick={() => onFavorite && onFavorite(1)}>
                      <div className={styles.favorite}>
                        <img src={followIcon} alt="" className={styles.addIcon} />
                        关注
                      </div>
                    </div>
                  ) : (
                    <div className={styles.selectFavorite} onClick={() => onFavorite && onFavorite(2)}>
                      已关注
                    </div>
                  )}
                </div>
                <div className={styles.teamTitle}>{piTeamInfo.teamName}</div>
                <div className={styles.teamHospital}>
                  <span className={styles.teamHospitalLevel}>三甲</span>
                  <span className={styles.teamHospitalName}>{piTeamInfo.organName}</span>
                </div>
                {/* 问诊数据 */}
                {piTeamInfo ? (
                  <div className={styles.time}>
                    <p>
                      <span>问诊数</span>
                      <span>
                        {piTeamInfo.piStatisticsVO.serviceCounts
                          ? piTeamInfo.piStatisticsVO.serviceCounts > 9999
                            ? `${Number(piTeamInfo.piStatisticsVO.serviceCounts / 10000).toFixed(0)}万`
                            : piTeamInfo.piStatisticsVO.serviceCounts
                          : '-'}
                      </span>
                    </p>
                    <p style={{ marginLeft: '32px' }}>
                      <span>
                        服务患者<span style={{ fontSize: '0.71rem' }}>(线上)</span>
                      </span>
                      <span>
                        {piTeamInfo.piStatisticsVO.patientCounts
                          ? piTeamInfo.piStatisticsVO.patientCounts > 9999
                            ? `${Number(piTeamInfo.piStatisticsVO.patientCounts / 10000).toFixed(0)}万`
                            : piTeamInfo.piStatisticsVO.patientCounts
                          : '-'}
                      </span>
                    </p>
                    <p>
                      {/* <span>好评率</span>
                  <span>{piTeamInfo.piStatisticsVO.satisfaction || '0'}%</span> */}
                    </p>
                  </div>
                ) : null}

                {/* 医生擅长和简介 */}

                <div className={styles.intro}>
                  <div className={styles.introduce}>
                    <img src={goodAtIcon} alt="" className={styles.goodAt} />
                    <span className={styles.introContent}>{piTeamInfo.profession}</span>
                  </div>
                  <div className={styles.introduce}>
                    <img src={introduceIcon} alt="" className={styles.goodAt} />
                    <span className={styles.introContent}>{piTeamInfo.introduction}</span>
                  </div>
                  <div
                    className={styles.more}
                    onClick={() => {
                      history.push({
                        pathname: '/doctor/team/info',
                      });
                    }}
                  >
                    查看更多 {'>'}{' '}
                  </div>
                </div>
              </div>
              <div className={styles.division} />
              <div className={styles.teamLeader}>
                <div className={styles.teamLeaderLeft}>
                  <img src={piTeamInfo.piDocInfoVO.portrait ? piTeamInfo.piDocInfoVO.portrait : avatar} alt="" />
                  <div>首席专家</div>
                </div>
                <div className={styles.teamLeaderRight}>
                  <div>
                    <span className={styles.doctorName}>{piTeamInfo.piDocInfoVO.doctorName}</span>
                    <span className={styles.titleName}>{piTeamInfo.piDocInfoVO.titleName}</span>
                  </div>
                  <div>
                    <div className={styles.profession}>擅长：{piTeamInfo.piDocInfoVO.profession}</div>
                    <div className={styles.introduction}>简介：{piTeamInfo.piDocInfoVO.introduction}</div>
                  </div>
                  <div
                    className={styles.more}
                    onClick={() => {
                      history.push({
                        pathname: '/doctor/team/leader',
                      });
                    }}
                  >
                    查看更多 {'>'}{' '}
                  </div>
                </div>
              </div>
              <div className={styles.division} />
              <div className={styles.teamMembers} style={{ background: '#fff' }}>
                <div className={styles.box_title}>问诊流程</div>
                <div className={styles.serviceContent}>
                  <img className={styles.imgContent} src={piTeamInfo.applyProcess} />
                </div>
              </div>
              <div className={styles.division} />
              <div className={styles.teamMembers} style={{ background: '#fff', paddingBottom: 0 }}>
                <div className={styles.box_title}>团队成员</div>
                {piTeamInfo.teamMemberList.map((item, i) => {
                  return (
                    <div
                      className={styles.teamMembersItem}
                      onClick={() => {
                        goToDoctorDetails(item);
                      }}
                    >
                      <img className={styles.portrait} src={item.portrait ? item.portrait : avatar} alt="" />
                      <div className={styles.info}>
                        <div>
                          <span className={styles.doctorName}> {item.doctorName}</span>
                          <span className={styles.titleName}>{item.titleName}</span>
                        </div>
                        <div>
                          <div className={styles.profession}>
                            {' '}
                            <img src={goodAtIcon} alt="" className={styles.goodAt2} /> {item.profession}
                          </div>
                        </div>
                        <div className={styles.commont}>
                          好评率<span> {item.satisfaction}% </span> / 问诊量
                          <span> {item.servTimes}</span>
                        </div>
                        <div
                          className={styles.btn}
                          onClick={(e) => {
                            e.stopPropagation();
                            goToDoctorDetails(item);
                          }}
                        >
                          申请问诊
                        </div>
                      </div>
                    </div>
                  );
                })}
                <div />
              </div>
              <div className={styles.division} />
              <div className={styles.teamMembers} style={{ background: '#fff' }}>
                <div className={styles.box_title}>申请须知</div>
                <div className={styles.applyGuide}>{piTeamInfo.applyGuide}</div>
              </div>
              <div className={styles.division} />
              <div className={styles.teamMembers} style={{ background: '#fff' }}>
                <div className={styles.box_title}>服务内容</div>
                <div className={styles.serviceContent}>
                  <img className={styles.imgContent} src={piTeamInfo.serviceContent} />
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default connect(({ loading, piTeam }: { loading: Loading; piTeam: AnimationPlaybackEventInit }) => ({
  loading,
  getPiTeamInfoLoading: loading.effects['piTeam/getPiTeamInfo'],
  piTeam,
}))(App);
