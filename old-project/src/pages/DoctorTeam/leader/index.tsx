import React, { useState, useEffect, FC } from 'react';
import { connect, Dispatch, history, Loading } from 'umi';
import styles from './index.less';
import Icon1 from '../../../assets/team-desc-icon1.png';
import Icon2 from '../../../assets/team-desc-icon2.png';
import avatar from '../../../assets/speMedicine/default.jpg';

interface PageProps {}
const App: FC<PageProps> = (props: any) => {
  useEffect(() => {
    console.log(props.location.query.teamId);
  }, []);
  const {
    piTeam: {
      piTeamInfo: { piDocInfoVO },
    },
  } = props;
  return (
    <div className={styles.main}>
      {piDocInfoVO ? (
        <div>
          {' '}
          <div className={styles.headerBox}>
            <div className={styles.teamTitle}> {piDocInfoVO.doctorName}</div>
            <div className={styles.deptName}>{piDocInfoVO.deptName}</div>
            <div className={styles.teamHospital}>
              <span className={styles.teamHospitalLevel}>{piDocInfoVO.organLevel}</span>
              <span className={styles.teamHospitalName}>{piDocInfoVO.organName}</span>
            </div>
            <img src={piDocInfoVO.portrait || avatar} className={styles.avatar} alt="" />
          </div>
          <div className={styles.center}>
            <div className={styles.centerDescBox}>
              <div className={styles.centerDescBoxHeader}>
                <img className={styles.centerDescBoxHeaderIcon} src={Icon1} alt="" />
                <span className={styles.centerDescBoxHeaderName}>擅长疾病</span>
              </div>
              <div className={styles.centerDescBoxDesc}>{piDocInfoVO.profession}</div>
            </div>
            <div className={styles.centerDescBox}>
              <div className={styles.centerDescBoxHeader}>
                <img className={styles.centerDescBoxHeaderIcon} src={Icon2} alt="" />
                <span className={styles.centerDescBoxHeaderName}>个人简介</span>
              </div>
              <div className={styles.centerDescBoxDesc}>{piDocInfoVO.introduction}</div>
            </div>
          </div>
        </div>
      ) : null}
    </div>
  );
};
export default connect(({ loading, piTeam }: { loading: Loading; piTeam: any }) => ({
  loading,
  piTeam,
}))(App);
