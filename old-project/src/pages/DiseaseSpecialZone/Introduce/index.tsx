import React, { useMemo } from 'react';
import queryString from 'query-string';

import styles from './index.less';
import { Shenyizhi_Content_List, SPECIAL_CODE } from '../enum';

const randomDocListTypeList = {
  [SPECIAL_CODE.SYZ]: {
    conetntIntroList: Shenyizhi_Content_List,
  },
};

export default function () {
  const { type = '0', value = '' } = queryString.parse(window.location.search);

  const introInfo = useMemo(() => {
    const conetntIntroList = randomDocListTypeList[type]?.conetntIntroList;
    if (!conetntIntroList) return {};
    const content = conetntIntroList.find((v) => v.value === value);
    return content || { label: '', content: '' };
  }, [type]);

  return (
    <div className={styles.body}>
      <img src={require('../assets/<EMAIL>')} />
      <div className={styles.content}>
        <div className={styles.contentTitle}>
          <img src={require('../assets/jx.png')} />
          <span>{introInfo?.label}</span>
        </div>
        <div className={styles.contentBody}>{introInfo?.content}</div>
      </div>
    </div>
  );
}
