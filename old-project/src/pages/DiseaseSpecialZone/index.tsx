import React, { useEffect, useState } from 'react';
import definedAvatar from './assets/icon-doctor.png';
import { HxParameter } from '@/utils/parameter';
import queryString from 'query-string';
import { Toast } from 'antd-mobile';
import { history } from 'umi';
import { Image } from 'antd-mobile-v5';
import { getPatientLtxDoctor } from './service';
import styles from './index.less';
import { Shenyizhi_Content_List, SPECIAL_CODE } from './enum';

function Index() {
  const [doctorList, setDoctorList] = useState([]);
  const [zjList, setZjList] = useState([]);
  const { type = SPECIAL_CODE.SYZ } = queryString.parse(window.location.search);
  const randomDocListTypeList = {
    [SPECIAL_CODE.SYZ]: {
      documentTitle: '肾移植咨询',
      title: '肾移植咨询',
      introduce:
        '全国首创的由泌尿外科肾移植亚专业医疗团队、肾内科医疗团队、肾移植专业护理团队为一体的肾移植医护团队，实现了肾移植患者围术期、康复期、术后并发症期的全周期管理。从1979年开展第一例肾脏移植至今，已开展肾脏移植手术7000余例，在全国肾脏移植中心中居于前列，近4年肾移植年手术量平均超640例。\r\n①全球首先对肾移植供受者应用加速康复外科理念；\r\n②全球率先提出ABO血型不合肾移植的个体化预处理方案；\r\n③应用小儿和成人的双肾移植；\r\n④应用超小切口肾脏移植；\r\n⑤肾移植联合同期回肠膀胱扩大治疗神经源性膀胱导致的尿毒症；\r\n⑥探索HBV阳性的活体供者肾脏移植的相关应用；\r\n⑦国内率先开展机器人辅助腹腔镜肾移植单位之一，并完成了全国第一例经阴道置入肾脏机器人辅助肾脏移植；\r\n⑧成立了“四川大学华西医院肾脏移植专病联盟”以及建设川藏肾脏移植术后供受者随访网络。',
      ltxFirst: 'kt_first',
      ltxSecond: 'kt',
      topBg: require('./assets/syzbgpng.png'),
      conetntIntroList: Shenyizhi_Content_List,
    },
    [SPECIAL_CODE.SHUHOU]: {
      documentTitle: '术后疼痛规范化管理',
      title: '术后疼痛规范化管理',
      introduce: `术后慢性疼痛影响了超过10%的手术患者。有效管理术后30天亚急性疼痛可显著降低术后慢性疼痛的发生率。四川大学华西医院整合麻醉手术中心和疼痛科的优质资源，推出院外过渡性疼痛服务，帮助患者在术后康复过程中更好地应对疼痛。\r\n四川大学华西医院麻醉手术中心连续14年蝉联复旦大学中国最佳专科排行榜全国排名第一。四川大学华西医院疼痛科拥有4个病区，年门诊量超5万人次，是国家临床重点专科及四川省疼痛专科医师培训基地。`,
      ltxFirst: 'mzzq_first',
      ltxSecond: 'mzzq',
      topBg: require('./assets/<EMAIL>'),
      conetntIntroList: [],
    },
  };
  const randomDocListType = randomDocListTypeList[type];
  document.title = randomDocListType.documentTitle;
  console.log(document.title);

  const init = async () => {
    Toast.loading('');
    try {
      const [zjList, doctorList] = await Promise.all([
        getPatientLtxDoctor({
          pageNum: 1,
          pageSize: 100,
          randomDocListType: randomDocListType.ltxFirst,
        }),
        getPatientLtxDoctor({
          pageNum: 1,
          pageSize: 100,
          randomDocListType: randomDocListType.ltxSecond,
        }),
      ]);

      setZjList(zjList.list || []);
      setDoctorList(doctorList.list || []);
    } catch (err) {
      console.log(err);
    }
    Toast.hide();
  };

  useEffect(() => {
    init();
  }, []);

  const goToDoctorDetailsPage = (item) => {
    const { doctorId, deptName, organId, servCode } = item;
    const { channelCode, openId, organCode } = HxParameter;
    const query: any = {
      organCode,
      openId,
      doctorId,
      servCode,
      middleType: 1,
      channelCode,
      deptName,
      organId,
    };
    if (String(type) === '2') {
      query.pageChannelType = 2;
      query.sku = 13; // 铁道职工专用渠道
    }
    history.push({
      pathname: '/doctor/hxhome',
      query,
    });
  };

  return (
    <div className={styles.Container}>
      {[SPECIAL_CODE.SYZ, SPECIAL_CODE.SHUHOU].includes(String(type)) && (
        <div className={styles.Header}>
          <img className={styles.HeaerTop} src={randomDocListType.topBg} />
          <div className={styles.HeaderBody}>
            <img src={require('./assets/introducetititle.png')} alt="" />
            <div>{randomDocListType.introduce}</div>
          </div>
        </div>
      )}
      {[SPECIAL_CODE.SYZ, SPECIAL_CODE.SHUHOU].includes(String(type)) && zjList.length > 0 && (
        <div className={styles.DiseaseSpecialZoneZjjs}>
          <div className={styles.DiseaseSpecialZoneZjjsTitle}>
            <span>专家介绍</span>
          </div>
          {zjList.map((doctorItem: any) => {
            return (
              <div
                className={styles.DiseaseSpecialZoneZjjsBody}
                onClick={() => {
                  goToDoctorDetailsPage(doctorItem);
                }}
                key={doctorItem.doctorId}
              >
                <span
                  className={styles.DiseaseSpecialZoneZjjsAvatar}
                  style={{
                    backgroundImage: `url(${doctorItem.headPortraits || definedAvatar})`,
                  }}
                />
                <div className={styles.DiseaseSpecialZoneZjjsDes}>
                  <div className={styles.DiseaseSpecialZoneZjjsTitle}>
                    <span>{doctorItem.doctorName}</span>
                    <span>{doctorItem.titelName}</span>
                  </div>
                  <div className={styles.DiseaseSpecialZoneZjjsSubTitle}>
                    <span>{doctorItem.organName}</span>
                    <span>{doctorItem.deptName}</span>
                  </div>
                  <div className={styles.DiseaseSpecialZoneZjjsFooter}>{doctorItem.profession}</div>
                </div>
              </div>
            );
          })}
        </div>
      )}
      <div className={styles.DiseaseSpecialZoneYsjs}>
        <div className={styles.DiseaseSpecialZoneZjjsTitle}>
          <span>医生介绍</span>
        </div>
        {doctorList.map((doctorItem: any) => {
          return (
            <div
              className={styles.DiseaseSpecialZoneZjjsBody}
              key={doctorItem.doctorId}
              onClick={() => {
                goToDoctorDetailsPage(doctorItem);
              }}
            >
              <Image
                src={doctorItem.headPortraits || definedAvatar}
                className={styles.DiseaseSpecialZoneZjjsAvatar}
                fallback={<img src={definedAvatar} alt="" width="100%" height="100%" />}
                fit="fill"
              />
              <div className={styles.DiseaseSpecialZoneZjjsDes}>
                <div className={styles.DiseaseSpecialZoneZjjsTitle}>
                  <span>{doctorItem.doctorName}</span>
                  <span>{doctorItem.titelName}</span>
                </div>
                <div className={styles.DiseaseSpecialZoneZjjsSubTitle}>
                  <span>{doctorItem.organName}</span>
                  <span>{doctorItem.deptName}</span>
                </div>
                <div className={styles.DiseaseSpecialZoneZjjsFooter}>
                  <i />
                  <span>{doctorItem.profession}</span>
                </div>
              </div>
              <i />
            </div>
          );
        })}
      </div>
      {[SPECIAL_CODE.SYZ].includes(String(type)) && (
        <div className={styles.contentIntro}>
          <div>内容介绍</div>
          <div className={styles.contentIntroBody}>
            {randomDocListType.conetntIntroList.map((item: any, index: number) => {
              return (
                <div className={styles.contentIntroItem} key={item.value}>
                  <span>{item.label}</span>
                  <span
                    onClick={() => {
                      history.push({
                        pathname: '/disease-special-zone/introduce',
                        query: {
                          type: type,
                          value: item.value,
                        },
                      });
                    }}
                  >
                    <span>进入</span>
                    <span>&gt;</span>
                  </span>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}

export default Index;
