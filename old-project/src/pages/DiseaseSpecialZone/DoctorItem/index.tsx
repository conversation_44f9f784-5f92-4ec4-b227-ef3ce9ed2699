import React from 'react';
import styles from './index.less';
import definedAvatar from '../../../assets/winterbreath/icon-doctor.png';

const introImg = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/doctor/doctor-main-intro.png';
const goodAt = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/mobile/assets/shanchang.png';

const DoctorItem = (props) => {
  const {
    headPortraits = '',
    doctorName = '',
    titelName = '',
    organName = '',
    deptName = '',
    profession = '',
    teamName = '',
    introduction = '',
  } = props.rowData;
  const { onClick = null } = props;
  return (
    <div
      className={styles.doctorItem}
      onClick={() => {
        onClick && onClick();
      }}
    >
      <div className={styles.doctorItemBox}>
        <span
          className={styles.avatar}
          style={{
            backgroundImage: `url(${headPortraits || definedAvatar})`,
          }}
        />
        <div className={styles.content}>
          <div className={styles.header}>
            <div className={styles.doctorName}>{props.type === 'tdzx' ? teamName : doctorName}</div>
            {props.type !== 'tdzx' && <div className={styles.titelName}>{titelName}</div>}
          </div>
          <div className={styles.orgDep}>
            <span className={styles.organName}>{organName}</span>
            {props.type !== 'tdzx' && <span className={styles.deptName}>{deptName}</span>}
          </div>
          <div className={styles.profession}>
            <img src={props.type !== 'tdzx' ? goodAt : introImg} alt="" className={styles.goodAt} />
            <div className={styles.introContent}>{props.type !== 'tdzx' ? `${profession}` : `${introduction}`}</div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DoctorItem;
