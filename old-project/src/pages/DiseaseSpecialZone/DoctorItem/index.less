.doctorItem {
  background-color: #fff;
  padding: 32px 24px 0 24px;
  .doctorItemBox {
    display: flex;
    border-bottom: 1px solid #ebedf5;
    .avatar {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      background-size: 100% 100%;
      display: inline-block;
    }
    .content {
      width: 500px;
      margin-left: 24px;
      .header {
        display: flex;
        align-items: center;
        flex-direction: row;

        .doctorName {
          font-size: 36px;
          color: #03081a;
          font-weight: 600;
          margin-right: 16px;
        }

        .titelName {
          font-size: 28px;
          color: #03081a;
          margin-right: 24px;
        }

        .rank {
          background-color: #568df2;
          padding: 0 12px;
          color: #ffffff;
          border-radius: 8px;
          font-size: 18px;
          height: 32px;
          line-height: 32px;
          text-align: center;
        }

        .iconksjz {
          width: 86px;
          height: 35px;
          margin-left: 20px;
        }
      }

      .orgDep {
        font-size: 28px;
        color: #03081a;
        margin: 12px 0;

        .organName {
          margin-right: 16px;
        }
      }

      .satisfTimes {
        font-size: 24px;
        color: #989eb4;
        margin: 18px 0 34px 0;

        span {
          font-size: 32px;
          color: #3ad3c1;
          font-weight: bold;
        }
      }

      .profession {
        text-indent: 55px;
        font-size: 24px;
        width: 502px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        word-break: break-all; /*追加这一行代码*/
        position: relative;

        .goodAt {
          width: 50px;
          height: 24px;
          position: absolute;
          left: 0;
          top: 5.5px;
          // float: left; // 之前采用的是浮动，但是手机上显示不了图片
          // margin-top: 0.06px;
          // margin-right: 0.07px;
        }

        .introContent {
          font-size: 24px;
          color: #989eb4;
          max-width: 440px;
          white-space: nowrap;
          text-overflow: ellipsis;
          -o-text-overflow: ellipsis;
          overflow: hidden;
        }
      }

      .inquiryBox {
        display: flex;
        justify-content: space-between;
        margin-top: 32px;

        button {
          height: 53px;
          padding: 0 2px;
          background: #3ad3c1;
          border-radius: 40px;
          outline: none;
          font-size: 26px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #ffffff;
          border: none;
        }
      }

      .price {
        font-size: 28px;
        font-weight: bold;

        > span {
          color: #fbbc44;
        }

        > span:nth-child(2) {
          color: rgba(235, 237, 245, 1);
        }
      }

      .price_ {
        font-size: 36px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #fe8f3c;

        span {
          color: #fe8f3c;
          font-size: 18px;
        }
      }

      .servList {
        display: flex;
        flex-wrap: wrap;

        .servItem {
          text-align: center;
          padding: 5px 10px;
          background-color: #fff0e0;
          font-size: 22px;
          border-radius: 6px;
          color: #ff8915;
          margin-right: 20px;
          margin-top: 10px;
        }
      }
    }
  }
}
