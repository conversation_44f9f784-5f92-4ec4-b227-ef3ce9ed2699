.Container {
  height: 100vh;
  padding-bottom: 24px;
  overflow: auto;
  background-color: #f5f6fa;
  .Header {
    margin: 0 auto;
    .<PERSON><PERSON><PERSON><PERSON><PERSON> {
      width: 750px;
      height: 392px;
    }

    .HeaderBody {
      margin: 0 auto;
      position: relative;
      width: 702px;
      margin-top: -116px;
      background-image: url(./assets/introducebg.png);
      background-size: 100% 100%;
      // height: 1124px;
      display: flex;
      flex-direction: column;
      align-items: center;
      > img {
        width: 336px;
        height: 54px;
        margin-top: 32px;
      }
      > div {
        width: 100%;
        padding: 24px;
        font-size: 28px;
        font-family: PingFang SC, PingFang SC-Regular;
        font-weight: Regular;
        text-align: justify;
        color: #03081a;
        line-height: 44px;
        white-space: pre-wrap;
      }
    }
  }
  .DiseaseSpecialZoneZjjs {
    margin: 0 auto;
    margin-top: 24px;
    width: 702px;
    padding: 30px 24px;
    background-color: #fff;
    border-radius: 12px;
    > .DiseaseSpecialZoneZjjsTitle {
      > span {
        height: 48px;
        color: #03081a;
        font-weight: 500;
        font-size: 36px;
        line-height: 48px;
      }

      margin-bottom: 26px;
    }
    .DiseaseSpecialZoneZjjsBody {
      display: flex;
      align-items: center;
      width: 654px;
      height: 224px;
      padding: 24px;
      background: #fff9ee;
      border-radius: 12px;
      .DiseaseSpecialZoneZjjsAvatar {
        display: inline-block;
        min-width: 124px;
        max-width: 124px;
        min-height: 124px;
        max-height: 124px;
        margin-right: 24px;
        background-size: 100% 100%;
        border-radius: 50%;
      }
      .DiseaseSpecialZoneZjjsDes {
        display: flex;
        flex-direction: column;
        height: 176px;
        .DiseaseSpecialZoneZjjsTitle {
          > span:nth-child(1) {
            height: 52px;
            margin-right: 24px;
            color: #03081a;
            font-weight: 600;
            font-size: 36px;
            line-height: 52px;
          }
          > span:nth-child(2) {
            height: 40px;
            color: #03081a;
            font-weight: 400;
            font-size: 28px;
            line-height: 40px;
          }
        }
        .DiseaseSpecialZoneZjjsSubTitle {
          height: 44px;
          margin-top: 4px;
          overflow: hidden;
          color: #03081a;
          font-weight: 400;
          font-size: 28px;
          line-height: 44px;
          width: 500px;
          /* 文本不会换行 */
          white-space: nowrap;

          /* 当文本溢出包含元素时，以省略号表示超出的文本 */
          text-overflow: ellipsis;
          > span:nth-child(1) {
            margin-right: 16px;
          }
        }
        .DiseaseSpecialZoneZjjsFooter {
          display: -webkit-box;
          width: 458px;
          height: 68px;
          overflow: hidden;
          color: rgba(3, 8, 26, 0.7);
          font-weight: 400;
          font-size: 24px;
          line-height: 34px;
          text-overflow: ellipsis;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
    }
  }

  .DiseaseSpecialZoneYsjs {
    margin: 0 auto;
    margin-top: 24px;
    padding: 30px 24px;
    padding-bottom: 0;
    background-color: #fff;
    border-radius: 12px;
    width: 702px;
    > .DiseaseSpecialZoneZjjsTitle {
      > span {
        height: 48px;
        color: #03081a;
        font-weight: 500;
        font-size: 36px;
        line-height: 48px;
      }

      padding-bottom: 26px;
    }
    .DiseaseSpecialZoneZjjsBody {
      position: relative;
      display: flex;
      align-items: center;
      width: 654px;
      height: 224px;
      padding: 24px 0;
      border-radius: 12px;
      .DiseaseSpecialZoneZjjsAvatar {
        display: inline-block;
        min-width: 124px;
        max-width: 124px;
        min-height: 124px;
        max-height: 124px;
        margin-right: 24px;
        background-size: 100% 100%;
        border-radius: 50%;
      }
      .DiseaseSpecialZoneZjjsDes {
        display: flex;
        flex-direction: column;
        height: 176px;
        .DiseaseSpecialZoneZjjsTitle {
          > span:nth-child(1) {
            height: 52px;
            margin-right: 24px;
            color: #03081a;
            font-weight: 600;
            font-size: 36px;
            line-height: 52px;
          }
          > span:nth-child(2) {
            height: 40px;
            color: #03081a;
            font-weight: 400;
            font-size: 28px;
            line-height: 40px;
          }
        }
        .DiseaseSpecialZoneZjjsSubTitle {
          height: 44px;
          margin-top: 4px;
          color: #03081a;
          font-weight: 400;
          font-size: 28px;
          line-height: 44px;
          margin-top: 4px;
          width: 500px;
          overflow: hidden;
          /*文本不会换行*/
          white-space: nowrap;
          /*当文本溢出包含元素时，以省略号表示超出的文本*/
          text-overflow: ellipsis;
          > span:nth-child(1) {
            margin-right: 16px;
          }
        }
        .DiseaseSpecialZoneZjjsFooter {
          position: relative;
          width: 502px;
          > i {
            position: absolute;
            top: 4px;
            left: 0;
            display: inline-block;
            width: 50px;
            height: 24px;
            background-image: url(./assets/sc.png);
            background-size: 100% 100%;
          }
          > span {
            display: block;
            display: -webkit-box;
            height: 64px;
            overflow: hidden;
            color: #989eb4;
            font-weight: 400;
            // width: 458px;
            font-size: 24px;
            line-height: 34px;
            text-indent: 54px;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
          }
        }
      }
      > i {
        position: absolute;
        right: -24px;
        bottom: 0;
        width: 526px;
        height: 2px;
        background: #f5f6fa;
      }
    }
  }

  .contentIntro {
    margin: 0 auto;
    width: 702px;
    background: #ffffff;
    border-radius: 24px;
    margin-top: 24px;
    padding: 36px 20px;
    > div:nth-child(1) {
      height: 50px;
      font-size: 36px;
      font-family: PingFang SC, PingFang SC-Semibold;
      font-weight: bolder;
      text-align: left;
      color: #03081a;
      line-height: 50px;
    }
    .contentIntroBody {
      > div {
        padding: 32px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        > span:nth-child(1) {
          font-size: 28px;
          font-family: PingFang SC, PingFang SC-Regular;
          font-weight: Regular;
          text-align: left;
          color: #03081a;
          line-height: 40px;
        }
        > span:nth-child(2) {
          padding: 6px 16px;
          line-height: 44px;
          background: #ffffff;
          border: 2px solid #3ad3c1;
          border-radius: 8px;
          color: #3ad3c1;
          font-size: 24px;
          > span:nth-child(1) {
            margin-right: 8px;
          }
        }
      }
      > div + div {
        border-bottom: 2px solid #f6f8ff;
      }
    }
  }
}
