/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2021-06-15 16:13:16
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023-12-27 15:05:08
 */
/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2021-06-15 16:13:16
 * @LastEditor: <PERSON>
 * @LastEditTime: 2021-11-18 20:49:57
 */
// 一些公共的接口
import request from '@/utils/request';
import { creatSgin } from './FaceRecognitionST/utils/fun';
import { getToken } from '@/utils/parameter';

const stHost = 'http://xxzx.wchscu.cn';
const stCode = 'hytSys';
const stSecret = 'M4SwZaFIozbosEpZd8';

/** 获取文案 */
export const queryText = async (data: {
  /** 文案编码 */
  textCode: string;
}) =>
  request('/cloud/resource/text/query', {
    method: 'POST',
    data,
    prefix: API_BASE,
  });

/** 获取文案列表 */
export const queryTextList = async (data: {
  /** 文案编码 */
  textCode: string[];
}) =>
  request('/cloud/hosplatcustomer/resource/text/queryList', {
    method: 'POST',
    data,
    prefix: API_BASE,
  });

/** 人脸识别 */
export const faceRecognition = async (data: {}) =>
  request('/toFaceRecognition', {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });

// 商汤人脸补录
export const stSaveFaceInfo = async (data: object): Promise<any> => {
  return request('/cloud/hosplatcustomer/cardservice/saveStFaceInfo', {
    method: 'POST',
    data,
  });
};

// 商汤人脸识别列表
export const stDeleteFaceInfo = async (data: object): Promise<any> => {
  return request('/cloud/hosplatcustomer/realname/cancelStFaceInfo', {
    method: 'POST',
    data,
  });
};

// 华为人脸识别接口
export const saveHwFaceInfoH5 = async (formData: any): Promise<any> =>
  // fetch(`${API_BASE}/cloud/realname/hw/saveHwFaceInfoH5`, {
  fetch(`${API_BASE}/cloud/hosplatcustomer/realname/saveHwFaceInfoH5`, {
    method: 'POST',
    headers: {
      token: getToken(),
      accessToken: getToken(),
    },
    body: formData,
  }).then((res) => res.json());

// 商汤H5获取登录凭证
export const getStAccessToken = (data: Object) =>
  request(`/cloud/hosplatcustomer/realname/getStAccessToken`, {
    method: 'POST',
    data,
  });

/** 获取并按邮寄业务渠道实名方式列表 */
export const getCheckType = async (data: any) =>
  request(`${API_BASE}/cloud/hosplatcustomer/realname/checktype`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });

/** 保存人脸识别 */
export const saveFaceResult = async (data: any) =>
  request(`${API_BASE}/cloud/hosplatcustomer/realname/saveFaceResult`, {
    method: 'POST',
    data: {
      ...data,
      showOriginData: true,
    },
  });
