.container {
  display: flex;
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  justify-content: center;
  align-items: flex-start;
  background: linear-gradient(180deg, #e7f8f6 0%, #ffffff 100%);

  header {
    font-size: 56px;
    font-weight: 500;
    color: #333333;
    line-height: 80px;
    text-align: center;
  }

  .faceWrap {
    padding-top: 88px;
    height: 100%;
  }

  .circleContainer {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 700px;
    height: 700px;
    z-index: 10;
    margin-top: 90px;
  }

  .insideCircle {
    position: absolute;
    display: flex;
    width: 600px;
    height: 600px;
    border-radius: 50%;
    background-color: #cdcdcd;
    overflow: hidden;
    z-index: 20;
  }

  video {
    position: absolute;
    /* top: 25%;
  left: 25%; */
    display: flex;
    justify-content: center;
    align-items: center;
    width: 600px;
    height: 600px;
    transform: scale(1.5, 1.5) rotateY(180deg);
    -ms-transform: scale(1.5, 1.5) rotateY(180deg);
    -webkit-transform: scale(1.5, 1.5) rotateY(180deg);
    /* transform: rotateY(180deg);
  -ms-transform: rotateY(180deg);
  -webkit-transform: rotateY(180deg); */
    z-index: 30;
  }

  .head {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/face-recognition/face_outline.png');
    background-repeat: no-repeat;
    background-size: contain;
    text-align: center;
    z-index: 40;
  }

  .tips {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100px;
    background-color: rgba(125, 125, 125, 0.75);
    text-align: center;
    font-size: 24px;
    color: #fff;
    z-index: 50;
  }

  .tips.blue {
    background-color: rgba(61, 217, 248, 0.75);
  }

  .tips span {
    margin-top: 20px;
  }

  .outsideCircleContainer {
    position: absolute;
    display: block;
    width: 700px;
    height: 700px;
    // background-image: url("https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/face-recognition/a-19.png");
    // background-size: contain;
    z-index: 60;
  }

  .outsideCircle {
    display: block;
    width: 100%;
    height: 100%;
    background-image: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/face-recognition/a-01.png');
    background-size: contain;
  }

  .outsideCircle.rotating {
    animation: anmRotate 3s linear infinite;
    -moz-animation: anmRotate 3s linear infinite;
    -webkit-animation: anmRotate 3s linear infinite;
    -o-animation: anmRotate 3s linear infinite;
  }

  .guide {
    margin-top: 60px;
    display: flex;
    min-height: 40px;
    justify-content: center;
    align-items: center;
    font-size: 42px;
    font-weight: 600;
    color: #fe8f3c;
    line-height: 44px;
  }

  .buttons {
    display: flex;
    justify-content: center;
    margin-top: 140px;
    flex-direction: column;
    align-items: center;
  }

  .buttons .button {
    width: 300px;
    height: 80px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: #ffffff;
    border-radius: 52px;
    border: 2px solid #989eb4;
    font-size: 28px;
    cursor: pointer;
    color: #03081a;
    margin-top: 20px;
  }

  .closeButton {
    position: fixed;
    top: 20px;
    left: 20px;
    display: block;
    width: 64px;
    height: 64px;
    background-image: url('https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/face-recognition/a-03.png');
    cursor: pointer;
  }

  .progressWrap {
    display: flex;
    justify-content: center;
    margin-top: 60px;
  }

  .progressTime {
    font-size: 36px;
    font-weight: bold;
    color: @brand-primary;
  }

  .tipWrap {
    min-height: 100px;
  }

  @keyframes anmRotate {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  @-moz-keyframes anmRotate {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  @-webkit-keyframes anmRotate {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  @-o-keyframes anmRotate {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }
}
