import MobileDetect from 'mobile-detect';
import getIphoneModel from '@/utils/iPhone-detect.min.js';

/**
 * 获取用户媒体设备
 * @param {object} constraints - 选项
 * @param {function} callback - 处理回调
 */
export const getUserMedia = (constraints, callback) => {
  // 老的浏览器可能根本没有实现 mediaDevices，所以我们可以先设置一个空的对象
  if (navigator.mediaDevices === undefined) {
    navigator.mediaDevices = {};
  }

  // 一些浏览器部分支持 mediaDevices。我们不能直接给对象设置 getUserMedia
  // 因为这样可能会覆盖已有的属性。这里我们只会在没有getUserMedia属性的时候添加它。
  if (navigator.mediaDevices.getUserMedia === undefined) {
    navigator.mediaDevices.getUserMedia = function (constraints) {
      // 首先，如果有getUserMedia的话，就获得它
      const getUserMedia =
        navigator?.getUserMedia ||
        navigator?.webkitGetUserMedia ||
        navigator?.mozGetUserMedia ||
        navigator?.msGetUserMedia ||
        navigator?.oGetUserMedia;

      // 一些浏览器根本没实现它 - 那么就返回一个error到promise的reject来保持一个统一的接口
      if (!getUserMedia) {
        return Promise.reject(new Error('getUserMedia is not implemented in this browser'));
      }

      // 否则，为老的navigator.getUserMedia方法包裹一个Promise
      return new Promise(function (resolve, reject) {
        getUserMedia.call(navigator, constraints, resolve, reject);
      });
    };
  }
  try {
    navigator.mediaDevices
      .getUserMedia(constraints)
      .then((stream) => {
        callback(false, stream);
      })
      .catch((error) => {
        console.log('error:', JSON.stringify(error));
        callback(error);
      });
  } catch (error) {
    console.log(error, '111111');
  }
};

/**
 * Blob转Base64
 * @param {blob} blob - Blob数据
 * @param {function} callback - 处理回调
 */
export const blobToBase64 = (blob, callback) => {
  const fileReader = new FileReader();
  fileReader.onload = function (e) {
    callback(e?.target?.result);
  };
  fileReader.readAsDataURL(blob);
};

/**
 * 获取手机型号
 * @return {*}
 */

export const getPhone = () => {
  const contains = function (deviceInfo: Array<string>, needle: string) {
    for (const i in deviceInfo) {
      if (deviceInfo[i].indexOf(needle) > 0) return i;
    }
    return -1;
  };
  const device_type = navigator.userAgent; // 获取userAgent信息
  const md = new MobileDetect(device_type); // 初始化mobile-detect
  let os = md.os(); // 获取系统
  let model = '';
  if (os === 'iOS') {
    // ios系统的处理
    os = md.os() + md.version('iPhone');
    model = getIphoneModel();
  } else if (os === 'AndroidOS') {
    // Android系统的处理
    os = md.os() + md.version('Android');
    const sss: any = device_type.split(';');
    const i = contains(sss, 'Build/');
    if (i > -1) {
      model = sss[i].substring(0, sss[i].indexOf('Build/'));
    }
  }
  const brand = md.mobile() !== 'UnknownPhone' ? md.mobile() : md.phone() !== 'UnknownPhone' ? md.phone() : '';
  return os === 'iOS' ? `${model}(${os})` : `${brand}${model}(${os})`;
};

/**
 * 对象转formdata
 * @return {*}
 */
export const paramsToFormData = (obj, form_data) => {
  const data = [];
  if (obj instanceof File) {
    data.push({ key: '', value: obj });
  } else if (typeof obj == 'object') {
    for (let j in obj) {
      let arr = paramsToFormData(obj[j]);
      for (let k = 0, l = arr.length; k < l; k++) {
        let key = !!form_data ? j + arr[k].key : '.' + j + '' + arr[k].key;
        data.push({ key: key, value: arr[k].value });
      }
    }
  } else {
    data.push({ key: '', value: obj });
  }
  if (!!form_data) {
    // 封装
    for (let i = 0, len = data.length; i < len; i++) {
      form_data.append(data[i].key, data[i].value);
    }
  } else {
    return data;
  }
};
