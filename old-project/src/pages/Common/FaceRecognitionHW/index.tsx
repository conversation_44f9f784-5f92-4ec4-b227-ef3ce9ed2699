import React, { useEffect, useRef, useState } from 'react';
import { connect, Dispatch, history, useDispatch } from 'umi';
import { Toast } from 'antd-mobile';
import classnames from 'classnames';
import { getOrganCode, getChannelCode } from '@/utils/parameter';
import { HxSessionStorage } from '@/utils/storage';
import qs from 'query-string';
import { Modal, ProgressCircle } from 'antd-mobile-v5';
import styles from './index.less';
import { blobToBase64, getUserMedia, getPhone } from './utils/tool';
import { VIDEO_TIPS, VIDEO_TYPE } from './dataDictionary';
import { CommonState } from '../data.d';
import { saveHwFaceInfoH5 } from '../service';
import HxModal from '@/components/HxModal';

interface IProps {
  dispatch: Dispatch;
  location: {
    query: {
      bussinessCode: string;
      callBackUrl: string;
      realNameType: string;
      from: string;
      redirectUrl: string;
    };
  };
}
const FaceRecognitionHW: React.FC<IProps> = (props) => {
  /* porps */
  const dispatch = useDispatch();
  const query = qs.parse(window.location.search.slice(1));
  const { bussinessCode = '', callBackUrl = '', realNameType = '', from = '', redirectUrl } = query;
  /* state */
  const videoRef = useRef<HTMLVideoElement | null>(null);
  /* 视频流 */
  let videoStream: any = null;
  // const [videoStream, setVideoStream] = useState<any>(3434);
  /* MediaRecorder实例 */
  const [recorder, setRecorder] = useState<MediaRecorder | null>(null);
  /* 视频blob */
  // const [videoBlob, setVideoBlob] = useState<Blob[]>([]);
  let videoBlob: any = [];
  /* 是否正在采集视频 */
  const [running, setRunning] = useState<boolean>(false);
  /* 视频采集状态 */
  const [videoState, setVideoState] = useState<number>(0);
  /* 视频采集动作 */
  const [videoGuide, setVideoGuide] = useState<string>('');

  /* 是否重新采集视频 */
  const [isRestart, setIsRestart] = useState<boolean>(false);

  /* 倒计时比例 */
  const [timeProgressRate, setTimeProgressRate] = useState(0);

  const [countdownNum, setCountdownNum] = useState<number>();

  /* 采集时长 */
  const recordTime = 3000;

  /**
   * 结束
   */
  const stop = () => {
    // 关闭媒体流
    if (typeof videoStream?.stop === 'function') {
      videoStream.stop();
    } else {
      const trackList = [videoStream?.getAudioTracks(), videoStream?.getVideoTracks()];
      // eslint-disable-next-line no-plusplus
      for (let i = 0; i < trackList?.length; i++) {
        const tracks = trackList[i];
        if (tracks && tracks.length > 0) {
          // eslint-disable-next-line no-plusplus
          for (let j = 0; j < tracks.length; j++) {
            const track = tracks[j];
            if (typeof track.stop === 'function') {
              track.stop();
            }
          }
        }
      }
    }
    // 重置检测状态
    setRunning(false);
  };

  /**
   * 录制结束回调
   */
  const onStopCallback = async (e) => {
    Toast.loading('加载中');

    if (!videoBlob.length) {
      Toast.fail('视频录制失败！');
      setIsRestart(true);
      setTimeProgressRate(0);
      return;
    }
    // 录像数据
    const totalBlob = new Blob(videoBlob, {
      type: e?.currentTarget?.mimeType,
    });

    // 结束
    stop();

    setVideoState(1);
    // showGuide('视频采集成功，请在控制台查看结果');
    setRunning(false);

    const registerCardInfo = HxSessionStorage.get('registerCardInfo') || {};

    const { tel, credNo, patientName, nationName = '汉族' } = registerCardInfo;

    /* 获取手机型号用于错误捕捉 */
    let phoneType = '';
    try {
      phoneType = getPhone();
    } catch (error) {
      console.log('error:', error);
    }

    const videoType = VIDEO_TYPE.find((i) => totalBlob?.type.includes(i));
    const file = new File([totalBlob], `${new Date().getTime().toString()}${videoType && '.'}${videoType}`, {
      type: totalBlob.type,
      lastModified: Date.now(),
    });
    // const payload = {
    //   bussinessCode,
    //   organCode: getOrganCode(),
    //   channel: getChannelCode(),
    //   phoneType,
    //   realNameType,
    //   file,
    //   realNamePersonInfo: {
    //     phone: tel,
    //     credNo,
    //     name: patientName,
    //     nation: nationName,
    //   },
    // };
    const form_data = new FormData();
    form_data.append('bussinessCode', bussinessCode);
    form_data.append('organCode', getOrganCode());
    form_data.append('channel', getChannelCode());
    form_data.append('phoneType', phoneType);
    form_data.append('realNameType', realNameType);
    form_data.append('file', file);
    form_data.append('realNamePersonInfo.phone', tel);
    form_data.append('realNamePersonInfo.credNo', credNo);
    form_data.append('realNamePersonInfo.name', patientName);
    form_data.append('realNamePersonInfo.nation', nationName);
    /* 转formdata */
    saveHwFaceInfoH5(form_data)
      .then((res) => {
        const { code, msg } = res || {};
        const tryFaceIdTime = HxSessionStorage.get('tryFaceIdTime') || 1;
        if (code === '1') {
          if (res?.data?.realNameId) {
            Toast.success('视频采集成功');
            if (!!from) {
              window.location.replace(`${decodeURIComponent(redirectUrl)}&toRealnameId=${res?.data?.realNameId}`);
            } else {
              window.location.replace(`${callBackUrl}&toRealnameId=${res?.data?.realNameId}`);
            }
          }
        } else {
          const tryFaceIdTime = HxSessionStorage.get('tryFaceIdTime') || 1;

          const { redirect } = qs.parse(window.location.href.split('?')[1]) || {};

          if (tryFaceIdTime > 2) {
            const timer = setTimeout(() => {
              clearTimeout(timer);
              if (from === 'faceCheck') {
                HxModal.show({
                  title: '提示',
                  content: '人脸识别失败，请选择“上传证件”进行实名认证',
                  actions: [
                    {
                      text: '确定',
                      className: 'primary',
                      onClick: () => {
                        Modal.clear();
                        window.history.go(-1);
                      },
                    },
                  ],
                });
                return;
              }
              Toast.info('人脸识别失败，请上传证件照', 2);
              // 上传证件照
              history.replace(`/patientcard/authentification?redirect=${redirect}&realnameType=MANUAL_CHECK`);
              // window.location.replace(callBackUrl);
            }, 2000);
          } else {
            Toast.fail(msg);
          }
          setIsRestart(true);
          setTimeProgressRate(0);
        }
        HxSessionStorage.set('tryFaceIdTime', tryFaceIdTime + 1);
      })
      .catch((e) => {});
  };
  const _onStopCallback = async (e) => {
    Toast.loading('加载中');

    if (!videoBlob.length) {
      Toast.fail('视频录制失败！');
      setIsRestart(true);
      setTimeProgressRate(0);
      return;
    }
    // 录像数据
    const totalBlob = new Blob(videoBlob, {
      type: e?.currentTarget?.mimeType,
    });
    console.log('blob', totalBlob);

    // 转换为Base64
    blobToBase64(totalBlob, (base64Data) => {
      // 结束
      stop();

      setVideoState(1);
      // showGuide('视频采集成功，请在控制台查看结果');
      setRunning(false);

      const registerCardInfo = HxSessionStorage.get('registerCardInfo') || {};

      const { tel, credNo, patientName, nationName = '汉族' } = registerCardInfo;

      /* 获取手机型号用于错误捕捉 */
      let phoneType = '';
      try {
        phoneType = getPhone();
      } catch (error) {
        console.log('error:', error);
      }
      /* 人脸采集完成 */
      dispatch({
        type: 'patientCard/checkAndSaveFaceInfo',
        payload: {
          bussinessCode,
          organCode: getOrganCode(),
          channel: getChannelCode(),
          phoneType,
          realNameType,
          realNamePersonInfo: {
            phone: tel,
            credNo,
            video64: base64Data.replace(/^data.*base64,/, ''), // 去掉视频base64前坠
            name: patientName,
            nation: nationName,
          },
        },
        callback: (res: any) => {
          const { code, msg } = res || {};
          const tryFaceIdTime = HxSessionStorage.get('tryFaceIdTime') || 1;
          if (code === '1') {
            if (res?.data?.realNameId) {
              Toast.success('视频采集成功');
              if (!!from) {
                window.location.replace(`${redirectUrl}&toRealnameId=${res?.data?.realNameId}`);
              } else {
                window.location.href = `${callBackUrl}&toRealnameId=${res?.data?.realNameId}`;
              }
            }
          } else {
            const tryFaceIdTime = HxSessionStorage.get('tryFaceIdTime') || 1;

            const { redirect } = qs.parse(window.location.href.split('?')[1]) || {};

            if (tryFaceIdTime > 2) {
              Toast.info('人脸识别失败，请上传证件照', 2);
              const timer = setTimeout(() => {
                clearTimeout(timer);
                // 上传证件照
                history.replace(`/patientcard/authentification?redirect=${redirect}&realnameType=MANUAL_CHECK`);
                // window.location.replace(callBackUrl);
              }, 2000);
            } else {
              Toast.fail(msg);
            }
            setIsRestart(true);
            setTimeProgressRate(0);
          }
          HxSessionStorage.set('tryFaceIdTime', tryFaceIdTime + 1);
        },
      });
    });
  };

  /**
   * 根据动作码返回引导信息
   * @param {number} action - 动作码
   * @returns
   */
  const getGuideByAction = (action: number) => {
    switch (action) {
      case 1:
        return '请向右摇头';
      case 2:
        return '请向左摇头';
      case 3:
        return '请缓慢点头';
      case 4:
        return '请缓慢张嘴';
      default:
        return '';
    }
  };

  /**
   * 初始化进度
   * @return {*}
   */
  const initProgress = () => {
    setTimeout(() => {
      const _progressRate = timeProgressRate + (200 / 3000) * 100;
      setTimeProgressRate(_progressRate);
    }, 200);
  };
  useEffect(() => {
    if (timeProgressRate <= 100 && timeProgressRate > 0) {
      initProgress();
    }
  }, [timeProgressRate]);

  /**
   * 验证码倒计时
   * @param {*} number 倒计时
   */
  const countDown: (num: number) => void = (num = 60) => {
    num -= 1;
    const Timer: number = window.setTimeout(() => {
      if (num > 1) {
        countDown(num);
      } else {
        setCountdownNum(0);
      }
    }, 1000);
    setCountdownNum(num);
  };

  /**
   * 录制
   */
  function record(_recorder: MediaRecorder) {
    // 显示引导信息
    // showGuide('正在采集视频数据...');
    const guide = getGuideByAction(3);
    setVideoGuide(guide);

    // 延迟0.5秒，给予用户足够的反应时间
    /* 环形进度 */
    initProgress();
    /* 倒计时 */
    countDown(4);
    setTimeout(() => {
      // 清空缓存
      videoBlob = [];

      // 开始录制
      _recorder?.start(recordTime);
      setTimeout(() => {
        // 录制
        _recorder.stop();
      }, recordTime);
    }, 3000);
  }

  /**
   * 开始
   */
  const start = () => {
    setVideoGuide('');
    // 获取用户媒体设备
    getUserMedia(
      {
        video: {
          // 权限
          permissions: {
            'video-capture': {
              description: 'Required to capture video using getUserMedia()',
            },
          },
          // 优先使用前置摄像头
          facingMode: 'user',
          width: {
            ideal: 600,
          },
          height: {
            ideal: 600,
          },
          frameRate: {
            // 最小帧率
            min: 10,
            // 理想帧率
            ideal: 30,
            // 最大帧率
            max: 30,
          },
        },
        // 静音
        audio: false,
      },
      (err, stream) => {
        console.log('err:', err);
        if (err) {
          setVideoState(3);
          setRunning(false);

          /* 重新采集 */
          setIsRestart(true);
          setTimeProgressRate(0);
          Toast.info('人脸识别失败，请上传证件照', 2, () => {
            const { redirect } = qs.parse(window.location.href.split('?')[1]) || {};

            history.replace(`/patientcard/authentification?redirect=${redirect}&realnameType=MANUAL_CHECK`);
          });

          return;
        }

        videoRef.current.srcObject = stream;

        videoRef.current?.play();

        videoStream = stream;

        // 这里涉及到视频的容器以及编解码参数，这个与浏览器有密切的关系
        let options: any = { mimeType: 'video/webm' };
        try {
          if (MediaRecorder.isTypeSupported('video/webm; codecs=vp9')) {
            options = { mimeType: 'video/webm; codecs=vp9' };
          } else if (MediaRecorder.isTypeSupported('video/webm')) {
            options = { mimeType: 'video/webm' };
          } else if (MediaRecorder.isTypeSupported('video/mp4')) {
            options = { mimeType: 'video/mp4', videoBitsPerSecond: 100000 };
          } else {
            console.error('no suitable mimetype found for this device');
          }
        } catch (error) {
          console.log('error:', error);
        }
        const _recorder = new MediaRecorder(stream, options);
        _recorder.ondataavailable = function (e) {
          const _videoBlob = videoBlob;
          _videoBlob.push(e.data);
          videoBlob = _videoBlob;
        };
        _recorder.onstop = onStopCallback;

        setRecorder(_recorder);
        record(_recorder);
      },
    );
  };

  /**
   * 获取提示文案
   * @return {*}
   */
  const tipsText = () => {
    return VIDEO_TIPS.find((i) => i.state === videoState)?.tip;
  };
  /**
   * 初始化并开始采集
   */
  const initAndStart = () => {
    // 防止重复点击
    if (running) {
      return;
    }

    setVideoState(0);

    // 设置为正在检测
    setRunning(true);

    /* 隐藏重新采集按钮 */
    setIsRestart(false);

    /* 视频清空 */
    videoBlob = [];

    // 开始
    start();
  };

  /**
   * 取消采集视频
   * @return {*}
   */
  const stopVideo = () => {
    window.location.replace(callBackUrl);
  };

  useEffect(() => {
    /* 记录人脸采集的次数 */
    HxSessionStorage.get('tryFaceIdTime') || 1;

    initAndStart();
    return () => {
      recorder?.stop();
      setRecorder(null);
      videoRef?.current?.pause();
      // stop();
      videoBlob = [];
      setRunning(false);
      setVideoState(0);
      HxSessionStorage.remove('tryFaceIdTime');
    };
  }, []);

  return (
    <div className={styles.container}>
      <div className={styles.faceWrap}>
        <header>请将人脸移至框内</header>
        <div className={styles.circleContainer}>
          <div className={styles.insideCircle}>
            {running && <video ref={videoRef} muted playsInline webkit-playsinline width="600" height="600" />}
            <div className={styles.head} />
            {!!videoState && (
              <div className={classnames(styles.tips, styles.blue)}>
                <span className={styles.lblTips}>{tipsText()}</span>
              </div>
            )}
          </div>
          <div className={styles.outsideCircleContainer}>
            <div className={classnames(styles.outsideCircle, running && styles.rotating)} />
          </div>
        </div>
        <div className={styles.tipWrap}>
          {timeProgressRate > 0 && timeProgressRate <= 100 ? (
            <div className={styles.progressWrap}>
              <ProgressCircle percent={parseInt(timeProgressRate)} style={{ '--track-width': '6px' }}>
                <div className={styles.progressTime}>{countdownNum}</div>
              </ProgressCircle>
            </div>
          ) : (
            <span className={styles.guide}>{videoGuide}</span>
          )}
        </div>
        <div className={styles.buttons}>
          {isRestart && (
            <div className={styles.button} onClick={initAndStart}>
              重新采集
            </div>
          )}
          {running && (
            <div className={styles.button} onClick={stopVideo}>
              取消
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default connect(({ common }: { common: CommonState }) => ({
  common,
}))(FaceRecognitionHW);
