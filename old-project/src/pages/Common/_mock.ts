import { Request, Response } from 'express';
import { commonSuccessResponse } from '../../../mock/util';

const queryTextList = [
  {
    textCode: 'CASE_MAIL_VERIFY_TEXT',
    textContent:
      '<div style="font-size: 12px; color: #999999; padding: 20px 0" ><div><span>大陆公民持有的</span><span style="color: #32b9aa">本人有效二代身份证</span><span>；</span></div><div><span>请上传</span><span style="color: #32b9aa">该就诊卡对应证件号的真实证件</span><span>。</span><span>(若您上传的证件照信息与就诊卡信息不匹配将不能进行线上邮寄业务)；</span></div><div><span>拍摄时请确保身份证的</span><span style="color: #32b9aa">边框完整，字体清晰，亮度均匀</span><span>；</span></div></div>',
  },
  {
    textCode: 'CASE_MAIL_NOTICE',
    textContent:
      '<div><div>严明济最府是声名委共八严调什引红据示期照受场作级严因代没小调影北最战目对心从深其量选可进青至备会指展过本记合己解即决日织是特位布半除。算包必西京号专式变石改图区放命律直府流月例整与参月不题关料养例观你产六象展按明现准面段后整光现系较心展全新品队。着四指见须查物京毛使龙组较时她军广厂气志话除式建确问更权别织压极报起列实强口信属权。</div><div>条法目下始代都总头况说深切年太种组包自了你内八从之还角东我回采火查子制究当低所生东火今可业取区一验影风维义还导约车什好验非难话特处一平力质开八很。开西就条门边包意清也见记万间低八有利那区极观分改写那段又大说式高见用状资划者林件热法可派开接并受件除带便然线。 度场历个气即往设然金研起委先千示效特红农形府集场严了始社米金比收劳能文么过生定道思行七着或着共治权满。解太生那工始可活积民精听道规得非基资七现群中争王社拉条约再应门水把五道单织世支华单治天年条严运因市。采形当采数是它经党产部生领难美治史消子平全说也选和等导收明而定展法约政况收系土图。天人等开要太低该太但三动少思人的力再系自学第即南林专下领之得律增深保民人命他名或七广各则任该采法府收平真区老和张型东广。</div><div>许际做学能长但能准目更建系化年土要把则先细记需达型约量面按委话器身适已车时素铁布门查干水非持张到。千精有级等格体县保证识些样求量使育油交容大即布办然总周变路格团即受率老写为去再许热计很热角多市个几在万增王斗。</div></div>',
  },
  {
    textCode: 'CASE_MAIL_POPUP_WIN',
    textContent:
      '<div style="text-align:left;color:rgba(0,0,0,0.85)"><div>1、请上传有效期内的真实证件、证件类型：中国居民身份证 </div><div>2、请上传该就诊卡对应证件号的真实证件。若您上传的证件照信息与就诊卡信息不匹配将不能进行线上邮寄业务</div></div>',
  },
];

export default {
  // 文案配置（须知等）
  'POST /cloud//resource/text/query': (req: Request, res: Response) => {
    const { textCode } = req.body;
    const textContent = queryTextList.find((item) => item.textCode === textCode)?.textContent;
    setTimeout(() => {
      res.status(200).send({
        ...commonSuccessResponse,
        data: { textContent },
      });
    }, 500);
  },
  // 人脸识别
  'POST /toFaceRecognition': (req: Request, res: Response) => {
    setTimeout(() => {
      res.status(200).send({
        ...commonSuccessResponse,
      });
    }, 500);
  },
};
