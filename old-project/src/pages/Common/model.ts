/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2021-06-15 16:13:16
 * @LastEditor: <PERSON>
 * @LastEditTime: 2023-12-27 15:00:47
 */
// 一些公共的model
import { Effect, Reducer } from 'umi';
import { CommonState } from './data.d';
import {
  queryText,
  faceRecognition,
  stDeleteFaceInfo,
  stSaveFaceInfo,
  queryTextList,
  getStAccessToken,
  getCheckType,
  saveFaceResult,
} from './service';

interface ModelType {
  namespace: string;

  state: CommonState;

  effects: {
    /** 配置文案 */
    queryText: Effect;
    /** 人脸识别 */
    faceRecognition: Effect;
    stDeleteFaceInfo: Effect;
    stSaveFaceInfo: Effect;
    queryTextList: Effect;
    getStAccessToken: Effect;
    /* 获取人脸识别类型 */
    queryGetCheckType: Effect;
    /* 保存人脸识别 */
    querySaveFaceResult: Effect;
  };

  reducers: {
    updateState: Reducer;
  };
}

const Model: ModelType = {
  namespace: 'common',

  state: {},

  effects: {
    *queryText({ payload, callback }, { call }) {
      const data = yield call(queryText, payload);
      const { textContent } = data || {};
      callback && callback(textContent);
    },
    *queryTextList({ payload, callback }, { call }) {
      const data = yield call(queryTextList, payload);
      const { texts } = data || {};
      callback && callback(texts);
    },
    *faceRecognition({ payload, callback }, { call }) {
      const res = yield call(faceRecognition, payload);
      const { code } = res || {};
      callback && callback(code);
    },
    *stSaveFaceInfo({ payload, callback }, { call }) {
      const res = yield call(stSaveFaceInfo, payload);
      const { code } = res || {};
      callback && callback(code);
    },
    *stDeleteFaceInfo({ payload, callback }, { call }) {
      const res = yield call(stDeleteFaceInfo, payload);
      const { code } = res || {};
      callback && callback(code);
    },
    *getStAccessToken({ payload, callback }, { call }) {
      const res = yield call(getStAccessToken, payload);
      callback && callback(res);
    },
    *queryGetCheckType({ payload, callback }, { call }) {
      const res = yield call(getCheckType, payload);
      const { code, data } = res || {};
      code === '1' && callback && callback(data);
    },
    *querySaveFaceResult({ payload, callback }, { call }) {
      const res = yield call(saveFaceResult, payload);
      const { code } = res || {};
      code === '1' && callback && callback(res);
    },
  },

  reducers: {
    updateState(state, { payload }) {
      return {
        ...state,
        ...payload,
      };
    },
  },
};

export default Model;
