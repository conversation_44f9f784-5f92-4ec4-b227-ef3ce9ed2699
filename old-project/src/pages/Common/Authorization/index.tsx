import React, { FC } from 'react';
import { history, Dispatch, connect, IGlobalModelState, IChronicDiseaseModelState, useLocation } from 'umi';
import { Toast } from 'antd-mobile';
// import { decode } from 'js-base64';
import AppScheme from '@/utils/AppScheme';
import styles from './index.less';
import { HXMG } from './logic';
import { pageShowData } from './dataDictionary';

const logo = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/hytlogo.png';

interface IPageProps {
  dispatch: Dispatch;
  global: IGlobalModelState;
  authorization: IChronicDiseaseModelState;
}

const CommonAuthorization: FC<IPageProps> = ({ dispatch }) => {
  const {
    query: { authServeCode = '', data = '{}', content = '' },
  }: any = useLocation();

  const onHandleClick = (isAgreen: boolean = false) => {
    switch (authServeCode) {
      case 'HXMG':
        HXMG(isAgreen, dispatch, content);
        break;

      default:
        break;
    }
  };

  const goToAgreement = () => {
    switch (authServeCode) {
      case 'HXMG':
        // eslint-disable-next-line no-case-declarations
        const url = pageShowData.find((i) => i.key === 'HXMG')?.agreementUrl;
        window.location.href = url || '';
        break;
      default:
        break;
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.info}>
        <img src={logo} alt="" className={styles.bgImage} />
        <div className={styles.right}>
          <div>
            该服务由<span>{pageShowData.find((i) => i.key === authServeCode)?.name}</span>提供，
          </div>
          <div>需要获取以下权限：</div>
        </div>
      </div>
      <div className={styles.content}>
        <ul>
          <li>获取您的公开信息（昵称、头像、性别等）</li>
          <li>使用您的身份信息办理业务（姓名、身份证、手机号）</li>
        </ul>
      </div>
      <div className={styles.agreementArea}>
        确认授权即视为本人知情并同意<span onClick={() => goToAgreement()}>《用户授权协议》</span>
      </div>
      <div className={styles.btnBox}>
        <div onClick={() => onHandleClick(true)}>确认授权</div>
        <div onClick={() => onHandleClick(false)}>暂不授权</div>
      </div>
    </div>
  );
};

export default connect(({ chronicDisease }: { chronicDisease: IChronicDiseaseModelState }) => ({
  chronicDisease,
}))(CommonAuthorization);
