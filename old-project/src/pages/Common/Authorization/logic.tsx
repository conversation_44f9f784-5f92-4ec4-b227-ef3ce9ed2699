import React from 'react';
import { Dispatch, history } from 'umi';
import { Toast } from 'antd-mobile';
import { isHytPerson, isHytDoctor } from '@/utils/platform';
import AppScheme from '@/utils/AppScheme';

/**
 * 华西乳腺业务授权
 * @param dispatch
 * @param content 第三方乳腺页面需要拼的参数
 * @param isAgreen 授权按钮
 */
const HXMG = (isAgreen: boolean = false, dispatch: Dispatch, content: String = '') => {
  // 点击授权
  const auth = () => {
    dispatch({
      type: 'authorization/authorize',
      payload: {
        businessCode: 'HXRX',
        clientId: 'HXRX',
        isAgree: 1,
        redirectURL: '1',
      },
      callback: (res: any = {}) => {
        console.log('res:', res);
        const { code = '' } = res;
        if (code) {
          Toast.success('授权成功！');
          /* 跳转到三方乳腺地址 */
          window.location.href = `${HXRX_URL}?content=${content}`;
        }
      },
    });
  };
  if (!isAgreen) {
    Toast.info('您已拒绝授权', 1);
    const timer = setTimeout(() => {
      clearTimeout(timer);

      if (isHytPerson() || isHytDoctor()) {
        AppScheme.closeWeb();
      } else {
        history.goBack();
      }
    }, 1000);
  } else {
    auth();
  }
};

export { HXMG };
