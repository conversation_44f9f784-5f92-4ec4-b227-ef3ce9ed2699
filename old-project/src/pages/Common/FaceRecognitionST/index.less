.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 2.13rem;

  .notice {
    color: #03081a;
    font-weight: 500;
    font-size: 1.71rem;
    text-align: center;
  }

  .self {
    font-size: 1rem;
  }

  .phone {
    font-size: 1.71rem;
  }

  .name {
    margin: 0.75rem;
    color: #03081a;
    font-weight: 400;

    > span {
      color: #32b9aa;
    }
  }

  .face {
    height: 21.5rem;
    text-align: center;

    video {
      width: 100%;
      object-fit: fill;
      visibility: hidden;
    }

    canvas {
      width: 20rem;
      height: 20rem;
      margin-left: 5px;
      border: 0.5rem solid #3ad3c1;
      border-radius: 50%;
    }
  }

  .icon {
    width: 9.29rem;
    margin: 0.75rem;
  }
}
