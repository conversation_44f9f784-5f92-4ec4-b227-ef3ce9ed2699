/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2021-11-17 20:03:02
 * @LastEditor: <PERSON>
 * @LastEditTime: 2022-02-14 10:54:59
 */

import axios from 'axios';
import QueryString from 'qs';
import { query } from '@/pages/PatientPerson/service';
import { creatSgin } from './fun';

const Token = null;
const Host = 'http://xxzx.wchscu.cn';
const Code = 'hytSys';
const Secret = 'M4SwZaFIozbosEpZd8';
// const Strs = null;
// const TimeOut = 10000;
const OperatePerson = 'hytH5';

// 公安部认证接口
const identityValidation = function (name, idNumber, image, call) {
  if (name === '' || idNumber === '' || image === '') {
    return call({
      success: false,
      msg: '参数错误',
    });
  }

  const data = {
    name,
    identityNo: idNumber,
    image,
    token: Token,
  };

  axios({
    method: 'post',
    url: `${Host}/face/identityh5`,
    data: QueryString.stringify({ jsonStr: JSON.stringify(data) }),
  }).then((result) => {
    console.log(result);
    if (result.code === '200') {
      return call({
        success: true,
        msg: result.msg,
        data: result.data,
      });
    }
    return call({
      success: false,
      msg: result.msg,
    });
  });
};

//  1:1对比
const compareImages = function (firstImage, secondImage, call) {
  const data = {
    firstData: firstImage,
    secondData: secondImage,
  };

  const url = `${Host}/h5/compareImages`;
  axios({
    method: 'post',
    url,
    data: QueryString.stringify({ jsonStr: JSON.stringify(data) }),
  })
    .then((result) => {
      const res = JSON.parse(JSON.parse(result.data));
      if (res.code === '0000' && res.data.result[0].pass) {
        return call({
          success: true,
          msg: `1:1对比成功, 相似度：${res.data.result[0].similarity}`,
        });
      }
      return call({
        success: false,
        msg: `1:1对比失败, 相似度：${res.data.result[0].similarity}`,
      });
    })
    .catch((err) => {});
};

// 获取人脸识别登录凭证
const getToken = function (call) {
  const ts = Date.parse(new Date().toString()) / 1000;
  const Strs = Code + Secret + ts;
  const getBase64Url = `${Host}/face/getbase64Code?code=${encodeURIComponent(Strs)}`;

  axios({
    method: 'POST',
    url: getBase64Url,
    data: null,
  }).then((base64Result) => {
    if (base64Result.code === '1') {
      const base64Str = base64Result.data;
      const encryptionCode = `${Code},${Secret},${ts},${base64Str}`;
      const getTokenUrl = `${Host}/face/login?encryptionCode=${encodeURIComponent(encryptionCode)}`;
      axios({
        method: 'POST',
        url: getTokenUrl,
        data: {},
      }).then((tokenResult) => {
        if (tokenResult.code === '1') {
          return call(tokenResult.data);
        }
        return call('');
      });
    } else {
      return call('');
    }
  });
};

// 人员入库
const createPerson = function (name, idNumber, image, call) {
  if (name === '' || idNumber === '' || image === '') {
    return call({
      success: false,
      msg: '参数错误',
    });
  }

  getToken(function (token) {
    if (token === '') {
      return call({
        success: false,
        msg: '初始化FaceService失败',
      });
    }

    // 人员入库
    const data = {
      cnName: name,
      idNumber,
      imageContent: image,
      imageName: `${idNumber}.png`,
      token,
      operatePerson: OperatePerson,
    };

    const url = `${Host}/face/createPersonh5`;

    axios({
      method: 'POST',
      url,
      data: QueryString.stringify({ jsonStr: JSON.stringify(data) }),
    }).then((result) => {
      if (result.code === '200') {
        return call({
          success: true,
          msg: result.msg,
          data: result.data,
        });
      }
      return call({
        success: false,
        msg: result.msg,
      });
    });
  });
};

// 更新人员底库
const updatePersonData = function (name, idNumber, livebodyImage, call) {
  getToken(function (token) {
    if (token === '') {
      call({
        success: false,
        msg: '初始化FaceService失败',
      });
    }

    // 调用更新人员接口
    const updatePersonData = {
      cnName: name,
      idNumber,
      isUpdateImage: '0',
      imageContent: livebodyImage,
      imageName: `${idNumber}.png`,
      token,
      operatePerson: OperatePerson,
    };

    const url = `${Host}/face/updatePersonh5`;
    axios({
      method: 'POST',
      url,
      data: { jsonStr: JSON.stringify(updatePersonData) },
    }).then((result) => {
      if (result.code === '200') {
        return call({
          success: true,
          msg: result.msg,
          data: result.data,
        });
      }
      return call({
        success: false,
        msg: result.msg,
      });
    });
  });
};

// 更新人员
const updatePerson = function (name, idNumber, livebodyImage, approve = '1', call) {
  if (name === '' || idNumber === '' || livebodyImage === '') {
    return call({
      success: false,
      msg: '参数错误',
    });
  }

  switch (approve) {
    case '1':
      identityValidation(name, idNumber, livebodyImage, function (obj) {
        if (obj.success) {
          updatePersonData(name, idNumber, livebodyImage, call);
        } else {
          return call({
            success: false,
            msg: obj.msg,
          });
        }
      });
      break;
    default:
      getToken(function (token) {
        if (token === '') {
          return call({
            success: false,
            msg: '初始化FaceService失败',
          });
        }
        // 获取底图
        const url = `${Host}/face/getFaceImage?idNumber=${idNumber}&token=${token}`;
        axios({
          method: 'POST',
          url,
          data: {},
        }).then((res) => {
          if (res.code === '1' && res.data !== '') {
            const oldImage = res.data;
            // 1:1 对比
            compareImages(livebodyImage, oldImage, function (obj) {
              if (!obj.success) {
                return call({
                  success: false,
                  msg: obj.msg,
                });
              }
              // 更新人员
              updatePersonData(name, idNumber, livebodyImage, call);
            });
          } else {
            return call({
              success: false,
              msg: '人员底库不存在',
            });
          }
        });
      });
      break;
  }
};

// 删除人员底库
const deletePersonData = function (idNumber, call) {
  getToken(function (token) {
    if (token === '') {
      call({
        success: false,
        msg: '初始化FaceService失败',
      });
    }
    const url = `${Host}/face/deletePersonByIdNumber?idNumber=${idNumber}&operatePerson=${OperatePerson}&token=${token}`;
    axios({
      method: 'POST',
      url,
      data: {},
    }).then((result) => {
      if (result.code === '200') {
        return call({
          success: true,
          msg: result.msg,
          data: result.data,
        });
      }
      return call({
        success: false,
        msg: result.msg,
      });
    });
  });
};

// 删除人员
const deletePerson = function (name, idNumber, livebodyImage, approve = '1', call) {
  if (name === '' || idNumber === '' || livebodyImage === '') {
    return call({
      success: false,
      msg: '参数错误',
    });
  }

  switch (approve) {
    case '1':
      identityValidation(name, idNumber, livebodyImage, function (obj) {
        if (obj.success) {
          deletePersonData(idNumber, call);
        } else {
          return call({
            success: false,
            msg: obj.msg,
          });
        }
      });
      break;
    default:
      getToken(function (token) {
        if (token === '') {
          return call({
            success: false,
            msg: '初始化FaceService失败',
          });
        }
        // 获取底图
        const url = `${Host}/face/getFaceImage?idNumber=${idNumber}&token=${token}`;
        axios({
          method: 'POST',
          url,
          data: {},
        }).then((res) => {
          if (res.code === '1' && res.data !== '') {
            const oldImage = res.data;
            // 1:1 对比
            compareImages(livebodyImage, oldImage, function (obj) {
              if (!obj.success) {
                return call({
                  success: false,
                  msg: obj.msg,
                });
              }
              // 删除人员
              deletePersonData(idNumber, call);
            });
          } else {
            return call({
              success: false,
              msg: '人员底库不存在',
            });
          }
        });
      });
      break;
  }
};

// H5活体检测
const livingBodyValidation = function (imageBase64, requestId, actions = null, call) {
  const url = `${Host}/h5/livingBodyValidation`;
  const ts = new Date().getTime();

  const data = {
    requestId,
    actions,
    images: [
      {
        imageBase64,
        ts,
      },
    ],
    extraInfo: false,
  };
  const json = creatSgin(data);
  console.log('livingBodyValidation');
  const contentData = QueryString.stringify({ jsonStr: JSON.stringify(json) });

  axios({
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
    method: 'POST',
    url,
    data: contentData,
  }).then((res) => {
    console.log(res);
    const result = JSON.parse(JSON.parse(res.data));
    console.log('123123123123123', result);
    call(result);
  });
};

// ocr识别
const ocrValidation = function (imageBase64, call) {
  const data = {
    data: imageBase64,
  };
  const url = `${Host}/h5/ocr`;

  axios({
    method: 'POST',
    url,
    data: QueryString.stringify({ jsonStr: JSON.stringify(data) }),
  }).then((res) => {
    res = JSON.parse(JSON.parse(res));
    console.log(res);
    if (res.code === '0000') {
      return call({
        success: true,
        msg: '识别成功',
        data: res.data,
      });
    }
    return call({
      success: false,
      msg: `识别失败：${res.msg}`,
    });
  });
};

export { getToken, createPerson, updatePerson, deletePerson, identityValidation, livingBodyValidation, ocrValidation };
