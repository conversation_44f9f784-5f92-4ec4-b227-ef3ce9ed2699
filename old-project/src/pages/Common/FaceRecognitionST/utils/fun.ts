/*
 * @Description:
 * @Author: <PERSON>
 * @Date: 2021-11-18 14:55:08
 * @LastEditor: <PERSON>
 * @LastEditTime: 2021-11-18 16:53:14
 */
// 获取活体请求签名

import JSEncrypt from 'jsencrypt';
import md5 from 'blueimp-md5';

function imageEntityMd5(entity) {
  const imageMd5 = md5(entity.imageBase64);
  return imageMd5 + entity.ts;
}

function stringMd5(value) {
  return md5(value);
}

function creatSgin(json) {
  const md5Array = [];
  for (let i = 0; i < json.images.length; i++) {
    const md5 = imageEntityMd5(json.images[i]);
    md5Array[i] = md5;
  }

  md5Array.sort();

  let buffer = `requestId=${json.requestId}&`;

  if (json.actions != null) {
    buffer += 'actions=';
    for (let i = 0; i < json.actions.length; i++) {
      buffer += json.actions[i];
    }
    buffer += '&';
  }

  if (json.extraInfo != null) {
    buffer += `extraInfo=${json.extraInfo}&`;
  }

  for (let i = 0; i < md5Array.length; i++) {
    buffer += `image=${md5Array[i]}`;

    if (i != md5Array.length - 1) {
      buffer += '&';
    }
  }

  const md5 = stringMd5(buffer);

  const key =
    'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAgaNJRYD1prXyns/2L35SMITkh0AvG/9Zs65PRqCJrmmuyzFJrYm5rFTXnu3VRc0nEaBlypYGqt4fyL6bUMP02bsBinddpnAr+Q8QVk1vKYxbjn5ImNdn1LV+KCAXRurRhnj/qyEi6iXGvx8KOf0s8n3c8+FmDQgLT0R2BrMgbiaK3HNx0O8/O5Jjn4fZ09A9y0oAeVRH2x3rJht7qtdbb198eiIxU2XQTwS/bVhJZ27cmjy3QeGLtzQNtPKzK7C61WL22RCaL3JsJybrWPbXsnBvN5+rMlRSCTb2tnCfdMYpylcW7ltkAkmmrBw9RwEmLTAgYkVqtNSi6Fg5gt1ErwIDAQAB';

  const Encrypt = new JSEncrypt();
  Encrypt.setPublicKey(key);
  const sgin = Encrypt.encrypt(md5);

  json.sgin = sgin;

  return json;
}

// 第一个数组减去第二个数组
function arr_dive(aArr, bArr) {
  if (bArr.length === 0) {
    return aArr;
  }
  const diff = [];
  const str = bArr.join('&quot;&quot;');
  for (const e in aArr) {
    if (str.indexOf(aArr[e]) === -1) {
      diff.push(aArr[e]);
    }
  }
  return diff;
}

export { creatSgin, arr_dive, imageEntityMd5 };
