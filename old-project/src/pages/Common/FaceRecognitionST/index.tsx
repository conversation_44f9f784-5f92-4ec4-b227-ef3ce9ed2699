import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Toast, Modal } from 'antd-mobile';
import { connect, Dispatch, Loading } from 'umi';
import { CloseCircleFilled, CheckCircleFilled } from '@ant-design/icons';
import styles from './index.less';
import { CommonState } from '../data.d';
import { identityValidation, create<PERSON>erson, deletePerson, update<PERSON>erson, livingBodyValidation } from './utils/face';
import { arr_dive } from './utils/fun';
import { doZYJL } from '../FaceRecognition/logic';
import { getOrganCode } from '@/utils/parameter';

const holdPhone = require('@/assets/FaceRecognition/hold_phone.png');

const actionData = {
  1: '请眨眼',
  2: '请张嘴',
  3: '请点头',
  4: '请摇头',
};

// 动作序列 1234分别代表眨眼、张嘴、点头、摇头 可以自由组合,动作不可重复
const randActions = [
  // [1,2],
  // [1,3],
  // [1,4],
  [2, 3],
  [2, 4],
  [3, 4],
  // [1,2,3],
  // [1,2,4],
  [2, 3, 4],
  // [1,2,3,4]
];

const ResultCode = {
  '66010801': '非法请求',
  '66010204': '预检超时',
  '66010608': '检测目标发生变化',
  '66010205': '动作检测超时',
  '66010606': '无人脸',
  '66010604': '质量不通过',
  '66010609': '眨眼检测通过',
  '66010610': '张嘴检测通过',
  '66010611': '点头检测通过',
  '66010612': '摇头检测通过',
  '66010613': '预检通过',
  '66010614': '动作检测失败',
  '66010615': '活体检测失败',
  '66010616': '多人脸',
  '66010617': '多人脸中止',
  '66010605': '检测结果未生成',
  '66010401': '没有调用权限：未授权、被管理员禁用',
  '66010402': '授权验证有误',
  '66010403': '授权过期,请获取授权信息',
  '66010501': '请获取token信息',
  '66010502': '无效的token信息：key 和 value不匹配',
  '66010301': '调用地址不存在',
  '66010201': '参数必填（**指代具体参数字段）',
  '66010202': '非法参数类型',
  '66010203': '参数解析异常（**指代具体参数字段）',
  '66010601': '非法图片类型',
  '66010602': '非法图片尺寸',
  '66010603': '图片解析错误',
  '66990101': '服务内部错误',
  '0000': '认证成功',
};

interface FaceRecognitionProps {
  dispatch: Dispatch;
  submitLoading: boolean | undefined;
  location: {
    query: {
      /** 就诊卡ID */
      cardId?: string;
      /** 下一个页面标识 */
      to: string;
      /** 操作标识 */
      operation: string;
      /** 识别人姓名 */
      name: string;
      credNo: string;
      redirect: string;
    };
  };
}

const FaceRecognition: React.FC<FaceRecognitionProps> = (props) => {
  // props
  const {
    dispatch,
    location: {
      query: { operation = 'create', to = 'ƒ', cardId = '', name = '', credNo = '', redirect = '' },
    },
  } = props;

  // state
  const [noticeStr, setNoticeStr] = useState<String>('请保持在人脸框内');

  const videoStreamRef = useRef<any>(null);

  useEffect(() => {
    switch (operation) {
      case 'CREATE':
      case 'CREATE_CARD':
        openCamera(doCreatePerson);
        break;

      case 'UPDATE':
        openCamera(doUpdatePerson);
        break;

      case 'DELETE':
        openCamera(doDeletePerson);
        break;

      default:
        break;
    }
  }, [credNo]);

  useEffect(() => {
    dispatch({
      type: 'common/getStAccessToken',
      payload: {
        organCode: getOrganCode(),
      },
      callback(res) {
        console.log(res);
      },
    });

    return () => {
      stop();
    };
  }, []);

  const afterFailed = () => {
    if (redirect) {
      window.location.href = redirect;
    }
  };

  /** 人脸识别成功后的操作 */
  const afterSuccess = (data) => {
    if (redirect) {
      window.location.href = `${redirect}&BizToken=${data.requestId}`;
      return;
    }
    switch (to) {
      case 'ZYJL': // 进入住院记录页面
        doZYJL(cardId);
        break;

      default:
        break;
    }
  };

  // 测试人员数据
  let LivingImgae = null;

  function doCreatePerson(imageBase64) {
    if (LivingImgae != null) {
      return;
    }
    // 活体认证
    LivingImgae = imageBase64; // 活体认证图片
    // 公安部认证
    identityValidation(name, credNo, LivingImgae, function (obj) {
      if (!obj.success) {
        Toast.fail(obj.msg);
        afterFailed();
      } else {
        createPerson(name, credNo, LivingImgae, function (obj) {
          if (!obj.success) {
            // 失败
            Toast.fail(obj.msg);
            afterFailed();
            return;
          }

          if (operation === 'CREATE') {
            dispatch({
              type: 'common/stSaveFaceInfo',
              payload: {},
              callback: (code) => {
                if (code === '1') {
                  afterSuccess();
                } else {
                  Modal.alert(
                    <div>
                      <CloseCircleFilled style={{ color: '#ea5559' }} className={styles.modalIcon} />
                      <span>创建失败</span>
                    </div>,
                    [
                      {
                        text: '重试',
                        onPress: () => {
                          afterFailed();
                        },
                      },
                    ],
                  );
                }
              },
            });
          }
          console.log(obj);
        });
      }
    });
  }

  function doUpdatePerson(imageBase64) {
    // 活体认证
    if (LivingImgae != null) {
      return;
    }
    LivingImgae = imageBase64;

    updatePerson(name, credNo, LivingImgae, '0', function (obj) {
      if (!obj.success) {
        // 失败
        Toast.fail(obj.msg);
        afterFailed();
        return;
      }
      // 更新成功
      dispatch({
        type: 'common/stSaveFaceInfo',
        payload: {},
        callback: (code) => {
          if (code === '1') {
            afterSuccess();
          } else {
            Modal.alert(
              <div>
                <CloseCircleFilled style={{ color: '#ea5559' }} className={styles.modalIcon} />
                <span>更新失败</span>
              </div>,
              [
                {
                  text: '重试',
                  onPress: () => {
                    afterFailed();
                  },
                },
              ],
            );
          }
        },
      });
      console.log(obj);
    });
  }

  function doDeletePerson(imageBase64) {
    // 活体认证
    if (LivingImgae != null) {
      return;
    }
    LivingImgae = imageBase64;
    deletePerson(name, credNo, LivingImgae, '0', function (obj) {
      if (!obj.success) {
        // 失败
        Toast.fail(obj.msg);
        afterFailed();
        return;
      }
      dispatch({
        type: 'common/stDeleteFaceInfo',
        payload: {},
        callback: (code) => {
          if (code === '1') {
            afterSuccess();
          } else {
            Modal.alert(
              <div>
                <CloseCircleFilled style={{ color: '#ea5559' }} className={styles.modalIcon} />
                <span>删除失败</span>
              </div>,
              [
                {
                  text: '重试',
                  onPress: () => {
                    afterFailed();
                  },
                },
              ],
            );
          }
        },
      });
    });
  }

  const stop = () => {
    if (videoStreamRef.current) {
      // 关闭媒体流
      if (typeof videoStreamRef.current?.stop === 'function') {
        videoStreamRef.current?.stop();
      } else {
        const trackList = [videoStreamRef.current?.getAudioTracks(), videoStreamRef.current?.getVideoTracks()];
        // eslint-disable-next-line no-plusplus
        for (let i = 0; i < trackList?.length; i++) {
          const tracks = trackList[i];
          if (tracks && tracks.length > 0) {
            // eslint-disable-next-line no-plusplus
            for (let j = 0; j < tracks.length; j++) {
              const track = tracks[j];
              if (typeof track.stop === 'function') {
                track.stop();
              }
            }
          }
        }
      }
    }
    Timer && clearInterval(Timer);
  };

  let call = null; // 回调函数
  let Timer; // 定时任务
  let Die = false;

  // 打开摄像头
  function openCamera(fun) {
    call = fun;
    const constraints = {
      audio: false, // 是否开启麦克风
      video: {
        facingMode: 'user', // 默认前置摄像头
        width: 480,
        height: 480,
      },
    };

    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
      // 标准API
      navigator.mediaDevices
        .getUserMedia(constraints)
        .then((mediaStream) => {
          videoStreamRef.current = mediaStream;
          getVideoStream(mediaStream);
        })
        .catch((error) => {
          console.log(error);
        });
    } else if (navigator.webkitGetUserMedia) {
      // webkit核心浏览器
      navigator.webkitGetUserMedia(constraints, getVideoStream, getFail);
    } else if (navigator.mozGetUserMedia) {
      // firfox浏览器
      navigator.mozGetUserMedia(constraints, getVideoStream, getFail);
    } else if (navigator.getUserMedia) {
      // 旧版API
      navigator.getUserMedia(constraints, getVideoStream, getFail);
    } else {
      console.log('不支持摄像头调用！');
    }
  }

  function startLivingValidate() {
    // 截取图片活体认证
    const _requestId = new Date().getTime(); // 检查请求ID
    const _actions_success = []; // 已经检查完的步骤
    const _actions = randActions[Math.floor(Math.random() * randActions.length)]; // 待检查的步骤
    Timer = setInterval(() => {
      // 开始取帧图片
      getFrameImg(_requestId, Timer, _actions, _actions_success);
    }, 150);
  }

  // 成功开启摄像头的回调
  function getVideoStream(stream) {
    const video = document.getElementById('video');
    const canvas = document.getElementById('canvas_show');
    const context = canvas.getContext('2d');

    try {
      window.stream = stream;
      video.srcObject = stream;
    } catch (error) {
      video.src = window.URL.createObjectURL(stream);
    }

    video.play();

    setTimeout(() => {
      drawVideoAtCanvas(canvas, video, context);
    }, 300);

    startLivingValidate();
  }

  // 开启摄像头失败的回调
  function getFail() {
    console.log('摄像头失败');
    console.log('不支持摄像头调用！');
  }

  // 将视频流渲染至canvas
  function drawVideoAtCanvas(canvas, video, context) {
    const focalRatio = 1.2; // 相机焦距比例 建议1.0-1.5之间 数值越大 焦距越大
    const showCanvasWidth = 400; // 需要显示视频流的画布宽度
    const showCanvasHeight = 400; // 需要显示视频流的画布高度
    canvas.width = showCanvasWidth;
    canvas.height = showCanvasHeight;

    window.setInterval(() => {
      context.drawImage(video, 0, 0, showCanvasWidth * focalRatio, showCanvasHeight * focalRatio);
    }, 60);
  }

  // 截取图片活体认证
  function getFrameImg(requestId, Timer, actions, actions_success) {
    const canvas1 = document.getElementById('canvas_show');
    const dataURL1 = canvas1.toDataURL('image/jpeg'); // base64帧图片
    const imageBase64 = dataURL1.split(',')[1];

    // 活体认证
    livingBodyValidation(imageBase64, requestId, actions, function (result) {
      if (result.code === '66010604') {
        // $('#msg').html('请保证画面光线适宜、清晰无遮挡');
        setNoticeStr('请保证画面光线适宜、清晰无遮挡');
      }

      if (result.code === '66010605') {
        if (actions_success.length === 0) {
          // $('#msg').html(actionData[actions[0]]);
          setNoticeStr(actionData[actions[0]]);
        } else {
          const diff = arr_dive(actions, actions_success);
          // $('#msg').html(actionData[diff[0]]);
          setNoticeStr(actionData[diff[0]]);
        }
      }

      if (result.code === '66010609') {
        if (actions_success.indexOf(1) === -1) {
          actions_success.push(1);
        }
      }
      if (result.code === '66010610') {
        if (actions_success.indexOf(2) === -1) {
          actions_success.push(2);
        }
      }
      if (result.code === '66010611') {
        if (actions_success.indexOf(3) === -1) {
          actions_success.push(3);
        }
      }
      if (result.code === '66010612') {
        if (actions_success.indexOf(4) === -1) {
          actions_success.push(4);
        }
      }

      if (result.code === '66010606') {
        // $('#msg').text(ResultCode[result.code]);
        setNoticeStr(ResultCode[result.code]);
      }

      if (
        (result.code === '66010204' ||
          result.code === '66010205' ||
          result.code === '66010614' ||
          result.code === '66010615' ||
          result.code === '66010617') &&
        !Die
      ) {
        alert(ResultCode[result.code]);
        clearInterval(Timer);
        // $('#msg').hide();
        setNoticeStr('请保持在人脸框内');
        Die = true;
        startLivingValidate();
        return false;
      }

      if (result.code === '0000') {
        console.log(result);
        clearInterval(Timer);
        // $('#canvas_show').hide();
        // $('#livingBodyImage').show();
        // $('#msg').html(`识别结果：${ResultCode[result.code]}`);
        setNoticeStr(`识别结果：${ResultCode[result.code]}`);
        // $('#livingBodyImage').attr('src', `data:image/jpg;base64,${result.data.previewImageBase64}`);
        call(result.data.previewImageBase64);
      }
    });
  }

  return (
    <div className={styles.container}>
      <div className={styles.notice}>{noticeStr}</div>
      <div className={styles.self}>
        <div className={styles.name}>
          确认是 <span>*{name.slice(1)}</span> 本人操作
        </div>
      </div>
      <div className={styles.face}>
        <canvas id="canvas_show" />
        <video id="video" autoPlay x5-video-player-type="h5" className="pic_video" />
      </div>
      <div className={styles.phone}>
        <div className={styles.name}>
          请手持手机<span>对准镜头</span>
        </div>
      </div>
      <img className={styles.icon} src={holdPhone} alt="" />
    </div>
  );
};

export default connect(({ common }: { common: CommonState }) => ({
  common,
}))(FaceRecognition);
