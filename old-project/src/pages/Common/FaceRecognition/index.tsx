import React, { useEffect, useState } from 'react';
import { Button, Toast, Modal } from 'antd-mobile';
import { connect, Dispatch, Loading } from 'umi';
import { CloseCircleFilled, CheckCircleFilled } from '@ant-design/icons';
import styles from './index.less';
import { CommonState } from '../data.d';
import { doZYJL } from './logic';

const face_bg = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/face-recognition/face_bg.png';
const face_user = 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/face-recognition/face_user.png';
const exampleImgs = [
  'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/face-recognition/face_more_action.png',
  'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/face-recognition/face_reflective.png',
  'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/face-recognition/face_blurry.png',
];

interface FaceRecognitionProps {
  dispatch: Dispatch;
  submitLoading: boolean | undefined;
  location: {
    query: {
      /** 就诊卡ID */
      cardId?: string;
      /** 下一个页面标识 */
      to: string;
      /** 识别人姓名 */
      name: string;
    };
  };
}

const FaceRecognition: React.FC<FaceRecognitionProps> = (props) => {
  // props
  const {
    dispatch,
    submitLoading,
    location: {
      query: { to = 'ƒ', cardId = '', name = '' },
    },
  } = props;

  // state
  const [videoSrc, setVideoSrc] = useState<any>(null);
  const [submitVisible, setSubmitVisible] = useState(true);
  const [successVisible, setSuccessVisible] = useState(false);

  useEffect(() => {
    submitLoading && Toast.loading('上传中');
  }, [submitLoading]);

  const onUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { files } = event.target;
    console.log('onUpload', files);
    if (files && files.length > 0) {
      const file = files[0];
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.addEventListener('loadend', () => {
        setVideoSrc(reader.result);
        // console.log('videoSrc', reader.result);
      });
    } else {
      Toast.info('请重新上传视频');
    }
  };

  /** 人脸识别成功后的操作 */
  const afterSuccess = () => {
    switch (to) {
      case 'ZYJL': // 进入住院记录页面
        doZYJL(cardId);
        break;

      default:
        break;
    }
  };

  const onSubmit = () => {
    setSubmitVisible(false);
    dispatch({
      type: 'common/faceRecognition',
      payload: {},
      callback: (code) => {
        if (code === '1') {
          setSuccessVisible(true);
          // 进入住院记录列表页面
          afterSuccess();
        } else {
          Modal.alert(
            <div>
              <CloseCircleFilled style={{ color: '#ea5559' }} className={styles.modalIcon} />
              <span>审核未通过</span>
            </div>,
            <div style={{ color: 'rgba(0,0,0,0.85);' }}>
              返回SKD字段大师未发货反倒是发手机号的斯卡迪sad啥课的按时等等奥斯卡就打开了撒大家奥斯卡了就看到静{' '}
            </div>,
            [
              { text: '关闭', onPress: () => {} },
              {
                text: '重新验证',
                onPress: () => {
                  setVideoSrc(null);
                },
              },
            ],
          );
        }
      },
    });
  };

  return (
    <div>
      {videoSrc ? (
        <div className={styles.videoContainer}>
          <div className={styles.videoDiv}>
            <video src={videoSrc} controls="controls">
              <track default kind="captions" srcLang="en" src={videoSrc} />
              该浏览器暂不支持video
            </video>
          </div>
          {submitVisible && (
            <div className={styles.submitButton}>
              <Button type="primary" style={{ color: '#ffffff' }} onClick={onSubmit}>
                提交
              </Button>
            </div>
          )}
          CheckCircleFilled
          <Modal
            className={styles.successModal}
            visible={successVisible}
            title={
              <div className={styles.successTitle}>
                <CheckCircleFilled style={{ color: '#5db6aa' }} className={styles.modalIcon} />
                <span>审核通过</span>
              </div>
            }
            transparent
          >
            <div />
          </Modal>
        </div>
      ) : (
        <div className={styles.container}>
          <img className={styles.bg} src={face_bg} alt="" />
          <div className={styles.content}>
            <div className={styles.notice}>
              <span>请开始录入真人视频录人至少保证</span>
              <span className={styles.emphasizeText}> 5秒 </span>
              <span>以上</span>
            </div>
            <div className={styles.user}>
              <span>确认是</span>
              <span className={styles.emphasizeText}> {name.split('').fill('*', 0, 1)} </span>
              <span>本人操作</span>
            </div>
            <img className={styles.userIcon} src={face_user} alt="" />
            <div className={styles.exampleImgs}>
              {exampleImgs.map((item) => (
                <img key={item} src={item} alt="" />
              ))}
            </div>
            <div className={styles.button}>
              <div className={styles.uploadVedio}>
                <Button type="primary" style={{ color: '#ffffff' }}>
                  开始验证
                </Button>
                <input
                  className={styles.upload}
                  onChange={onUpload}
                  type="file"
                  accept="video/*"
                  name=""
                  id=""
                  capture="camcorder"
                />
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default connect(({ common, loading }: { common: CommonState; loading: Loading }) => ({
  common,
  submitLoading: loading.effects['common/faceRecognition'],
}))(FaceRecognition);
