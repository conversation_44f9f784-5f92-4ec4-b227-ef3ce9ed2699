.container {
  position: relative;
  color: #333;
  background-color: #fff;

  .bg {
    width: 100vw;
    height: 100vh;
  }

  .content {
    position: absolute;
    top: 0;

    .notice {
      padding: 80px 150px 0 150px;
      font-weight: 500;
      font-size: 40px;
      text-align: center;
    }

    .user {
      margin-top: 16px;
      text-align: center;
    }

    .emphasizeText {
      color: #32b9aa;
    }

    .userIcon {
      width: 100%;
      padding: 20px 0;
    }

    .exampleImgs {
      display: flex;
      justify-content: space-between;
      padding: 40px 60px;
      img {
        width: 144px;
        height: 180px;
      }
    }

    .button {
      padding: 40px 20px;
      .uploadVedio {
        position: relative;
        .upload {
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          left: 0;
          width: 100%;
          opacity: 0;
        }
      }
    }
  }
}

.videoContainer {
  height: 100vh;
  background-color: #000;
  .videoDiv {
    width: 100vw;
    height: calc(100vh - 200px);

    video {
      width: 100%;
      height: 100%;
      object-fit: fill;
    }
  }

  .submitButton {
    padding: 20px;
  }
}

.successModal {
  width: 400px;

  .successTitle {
    padding: 60px;
  }
}

.modalIcon {
  margin-right: 8px;
  font-size: 36px;
}
