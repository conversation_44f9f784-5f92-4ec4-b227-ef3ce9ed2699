.callback {
  width: 100vw;
  display: flex;
  justify-content: center;
  flex-direction: column;
  padding-top: 60px;
  .pageWrap {
    min-height: 250px;
  }
  // 订单信息
  .orderInfo {
    box-sizing: border-box;
    margin-top: 20px;
    padding-bottom: 46px;

    .orderInfoDetails {
      box-sizing: border-box;
      padding: 28px 20px 0 60px;

      div {
        margin-bottom: 28px;
        color: #03081a;
        font-size: 28px;
      }

      div:last-child {
        margin: 0;
      }
    }
  }
  .loadingState {
    display: flex;
    flex-direction: column;
    align-items: center;
    .payState {
      font-size: 36px;
      font-weight: 500;
      color: #3ad3c1;
      margin-top: 36px;
      &.error {
        color: #fc4553;
      }
    }
    img {
      width: 144px;
      height: 144px;
    }
  }
  .btnWrap {
    width: 100%;
    text-align: center;
    margin-top: 54px;
    :global {
      .adm-space {
        --gap: 60px;
      }
      .adm-button {
        width: 502px;
        height: 88px;
        &.goHome {
          margin-top: 54px;
        }
      }
      .adm-button-default {
        background: transparent !important;
        color: #3ad3c1;
        border-color: #3ad3c1;
        &:active {
          &::before {
            opacity: 0;
          }
        }
      }
    }
  }
  .loadingWrap {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    .hint {
      text-align: center;
      font-size: 30px;
      margin-top: 40px;
    }
  }
  .skChase {
    width: 50px;
    height: 50px;
    margin: 0 auto;
    position: relative;
    animation: skChase 2.5s infinite linear both;
  }

  .skChaseDot {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    animation: skChaseDot 2s infinite ease-in-out both;
  }

  .skChaseDot:before {
    content: '';
    display: block;
    width: 25%;
    height: 25%;
    background-color: #3ad3c1;
    border-radius: 100%;
    animation: skChaseDot-before 2s infinite ease-in-out both;
  }

  .skChaseDot:nth-child(1) {
    animation-delay: -1.1s;
  }
  .skChaseDot:nth-child(2) {
    animation-delay: -1s;
  }
  .skChaseDot:nth-child(3) {
    animation-delay: -0.9s;
  }
  .skChaseDot:nth-child(4) {
    animation-delay: -0.8s;
  }
  .skChaseDot:nth-child(5) {
    animation-delay: -0.7s;
  }
  .skChaseDot:nth-child(6) {
    animation-delay: -0.6s;
  }
  .skChaseDot:nth-child(1):before {
    animation-delay: -1.1s;
  }
  .skChaseDot:nth-child(2):before {
    animation-delay: -1s;
  }
  .skChaseDot:nth-child(3):before {
    animation-delay: -0.9s;
  }
  .skChaseDot:nth-child(4):before {
    animation-delay: -0.8s;
  }
  .skChaseDot:nth-child(5):before {
    animation-delay: -0.7s;
  }
  .skChaseDot:nth-child(6):before {
    animation-delay: -0.6s;
  }

  @keyframes skChase {
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes skChaseDot {
    80%,
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes skChaseDot-before {
    50% {
      transform: scale(0.4);
    }
    100%,
    0% {
      transform: scale(1);
    }
  }
}
