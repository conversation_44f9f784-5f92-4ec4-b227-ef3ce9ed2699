/*
 * @Author: yyl
 * @Date: 2023-05-10 17:50:00
 * @LastEditors: yyl
 * @LastEditTime: 2024-02-27 17:24:45
 * @Description: 医保支付（易联众）支付完成回调页面
 */
import React, { Fragment, useEffect, useState } from 'react';
import { getTrade } from '@/pages/OutpatientPayment/service';
import { Button, Space } from 'antd-mobile-v5';
import { Dispatch, history } from 'umi';
import classnames from 'classnames';
import { getOpenId, getOrganCode, getToken } from '@/utils/parameter';
import qs from 'query-string';
import dayjs from 'dayjs';
import { isAlipay, isHytPerson, isWechat } from '@/utils/platform';
import AppScheme from '@/utils/AppScheme';
import { BILL_STATUS, BUTTON_LIST, PLATFORM_APPID } from './dataDictionary';
import styles from './index.less';

const success = 'https://cdnhyt.cd120.com/person/assets/common/success.png';
const error = 'https://cdnhyt.cd120.com/person/assets/common/error.png';

interface IProps {
  dispatch: Dispatch;
  location: {
    query: {
      dealSeq: string;
      bizSysSeq: string;
      code: string; // 对应业务的code
    };
  };
}
const CallBackPage: React.FC<IProps> = (props) => {
  const { whetherAlipay } = window;
  const { location: { query: { dealSeq = '', bizSysSeq = '', code = 'outpatientpayment' } = {} } = {} } = props;
  let timer: any = null;
  let count: number = 1;
  const endCount: number = 30; // 轮询30次
  const [loading, setLoading] = useState<boolean>(true);
  const [payState, setPayState] = useState<string>('toPay'); // 支付状态
  const [orderInfo, setOrderInfo] = useState<any>({}); // 订单信息
  /**
   * 清除定时
   */

  const clearTimerFun = () => {
    clearInterval(timer);
  };
  /**
   * 查询订单信息
   * @return {*}
   */
  const fetchData = () => {
    getTrade({ dealSeq, bizSysSeq, organCode: getOrganCode() })
      .then((res) => {
        if (res.code === '1') {
          try {
            const { billList = [] } = res.data;
            if (billList[0]?.billStatus === 'paid') {
              setOrderInfo(res.data);
            }
            /* 设置状态 */
            setPayState(billList[0].billStatus);
            if (billList[0]?.billStatus !== 'toPay') {
              clearTimerFun();
              setLoading(false);
            }
          } catch (error) {
            console.log('error:', error);
          }
        }
        if (res.code === '0') {
          try {
            clearTimerFun();
            setLoading(false);
          } catch (error) {
            console.log('error:', error);
          }
        }
      })
      .catch((e) => {
        console.log('e:', e);
        setLoading(false);
        clearTimerFun();
      });
  };

  /* 拉起小程序 */
  const openAliPay = (page: string) => {
    const appId = PLATFORM_APPID[getOrganCode()];
    const url = `alipays://platformapi/startapp?appId=${appId}&page=${page}`;
    const lastUrl = `https://ds.alipay.com/?scheme=${encodeURIComponent(url)}`;
    window.location.href = lastUrl;
  };

  /* 跳转首页 */
  const goToHome = () => {
    /* 支付宝环境返回首页 */
    if (isAlipay()) {
      /* 小程序环境直接跳转 */
      if (whetherAlipay) {
        my.navigateTo({ url: '/pages/index/index' });
        return;
      }
      /* 非小程序环境拉起小程序 */
      openAliPay('pages/index/index');
    }
    if (isWechat()) {
      if (getOrganCode() === 'HID0101') {
        // history.push({
        //   pathname: `/hxHome?openid=${getOpenId()}&token=${getToken()}`,
        // });
        window.location.href = `${THE_DOMAIN}/hxHome?openid=${getOpenId()}&organCode=${getOrganCode()}&token=${getToken()}`;
        // window.location.href = `${YH_HX_HOME}&openid=${getOpenId()}&token=${getToken()}`;
      } else {
        history.push({
          pathname: '/home',
        });
      }
      return;
    }
    if (isHytPerson()) {
      AppScheme.closeWeb();
    }
  };

  /**
   * 按钮跳转
   */
  const goTo = (item: any) => {
    const { linkType, path } = item || {};
    const { patPmi, outPatientId, cardId } = orderInfo || {};
    // 跳转首页
    if (linkType === 1) {
      goToHome();
      return;
    }
    /* 跳转到订单详情 */
    /* 支付宝环境 */
    if (isAlipay()) {
      /* 根据不同organCode跳转到不同小程序 */
      /* 判断小程序环境 */
      if (whetherAlipay) {
        /* 华西医院跳转到webview */
        if (getOrganCode() === 'HID0101') {
          history.push({
            pathname: path,
            query: { cardId, outpatientId: outPatientId, papmi: patPmi },
          });
          return;
        }
        /* 平台医院直接跳转到对应的小程序 */
        my.navigateTo({
          url: `/pages/outpatientPayment/detail?${qs.stringify({ outpatientId: outPatientId, papmi: patPmi })}`,
        });
      } else {
        if (getOrganCode() === 'HID0101') {
          /* 华西医院小程序页面链接拼接 */
          const payload = { cardId, outpatientId: outPatientId, papmi: patPmi };
          const queryUrl = encodeURIComponent(`/person${path}?${qs.stringify(payload)}`);
          const encodeUrl = encodeURIComponent(`url=${queryUrl}`);
          const url = `pages/web-view/web-view?${encodeUrl}`;
          openAliPay(url);
          return;
        }
        /* 平台医院小程序 */
        openAliPay(`pages/outpatientPayment/detail?${qs.stringify({ outpatientId: outPatientId, papmi: patPmi })}`);
      }
    } else {
      history.push({
        pathname: path,
        query: { cardId, outpatientId: outPatientId, papmi: patPmi },
      });
    }
  };

  const formatDate = (val: any) => {
    let date = val;
    try {
      date = dayjs(val).format('YYYY-MM-DD HH:mm:ss');
    } catch (error) {
      console.log('error:', error);
    }
    return date;
  };
  useEffect(() => {
    timer = setInterval(() => {
      if (count > endCount) {
        clearTimerFun();
        setLoading(false);
      }
      count += 1;
      fetchData();
    }, 2000);
    return () => {
      clearTimerFun(); // 组件销毁时，清空定时器
    };
  }, []);
  return (
    <div className={styles.callback}>
      <div className={styles.pageWrap}>
        {loading && payState === 'toPay' && (
          <div className={styles.loadingWrap}>
            <div className={styles.skChase}>
              <div className={styles.skChaseDot} />
              <div className={styles.skChaseDot} />
              <div className={styles.skChaseDot} />
              <div className={styles.skChaseDot} />
              <div className={styles.skChaseDot} />
              <div className={styles.skChaseDot} />
            </div>
            <p className={styles.hint}>支付确认中，请稍后...</p>
          </div>
        )}
        {!loading && (
          <div className={styles.loadingState}>
            {payState === 'paid' ? (
              <Fragment>
                <img src={success} alt="" />
                <span className={styles.payState}>支付成功</span>
              </Fragment>
            ) : (
              <Fragment>
                <img src={error} alt="" />
                <span className={classnames(styles.payState, styles.error)}>支付失败</span>
              </Fragment>
            )}
          </div>
        )}
      </div>
      {/* 订单信息 */}
      {payState === 'paid' && (
        <div className={styles.orderInfo}>
          <div className={styles.orderInfoDetails}>
            <div>付款金额：{orderInfo?.billList[0]?.amountPayable}</div>
            <div>商品名称：{orderInfo?.goodsName}</div>
            <div>商户名称：{orderInfo?.billList[0]?.merchantName}</div>
            <div>交易状态：{BILL_STATUS.find((i) => i.value === orderInfo?.billList[0]?.billStatus)?.label}</div>
            <div>支付方式：{orderInfo?.paymentName}</div>
            <div>支付时间：{formatDate(orderInfo?.billList[0]?.updateTime)}</div>
            <div>订单编号：{orderInfo?.billList[0]?.dealSeq}</div>
            <div>订单单号：{orderInfo?.billList[0]?.bizTradeNo}</div>
          </div>
        </div>
      )}

      {/* 按钮 */}
      {!loading && (
        <div className={styles.btnWrap}>
          {payState === 'paid' ? (
            <Space justify="center" direction="vertical">
              {BUTTON_LIST[code].map((item) => {
                return (
                  <Button
                    color={item.btnType}
                    shape="rounded"
                    key={item.id}
                    onClick={() => {
                      goTo(item);
                    }}
                  >
                    {item.text}
                  </Button>
                );
              })}
            </Space>
          ) : (
            <Button shape="rounded" color="primary" className={styles.goHome} onClick={goToHome}>
              返回首页
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

export default CallBackPage;
