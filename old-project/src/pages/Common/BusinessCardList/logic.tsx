import React from 'react';
import { Dispatch, history } from 'umi';
import { Modal, Toast } from 'antd-mobile';
import { ExclamationCircleFilled } from '@ant-design/icons';
import { ICardItem } from '@/pages/PatientCard/data.d';
import { getToken, getOrganCode, getChannelCode } from '@/utils/parameter';
import { HxSessionStorage } from '@/utils/storage';
import { StorageEnum } from '@/utils/enum';
import { Face } from '@/utils/useFace';
import styles from './logic.less';

/**
 * 病案邮寄的逻辑处理
 * 华西
 * @param dispatch
 * @param data 就诊卡数据
 */
const doBAYJ = (dispatch: Dispatch, data: ICardItem) => {
  // const { cardId } = data;
  const { cardId, patientName, credNo } = data;
  Toast.loading('加载中');
  // 验证就诊卡状态
  dispatch({
    type: 'caseMailing/requireProcess',
    payload: {
      cardId,
    },
    callback: (res: any) => {
      console.log('res:', res);
      Toast.hide();
      const { code, errCode, msg, data } = res;
      const errCodeList = ['C1000', 'C1001', 'C1002', 'C1003', 'C1004'];
      // C1000: '该就诊卡对应的身份证信息错误!',
      // C1001: '年龄<18，无法使用病例邮寄业务!!',
      // C1002: '该就诊卡对应的患者已死亡，无法使用病例邮寄业务!',
      // C1003: '改患者需要上传证件照信息!',
      // C1004: '需要上传5S患者本人视频录!',

      // code==='1' 直接走住院记录（by小马哥）
      /* 20220527 新增病案邮寄选卡验证接口返回数据结构调整 */
      if (code === '1') {
        history.push({
          pathname: '/casemailing/type',
          query: {
            cardId,
            data: JSON.stringify(data),
          },
        });
        return;
      }

      if (code === '0' && errCodeList.some((item) => errCode === item && item !== 'C1003' && item !== 'C1004')) {
        Modal.alert(
          <div>
            <ExclamationCircleFilled className={styles.infoIcon} style={{ color: '#FE8F3C' }} />
            <span>温馨提示</span>
          </div>,
          <div style={{ color: 'rgba(0,0,0,0.85)' }}>{msg}</div>,
          [{ text: '我知道了', onPress: () => console.log('ok') }],
        );
        return;
      }

      // 其他错误进行提示
      if (code === '0' && errCodeList.every((item) => item !== errCode)) {
        Toast.info(msg);
      }
    },
  });
};

/**
 * 病案邮寄的逻辑处理
 * 上锦医院
 * @param dispatch
 * @param data 就诊卡数据
 */
const doSJBAYJ = (dispatch: Dispatch, data: ICardItem) => {
  // const { cardId } = data;
  const { cardId, patientName, credNo } = data;

  Toast.loading('加载中');
  // 验证就诊卡状态
  dispatch({
    type: 'medicalRecord/requireProcess',
    payload: {
      cardId,
    },
    callback: async (res: any) => {
      Toast.hide();
      const { code, errCode, msg } = res;
      const errCodeList = ['C1000', 'C1001', 'C1002', 'C1003', 'C1004'];
      // C1000: '该就诊卡对应的身份证信息错误!',
      // C1001: '年龄<18，无法使用病例邮寄业务!!',
      // C1002: '该就诊卡对应的患者已死亡，无法使用病例邮寄业务!',
      // C1003: '需要上传5S患者本人视频录!',
      // C1004: '改患者需要上传证件照信息!',

      // code==='1' 直接走住院记录
      if (code === '1') {
        history.push({
          pathname: '/sjmedicalrecord/hospitalizationrecords',
          query: {
            cardId,
            credNo,
            isJumpFace: true,
          },
        });
        return;
      }

      if (code === '0' && errCodeList.some((item) => errCode === item && item !== 'C1004' && item !== 'C1003')) {
        Modal.alert(
          <div>
            <ExclamationCircleFilled className={styles.infoIcon} style={{ color: '#FE8F3C' }} />
            <span>温馨提示</span>
          </div>,
          <div style={{ color: 'rgba(0,0,0,0.85)' }}>{msg}</div>,
          [{ text: '我知道了', onPress: () => console.log('ok') }],
        );
        return;
      }

      if (code === '0' && errCode === 'C1003') {
        const cardInfo = HxSessionStorage.get(StorageEnum.BIZ_PATIENTCARD_DATA);
        const { credNo = '', patientName = '', credType = '01' } = cardInfo || {};
        const callBackUrl = `${THE_DOMAIN}/sjmedicalrecord/hospitalizationrecords?cardId=${cardId}&credNo=${credNo}&name=${patientName}&organCode=${getOrganCode()}&channelCode=${getChannelCode()}`;
        Face({
          bussinessCode: 'medical-record-send',
          credNo,
          credType,
          callBackUrl,
          patientName,
        });
        return;
      }

      // 其他错误进行提示
      if (code === '0' && errCodeList.every((item) => item !== errCode)) {
        Toast.info(msg);
      }
    },
  });
};

/**
 * 病案邮寄的逻辑处理
 * 天府医院
 * @param dispatch
 * @param data 就诊卡数据
 */
const doTFBAYJ = (dispatch: Dispatch, data: ICardItem) => {
  // const { cardId } = data;
  const { cardId, patientName, credNo } = data;

  Toast.loading('加载中');
  // 验证就诊卡状态
  dispatch({
    type: 'tfCaseMailing/requireProcess',
    payload: {
      cardId,
    },
    callback: async (res: any) => {
      Toast.hide();
      const { code, errCode, msg } = res;
      const errCodeList = ['C1000', 'C1001', 'C1002', 'C1003', 'C1004'];
      // C1000: '该就诊卡对应的身份证信息错误!',
      // C1001: '年龄<18，无法使用病例邮寄业务!!',
      // C1002: '该就诊卡对应的患者已死亡，无法使用病例邮寄业务!',
      // C1003: '改患者需要上传证件照信息!',
      // C1004: '需要上传5S患者本人视频录!',

      // code==='1' 直接走住院记录（by小马哥）
      if (code === '1') {
        history.push({
          pathname: '/tfcasemailing/hospitalizationrecords',
          query: {
            cardId,
            credNo,
            isJumpFace: true,
          },
        });
        return;
      }

      if (code === '0' && errCodeList.some((item) => errCode === item && item !== 'C1003' && item !== 'C1004')) {
        Modal.alert(
          <div>
            <ExclamationCircleFilled className={styles.infoIcon} style={{ color: '#FE8F3C' }} />
            <span>温馨提示</span>
          </div>,
          <div style={{ color: 'rgba(0,0,0,0.85)' }}>{msg}</div>,
          [{ text: '我知道了', onPress: () => console.log('ok') }],
        );
        return;
      }

      if (code === '0' && errCode === 'C1004') {
        const cardInfo = HxSessionStorage.get(StorageEnum.BIZ_PATIENTCARD_DATA);
        const { credNo = '', patientName = '', credType = '01' } = cardInfo || {};
        const callBackUrl = `${THE_DOMAIN}/tfcasemailing/hospitalizationrecords?cardId=${cardId}&credNo=${credNo}&name=${patientName}&organCode=${getOrganCode()}&channelCode=${getChannelCode()}`;
        Face({
          bussinessCode: 'medical-record-send',
          credNo,
          credType,
          callBackUrl,
          patientName,
        });
        return;
      }

      // 其他错误进行提示
      if (code === '0' && errCodeList.every((item) => item !== errCode)) {
        Toast.info(msg);
      }
    },
  });
};

export { doBAYJ, doSJBAYJ, doTFBAYJ };
