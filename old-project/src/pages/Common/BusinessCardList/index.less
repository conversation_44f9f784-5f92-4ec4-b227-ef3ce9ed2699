.container {
  height: -webkit-fill-available;

  .operation {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 30px 30px 30px 40px;
    background: @bg-color-gray;

    .img {
      width: 6px;
      height: 30px;
    }

    .residueCard {
      color: #222;
      font-size: 24px;
      span {
        padding: 0 5px 0 5px;
        color: #fab319;
      }
    }
  }

  .list {
    padding-bottom: 150px;
    background: @bg-color-gray;

    .card {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      &:active {
        opacity: 0.7;
      }
    }

    .noCard {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: 375px;

      .img {
        width: 166px;
        height: 120px;
      }

      .hintText {
        margin-top: 34px;
        color: #666;
        font-size: 26px;
      }
    }
  }

  .addPatientCard {
    position: fixed;
    bottom: 20px;
    left: 30px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 690px;
    height: 90px;
    color: #fff;
    font-size: 32px;
    background: @brand-primary;
    border-radius: 52px;

    &:active {
      opacity: 0.7;
    }
  }

  :global {
    .am-list-body {
      background: @bg-color-gray;
    }

    .am-list-body::before {
      display: none !important;
    }

    .am-list-body::after {
      display: none !important;
    }

    .am-pull-to-refresh-content-wrapper {
      min-height: 100vh;
    }
  }
}

.modal {
  .item {
    height: 100px;
    margin-bottom: 10px;
    color: #333;
    font-size: 28px;
    line-height: 100px;
    background: #fff;

    &:active {
      background: #f5f6f7;
    }
  }

  button {
    width: 100%;
    height: 100px;
    color: #ce0000;
    font-size: 28px;
    line-height: 100px;
    background: #fff;
    border: none;
  }

  :global(.am-modal-body) {
    background: #eee !important;
  }
}
