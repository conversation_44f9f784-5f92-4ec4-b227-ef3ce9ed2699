import React, { FC, useState, useEffect } from 'react';
import { PullToRefresh, ListView, Modal } from 'antd-mobile';
import { connect, history, Dispatch, Loading, ConnectProps } from 'umi';
import { HxIndicator, HxIcon } from '@/components';
import { patientcardAction } from '@/utils/image';
import { HxSessionStorage, HxLocalStorage } from '@/utils/storage';
import { getOrganCode, getToken, HxParameter } from '@/utils/parameter';
import { StorageEnum } from '@/utils/enum';
import { isHytPerson } from '@/utils/platform';
import AppScheme from '@/utils/AppScheme';
import { ItemCard, EHCard, EHCardNew, ItemCardNew } from '@/pages/PatientCard/components';
import { IPatientCardModelState } from '@/pages/PatientCard/model';
import { ICardItem } from '@/pages/PatientCard/data.d';
import styles from './index.less';
import { doBAYJ, doSJBAYJ, doTFBAYJ } from './logic';

interface IProps extends ConnectProps {
  patientCard: IPatientCardModelState;
  dispatch: Dispatch;
  loading?: boolean;
  location: {
    query: {
      /** 前一个页面标识 */
      from?: string;
    };
  };
}

const dataSource = new ListView.DataSource({
  rowHasChanged: (row1: any, row2: any) => row1 !== row2,
});

const HomePage: FC<IProps> = ({
  patientCard,
  loading,
  dispatch,
  location: {
    query: { from },
  },
}) => {
  // 可绑卡数量
  const [canBindCount, setCanBindCount] = useState<number>(0);
  // 是否刷新中
  const [refreshing, setRefreshing] = useState<boolean>(false);
  // 是否显示操作弹窗
  const [showModal, setShowModal] = useState<boolean>(false);
  // 卡列表
  const [userCardList, setUserCardList] = useState<ICardItem[]>([]);

  const fetchData = () => {
    // 当本地缓存机构编码与当前机构编码不一致时，从服务器获取当前机构信息进行缓存
    const { organCode = '' } = HxLocalStorage.get(StorageEnum.CARD_ORGAN_INFO) || {};
    HxParameter.organCode !== organCode && dispatch({ type: 'global/getOrganInfo' });
    // 修改资料等回来后清除model层
    dispatch({
      type: 'patientCard/saveCardInfo',
      payload: {
        occupationCode: '',
        occupationName: '',
        nationCode: '',
        nationName: '',
      },
    });
    dispatch({
      type: 'patientCard/cardList',
      callback: (res: any) => {
        const { canBindCount = 0, userCardList = [] } = res;
        setCanBindCount(canBindCount);
        setUserCardList(userCardList);
      },
    });
  };

  useEffect(() => {
    // mobile进选择添加卡方式界面，然后带了它的卡列表地址，存了modal
    // 添加卡成功会person列表页面后，有地址的话，会去mobile界面
    const { onlineUrl = '' } = patientCard;
    if (onlineUrl !== '') {
      window.location.href = decodeURIComponent(onlineUrl);
      return;
    }

    fetchData();

    window.appOnResume = fetchData;

    return function cleanup() {
      window.appOnResume = undefined;
    };
  }, []);

  /**
   * 刷新就诊卡列表
   */
  const onRefresh = () => {
    setRefreshing(true);
    dispatch({
      type: 'patientCard/cardList',
      callback: (res: any) => {
        const { canBindCount = 0, userCardList = [] } = res;
        setCanBindCount(canBindCount);
        setUserCardList(userCardList);
        setRefreshing(false);
      },
    });
  };

  /**
   * 添加就诊卡
   */
  const addPatientCard = () => {
    if (canBindCount === 0) {
      Modal.alert('提示', '您的就诊卡已达上限，暂不能添加就诊卡', [
        {
          text: '确定',
          onPress: () => {},
        },
      ]);
    } else if (isHytPerson()) {
      const { organCode = '' } = HxLocalStorage.get(StorageEnum.CARD_ORGAN_INFO) || {};
      AppScheme.toBindPatientCard(`organCode=${organCode}`);
    } else {
      history.push('/patientcard/add');
    }
  };

  const handleItemClick = async (data: ICardItem) => {
    const { vipType = '' } = data;
    HxSessionStorage.set(StorageEnum.BIZ_PATIENTCARD_DATA, data);

    // 获取健康人生会员日期
    if (vipType) {
      await dispatch({
        type: 'patientCard/getVipCardInfos',
        payload: {
          credNo: data.credNo,
          organCode: getOrganCode(),
        },
        callback: (res: any) => {
          const { startDate = '', endDate = '' } = res;
          // 将本次会话选择的就诊卡信息缓存在本地
          HxSessionStorage.set(StorageEnum.BIZ_PATIENTCARD_DATA, { ...data, startDate, endDate });
        },
      });
    }
    // 不同业务不同逻辑处理
    switch (from) {
      case 'BAYJ': // 病案邮寄
        doBAYJ(dispatch, data);
        break;

      case 'SJ_BAYJ': // 病案邮寄
        doSJBAYJ(dispatch, data);
        break;

      case 'TF_BAYJ':
        doTFBAYJ(dispatch, data);
        break;
      case 'HEALTHY_LIFE': // 科室主页 健康人生入口
        if (String(vipType) === '1') {
          // 1为会员
          history.push('/HealthyLife?isVip=1');
        } else {
          window.location.href = `${API_ZXMZ}/active/vipcard/home?organCode=HID0101&healthyLife=1&idCardNo=${
            data.credNo
          }&token=${getToken()}`;
        }
        break;
      default:
        break;
    }
  };

  const renderNewRow = (item: ICardItem) => {
    return (
      <div className={styles.card}>
        {item.cardType === 'EHCard' ? (
          <EHCardNew
            item={item}
            key={item.cardId}
            onClick={() => {
              handleItemClick(item);
            }}
            unbindInfo={undefined}
          />
        ) : (
          <ItemCardNew
            item={item}
            key={item.cardId}
            onClick={() => {
              handleItemClick(item);
            }}
            unbindInfo={undefined}
          />
        )}
      </div>
    );
  };

  const renderRow = (item: ICardItem): React.ReactElement => {
    if (['HID0101'].includes(getOrganCode())) {
      return renderNewRow(item);
    }
    return (
      <div className={styles.card}>
        {item.cardType === 'EHCard' ? (
          <EHCard
            item={item}
            key={item.cardId}
            onClick={() => {
              handleItemClick(item);
            }}
          />
        ) : (
          <ItemCard
            item={item}
            key={item.cardId}
            onClick={() => {
              handleItemClick(item);
            }}
          />
        )}
      </div>
    );
  };

  const { whetherAlipay }: any = window;
  return (
    <div className={styles.container}>
      <div className={styles.operation}>
        <img alt="" src={patientcardAction} className={styles.img} onClick={() => setShowModal(true)} />
        {!loading && (
          <div className={styles.residueCard}>
            还可绑卡数 <span>{canBindCount}</span> 张
          </div>
        )}
      </div>
      <div style={{ content: '', overflow: 'hidden' }} />
      <div className={styles.list}>
        {loading && !refreshing ? (
          <HxIndicator />
        ) : userCardList.length ? (
          <ListView
            useBodyScroll
            dataSource={dataSource.cloneWithRows(userCardList || [])}
            pullToRefresh={
              <PullToRefresh
                getScrollContainer={() => <></>}
                indicator={{}}
                direction="down"
                distanceToRefresh={25}
                damping={100}
                refreshing={refreshing}
                onRefresh={onRefresh}
              />
            }
            renderRow={renderRow}
            pageSize={10}
            initialListSize={10}
          />
        ) : (
          <div className={styles.noCard}>
            <HxIcon iconName="patient-nocard" className={styles.img} />
            <div className={styles.hintText}>暂无就诊卡</div>
          </div>
        )}
      </div>
      {whetherAlipay ? (
        <div
          className={styles.addPatientCard}
          onClick={() => {
            my.postMessage({ name: 'create-card-adult' });
          }}
        >
          注册就诊卡
        </div>
      ) : (
        <div className={styles.addPatientCard} onClick={addPatientCard}>
          注册就诊卡
        </div>
      )}

      <Modal
        className={styles.modal}
        popup
        visible={showModal}
        onClose={() => setShowModal(false)}
        animationType="slide-up"
      >
        <div
          className={styles.item}
          onClick={() => {
            history.push('/patientcard/auditcardlist');
          }}
        >
          审核的就诊卡
        </div>
        <button type="button" onClick={() => setShowModal(false)}>
          取消
        </button>
      </Modal>
    </div>
  );
};

export default connect(({ patientCard, loading }: { patientCard: IPatientCardModelState; loading: Loading }) => ({
  patientCard,
  loading: loading.effects['patientCard/cardList'],
}))(HomePage);
