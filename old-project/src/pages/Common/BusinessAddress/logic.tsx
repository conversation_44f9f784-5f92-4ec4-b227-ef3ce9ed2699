import { Dispatch, history } from 'umi';
import { Toast, Modal } from 'antd-mobile';
import { IAddrerssItem } from '@/pages/Address/data.d';
import { HxSessionStorage } from '@/utils/storage';
import { isHytPerson } from '@/utils/platform';
import qs from 'query-string';
import AppScheme from '@/utils/AppScheme';

/**
 * 申请病历复印页面的逻辑处理
 * @param dispatch
 * @param addressData 地址信息
 * @param otherQuery 业务地址页面除to的其它路由参数【需要一并返回给申请病历复印页面】
 */
const doSQBLFY = (dispatch: Dispatch, addressData: IAddrerssItem, otherQuery: any) => {
  // 调接口判断所选地址是否支持邮寄
  const {
    addressId,
    contactUserName,
    phone,
    detailAddress,
    provinceName,
    cityName,
    areaName,
    defaultAddress,
  } = addressData;
  dispatch({
    type: 'caseMailing/requireUserAddress',
    payload: {
      addressId,
    },
    callback: () => {
      const addressObj2JSON = JSON.stringify(addressData);
      // 如果支持
      if (isHytPerson()) {
        // 调原生协议传数据
        const params = `addressId=${addressId}&contactUserName=${contactUserName}&phone=${phone}&detailAddress=${provinceName}${cityName}${areaName}${detailAddress}&defaultAddress=${defaultAddress}&provinceName=${provinceName}&cityName=${cityName}&areaName=${areaName}&onlyDetailAddress=${detailAddress}`;
        AppScheme.toCaseMailSelectAddress(params);
      } else {
        // 网页返回申请病历复印页面
        history.push({
          pathname: '/casemailing/application',
          query: {
            ...otherQuery,
            data: addressObj2JSON,
          },
        });
      }
    },
  });
};
const doXXYPPS = (dispatch: Dispatch, addressData: IAddrerssItem, otherQuery: any) => {
  const { admId, isChange } = otherQuery;
  /* 20220614 新增destAddress入参 */
  const { detailAddress, provinceName, cityName, areaName, areaCode, cityCode } = addressData;
  const address = `${provinceName || ''}${cityName || ''}${areaName || ''}`;
  dispatch({
    type: 'prescription/queryMoney',
    payload: {
      areaCode: areaCode || cityCode,
      destAddress: address + detailAddress,
    },
    callback: (res: any) => {
      const { code, message } = res;
      if (code === '-2') {
        Modal.alert('提示', message, [{ text: '确定', onPress: () => {} }]);
      } else if (code === '-1') {
        Toast.info(message, 1.5);
      } else if (code === '1') {
        const {
          data: { price = '' },
        } = res;
        HxSessionStorage.set(admId, { ...addressData, price, isChange });
        history.go(-1);
      } else {
        Toast.info(message, 1.5);
      }
    },
  });
};

const doMALL = (addressData: IAddrerssItem, otherQuery: any) => {
  const {
    defaultAddress,
    contactUserName,
    phone,
    detailAddress,
    provinceName,
    cityName,
    areaName,
    provinceCode,
    cityCode,
    areaCode,
  } = addressData;
  const addressObj2JSON = JSON.stringify(addressData);
  if (isHytPerson()) {
    // 调原生协议传数据
    const params = `isDefault=${defaultAddress}&userName=${contactUserName}&userPhone=${phone}&detailAddress=${detailAddress}&provinceCode=${provinceCode}&provinceName=${provinceName}&cityCode=${cityCode}&cityName=${cityName}&districtCode=${areaCode}&districtName=${areaName}`;
    AppScheme.toHYTMall(params);
  } else {
    // 网页返回申请病历复印页面
    history.replace({
      pathname: '/drugplanandorder/distribution',
      query: {
        ...otherQuery,
        data: addressObj2JSON,
      },
    });
  }
};

/**
 * 线下药品配送详情页面的逻辑处理
 * @param dispatch
 * @param addressData 地址信息
 * @param otherQuery 业务地址页面除to的其它路由参数【需要一并返回给申请病历复印页面】
 */

/**
 * 上锦医院申请病历复印页面的逻辑处理
 * @param dispatch
 * @param addressData 地址信息
 * @param otherQuery 业务地址页面除to的其它路由参数【需要一并返回给申请病历复印页面】
 */
const doSJBLFY = (dispatch: Dispatch, addressData: IAddrerssItem, otherQuery: any) => {
  // dispatch({
  //   type: 'caseMailing/requireUserAddress',
  //   payload: {
  //     addressId,
  //   },
  // callback: () => {
  const addressObj2JSON = JSON.stringify(addressData);
  // 网页返回申请病历复印页面
  history.push({
    pathname: '/sjrecordorder/detail',
    query: {
      ...otherQuery,
      data: addressObj2JSON,
    },
  });
  // },
  // });
};

/**
 * 申请病历复印页面的逻辑处理
 * @param dispatch
 * @param addressData 地址信息
 * @param otherQuery 业务地址页面除to的其它路由参数【需要一并返回给申请病历复印页面】
 */
const doTFBLFY = (dispatch: Dispatch, addressData: IAddrerssItem, otherQuery: any) => {
  // dispatch({
  //   type: 'caseMailing/requireUserAddress',
  //   payload: {
  //     addressId,
  //   },
  //   callback: () => {
  const addressObj2JSON = JSON.stringify(addressData);
  // 网页返回申请病历复印页面
  history.push({
    pathname: '/tfcasemailing/application',
    query: {
      ...otherQuery,
      data: addressObj2JSON,
    },
  });
  // },
  // });
};

export { doSQBLFY, doXXYPPS, doMALL, doSJBLFY, doTFBLFY };
