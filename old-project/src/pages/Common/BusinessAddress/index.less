@import '~@/styles/mixin.less';

.container {
  min-height: 100vh;
  background: @bg-color-gray;

  .list {
    padding-bottom: 120px;
    .addressitem {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20px;
      padding: 20px 30px;
      background-color: #fff;
      border-bottom: 1px solid #f5f6f7;

      .detail {
        .namePhone {
          display: flex;
          flex-direction: row;
          align-items: center;
          .contactUserName {
            color: #333;
            font-size: 28px;
          }
          .phone {
            margin-left: 10px;
            color: #999;
            font-size: 26px;
          }
        }
        .address {
          margin-top: 10px;
          color: #666;
          font-size: 26px;
          .defaultAddress {
            display: inline;
            margin-right: 10px;
            padding: 1px 12px;
            color: #fff;
            font-size: 20px;
            background-color: #fea75a;
            border-radius: 15px;
          }
        }
      }
      .img {
        height: 100%;
        padding-left: 41px;
        .edit {
          width: 72px;
          height: 72px;
        }
      }
    }
  }

  .add {
    position: fixed;
    bottom: 20px;
    left: 30px;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 690px;
    height: 90px;
    color: #fff;
    font-size: 32px;
    background: @brand-primary;
    border-radius: 16px;

    &:active {
      opacity: 0.9;
    }
  }

  :global {
    .am-list-body {
      padding: 20px;
      background: @bg-color-gray;
      padding-bottom: 110px;
    }

    .am-list-body::before {
      display: none !important;
    }

    .am-list-body::after {
      display: none !important;
    }

    .am-pull-to-refresh-content-wrapper {
      min-height: 100vh;
    }
  }
}
