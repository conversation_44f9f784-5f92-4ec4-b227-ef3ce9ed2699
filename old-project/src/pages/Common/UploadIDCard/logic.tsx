import React, { ReactElement } from 'react';
import { history, Dispatch } from 'umi';
import { Modal, Toast } from 'antd-mobile';
import { ExclamationCircleFilled, CheckCircleFilled } from '@ant-design/icons';
import { getOrganCode } from '@/utils/parameter';
import { ConfirmApplyQueryType } from '@/pages/CaseMailing/service';
import { HxLocalStorage } from '@/utils/storage';
import styles from './logic.less';

/**
 * 去住院记录页面的逻辑处理
 * @param cardId 就诊卡ID
 * @param to 下一个页面标识
 * @param name 患者姓名
 */
const doZYJL = (cardId: string, to: string, name: string) => {
  // TODO: 调商汤接口，上传身份证
  const isPass = true;

  // 验证身份证和就诊卡人信息是否匹配，不匹配就提示
  if (isPass) {
    // 成功，则进入人脸识别
    history.push({
      pathname: '/common/facerecognition',
      query: {
        to,
        cardId,
        name,
      },
    });
  } else {
    Modal.alert(
      <div>
        <ExclamationCircleFilled className={styles.warningIcon} />
        温馨提示
      </div>,
      <div style={{ color: 'rgba(0,0,0,0.85)' }}>身份证信息与就诊卡信息不匹配，非患者本人暂不支持线上病案邮寄。</div>,
      [
        {
          text: '我知道了',
          onPress: () => {},
        },
      ],
    );
  }
};

/**
 * 去病历订单的逻辑处理
 * @param dispatch
 * @param dispatchType 接口名称
 * @param queryData 接口入参
 */
const doBLDD = (dispatch: Dispatch, dispatchType: string, queryData: ConfirmApplyQueryType) => {
  let popupContentDom: ReactElement;
  if (queryData?.wayCode === 1) {
    popupContentDom = (
      <div style={{ color: 'rgba(0, 0, 0, 0.85)' }}>
        已提交病案复印申请，请等待病案人员审批。审批通过后会在
        <span style={{ color: '#fa974c' }}>【四川大学华西微信公众号】</span>和
        <span style={{ color: '#fa974c' }}>【华医通APP】</span>内发送推送，提醒您前往
        <span style={{ color: '#fa974c' }}>【我的-病案订单-订单详情】</span>选择预约取件时间，以便您取件。
      </div>
    );
  } else {
    popupContentDom = (
      <div style={{ color: 'rgba(0, 0, 0, 0.85)' }}>
        已提交病案复印申请， 请等待病案人员审批，审批后会在四川大学华西医院微信公众号和APP内推送提醒您
      </div>
    );
  }
  // 向后端发送病历邮寄申请请求，成功后进入病历订单页面
  dispatch({
    type: dispatchType,
    payload: queryData,
    callback: () => {
      Modal.alert(
        <div>
          <CheckCircleFilled className={styles.successIcon} />
          <span>申请已提交</span>
        </div>,
        popupContentDom,
        [
          {
            text: '查看订单',
            onPress: () => {
              history.push(`/caseprintorder/home?wayCode=${queryData?.wayCode}`);
            },
          },
        ],
      );
    },
  });
};

/**
 * 华西上锦医院病例复印上传身份证
 * @photoList 上传的身份证
 */

const doSJBLDD = (dispatch: Dispatch, photoList: any) => {
  const idImages = photoList.map((item) => ({
    imageUrl: item.defaultImage,
    imageType: `idCard_${item.type}`,
  }));

  const APPLY_RECORD = JSON.parse(HxLocalStorage.get('APPLY_RECORD'));

  const applyData = { ...APPLY_RECORD, idImages };

  HxLocalStorage.set('APPLY_RECORD', JSON.stringify(applyData));

  history.push('/sjmedicalrecord/recordapply');
};

/**
 * 天府医院去病历订单的逻辑处理
 * @param dispatch
 * @param dispatchType 接口名称
 * @param queryData 接口入参
 */
const doTFBLDD = (dispatch: Dispatch, dispatchType: string, queryData: ConfirmApplyQueryType) => {
  let popupContentDom: ReactElement;
  if (queryData?.wayCode === 1) {
    popupContentDom = (
      <div style={{ color: 'rgba(0, 0, 0, 0.85)' }}>
        已提交病案复印申请，请等待病案人员审批。审批通过后会在
        <span style={{ color: '#fa974c' }}>【四川大学华西微信公众号】</span>和
        <span style={{ color: '#fa974c' }}>【华医通APP】</span>内发送推送，提醒您前往
        <span style={{ color: '#fa974c' }}>【我的-病案订单-订单详情】</span>选择预约取件时间，以便您取件。
      </div>
    );
  } else {
    popupContentDom = (
      <div style={{ color: 'rgba(0, 0, 0, 0.85)' }}>
        已提交病案复印申请， 请等待病案人员审批，审批后会在四川大学华西医院微信公众号和APP内推送提醒您
      </div>
    );
  }
  // 向后端发送病历邮寄申请请求，成功后进入病历订单页面
  dispatch({
    type: dispatchType,
    payload: { ...queryData, organCode: getOrganCode() },
    callback: () => {
      Modal.alert(
        <div>
          <CheckCircleFilled className={styles.successIcon} />
          <span>申请已提交</span>
        </div>,
        popupContentDom,
        [
          {
            text: '查看订单',
            onPress: () => {
              history.push(`/tfcaseprintorder/home?wayCode=${queryData?.wayCode}`);
            },
          },
        ],
      );
    },
  });
};

export { doZYJL, doBLDD, doSJBLDD, doTFBLDD };
