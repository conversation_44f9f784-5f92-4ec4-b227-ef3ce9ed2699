.container {
  height: 100vh;
  background-color: #fff;

  .upload {
    display: flex;
    justify-content: space-between;
    padding: 20px;

    .uploadImage {
      position: relative;

      img {
        width: 340px;
        height: 230px;
      }

      .imagePicker {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        opacity: 0;
      }
    }

    .text {
      padding-top: 20px;
      color: #333;
      text-align: center;
    }
  }

  .margin {
    height: 20px;
    background-color: #f2f2f2;
  }

  .claim {
    margin-top: 20px;
    padding: 20px;

    .claimTitle {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: 24px;

      .horizontalLine {
        width: 32px;
        height: 1px;
        background: #ccc;
      }

      .claimTitleText {
        margin: 0 30px;
        color: #333;
        font-weight: 500;
        font-size: 36px;
      }
    }

    .claimText {
      padding: 40px 0;
      color: #999;
      font-size: 24px;

      .noticeText {
        color: #32b9aa;
      }
    }

    .exampleImgs {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 40px;

      img {
        width: 150px;
        height: 108px;
      }

      .exampleImgsText {
        color: #333;
        font-size: 24px;
        text-align: center;
      }
    }

    .button {
      // padding-top: 200px;
      position: fixed;
      width: 100%;
      left: 0;
      bottom: 0;
      padding: 16px 24px;
      box-shadow: 0px -4px 8px 0px rgba(152, 158, 180, 0.1);
      padding-bottom: calc(16px + constant(safe-area-inset-bottom)); ///兼容 IOS<11.2/
      padding-bottom: calc(16px + env(safe-area-inset-bottom)); ///兼容 IOS>11.2/
    }
  }
}

.warningIcon {
  margin-right: 8px;
  color: #f0944f;
  font-size: 36px;
}

:global {
  .am-button {
    &::before {
      border: none!important;
    }
  }
}
