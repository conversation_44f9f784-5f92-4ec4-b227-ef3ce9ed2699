import React, { useEffect, useState } from 'react';
import { Toast, Button, Modal } from 'antd-mobile';
import { connect, Dispatch, Loading } from 'umi';
import { ExclamationCircleFilled } from '@ant-design/icons';
import OssUpload from '@/utils/ossUpload';
import { HxIndicator } from '@/components';
import _ from 'lodash';
import styles from './index.less';
import { doZYJL, doBLDD, doSJBLDD, doTFBLDD } from './logic';

const photoDefaultList = [
  {
    defaultImage: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/front_of_ID_card.png',
    text: '身份证人脸面',
    key: 1,
    type: 'front',
  },
  {
    defaultImage: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/reverse_side_of_ID_card.png',
    text: '身份证国徽面',
    key: 2,
    type: 'back',
  },
];

const exampleImgs = [
  {
    image: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/ID_card_standard.png',
    text: '标准',
  },
  {
    image: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/ID_card_missing.png',
    text: '边框缺失',
  },
  {
    image: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/ID_card_blurry.png',
    text: '照片模糊',
  },
  {
    image: 'https://hxgywx.oss-cn-shenzhen.aliyuncs.com/person/assets/common/ID_card-reflective.png',
    text: '拍摄反光',
  },
];

const toList = [
  {
    key: 'ZYJL',
    desc: '进入人脸识别页面并通过人脸识别后，进入住院记录页面',
    buttonName: '下一步',
  },
  {
    key: 'BLDD',
    desc: '上传身份证后，进入病历订单页面',
    buttonName: '提交病案申请',
  },
  {
    key: 'BLDD_UPDATE',
    desc: '修改身份证后，进入病历订单页面',
    buttonName: '提交病案申请',
  },
  {
    key: 'SJ_BLDD',
    desc: '修改身份证后，进入病历订单页面',
    buttonName: '下一步',
  },
];

interface UploadIDCardProps {
  dispatch: Dispatch;
  loading: boolean | undefined;
  location: {
    query: {
      /** 患者姓名【该页面需要显示】 */
      name: string;
      /** 下一个页面标识【该页面的逻辑标识】 */
      to: string;
      /** 就诊卡ID【住院记录页面需要】 */
      cardId?: string;
      /** BLDD （病历订单）业务调用确认订单接口需要的入参【需要使用JSON.parse()解析】 */
      queryData?: string;
      /**  申请记录ID 【 BLDD_UPDATE （病历订单修改证件照）接口需要的入参】 */
      recordId?: string;
      /**  病案邮寄类型 【 BLDD_UPDATE （病历订单修改证件照）接口需要的入参】 */
      wayCode?: number;
    };
  };
}

const UploadIDCard: React.FC<UploadIDCardProps> = (props) => {
  // props
  const {
    dispatch,
    loading,
    location: {
      query: { to = '', cardId = '', name = '', queryData, recordId, wayCode },
    },
  } = props;

  //  state
  const [photoList, setPhotoList] = useState(photoDefaultList);
  const [notice, setNotice] = useState(''); // 页面提示
  const [buttonName, setButtonName] = useState('下一步');
  const [modelNotice, setModelNotice] = useState(''); // modal提示内容

  /**
   * 获取并设置文案
   * @param textCode 文案类型（接口入参）
   * @param setFunction 设置文案
   */
  const fetchText = (textCode: string, setFunction: React.Dispatch<React.SetStateAction<string>>) => {
    dispatch({
      type: 'common/queryText',
      payload: {
        textCode,
      },
      callback: (text: string) => {
        setFunction(text);
      },
    });
  };

  useEffect(() => {
    // 上传身份证页面的文案
    fetchText('CASE_MAIL_VERIFY_TEXT', setNotice);
    // 二次提醒弹窗文案
    fetchText('CASE_MAIL_POPUP_WIN', setModelNotice);
  }, []);

  useEffect(() => {
    if (to) {
      const currentButtonName = toList.find((item) => item.key === to)?.buttonName;
      currentButtonName && setButtonName(currentButtonName);
    }
  }, [to]);

  /**
   * 上传照片
   * @param e 事件
   * @param key 当前上传照片的key
   */
  const onUpload = (e: React.ChangeEvent<HTMLInputElement>, key: number) => {
    const files = e.target.files || [];
    if (files?.length > 0) {
      Toast.loading('上传中', 0);
      const upload = new OssUpload();
      upload
        .uploadImg({ file: files[0] })
        .then((res: any) => {
          console.log('res', res);
          setPhotoList((pre) => {
            return pre.map((item) => {
              if (item.key === key) {
                return {
                  ...item,
                  defaultImage: res,
                };
              }
              return item;
            });
          });
          Toast.hide();
          Toast.info('上传成功～', 1);
        })
        .catch((err) => {
          Toast.fail('上传照片出错，请重试');
          console.log('err', err);
        });
    }
  };

  /**
   * 显示温馨提示
   */
  const showNotice = () => {
    Modal.alert(
      <div>
        <ExclamationCircleFilled className={styles.warningIcon} />
        温馨提示
      </div>,
      <div dangerouslySetInnerHTML={{ __html: modelNotice }} />,
      [
        { text: '取消操作', onPress: () => console.log('cancel'), style: { color: '#999999' } },
        {
          text: '我知道了',
          onPress: () => {
            // onOk(nextPage);
            // 调接口的入参
            let newQueryData: any = {};
            let dispatchType = '';

            switch (to) {
              case 'ZYJL': // 住院记录
                doZYJL(cardId, to, name);
                break;

              case 'BLDD': // 病历订单（申请病历复印时上传证件照）
              case 'BLDD_UPDATE': // 病历订单（申请病历复印被拒绝后修改证件照）
                if (to === 'BLDD_UPDATE') {
                  newQueryData.recordId = recordId;
                  newQueryData.wayCode = wayCode;
                  dispatchType = 'caseMailing/updateIdImages';
                } else if (queryData) {
                  // 申请时上传证件照
                  newQueryData = JSON.parse(queryData);

                  try {
                    if ('applyRecordId' in newQueryData && newQueryData.applyRecordId) {
                      /* 家属代办 */
                      dispatchType = 'caseMailing/saveProxyApplyRecord';
                    } else {
                      /* 本人办理 */
                      dispatchType = 'caseMailing/confirmApply';
                    }
                  } catch (error) {
                    console.log('error:', error);
                  }
                }
                newQueryData.idImages = photoList.map((item) => ({
                  imagesUrl: item.defaultImage,
                  type: item.type,
                }));
                doBLDD(dispatch, dispatchType, newQueryData);
                break;
              case 'SJ_BLDD':
                doSJBLDD(dispatch, photoList);
                break;

              case 'TF_BLDD': // 病历订单（申请病历复印时上传证件照）
              case 'TF_BLDD_UPDATE': // 病历订单（申请病历复印被拒绝后修改证件照）
                if (to === 'TF_BLDD_UPDATE') {
                  newQueryData.recordId = recordId;
                  newQueryData.wayCode = wayCode;
                  dispatchType = 'tfCaseMailing/updateIdImages';
                } else if (queryData) {
                  // 申请时上传证件照
                  newQueryData = JSON.parse(queryData);
                  dispatchType = 'tfCaseMailing/confirmApply';
                }

                newQueryData.idImages = photoList.map((item) => ({
                  imagesUrl: item.defaultImage,
                  type: item.type,
                }));
                doTFBLDD(dispatch, dispatchType, newQueryData);
                break;

              default:
                break;
            }
          },
        },
      ],
    );
  };

  const onNext = _.debounce(() => {
    // 先校验是否上传证件照
    if (photoList.some((item, index) => item.defaultImage === photoDefaultList[index].defaultImage)) {
      Toast.info(to === 'SJ_BLDD' ? '您还未上传身份证～' : '请上传证件照');
      return;
    }
    showNotice();
  }, 300);

  /**
   * @name: 检测是否已经上传了
   * @param {*}
   * @return {*}
   */

  const checkUpImg = () => {
    return !photoList.some((item, index) => item.defaultImage === photoDefaultList[index].defaultImage);
  };

  return (
    <div className={styles.container}>
      {loading && <HxIndicator />}
      <div className={styles.upload}>
        {photoList.map((item) => (
          <div key={item.key}>
            <div className={styles.uploadImage}>
              <img src={item.defaultImage} alt="" />
              <input
                className={styles.imagePicker}
                type="file"
                accept="image/*"
                onChange={(e) => {
                  onUpload(e, item.key);
                }}
              />
            </div>
            <div className={styles.text}>{item.text}</div>
          </div>
        ))}
      </div>
      <div className={styles.margin} />
      <div className={styles.claim}>
        <div className={styles.claimTitle}>
          <span className={styles.horizontalLine} />
          <span className={styles.claimTitleText}>身份证拍摄要求</span>
          <span className={styles.horizontalLine} />
        </div>
        <div dangerouslySetInnerHTML={{ __html: notice }} />
        <div className={styles.exampleImgs}>
          {exampleImgs.map((item) => (
            <div key={item.text}>
              <img src={item.image} alt="" />
              <div className={styles.exampleImgsText}>{item.text}</div>
            </div>
          ))}
        </div>
        <div className={styles.button}>
          <Button
            type="primary"
            onClick={onNext}
            style={{ color: '#ffffff', borderRadius: '52px', backgroundColor: checkUpImg() ? '#3AD3C1' : '#B0B3BF' }}
          >
            {buttonName}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default connect(({ loading }: { loading: Loading }) => ({ loading: loading.global }))(UploadIDCard);
