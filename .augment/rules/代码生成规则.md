---
type: "always_apply"
---

# 项目开发规则

## 代码生成规则

- 必须保持向后兼容，不得破坏现有功能
- 新增代码必须与现有逻辑兼容
- 除非用户明确要求重构，否则只做增量修改

## 逻辑兼容规则

- 新增逻辑必须确保向下兼容，不能影响已有的业务流程
- 修改条件判断时，必须考虑所有现有的执行路径
- 添加新功能时，应使用条件分支而非直接修改原有逻辑
- 确保新逻辑在各种边界情况下都不会破坏原有功能

## 小程序兼容规则

- 技术栈：uniapp + Vue3 + setup 语法
- 目标平台：微信小程序、支付宝小程序、抖音小程序
- 确保代码在所有目标平台正常运行

## 分包规则

- 如果出现分包引入其他分包的 API 接口，不做引用，直接在本分包增加即可
- 每个分包保持独立性，避免分包间的直接依赖关系

## 文档生成规则

- 禁止自动生成文档格式的内容
- 只有用户明确要求"生成文档"时才输出文档格式
- 默认使用简洁的回复格式

## React 迁移规则

从 react-old 项目迁移到 uniapp 项目时：

1. **设计稿规格**：基于 750px 设计稿，按 1:1 比例迁移尺寸
2. **样式优先级**：确保 UI 样式完全一致，包括布局、颜色、字体、间距
3. **逻辑保持**：业务逻辑、交互流程、数据处理保持与原 React 版本一致
4. **适配转换**：React JSX 语法转换为 Vue3 template 语法
5. **API 调用适配**：
   - 必须先使用 codebase-retrieval 工具查看当前 uniapp 项目的 request 结构
   - 参考现有的 API 调用方式和参数格式
   - React 项目的某些参数在 uniapp 项目中可能不存在，需要根据实际情况调整
   - 保持接口地址和核心业务参数不变，但调用方式要符合当前项目规范
6. **图片迁移**：复制老项目到新项目页面对应的 assets 目录下
